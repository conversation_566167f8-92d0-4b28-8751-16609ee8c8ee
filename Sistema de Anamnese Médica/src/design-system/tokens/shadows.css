/* Medical Shadow Tokens - Premium Healthcare Depth System v3.0 */

:root {
  /* Medical Shadow Base Values - Enhanced */
  --medical-shadow-color: rgba(15, 23, 42, 0.08);      /* Softer primary shadow */
  --medical-shadow-color-dark: rgba(0, 0, 0, 0.2);     /* Refined dark shadow */
  --medical-shadow-color-light: rgba(15, 23, 42, 0.03); /* Ultra-light shadow */
  --medical-shadow-color-medium: rgba(15, 23, 42, 0.12); /* Medium shadow */
  --medical-shadow-color-strong: rgba(15, 23, 42, 0.16); /* Strong shadow */
  
  /* Medical Shadow Scale - Premium Professional Hierarchy */
  --medical-shadow-none: none;
  --medical-shadow-xs: 0 1px 2px 0 var(--medical-shadow-color-light);
  --medical-shadow-sm: 0 1px 3px 0 var(--medical-shadow-color), 0 1px 2px -1px var(--medical-shadow-color-light);
  --medical-shadow-base: 0 4px 6px -1px var(--medical-shadow-color), 0 2px 4px -2px var(--medical-shadow-color-light);
  --medical-shadow-md: 0 6px 10px -2px var(--medical-shadow-color), 0 2px 4px -2px var(--medical-shadow-color-light);
  --medical-shadow-lg: 0 10px 15px -3px var(--medical-shadow-color-medium), 0 4px 6px -4px var(--medical-shadow-color);
  --medical-shadow-xl: 0 20px 25px -5px var(--medical-shadow-color-strong), 0 8px 10px -6px var(--medical-shadow-color-medium);
  --medical-shadow-2xl: 0 25px 50px -12px var(--medical-shadow-color-strong);
  --medical-shadow-3xl: 0 35px 60px -12px var(--medical-shadow-color-strong);
  
  /* Medical Component Specific Shadows */
  --medical-shadow-card: var(--medical-shadow-sm);
  --medical-shadow-card-hover: var(--medical-shadow-md);
  --medical-shadow-elevated: var(--medical-shadow-lg);
  --medical-shadow-modal: var(--medical-shadow-2xl);
  --medical-shadow-dropdown: var(--medical-shadow-lg);
  --medical-shadow-tooltip: var(--medical-shadow-base);
  
  /* Medical Glass Morphism Shadows */
  --medical-shadow-glass: 0 4px 16px 0 rgba(15, 23, 42, 0.08), 0 1px 4px 0 rgba(15, 23, 42, 0.04);
  --medical-shadow-glass-hover: 0 8px 24px 0 rgba(15, 23, 42, 0.12), 0 2px 8px 0 rgba(15, 23, 42, 0.08);
  --medical-shadow-glass-elevated: 0 12px 32px 0 rgba(15, 23, 42, 0.16), 0 4px 12px 0 rgba(15, 23, 42, 0.12);
  
  /* Medical Priority-Based Shadows */
  --medical-shadow-critical: 0 4px 16px 0 rgba(217, 45, 32, 0.2), 0 2px 8px 0 rgba(217, 45, 32, 0.1);
  --medical-shadow-urgent: 0 4px 16px 0 rgba(247, 144, 9, 0.2), 0 2px 8px 0 rgba(247, 144, 9, 0.1);
  --medical-shadow-emergency: 0 8px 24px 0 rgba(217, 45, 32, 0.3), 0 4px 12px 0 rgba(217, 45, 32, 0.2);
  
  /* Medical Interactive Shadows */
  --medical-shadow-button: 0 2px 4px 0 rgba(15, 23, 42, 0.1);
  --medical-shadow-button-hover: 0 4px 8px 0 rgba(15, 23, 42, 0.15);
  --medical-shadow-button-active: 0 1px 2px 0 rgba(15, 23, 42, 0.1);
  --medical-shadow-input: inset 0 2px 4px 0 rgba(15, 23, 42, 0.06);
  --medical-shadow-input-focus: 0 0 0 3px rgba(10, 110, 189, 0.1), inset 0 2px 4px 0 rgba(15, 23, 42, 0.06);
  
  /* Medical Dashboard Shadows */
  --medical-shadow-dashboard: var(--medical-shadow-sm);
  --medical-shadow-widget: var(--medical-shadow-xs);
  --medical-shadow-hero: var(--medical-shadow-xl);
  
  /* Medical Status Indicator Shadows */
  --medical-shadow-status-glow: 0 0 8px 2px;
  --medical-shadow-critical-glow: var(--medical-shadow-status-glow) rgba(217, 45, 32, 0.3);
  --medical-shadow-urgent-glow: var(--medical-shadow-status-glow) rgba(247, 144, 9, 0.3);
  --medical-shadow-success-glow: var(--medical-shadow-status-glow) rgba(34, 197, 94, 0.3);
}

/* Dark Mode Shadow Adjustments */
[data-theme="dark"], .dark {
  --medical-shadow-color: rgba(0, 0, 0, 0.5);
  --medical-shadow-color-light: rgba(0, 0, 0, 0.3);
  
  /* Enhanced shadows for dark mode visibility */
  --medical-shadow-base: 0 4px 8px 0 var(--medical-shadow-color), 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  --medical-shadow-elevated: 0 12px 20px 0 var(--medical-shadow-color), 0 4px 8px 0 rgba(0, 0, 0, 0.3);
  --medical-shadow-modal: 0 25px 60px -12px var(--medical-shadow-color);
  
  /* Dark mode glass shadows */
  --medical-shadow-glass: 0 4px 16px 0 rgba(0, 0, 0, 0.3), 0 1px 4px 0 rgba(0, 0, 0, 0.2);
  --medical-shadow-glass-hover: 0 8px 24px 0 rgba(0, 0, 0, 0.4), 0 2px 8px 0 rgba(0, 0, 0, 0.3);
}

/* Medical Shadow Utility Classes */
.shadow-medical-none { box-shadow: var(--medical-shadow-none); }
.shadow-medical-xs { box-shadow: var(--medical-shadow-xs); }
.shadow-medical-sm { box-shadow: var(--medical-shadow-sm); }
.shadow-medical-base { box-shadow: var(--medical-shadow-base); }
.shadow-medical-md { box-shadow: var(--medical-shadow-md); }
.shadow-medical-lg { box-shadow: var(--medical-shadow-lg); }
.shadow-medical-xl { box-shadow: var(--medical-shadow-xl); }
.shadow-medical-2xl { box-shadow: var(--medical-shadow-2xl); }

/* Medical Component Shadow Classes */
.shadow-medical-card { box-shadow: var(--medical-shadow-card); }
.shadow-medical-card-hover { box-shadow: var(--medical-shadow-card-hover); }
.shadow-medical-elevated { box-shadow: var(--medical-shadow-elevated); }
.shadow-medical-modal { box-shadow: var(--medical-shadow-modal); }
.shadow-medical-dropdown { box-shadow: var(--medical-shadow-dropdown); }
.shadow-medical-tooltip { box-shadow: var(--medical-shadow-tooltip); }

/* Medical Glass Morphism Shadow Classes */
.shadow-medical-glass { box-shadow: var(--medical-shadow-glass); }
.shadow-medical-glass-hover { box-shadow: var(--medical-shadow-glass-hover); }
.shadow-medical-glass-elevated { box-shadow: var(--medical-shadow-glass-elevated); }

/* Medical Priority Shadow Classes */
.shadow-medical-critical { box-shadow: var(--medical-shadow-critical); }
.shadow-medical-urgent { box-shadow: var(--medical-shadow-urgent); }
.shadow-medical-emergency { box-shadow: var(--medical-shadow-emergency); }

/* Medical Interactive Shadow Classes */
.shadow-medical-button { box-shadow: var(--medical-shadow-button); }
.shadow-medical-button-hover { box-shadow: var(--medical-shadow-button-hover); }
.shadow-medical-button-active { box-shadow: var(--medical-shadow-button-active); }
.shadow-medical-input { box-shadow: var(--medical-shadow-input); }
.shadow-medical-input-focus { box-shadow: var(--medical-shadow-input-focus); }

/* Medical Dashboard Shadow Classes */
.shadow-medical-dashboard { box-shadow: var(--medical-shadow-dashboard); }
.shadow-medical-widget { box-shadow: var(--medical-shadow-widget); }
.shadow-medical-hero { box-shadow: var(--medical-shadow-hero); }

/* Medical Status Glow Classes */
.shadow-medical-critical-glow { box-shadow: var(--medical-shadow-critical-glow); }
.shadow-medical-urgent-glow { box-shadow: var(--medical-shadow-urgent-glow); }
.shadow-medical-success-glow { box-shadow: var(--medical-shadow-success-glow); }

/* Medical Hover Shadow Transitions */
.shadow-medical-hover-transition {
  transition: box-shadow 200ms ease-out;
}

.shadow-medical-hover-transition:hover {
  box-shadow: var(--medical-shadow-elevated);
}

.shadow-medical-glass-hover-transition {
  transition: box-shadow 200ms ease-out;
}

.shadow-medical-glass-hover-transition:hover {
  box-shadow: var(--medical-shadow-glass-hover);
}

/* Medical Focus Shadow (for accessibility) */
.shadow-medical-focus {
  box-shadow: 0 0 0 3px rgba(10, 110, 189, 0.1);
}

.shadow-medical-focus-critical {
  box-shadow: 0 0 0 3px rgba(217, 45, 32, 0.1);
}

.shadow-medical-focus-urgent {
  box-shadow: 0 0 0 3px rgba(247, 144, 9, 0.1);
}

/* Medical Inset Shadows */
.shadow-medical-inset { 
  box-shadow: inset 0 2px 4px 0 rgba(15, 23, 42, 0.06); 
}

.shadow-medical-inset-lg { 
  box-shadow: inset 0 4px 8px 0 rgba(15, 23, 42, 0.1); 
}

/* Medical Performance Optimizations */
.shadow-medical-gpu {
  transform: translateZ(0);
  will-change: box-shadow;
}

/* Medical Text Shadows for Better Readability */
.text-shadow-medical-light {
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.text-shadow-medical-dark {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Medical Drop Shadow Filters (for SVGs and complex elements) */
.filter-medical-drop-shadow-sm {
  filter: drop-shadow(0 1px 2px rgba(15, 23, 42, 0.1));
}

.filter-medical-drop-shadow-md {
  filter: drop-shadow(0 4px 6px rgba(15, 23, 42, 0.1));
}

.filter-medical-drop-shadow-lg {
  filter: drop-shadow(0 10px 15px rgba(15, 23, 42, 0.1));
}

/* Medical Print Shadows (removed for print) */
@media print {
  .shadow-medical-print-none {
    box-shadow: none !important;
  }
  
  .shadow-medical-print-subtle {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }
}