/* Medical Typography Tokens - Premium Healthcare Design System v3.0 */

:root {
  /* Medical Font Families - Premium Professional Stack */
  --medical-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --medical-font-family-mono: 'SF Mono', 'JetBrains Mono', 'Fira Code', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --medical-font-family-professional: 'Inter', 'SF Pro Text', system-ui, sans-serif;
  --medical-font-family-display: 'Inter', 'SF Pro Display', system-ui, sans-serif;
  
  /* Medical Font Sizes - Enhanced for Healthcare Readability */
  --medical-text-xs: 0.75rem;     /* 12px - Small labels, metadata */
  --medical-text-sm: 0.875rem;    /* 14px - Secondary text, captions */
  --medical-text-base: 1rem;      /* 16px - Primary body text */
  --medical-text-lg: 1.125rem;    /* 18px - Large body text, important info */
  --medical-text-xl: 1.25rem;     /* 20px - Subheadings */
  --medical-text-2xl: 1.5rem;     /* 24px - Section headings */
  --medical-text-3xl: 1.875rem;   /* 30px - Page headings */
  --medical-text-4xl: 2.25rem;    /* 36px - Major headings */
  --medical-text-5xl: 3rem;       /* 48px - Hero text */
  --medical-text-6xl: 3.75rem;    /* 60px - Display text */
  
  /* Medical Line Heights - Enhanced for Healthcare Documentation */
  --medical-leading-none: 1;          /* For display text */
  --medical-leading-tight: 1.25;      /* For headings */
  --medical-leading-snug: 1.375;      /* For subheadings */
  --medical-leading-normal: 1.5;      /* For body text */
  --medical-leading-relaxed: 1.625;   /* For comfortable reading */
  --medical-leading-loose: 1.75;      /* For accessibility */
  --medical-leading-medical: 1.7;     /* Optimal for medical documentation */
  --medical-leading-extra-loose: 2;   /* For maximum readability */
  
  /* Medical Font Weights */
  --medical-font-light: 300;
  --medical-font-normal: 400;
  --medical-font-medium: 500;
  --medical-font-semibold: 600;
  --medical-font-bold: 700;
  --medical-font-extrabold: 800;
  --medical-font-black: 900;
  
  /* Medical Letter Spacing */
  --medical-tracking-tighter: -0.05em;
  --medical-tracking-tight: -0.025em;
  --medical-tracking-normal: 0em;
  --medical-tracking-wide: 0.025em;
  --medical-tracking-wider: 0.05em;
  --medical-tracking-widest: 0.1em;
}

/* Medical Typography Scale Classes */
.text-medical-xs {
  font-size: var(--medical-text-xs);
  line-height: var(--medical-leading-normal);
}

.text-medical-sm {
  font-size: var(--medical-text-sm);
  line-height: var(--medical-leading-relaxed);
}

.text-medical-base {
  font-size: var(--medical-text-base);
  line-height: var(--medical-leading-relaxed);
}

.text-medical-lg {
  font-size: var(--medical-text-lg);
  line-height: var(--medical-leading-relaxed);
}

.text-medical-xl {
  font-size: var(--medical-text-xl);
  line-height: var(--medical-leading-relaxed);
}

.text-medical-2xl {
  font-size: var(--medical-text-2xl);
  line-height: var(--medical-leading-normal);
}

.text-medical-3xl {
  font-size: var(--medical-text-3xl);
  line-height: var(--medical-leading-tight);
}

.text-medical-4xl {
  font-size: var(--medical-text-4xl);
  line-height: var(--medical-leading-tight);
}

.text-medical-5xl {
  font-size: var(--medical-text-5xl);
  line-height: var(--medical-leading-none);
}

/* Medical Font Family Classes */
.font-family-medical {
  font-family: var(--medical-font-family);
}

.font-medical-mono {
  font-family: var(--medical-font-family-mono);
  font-variant-numeric: tabular-nums;
}

.font-medical-professional {
  font-family: var(--medical-font-family-professional);
}

.font-medical-numeric {
  font-family: var(--medical-font-family-mono);
  font-variant-numeric: tabular-nums;
  text-align: right;
}

/* Medical Font Weight Classes */
.font-medical-light { font-weight: var(--medical-font-light); }
.font-medical-normal { font-weight: var(--medical-font-normal); }
.font-medical-medium { font-weight: var(--medical-font-medium); }
.font-medical-semibold { font-weight: var(--medical-font-semibold); }
.font-medical-bold { font-weight: var(--medical-font-bold); }
.font-medical-extrabold { font-weight: var(--medical-font-extrabold); }
.font-medical-black { font-weight: var(--medical-font-black); }

/* Medical Line Height Classes */
.leading-medical-none { line-height: var(--medical-leading-none); }
.leading-medical-tight { line-height: var(--medical-leading-tight); }
.leading-medical-snug { line-height: var(--medical-leading-snug); }
.leading-medical-normal { line-height: var(--medical-leading-normal); }
.leading-medical-relaxed { line-height: var(--medical-leading-relaxed); }
.leading-medical-loose { line-height: var(--medical-leading-loose); }
.leading-medical-medical { line-height: var(--medical-leading-medical); }

/* Medical Letter Spacing Classes */
.tracking-medical-tighter { letter-spacing: var(--medical-tracking-tighter); }
.tracking-medical-tight { letter-spacing: var(--medical-tracking-tight); }
.tracking-medical-normal { letter-spacing: var(--medical-tracking-normal); }
.tracking-medical-wide { letter-spacing: var(--medical-tracking-wide); }
.tracking-medical-wider { letter-spacing: var(--medical-tracking-wider); }
.tracking-medical-widest { letter-spacing: var(--medical-tracking-widest); }

/* Medical Typography Presets */
.medical-heading-1 {
  font-size: var(--medical-text-4xl);
  font-weight: var(--medical-font-bold);
  line-height: var(--medical-leading-tight);
  letter-spacing: var(--medical-tracking-tight);
  color: var(--medical-text-primary);
}

.medical-heading-2 {
  font-size: var(--medical-text-3xl);
  font-weight: var(--medical-font-semibold);
  line-height: var(--medical-leading-tight);
  letter-spacing: var(--medical-tracking-tight);
  color: var(--medical-text-primary);
}

.medical-heading-3 {
  font-size: var(--medical-text-2xl);
  font-weight: var(--medical-font-semibold);
  line-height: var(--medical-leading-snug);
  color: var(--medical-text-primary);
}

.medical-heading-4 {
  font-size: var(--medical-text-xl);
  font-weight: var(--medical-font-medium);
  line-height: var(--medical-leading-snug);
  color: var(--medical-text-primary);
}

.medical-body-large {
  font-size: var(--medical-text-lg);
  font-weight: var(--medical-font-normal);
  line-height: var(--medical-leading-relaxed);
  color: var(--medical-text-primary);
}

.medical-body-base {
  font-size: var(--medical-text-base);
  font-weight: var(--medical-font-normal);
  line-height: var(--medical-leading-relaxed);
  color: var(--medical-text-primary);
}

.medical-body-small {
  font-size: var(--medical-text-sm);
  font-weight: var(--medical-font-normal);
  line-height: var(--medical-leading-relaxed);
  color: var(--medical-text-secondary);
}

.medical-caption {
  font-size: var(--medical-text-xs);
  font-weight: var(--medical-font-normal);
  line-height: var(--medical-leading-normal);
  color: var(--medical-text-tertiary);
  letter-spacing: var(--medical-tracking-wide);
  text-transform: uppercase;
}

.medical-code {
  font-family: var(--medical-font-family-mono);
  font-size: var(--medical-text-sm);
  font-weight: var(--medical-font-normal);
  line-height: var(--medical-leading-normal);
  background-color: var(--medical-bg-secondary);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  color: var(--medical-color-primary);
}

.medical-measurement {
  font-family: var(--medical-font-family-mono);
  font-size: var(--medical-text-base);
  font-weight: var(--medical-font-medium);
  line-height: var(--medical-leading-normal);
  color: var(--medical-text-primary);
  font-variant-numeric: tabular-nums;
  text-align: right;
}

.medical-label {
  font-size: var(--medical-text-sm);
  font-weight: var(--medical-font-medium);
  line-height: var(--medical-leading-normal);
  color: var(--medical-text-secondary);
  letter-spacing: var(--medical-tracking-wide);
}

/* Medical Device Specific Typography */
.font-medical-tablet {
  font-size: calc(var(--medical-text-base) * 1.1);
  line-height: var(--medical-leading-loose);
  letter-spacing: var(--medical-tracking-wide);
}

.font-medical-mobile-large {
  font-size: calc(var(--medical-text-lg) * 1.2);
  line-height: var(--medical-leading-loose);
  letter-spacing: var(--medical-tracking-wide);
}

.font-medical-timer {
  font-family: var(--medical-font-family-mono);
  font-size: var(--medical-text-2xl);
  font-weight: var(--medical-font-bold);
  line-height: var(--medical-leading-none);
  font-variant-numeric: tabular-nums;
  letter-spacing: var(--medical-tracking-wide);
}

/* Responsive Typography for Medical Devices */
@media (min-width: 1440px) {
  /* Medical Workstation (1440px+) */
  .medical-workstation\:text-medical-lg { font-size: var(--medical-text-lg); }
  .medical-workstation\:text-medical-xl { font-size: var(--medical-text-xl); }
  .medical-workstation\:text-medical-2xl { font-size: var(--medical-text-2xl); }
}

@media (min-width: 768px) and (max-width: 1439px) {
  /* Medical Tablet (768px - 1439px) */
  .medical-tablet\:text-medical-base { font-size: var(--medical-text-base); }
  .medical-tablet\:text-medical-lg { font-size: var(--medical-text-lg); }
  .medical-tablet\:leading-loose { line-height: var(--medical-leading-loose); }
}

@media (max-width: 767px) {
  /* Medical Mobile (< 768px) */
  .medical-mobile\:text-medical-sm { font-size: var(--medical-text-sm); }
  .medical-mobile\:text-medical-base { font-size: var(--medical-text-base); }
  .medical-mobile\:leading-loose { line-height: var(--medical-leading-loose); }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: no-preference) {
  .medical-text-smooth {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Print Styles for Medical Documents */
@media print {
  .medical-print-optimize {
    font-family: Georgia, 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.4;
    color: black;
  }
  
  .medical-print-heading {
    font-weight: bold;
    page-break-after: avoid;
  }
  
  .medical-print-code {
    font-family: 'Courier New', monospace;
    font-size: 10pt;
  }
}