/* Medical Animation Tokens - Premium Healthcare Motion System v3.0 */

:root {
  /* Medical Animation Durations - Enhanced Professional Timing */
  --medical-duration-instant: 0ms;
  --medical-duration-fast: 150ms;      /* Quick interactions, hover states */
  --medical-duration-normal: 200ms;    /* Standard transitions, buttons */
  --medical-duration-base: 250ms;      /* Card animations, modals */
  --medical-duration-slow: 350ms;      /* Deliberate animations, page transitions */
  --medical-duration-slower: 500ms;    /* Emphasis animations, complex transitions */
  --medical-duration-loading: 1000ms;  /* Loading animations, progress */
  --medical-duration-toast: 3000ms;    /* Notification display duration */
  
  /* Medical Animation Easing - Professional Curves */
  --medical-ease-linear: linear;
  --medical-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --medical-ease-out: cubic-bezier(0, 0, 0.2, 1);      /* Preferred for medical UI */
  --medical-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --medical-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --medical-ease-professional: cubic-bezier(0.25, 0.46, 0.45, 0.94);  /* Trust-building curve */
  --medical-ease-medical: cubic-bezier(0.4, 0, 0.6, 1);               /* Medical-specific easing */
  
  /* Medical Animation Delays */
  --medical-delay-none: 0ms;
  --medical-delay-short: 75ms;
  --medical-delay-base: 150ms;
  --medical-delay-long: 300ms;
  
  /* Medical Transform Values */
  --medical-scale-enter: 0.95;
  --medical-scale-exit: 0.95;
  --medical-translate-enter: 0.5rem;
  --medical-translate-exit: -0.5rem;
  --medical-rotate-loading: 360deg;
  
  /* Medical Opacity Values */
  --medical-opacity-enter: 0;
  --medical-opacity-exit: 0;
  --medical-opacity-visible: 1;
  --medical-opacity-disabled: 0.5;
  --medical-opacity-loading: 0.7;
}

/* Medical Base Animation Classes */
.transition-medical-none { transition: none; }
.transition-medical-all { 
  transition: all var(--medical-duration-base) var(--medical-ease-out);
}
.transition-medical-colors {
  transition: color var(--medical-duration-base) var(--medical-ease-out),
              background-color var(--medical-duration-base) var(--medical-ease-out),
              border-color var(--medical-duration-base) var(--medical-ease-out);
}
.transition-medical-opacity {
  transition: opacity var(--medical-duration-base) var(--medical-ease-out);
}
.transition-medical-transform {
  transition: transform var(--medical-duration-base) var(--medical-ease-out);
}
.transition-medical-shadow {
  transition: box-shadow var(--medical-duration-base) var(--medical-ease-out);
}

/* Medical Duration Classes */
.duration-medical-instant { transition-duration: var(--medical-duration-instant); }
.duration-medical-fast { transition-duration: var(--medical-duration-fast); }
.duration-medical-base { transition-duration: var(--medical-duration-base); }
.duration-medical-slow { transition-duration: var(--medical-duration-slow); }
.duration-medical-slower { transition-duration: var(--medical-duration-slower); }

/* Medical Easing Classes */
.ease-medical-linear { transition-timing-function: var(--medical-ease-linear); }
.ease-medical-in { transition-timing-function: var(--medical-ease-in); }
.ease-medical-out { transition-timing-function: var(--medical-ease-out); }
.ease-medical-in-out { transition-timing-function: var(--medical-ease-in-out); }
.ease-medical-bounce { transition-timing-function: var(--medical-ease-bounce); }
.ease-medical-professional { transition-timing-function: var(--medical-ease-professional); }
.ease-medical-medical { transition-timing-function: var(--medical-ease-medical); }

/* Medical Delay Classes */
.delay-medical-none { transition-delay: var(--medical-delay-none); }
.delay-medical-short { transition-delay: var(--medical-delay-short); }
.delay-medical-base { transition-delay: var(--medical-delay-base); }
.delay-medical-long { transition-delay: var(--medical-delay-long); }

/* Medical Smooth Transition Presets */
.transition-medical-smooth {
  transition: all var(--medical-duration-base) var(--medical-ease-professional);
}

.transition-medical-input {
  transition: border-color var(--medical-duration-fast) var(--medical-ease-out),
              box-shadow var(--medical-duration-fast) var(--medical-ease-out),
              background-color var(--medical-duration-fast) var(--medical-ease-out);
}

.transition-medical-button {
  transition: all var(--medical-duration-fast) var(--medical-ease-out);
}

.transition-medical-card {
  transition: transform var(--medical-duration-base) var(--medical-ease-out),
              box-shadow var(--medical-duration-base) var(--medical-ease-out);
}

.transition-medical-workflow {
  transition: all var(--medical-duration-slow) var(--medical-ease-professional);
}

/* Medical Transform Classes */
.transform-medical-gpu { transform: translateZ(0); }
.transform-medical-lift { transform: translateY(-2px); }
.transform-medical-lift-lg { transform: translateY(-4px); }
.transform-medical-press { transform: translateY(1px) scale(0.98); }

/* Medical Animation Keyframes */
@keyframes medical-fade-in {
  from {
    opacity: var(--medical-opacity-enter);
  }
  to {
    opacity: var(--medical-opacity-visible);
  }
}

@keyframes medical-fade-out {
  from {
    opacity: var(--medical-opacity-visible);
  }
  to {
    opacity: var(--medical-opacity-exit);
  }
}

@keyframes medical-slide-up {
  from {
    opacity: var(--medical-opacity-enter);
    transform: translateY(var(--medical-translate-enter));
  }
  to {
    opacity: var(--medical-opacity-visible);
    transform: translateY(0);
  }
}

@keyframes medical-slide-down {
  from {
    opacity: var(--medical-opacity-visible);
    transform: translateY(0);
  }
  to {
    opacity: var(--medical-opacity-exit);
    transform: translateY(var(--medical-translate-exit));
  }
}

@keyframes medical-scale-in {
  from {
    opacity: var(--medical-opacity-enter);
    transform: scale(var(--medical-scale-enter));
  }
  to {
    opacity: var(--medical-opacity-visible);
    transform: scale(1);
  }
}

@keyframes medical-scale-out {
  from {
    opacity: var(--medical-opacity-visible);
    transform: scale(1);
  }
  to {
    opacity: var(--medical-opacity-exit);
    transform: scale(var(--medical-scale-exit));
  }
}

@keyframes medical-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(var(--medical-rotate-loading));
  }
}

@keyframes medical-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: var(--medical-opacity-loading);
  }
}

@keyframes medical-heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  14%, 42% {
    transform: scale(1.1);
  }
  28%, 70% {
    transform: scale(1.05);
  }
}

@keyframes medical-bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: var(--medical-ease-out);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: var(--medical-ease-in);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: var(--medical-ease-in);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes medical-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

@keyframes medical-glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(10, 110, 189, 0.2);
  }
  50% {
    box-shadow: 0 0 16px rgba(10, 110, 189, 0.4);
  }
}

@keyframes medical-emergency-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(217, 45, 32, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(217, 45, 32, 0);
    transform: scale(1.02);
  }
}

/* Medical Animation Utility Classes */
.animate-medical-fade-in {
  animation: medical-fade-in var(--medical-duration-base) var(--medical-ease-out);
}

.animate-medical-fade-out {
  animation: medical-fade-out var(--medical-duration-base) var(--medical-ease-out);
}

.animate-medical-slide-up {
  animation: medical-slide-up var(--medical-duration-base) var(--medical-ease-out);
}

.animate-medical-slide-down {
  animation: medical-slide-down var(--medical-duration-base) var(--medical-ease-out);
}

.animate-medical-scale-in {
  animation: medical-scale-in var(--medical-duration-base) var(--medical-ease-out);
}

.animate-medical-scale-out {
  animation: medical-scale-out var(--medical-duration-base) var(--medical-ease-out);
}

.animate-medical-spin {
  animation: medical-spin var(--medical-duration-loading) var(--medical-ease-linear) infinite;
}

.animate-medical-pulse {
  animation: medical-pulse var(--medical-duration-slower) var(--medical-ease-in-out) infinite;
}

.animate-medical-heartbeat {
  animation: medical-heartbeat var(--medical-duration-loading) var(--medical-ease-in-out) infinite;
}

.animate-medical-bounce {
  animation: medical-bounce var(--medical-duration-loading) var(--medical-ease-bounce);
}

.animate-medical-shake {
  animation: medical-shake var(--medical-duration-base) var(--medical-ease-in-out);
}

.animate-medical-glow {
  animation: medical-glow var(--medical-duration-slower) var(--medical-ease-in-out) infinite;
}

.animate-medical-emergency-pulse {
  animation: medical-emergency-pulse var(--medical-duration-slower) var(--medical-ease-in-out) infinite;
}

/* Medical Performance Optimizations */
.will-change-medical-auto { will-change: auto; }
.will-change-medical-transform { will-change: transform; }
.will-change-medical-opacity { will-change: opacity; }
.will-change-medical-scroll { will-change: scroll-position; }

.contain-medical-layout { contain: layout; }
.contain-medical-paint { contain: paint; }
.contain-medical-size { contain: size; }

.gpu-accelerated-medical {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Medical Hover and Focus Animations */
.hover\:transform-medical-lift:hover {
  transform: translateY(-2px);
}

.hover\:shadow-medical-hover:hover {
  box-shadow: var(--medical-shadow-elevated);
}

.focus\:outline-medical-focus:focus {
  outline: 2px solid var(--medical-focus-ring);
  outline-offset: 2px;
}

.focus\:ring-medical-focus:focus {
  box-shadow: 0 0 0 3px rgba(10, 110, 189, 0.1);
}

.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px var(--medical-focus-ring);
}

/* Medical Loading States */
.loading-medical-overlay {
  position: relative;
  pointer-events: none;
  opacity: var(--medical-opacity-loading);
}

.loading-medical-overlay::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.4), 
    transparent);
  animation: medical-loading-shimmer 1.5s infinite;
}

@keyframes medical-loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Medical Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .motion-reduce\:transition-none {
    transition: none !important;
  }
  
  .motion-reduce\:animation-none {
    animation: none !important;
  }
  
  .motion-reduce\:transform-none {
    transform: none !important;
  }
  
  /* Still allow essential medical animations with reduced intensity */
  .animate-medical-pulse {
    animation: medical-pulse var(--medical-duration-slower) ease-in-out infinite;
    animation-duration: calc(var(--medical-duration-slower) * 2);
  }
  
  .animate-medical-emergency-pulse {
    animation: none;
    background-color: var(--medical-color-critical);
  }
}

/* Medical Print Animations (removed for print) */
@media print {
  .print\:animate-none {
    animation: none !important;
  }
  
  .print\:transition-none {
    transition: none !important;
  }
  
  .print\:transform-none {
    transform: none !important;
  }
}