/* Medical Color Tokens - Enhanced Design System v3.0 - Premium Healthcare */

:root {
  /* Medical Primary Colors - Refined Professional Blue Palette */
  --medical-color-primary: #0066CC;        /* Refined medical blue - more vibrant yet professional */
  --medical-color-primary-50: #EBF4FF;     /* Ultra light blue background */
  --medical-color-primary-100: #DBEAFE;    /* Light blue for subtle backgrounds */
  --medical-color-primary-200: #BFDBFE;    /* Soft blue for hover states */
  --medical-color-primary-300: #93C5FD;    /* Medium light blue */
  --medical-color-primary-400: #60A5FA;    /* Bright blue for accents */
  --medical-color-primary-500: #3B82F6;    /* Standard blue */
  --medical-color-primary-600: #0066CC;    /* Primary medical blue */
  --medical-color-primary-700: #1D4ED8;    /* Darker blue for text */
  --medical-color-primary-800: #1E40AF;    /* Deep blue */
  --medical-color-primary-900: #1E3A8A;    /* Darkest blue */
  --medical-color-primary-950: #172554;    /* Ultra dark blue */
  
  /* Medical Secondary Colors - Trust Building Palette */
  --medical-color-secondary: #6366F1;
  --medical-color-secondary-50: #EEF2FF;
  --medical-color-secondary-100: #E0E7FF;
  --medical-color-secondary-200: #C7D2FE;
  --medical-color-secondary-300: #A5B4FC;
  --medical-color-secondary-400: #818CF8;
  --medical-color-secondary-500: #6366F1;
  --medical-color-secondary-600: #4F46E5;
  --medical-color-secondary-700: #4338CA;
  --medical-color-secondary-800: #3730A3;
  --medical-color-secondary-900: #312E81;
  
  /* Medical Status Colors - Enhanced Clinical Standards */
  --medical-color-critical: #E53E3E;       /* Refined critical red - more vibrant yet professional */
  --medical-color-critical-50: #FFF5F5;    /* Ultra light red background */
  --medical-color-critical-100: #FED7D7;   /* Light red for backgrounds */
  --medical-color-critical-200: #FEB2B2;   /* Soft red for hover states */
  --medical-color-critical-300: #FC8181;   /* Medium red */
  --medical-color-critical-400: #F56565;   /* Bright red for alerts */
  --medical-color-critical-500: #E53E3E;   /* Primary critical red */
  --medical-color-critical-600: #C53030;   /* Darker red */
  --medical-color-critical-700: #9B2C2C;   /* Deep red */
  --medical-color-critical-800: #822727;   /* Very dark red */
  --medical-color-critical-900: #63171B;   /* Darkest red */
  
  /* Medical Urgent Colors - Enhanced Amber/Orange Palette */
  --medical-color-urgent: #F59E0B;         /* Refined urgent amber - more balanced */
  --medical-color-urgent-50: #FFFBEB;      /* Ultra light amber background */
  --medical-color-urgent-100: #FEF3C7;     /* Light amber for backgrounds */
  --medical-color-urgent-200: #FDE68A;     /* Soft amber for hover states */
  --medical-color-urgent-300: #FCD34D;     /* Medium amber */
  --medical-color-urgent-400: #FBBF24;     /* Bright amber for alerts */
  --medical-color-urgent-500: #F59E0B;     /* Primary urgent amber */
  --medical-color-urgent-600: #D97706;     /* Darker amber */
  --medical-color-urgent-700: #B45309;     /* Deep amber */
  --medical-color-urgent-800: #92400E;     /* Very dark amber */
  --medical-color-urgent-900: #78350F;     /* Darkest amber */
  
  --medical-color-routine: var(--medical-color-primary);
  --medical-color-routine-50: var(--medical-color-primary-50);
  --medical-color-routine-100: var(--medical-color-primary-100);
  --medical-color-routine-200: var(--medical-color-primary-200);
  --medical-color-routine-300: var(--medical-color-primary-300);
  --medical-color-routine-400: var(--medical-color-primary-400);
  --medical-color-routine-500: var(--medical-color-primary-500);
  --medical-color-routine-600: var(--medical-color-primary-600);
  --medical-color-routine-700: var(--medical-color-primary-700);
  --medical-color-routine-800: var(--medical-color-primary-800);
  --medical-color-routine-900: var(--medical-color-primary-900);
  
  /* Medical Success Colors - Enhanced Green Palette */
  --medical-color-success: #10B981;        /* Refined success green - more professional */
  --medical-color-success-50: #ECFDF5;     /* Ultra light green background */
  --medical-color-success-100: #D1FAE5;    /* Light green for backgrounds */
  --medical-color-success-200: #A7F3D0;    /* Soft green for hover states */
  --medical-color-success-300: #6EE7B7;    /* Medium green */
  --medical-color-success-400: #34D399;    /* Bright green for success states */
  --medical-color-success-500: #10B981;    /* Primary success green */
  --medical-color-success-600: #059669;    /* Darker green */
  --medical-color-success-700: #047857;    /* Deep green */
  --medical-color-success-800: #065F46;    /* Very dark green */
  --medical-color-success-900: #064E3B;    /* Darkest green */
  
  --medical-color-info: #2E90FA;
  --medical-color-info-50: #EFF6FF;
  --medical-color-info-100: #DBEAFE;
  --medical-color-info-200: #BFDBFE;
  --medical-color-info-300: #93C5FD;
  --medical-color-info-400: #60A5FA;
  --medical-color-info-500: #3B82F6;
  --medical-color-info-600: #2E90FA;
  --medical-color-info-700: #1D4ED8;
  --medical-color-info-800: #1E40AF;
  --medical-color-info-900: #1E3A8A;

  /* Medical Background Colors - Enhanced */
  --medical-bg-primary: #FFFFFF;           /* Pure white for main backgrounds */
  --medical-bg-secondary: #F8FAFC;         /* Subtle gray for secondary areas */
  --medical-bg-tertiary: #F1F5F9;          /* Light gray for tertiary areas */
  --medical-bg-elevated: #FFFFFF;          /* White for elevated surfaces */
  --medical-bg-overlay: rgba(15, 23, 42, 0.05); /* Subtle overlay */
  --medical-bg-muted: #F9FAFB;             /* Very light background */
  --medical-bg-subtle: #F3F4F6;            /* Subtle background variation */

  /* Medical Glass Morphism Colors - Premium Enhanced */
  --medical-glass-bg: rgba(255, 255, 255, 0.85);      /* Refined glass background */
  --medical-glass-border: rgba(255, 255, 255, 0.18);  /* Subtle glass border */
  --medical-glass-hero: rgba(255, 255, 255, 0.95);    /* Hero section glass */
  --medical-glass-premium: rgba(255, 255, 255, 0.90); /* Premium glass effect */
  --medical-glass-card: rgba(255, 255, 255, 0.88);    /* Card glass effect */
  --medical-glass-sidebar: rgba(255, 255, 255, 0.92); /* Sidebar glass effect */
  
  /* Medical Text Colors */
  --medical-text-primary: #0F172A;
  --medical-text-secondary: #475569;
  --medical-text-tertiary: #64748B;
  --medical-text-inverse: white;
  --medical-text-muted: #94A3B8;
  
  /* Medical Border Colors */
  --medical-border-base: #E2E8F0;
  --medical-border-strong: #CBD5E1;
  --medical-border-critical: var(--medical-color-critical-400);
  --medical-border-urgent: var(--medical-color-urgent-400);
  --medical-border-success: var(--medical-color-success-400);
  --medical-border-info: var(--medical-color-info-400);
  
  /* Medical Focus and Interactive States */
  --medical-focus-ring: var(--medical-color-primary-500);
  --medical-focus-ring-critical: var(--medical-color-critical-500);
  --medical-focus-ring-urgent: var(--medical-color-urgent-500);
  
  /* Medical Card and Component Backgrounds */
  --medical-card-bg: white;
  --medical-card-patient: #FEFEFE;
  --medical-card-data: white;
  --medical-card-dashboard: rgba(255, 255, 255, 0.95);
  --medical-card-emergency: var(--medical-color-critical-50);
  
  /* Medical Input Colors */
  --medical-input-bg: white;
  --medical-input-border: var(--medical-border-base);
  --medical-input-border-focus: var(--medical-color-primary);
  --medical-input-border-error: var(--medical-color-critical);
  --medical-input-border-success: var(--medical-color-success);
  
  /* Medical Button Colors */
  --medical-button-primary-bg: var(--medical-color-primary);
  --medical-button-primary-hover: var(--medical-color-primary-700);
  --medical-button-primary-text: white;
  --medical-button-critical-bg: var(--medical-color-critical);
  --medical-button-critical-hover: var(--medical-color-critical-700);
  --medical-button-urgent-bg: var(--medical-color-urgent);
  --medical-button-urgent-hover: var(--medical-color-urgent-700);
  --medical-button-success-bg: var(--medical-color-success);
  --medical-button-success-hover: var(--medical-color-success-700);
  
  /* Medical Gradient Tokens - Professional Gradients */
  --medical-gradient-primary: linear-gradient(135deg, var(--medical-color-primary-500) 0%, var(--medical-color-primary-700) 100%);
  --medical-gradient-secondary: linear-gradient(135deg, var(--medical-color-secondary-500) 0%, var(--medical-color-secondary-700) 100%);
  --medical-gradient-critical: linear-gradient(135deg, var(--medical-color-critical-500) 0%, var(--medical-color-critical-700) 100%);
  --medical-gradient-urgent: linear-gradient(135deg, var(--medical-color-urgent-500) 0%, var(--medical-color-urgent-700) 100%);
  --medical-gradient-success: linear-gradient(135deg, var(--medical-color-success-500) 0%, var(--medical-color-success-700) 100%);
  --medical-gradient-soft: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  --medical-gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  
  /* Medical Shadow Tokens - Professional Depth */
  --medical-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --medical-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --medical-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --medical-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --medical-shadow-glow: 0 0 20px rgba(10, 110, 189, 0.3);
  --medical-shadow-critical: 0 0 20px rgba(217, 45, 32, 0.3);
  --medical-shadow-urgent: 0 0 20px rgba(247, 144, 9, 0.3);
  --medical-shadow-success: 0 0 20px rgba(34, 197, 94, 0.3);
  
  /* Medical Animation Tokens - Professional Timing */
  --medical-duration-fast: 150ms;
  --medical-duration-normal: 200ms;
  --medical-duration-slow: 300ms;
  --medical-ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --medical-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --medical-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Medical Colors */
[data-theme="dark"], .dark {
  /* Medical Primary Colors - Dark Mode Variants */
  --medical-color-primary-dark: #1E40AF;
  --medical-color-primary: #3B82F6;
  --medical-color-primary-hover: #2563EB;
  
  /* Medical Status Colors - Dark Mode */
  --medical-color-critical: #EF4444;
  --medical-color-critical-hover: #DC2626;
  --medical-color-urgent: #F59E0B;
  --medical-color-urgent-hover: #D97706;
  --medical-color-success: #10B981;
  --medical-color-success-hover: #059669;
  --medical-color-info: #3B82F6;
  --medical-color-info-hover: #2563EB;
  
  /* Medical Backgrounds - Dark Mode */
  --medical-bg-primary: #0F172A;
  --medical-bg-secondary: #1E293B;
  --medical-bg-tertiary: #334155;
  --medical-bg-elevated: #1E293B;
  --medical-bg-overlay: rgba(0, 0, 0, 0.8);
  
  /* Medical Glass Morphism - Dark Mode */
  --medical-glass-bg: rgba(30, 41, 59, 0.9);
  --medical-glass-border: rgba(148, 163, 184, 0.2);
  --medical-glass-hero: rgba(30, 41, 59, 0.95);
  --medical-glass-premium: rgba(30, 41, 59, 0.92);
  --medical-glass-dark: rgba(15, 23, 42, 0.9);
  --medical-glass-dark-border: rgba(148, 163, 184, 0.1);
  
  /* Medical Text Colors - Dark Mode */
  --medical-text-primary: #F8FAFC;
  --medical-text-secondary: #CBD5E1;
  --medical-text-tertiary: #94A3B8;
  --medical-text-inverse: #0F172A;
  --medical-text-muted: #64748B;
  
  /* Medical Border Colors - Dark Mode */
  --medical-border-base: #374151;
  --medical-border-strong: #4B5563;
  
  /* Medical Card Backgrounds - Dark Mode */
  --medical-card-bg: #1E293B;
  --medical-card-patient: #1E293B;
  --medical-card-data: #1E293B;
  --medical-card-dashboard: rgba(30, 41, 59, 0.95);
  
  /* Medical Input Colors - Dark Mode */
  --medical-input-bg: #1E293B;
  --medical-input-border: #374151;
  
  /* Medical Gradient Tokens - Dark Mode */
  --medical-gradient-primary: linear-gradient(135deg, var(--medical-color-primary) 0%, var(--medical-color-primary-hover) 100%);
  --medical-gradient-secondary: linear-gradient(135deg, var(--medical-color-secondary) 0%, var(--medical-color-secondary-700) 100%);
  --medical-gradient-soft: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(30, 41, 59, 0.7) 100%);
  --medical-gradient-glass: linear-gradient(135deg, rgba(30, 41, 59, 0.1) 0%, rgba(30, 41, 59, 0.05) 100%);
  
  /* Medical Shadow Tokens - Dark Mode */
  --medical-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --medical-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --medical-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --medical-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  --medical-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.4);
  --medical-shadow-critical: 0 0 20px rgba(239, 68, 68, 0.4);
  --medical-shadow-urgent: 0 0 20px rgba(245, 158, 11, 0.4);
  --medical-shadow-success: 0 0 20px rgba(16, 185, 129, 0.4);
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --medical-color-primary: #0000FF;
    --medical-color-critical: #FF0000;
    --medical-color-success: #00FF00;
    --medical-border-base: #000000;
    --medical-text-primary: #000000;
    --medical-bg-primary: #FFFFFF;
  }
  
  [data-theme="dark"], .dark {
    --medical-color-primary: #00FFFF;
    --medical-color-critical: #FF4444;
    --medical-color-success: #00FF00;
    --medical-border-base: #FFFFFF;
    --medical-text-primary: #FFFFFF;
    --medical-bg-primary: #000000;
  }
}

/* Utility Classes for Medical Colors */
.bg-medical-primary { background-color: var(--medical-color-primary); }
.bg-medical-critical { background-color: var(--medical-color-critical); }
.bg-medical-urgent { background-color: var(--medical-color-urgent); }
.bg-medical-routine { background-color: var(--medical-color-routine); }
.bg-medical-success { background-color: var(--medical-color-success); }
.bg-medical-info { background-color: var(--medical-color-info); }

.text-medical-primary { color: var(--medical-color-primary); }
.text-medical-critical { color: var(--medical-color-critical); }
.text-medical-urgent { color: var(--medical-color-urgent); }
.text-medical-success { color: var(--medical-color-success); }
.text-medical-info { color: var(--medical-color-info); }
.text-white { color: white; }

.border-medical-primary { border-color: var(--medical-color-primary); }
.border-medical-critical { border-color: var(--medical-border-critical); }
.border-medical-critical-border { border-color: var(--medical-color-critical); }
.border-medical-urgent { border-color: var(--medical-border-urgent); }
.border-medical-success { border-color: var(--medical-border-success); }

/* Medical Glass Morphism Classes */
.bg-medical-glass { background-color: var(--medical-glass-bg); }
.bg-medical-glass-premium { background-color: var(--medical-glass-premium); }
.bg-medical-glass-hero { background-color: var(--medical-glass-hero); }
.bg-medical-glass-dark { background-color: var(--medical-glass-dark); }

.border-medical-glass-border { border-color: var(--medical-glass-border); }
.border-medical-glass-dark-border { border-color: var(--medical-glass-dark-border); }

.backdrop-blur-medical { backdrop-filter: blur(12px); }
.backdrop-blur-medical-subtle { backdrop-filter: blur(8px); }
.backdrop-blur-medical-strong { backdrop-filter: blur(16px); }

/* Medical Gradient Classes */
.bg-medical-gradient-primary { background: var(--medical-gradient-primary); }
.bg-medical-gradient-secondary { background: var(--medical-gradient-secondary); }
.bg-medical-gradient-critical { background: var(--medical-gradient-critical); }
.bg-medical-gradient-urgent { background: var(--medical-gradient-urgent); }
.bg-medical-gradient-success { background: var(--medical-gradient-success); }
.bg-medical-gradient-soft { background: var(--medical-gradient-soft); }
.bg-medical-gradient-glass { background: var(--medical-gradient-glass); }

/* Medical Shadow Classes */
.shadow-medical-sm { box-shadow: var(--medical-shadow-sm); }
.shadow-medical-md { box-shadow: var(--medical-shadow-md); }
.shadow-medical-lg { box-shadow: var(--medical-shadow-lg); }
.shadow-medical-xl { box-shadow: var(--medical-shadow-xl); }
.shadow-medical-glow { box-shadow: var(--medical-shadow-glow); }
.shadow-medical-critical { box-shadow: var(--medical-shadow-critical); }
.shadow-medical-urgent { box-shadow: var(--medical-shadow-urgent); }
.shadow-medical-success { box-shadow: var(--medical-shadow-success); }

/* Medical Animation Classes */
.transition-medical-fast { transition-duration: var(--medical-duration-fast); }
.transition-medical-normal { transition-duration: var(--medical-duration-normal); }
.transition-medical-slow { transition-duration: var(--medical-duration-slow); }
.ease-medical-out { transition-timing-function: var(--medical-ease-out); }
.ease-medical-in { transition-timing-function: var(--medical-ease-in); }
.ease-medical-in-out { transition-timing-function: var(--medical-ease-in-out); }