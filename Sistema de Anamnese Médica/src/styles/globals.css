@custom-variant dark (&:is(.dark *));

/* Import Medical Design System Tokens */
@import '../design-system/tokens/index.css';

:root {
  /* 🏥 MEDICAL UI DESIGN TOKENS (WCAG 2.2 Compliant) */
  
  /* Core Colors - Medical Standards */
  --color-bg: #F8FAFC;
  --color-surface: #FFFFFF;
  --color-text: #101828;
  --color-muted: #475467;
  --color-border: #E5E7EB;

  --color-primary: #0A6EBD;   /* azul confiável */
  --color-primary-600: #0B5FA3;
  --color-primary-100: #E6F2FB;
  --color-primary-50: #F0F8FF;

  --color-accent: #12A594;    /* teal calmo */
  --color-success: #22C55E;
  --color-warning: #F79009;
  --color-danger: #D92D20;
  --color-info: #2E90FA;

  /* 🔤 TYPOGRAPHY SCALE PREMIUM - Medical Hierarchy Enhanced */
  --font-sans: ui-sans-serif, system-ui, "Inter", "SF Pro Text", Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
  
  /* Medical Typography - Optimized for Medical Readability */
  --font-medical-heading: var(--font-sans);
  --font-medical-body: var(--font-sans);
  --font-medical-mono: var(--font-mono);
  
  /* Medical Line Heights - Optimized for Medical Documentation */
  --leading-medical-tight: 1.2;
  --leading-medical-normal: 1.5;
  --leading-medical-relaxed: 1.6;
  --leading-medical-loose: 1.8;
  
  /* Text Sizes - Refined Scale */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px - base */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */
  --text-7xl: 4.5rem;     /* 72px */
  --text-8xl: 6rem;       /* 96px */
  --text-9xl: 8rem;       /* 128px */
  
  /* Legacy compatibility */
  --fs-300: var(--text-sm); 
  --fs-400: var(--text-base);
  --fs-500: var(--text-lg);
  --fs-600: var(--text-xl);
  --fs-700: var(--text-2xl);
  --fs-800: var(--text-4xl);
  
  /* Font Weights - Medical Appropriate */
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* 📐 SPACING SYSTEM PREMIUM (Golden Ratio Based) */
  --sp-0: 0px;
  --sp-px: 1px;
  --sp-0_5: 2px;   /* 0.125rem */
  --sp-1: 4px;     /* 0.25rem */
  --sp-1_5: 6px;   /* 0.375rem */
  --sp-2: 8px;     /* 0.5rem */
  --sp-2_5: 10px;  /* 0.625rem */
  --sp-3: 12px;    /* 0.75rem */
  --sp-3_5: 14px;  /* 0.875rem */
  --sp-4: 16px;    /* 1rem - base */
  --sp-5: 20px;    /* 1.25rem */
  --sp-6: 24px;    /* 1.5rem */
  --sp-7: 28px;    /* 1.75rem */
  --sp-8: 32px;    /* 2rem */
  --sp-9: 36px;    /* 2.25rem */
  --sp-10: 40px;   /* 2.5rem */
  --sp-11: 44px;   /* 2.75rem */
  --sp-12: 48px;   /* 3rem */
  --sp-14: 56px;   /* 3.5rem */
  --sp-16: 64px;   /* 4rem */
  --sp-20: 80px;   /* 5rem */
  --sp-24: 96px;   /* 6rem */
  --sp-28: 112px;  /* 7rem */
  --sp-32: 128px;  /* 8rem */
  --sp-36: 144px;  /* 9rem */
  --sp-40: 160px;  /* 10rem */
  --sp-44: 176px;  /* 11rem */
  --sp-48: 192px;  /* 12rem */
  --sp-52: 208px;  /* 13rem */
  --sp-56: 224px;  /* 14rem */
  --sp-60: 240px;  /* 15rem */
  --sp-64: 256px;  /* 16rem */
  --sp-72: 288px;  /* 18rem */
  --sp-80: 320px;  /* 20rem */
  --sp-96: 384px;  /* 24rem */

  /* 🎯 RADIUS SYSTEM PREMIUM */
  --radius-none: 0px;
  --radius-sm: 6px;       /* Mais suave */
  --radius-base: 8px;     /* Padrão */
  --radius-md: 12px;      /* Médio */
  --radius-lg: 16px;      /* Grande */
  --radius-xl: 20px;      /* Extra grande */
  --radius-2xl: 24px;     /* 2x grande */
  --radius-3xl: 32px;     /* 3x grande */
  --radius-full: 9999px;  /* Circular */
  
  /* 🌟 SHADOW SYSTEM PREMIUM - Medical Appropriate */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: 0 0 transparent;
  
  /* 🎨 BRAND SHADOWS - WellWave Themed */
  --shadow-brand-xs: 0 1px 2px 0 rgba(29, 78, 216, 0.05);
  --shadow-brand-sm: 0 1px 3px 0 rgba(29, 78, 216, 0.1), 0 1px 2px 0 rgba(29, 78, 216, 0.06);
  --shadow-brand-md: 0 4px 12px -2px rgba(29, 78, 216, 0.12), 0 4px 8px -2px rgba(59, 130, 246, 0.08);
  --shadow-brand-lg: 0 10px 25px -3px rgba(29, 78, 216, 0.15), 0 4px 12px -2px rgba(59, 130, 246, 0.1);
  --shadow-brand-xl: 0 20px 40px -4px rgba(29, 78, 216, 0.18), 0 8px 16px -4px rgba(59, 130, 246, 0.12);

  /* 🎬 ANIMATION SYSTEM PREMIUM - Performance Conscious */
  
  /* Duration Scale */
  --dur-instant: 0ms;
  --dur-fastest: 50ms;
  --dur-fast: 100ms;
  --dur-normal: 150ms;
  --dur-base: 200ms;
  --dur-slow: 300ms;
  --dur-slower: 400ms;
  --dur-slowest: 500ms;
  --dur-overlay: 600ms;
  
  /* Easing Curves - Medical UI Appropriate */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-standard: cubic-bezier(0.2, 0.8, 0.2, 1);
  --ease-smooth: cubic-bezier(0.25, 0.1, 0.25, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-spring: cubic-bezier(0.5, 1.25, 0.75, 1.25);
  
  /* Medical UI specific curves */
  --ease-medical: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-heartbeat: cubic-bezier(0.455, 0.03, 0.515, 0.955);

  /* 📐 CONTAINER SYSTEM PREMIUM - Medical Layout Optimized */
  
  /* Content Widths */
  --max-content-xs: 320px;     /* Mobile content */
  --max-content-sm: 576px;     /* Small screens */
  --max-content-md: 768px;     /* Tablet content */
  --max-content-lg: 1024px;    /* Desktop content */
  --max-content-xl: 1280px;    /* Large desktop */
  --max-content-2xl: 1536px;   /* Extra large */
  --max-content: 1200px;       /* Default - Medical optimal */
  --max-content-wide: 1600px;  /* Wide layouts */
  --max-content-full: 100%;    /* Full width */
  
  /* Reading Widths - Medical Text Optimization */
  --max-reading: 65ch;         /* Optimal reading width */
  --max-reading-wide: 75ch;    /* Wide reading */
  --max-reading-narrow: 45ch;  /* Narrow reading */
  
  /* Component Heights - Touch Targets Enhanced */
  --header-height: 72px;       /* Increased for better presence */
  --header-height-compact: 56px;
  --item-height: 48px;         /* Touch target minimum */
  --item-height-sm: 40px;
  --item-height-lg: 56px;
  --button-height: 44px;       /* Touch optimized */
  --button-height-sm: 36px;
  --button-height-lg: 52px;
  --sidebar-width: 280px;      /* Optimal sidebar */
  --sidebar-width-sm: 240px;
  --sidebar-width-collapsed: 64px;
  
  /* Z-index Scale - Medical UI Layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* 🎯 LEGACY COMPATIBILITY */
  --ratio-golden: 1.618;
  --spacing-unit: 8px;
  --spacing-xs: var(--sp-1);
  --spacing-sm: var(--sp-2);
  --spacing-md: var(--sp-4);
  --spacing-lg: var(--sp-6);
  --spacing-xl: var(--sp-8);
  --spacing-2xl: var(--sp-12);
  --spacing-3xl: var(--sp-16);
  --card-ratio: 1.5;
  --button-ratio: 2.5;
  --input-ratio: 3;
  --modal-ratio: 1.333;

  /* 🌊 PALETA DE CORES MÉDICAS PREMIUM ATUALIZADA */
  --medical-primary: #5AA9E6;     /* Azul confiança */
  --medical-secondary: #7FC8F8;   /* Azul céu */
  --medical-tertiary: #B8E0FF;    /* Azul claro */
  
  --medical-success: #00C896;     /* Verde positivo */
  --medical-warning: #FFB800;     /* Amarelo atenção */
  --medical-danger: #FF5A5F;      /* Vermelho urgência */
  --medical-info: #8B7FFF;        /* Roxo informação */
  
  /* NEUTROS PREMIUM */
  --neutral-0: #FFFFFF;
  --neutral-50: #FAFBFC;
  --neutral-100: #F6F8FA;
  --neutral-200: #E1E4E8;
  --neutral-300: #D1D5DA;
  --neutral-400: #959DA5;
  --neutral-500: #6A737D;
  --neutral-600: #586069;
  --neutral-700: #444D56;
  --neutral-800: #2F363D;
  --neutral-900: #24292E;
  --neutral-1000: #1B1F23;
  
  /* GRADIENTES MÉDICOS PREMIUM */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #56CCF2 0%, #2F80ED 100%);
  --gradient-warning: linear-gradient(135deg, #F2C94C 0%, #F2994A 100%);
  --gradient-danger: linear-gradient(135deg, #EB5757 0%, #9B51E0 100%);
  --gradient-premium: linear-gradient(180deg, 
    rgba(102, 126, 234, 0.1) 0%, 
    rgba(118, 75, 162, 0.05) 50%, 
    transparent 100%
  );

  /* 🌊 PALETA DE CORES OFICIAL WELLWAVE PREMIUM (Baseada no Logo) */
  --wave-lightest: #e8f4fd; /* Azul mais claro - refinado */
  --wave-light: #7dd3fc; /* Sky blue - mais vibrante */  
  --wave-medium: #3b82f6; /* Blue 500 - moderno */
  --wave-primary: #1d4ed8; /* Blue 700 - principal */
  --wave-deep: #1e40af; /* Blue 800 - profundo */
  --wave-darkest: #1e3a8a; /* Blue 900 - mais escuro */
  
  /* 🎨 PALETA PREMIUM EXPANDIDA */
  --wave-accent: #06b6d4; /* Cyan 500 - accent */
  --wave-success: #10b981; /* Emerald 500 - success */
  --wave-warning: #f59e0b; /* Amber 500 - warning */
  --wave-danger: #ef4444; /* Red 500 - danger */
  --wave-info: #8b5cf6; /* Violet 500 - info */
  
  /* 🌟 TONS PREMIUM MÉDICOS */
  --medical-blue-50: #eff6ff;
  --medical-blue-100: #dbeafe;
  --medical-blue-200: #bfdbfe;
  --medical-blue-300: #93c5fd;
  --medical-blue-400: #60a5fa;
  --medical-blue-500: #3b82f6;
  --medical-blue-600: #2563eb;
  --medical-blue-700: #1d4ed8;
  --medical-blue-800: #1e40af;
  --medical-blue-900: #1e3a8a;

  /* 🌊 GRADIENTES PREMIUM WELLWAVE ATUALIZADOS */
  --gradient-wave: linear-gradient(
    135deg,
    var(--wave-lightest) 0%,
    var(--wave-light) 25%,
    var(--wave-medium) 50%,
    var(--wave-primary) 75%,
    var(--wave-deep) 100%
  );
  --gradient-wave-soft: linear-gradient(
    180deg,
    var(--wave-lightest) 0%,
    var(--wave-light) 50%,
    var(--wave-medium) 100%
  );
  --gradient-wave-reverse: linear-gradient(
    135deg,
    var(--wave-deep) 0%,
    var(--wave-primary) 25%,
    var(--wave-medium) 50%,
    var(--wave-light) 75%,
    var(--wave-lightest) 100%
  );
  --gradient-wave-subtle: linear-gradient(
    90deg,
    rgba(29, 78, 216, 0.05) 0%,
    rgba(59, 130, 246, 0.08) 50%,
    rgba(30, 64, 175, 0.05) 100%
  );
  
  /* 🎨 NOVOS GRADIENTES PREMIUM */
  --gradient-wave-hero: linear-gradient(
    135deg,
    rgba(29, 78, 216, 0.95) 0%,
    rgba(59, 130, 246, 0.9) 50%,
    rgba(6, 182, 212, 0.85) 100%
  );
  --gradient-wave-glass: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  --gradient-wave-card: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );

  /* SISTEMA DE CORES OPENAI ADAPTADO */
  --bg-primary: #ffffff;
  --bg-secondary: #f7f9fb; /* Azul ultra suave */
  --bg-tertiary: #eff5fb; /* Azul ainda mais suave */
  --bg-accent: #e3f5ff; /* Fundo de destaque */

  --text-primary: #1a1a1a;
  --text-secondary: #4a5568;
  --text-tertiary: #718096;
  --text-disabled: #a0aec0;
  --text-accent: var(--wave-primary);

  --border-default: rgba(46, 124, 214, 0.1);
  --border-hover: rgba(46, 124, 214, 0.2);
  --border-focus: rgba(46, 124, 214, 0.4);
  --border-accent: rgba(74, 144, 226, 0.3);

  /* SOMBRAS PREMIUM WELLWAVE */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 40px rgba(102, 126, 234, 0.2);
  
  --shadow-wave-xs: 0 1px 2px rgba(30, 91, 168, 0.05);
  --shadow-wave-sm: 0 2px 4px rgba(30, 91, 168, 0.08);
  --shadow-wave-md: 0 4px 8px rgba(30, 91, 168, 0.1);
  --shadow-wave-lg: 0 8px 16px rgba(30, 91, 168, 0.12);
  --shadow-wave-xl: 0 16px 32px rgba(30, 91, 168, 0.15);
  --shadow-wave-glow: 0 0 40px rgba(74, 144, 226, 0.2);
  --shadow-wave-focus: 0 0 0 4px rgba(74, 144, 226, 0.1);

  /* GLASS EFFECT PREMIUM */
  --glass-bg: rgba(255, 255, 255, 0.7);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
  --backdrop-blur: saturate(180%) blur(20px);

  /* DARK MODE WELLWAVE */
  --dark-bg-primary: #0f1419;
  --dark-bg-secondary: #1a2332;
  --dark-bg-tertiary: #243447;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #b8c5d6;
  --dark-border: rgba(135, 206, 235, 0.1);

  /* TOKENS DO SISTEMA ANTERIOR (Compatibilidade) */
  --font-size: 14px;
  --background: var(--bg-primary);
  --foreground: var(--text-primary);
  --card: var(--bg-primary);
  --card-foreground: var(--text-primary);
  --popover: var(--bg-primary);
  --popover-foreground: var(--text-primary);
  --primary: var(--wave-primary);
  --primary-foreground: #ffffff;
  --secondary: var(--bg-secondary);
  --secondary-foreground: var(--text-primary);
  --muted: var(--bg-tertiary);
  --muted-foreground: var(--text-secondary);
  --accent: var(--bg-accent);
  --accent-foreground: var(--wave-primary);
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: var(--border-default);
  --input: transparent;
  --input-background: var(--bg-secondary);
  --switch-background: #cbced4;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: var(--wave-primary);
  --chart-1: var(--wave-primary);
  --chart-2: var(--wave-medium);
  --chart-3: var(--wave-deep);
  --chart-4: var(--wave-light);
  --chart-5: var(--wave-lightest);
  --radius: 0.625rem;

  /* SIDEBAR PREMIUM WELLWAVE - Medical Responsive */
  --sidebar: var(--color-surface);
  --sidebar-foreground: var(--color-text);
  --sidebar-primary: var(--color-primary);
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: var(--color-primary-100);
  --sidebar-accent-foreground: var(--color-primary-600);
  --sidebar-border: var(--color-border);
  --sidebar-ring: var(--color-primary);
  
  /* Medical UI Responsive Sidebar Widths */
  --sidebar-width-desktop: 280px;    /* ≥1024px */
  --sidebar-width-tablet: 240px;     /* ≥768px & <1024px */
  --sidebar-width-mobile: 100vw;     /* <768px drawer */
  --sidebar-width-collapsed: 72px;   /* Collapsed state */
  
  --sidebar-width: var(--sidebar-width-desktop);
  --sidebar-shadow: var(--shadow-md);
  --sidebar-border-radius: var(--radius-lg);

  /* HEADER PREMIUM */
  --header-height: 64px;
  --header-bg: var(--glass-bg);
  --header-border: var(--glass-border);
  --header-shadow: var(--glass-shadow);

  /* ANIMAÇÕES PREMIUM OPENAI-WELLWAVE */
  --openai-spring-stiffness: 260;
  --openai-spring-damping: 20;
  --openai-spring-mass: 1;
  --openai-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --openai-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --openai-ease-smooth: cubic-bezier(0.25, 0.1, 0.25, 1);
  --openai-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* TIMING REFINADO */
  --openai-duration-instant: 0.08s;
  --openai-duration-fast: 0.12s;
  --openai-duration-normal: 0.15s;
  --openai-duration-slow: 0.2s;
  --openai-duration-slower: 0.3s;
  --openai-duration-slowest: 0.5s;

  /* SOMBRAS OPENAI-STYLE */
  --openai-shadow-sm: var(--shadow-wave-sm);
  --openai-shadow-md: var(--shadow-wave-md);
  --openai-shadow-lg: var(--shadow-wave-lg);
  --openai-shadow-xl: var(--shadow-wave-xl);

  /* 🔤 FONT SYSTEM OPTIMIZADO WELLWAVE */
  --font-family-base: "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
  --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono",
    Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  --font-family-heading: var(--font-family-base);

  /* 📱 MEDICAL RESPONSIVE GRID SYSTEM */
  --grid-columns: 12;
  --grid-gap: var(--sp-4);
  --grid-gap-sm: var(--sp-2);
  --grid-gap-lg: var(--sp-6);
  --grid-container-max-width: var(--max-content);
  --grid-container-padding: var(--sp-8);
  
  /* Medical Breakpoints - Mobile First */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;   /* tablet */
  --breakpoint-md: 1024px;  /* desktop */
  --breakpoint-lg: 1280px;  /* large desktop */
  --breakpoint-xl: 1536px;  /* extra large */
}

/* TEMA CLARO (Padrão WellWave) */
[data-theme="light"] {
  --bg: var(--bg-primary);
  --bg-sidebar: var(--bg-secondary);
  --text: var(--text-primary);
  --border: var(--border-default);
}

/* TEMA ESCURO PREMIUM WELLWAVE */
.dark,
[data-theme="dark"] {
  --background: var(--dark-bg-primary);
  --foreground: var(--dark-text-primary);
  --card: var(--dark-bg-secondary);
  --card-foreground: var(--dark-text-primary);
  --popover: var(--dark-bg-secondary);
  --popover-foreground: var(--dark-text-primary);
  --primary: var(--wave-light);
  --primary-foreground: var(--dark-bg-primary);
  --secondary: var(--dark-bg-tertiary);
  --secondary-foreground: var(--dark-text-primary);
  --muted: var(--dark-bg-tertiary);
  --muted-foreground: var(--dark-text-secondary);
  --accent: var(--dark-bg-tertiary);
  --accent-foreground: var(--wave-light);
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: var(--dark-border);
  --input: var(--dark-bg-tertiary);
  --ring: var(--wave-light);
  --sidebar: var(--dark-bg-secondary);
  --sidebar-foreground: var(--dark-text-primary);
  --sidebar-primary: var(--wave-light);
  --sidebar-primary-foreground: var(--dark-bg-primary);
  --sidebar-accent: var(--dark-bg-tertiary);
  --sidebar-accent-foreground: var(--dark-text-primary);
  --sidebar-border: var(--dark-border);
  --sidebar-ring: var(--wave-light);

  /* Dark mode sombras */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.2);
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4), 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.5), 0 10px 10px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.6);
  --shadow-wave-xs: 0 1px 2px rgba(0, 0, 0, 0.2);
  --shadow-wave-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-wave-md: 0 4px 6px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-wave-lg: 0 10px 15px rgba(0, 0, 0, 0.4),
    0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-wave-xl: 0 20px 25px rgba(0, 0, 0, 0.5),
    0 10px 10px rgba(0, 0, 0, 0.2);
  --sidebar-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);

  --glass-bg: rgba(13, 17, 23, 0.7);
  --glass-border: rgba(48, 54, 61, 0.3);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(
    --sidebar-primary-foreground
  );
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(
    --sidebar-accent-foreground
  );
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
    font-size: var(--fs-400);
    line-height: 1.6; /* Medical readability standard */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    background: var(--color-bg);
    color: var(--color-text);
    
    /* Accessibility - Focus management */
    scroll-behavior: smooth;
  }

  /* Enhanced Reduced Motion Support - Medical Accessibility */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
    
    /* Keep essential focus indicators */
    *:focus-visible {
      transition: outline 0s;
    }
    
    /* Disable hover transforms but keep color changes */
    .medical-card:hover {
      transform: none !important;
    }
    
    button:hover, .hover-lift:hover {
      transform: none !important;
    }
  }

  /* Print styles for medical documents */
  @media print {
    .medical-sidebar, .no-print {
      display: none !important;
    }
    
    .medical-container {
      max-width: none;
      padding: 0;
    }
    
    .medical-card {
      box-shadow: none;
      border: 1px solid #ccc;
    }
  }

  /* 🔤 SYSTEM FONT OPTIMIZATIONS */
  .using-system-fonts {
    /* Ajustes específicos quando usando fonts do sistema */
    --font-family-base: -apple-system, BlinkMacSystemFont,
      "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell",
      "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  }

  .using-inter-font {
    /* Otimizações quando Inter está disponível */
    --font-family-base: "Inter", -apple-system,
      BlinkMacSystemFont, "Segoe UI", sans-serif;
  }

  /* Font loading optimization */
  @font-face {
    font-family: "Inter-Optimized";
    font-style: normal;
    font-weight: 400 700;
    font-display: swap;
    src: local("Inter"), local("Inter-Regular");
    unicode-range: U+0000-00FF, U+0131, U+0152-0153,
      U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074,
      U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
      U+FFFD;
  }

  /* Medical UI Global Transitions - Performance Optimized */
  * {
    transition-property: background-color, border-color, color,
      fill, stroke, opacity, box-shadow, transform;
    transition-duration: var(--dur-base);
    transition-timing-function: var(--ease-standard);
  }

  /* Performance optimizations for medical UI */
  .medical-card, .medical-input, button {
    will-change: transform, box-shadow;
  }

  /* Container improvements for medical layouts */
  .medical-container {
    max-width: var(--max-content);
    margin: 0 auto;
    padding: 0 var(--sp-8);
  }

  .medical-container-wide {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  /* Medical full-width layout optimizations */
  .medical-full-layout {
    width: 100vw;
    max-width: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .medical-content-area {
    width: 100%;
    max-width: none;
    padding: var(--sp-6);
  }

  .medical-section {
    padding: var(--sp-12) 0;
  }

  @media (max-width: 768px) {
    .medical-container, .medical-container-wide {
      padding: 0 var(--sp-4);
    }
    
    .medical-section {
      padding: var(--sp-8) 0;
    }
  }

  /* Container principal WellWave Premium - Medical UI Enhanced */
  .app-container {
    display: flex;
    height: 100vh;
    background: var(--color-bg);
    font-family: var(--font-sans);
    font-size: var(--fs-400);
    line-height: 1.6;
    color: var(--color-text);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* Medical UI focus management */
    outline: none;
  }

  /* Medical focus indicators - WCAG 2.2 compliant */
  *:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }

  /* Medical UI Component Classes */
  .medical-card {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: var(--sp-6);
    transition: all var(--dur-base) var(--ease-standard);
  }

  .medical-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .medical-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    overflow: hidden;
  }

  .medical-table th {
    background: var(--color-primary-50);
    color: var(--color-text);
    font-weight: 600;
    font-size: var(--fs-300);
    padding: var(--sp-4);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .medical-table td {
    padding: var(--sp-4);
    border-bottom: 1px solid var(--color-border);
    font-size: var(--fs-400);
    line-height: 1.5;
  }

  .medical-table tr:hover {
    background: var(--color-primary-50);
  }

  /* MEDICAL INPUT STYLES - Premium Healthcare v3.0 */
  .medical-input {
    width: 100%;
    min-height: var(--button-height);
    padding: var(--medical-spacing-base) var(--medical-spacing-lg);
    background: var(--medical-bg-elevated);
    border: 1px solid var(--medical-border-base);
    border-radius: var(--radius-lg);
    font-size: var(--medical-text-base);
    font-family: var(--medical-font-family);
    line-height: var(--medical-leading-normal);
    color: var(--medical-text-primary);
    transition: all 200ms ease-out;
    box-shadow: var(--medical-shadow-input);
  }

  .medical-input:hover {
    border-color: var(--medical-border-strong);
    box-shadow: var(--medical-shadow-sm);
  }

  .medical-input:focus {
    outline: none;
    border-color: var(--medical-color-primary);
    box-shadow: var(--medical-shadow-input-focus);
    background: var(--medical-bg-primary);
  }

  .medical-input::placeholder {
    color: var(--medical-text-tertiary);
    font-weight: 400;
  }

  .medical-input:disabled {
    background: var(--medical-bg-muted);
    border-color: var(--medical-border-base);
    color: var(--medical-text-muted);
    cursor: not-allowed;
  }

  /* Toast notification styles */
  .medical-toast {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--sp-4);
    min-width: 320px;
    max-width: 480px;
  }

  .medical-toast.success {
    border-left: 4px solid var(--color-success);
  }

  .medical-toast.warning {
    border-left: 4px solid var(--color-warning);
  }

  .medical-toast.error {
    border-left: 4px solid var(--color-danger);
  }

  .medical-toast.info {
    border-left: 4px solid var(--color-info);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --color-border: #000000;
      --color-text: #000000;
      --shadow-sm: 0 2px 4px rgba(0,0,0,0.4);
    }
  }

  /* 📐 GRID SYSTEM 12 COLUNAS */
  .grid-container {
    max-width: var(--grid-container-max-width);
    margin: 0 auto;
    padding: 0 var(--grid-container-padding);
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(var(--grid-columns), 1fr);
    gap: var(--grid-gap);
  }

  .grid-gap-sm {
    gap: var(--grid-gap-sm);
  }

  .grid-gap-lg {
    gap: var(--grid-gap-lg);
  }

  /* Medical UI Responsive Columns - Mobile First */
  .col-1 { grid-column: span 1; }
  .col-2 { grid-column: span 2; }
  .col-3 { grid-column: span 3; }
  .col-4 { grid-column: span 4; }
  .col-5 { grid-column: span 5; }
  .col-6 { grid-column: span 6; }
  .col-7 { grid-column: span 7; }
  .col-8 { grid-column: span 8; }
  .col-9 { grid-column: span 9; }
  .col-10 { grid-column: span 10; }
  .col-11 { grid-column: span 11; }
  .col-12 { grid-column: span 12; }

  /* Medical Responsive Behavior */
  @media (max-width: 768px) {
    .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
      grid-column: span 12;
    }
    
    /* Mobile sidebar adjustments */
    :root {
      --sidebar-width: var(--sidebar-width-mobile);
      --grid-container-padding: var(--sp-4);
    }
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    /* Tablet sidebar adjustments */
    :root {
      --sidebar-width: var(--sidebar-width-tablet);
    }
  }

  @media (min-width: 1024px) {
    /* Desktop sidebar adjustments */
    :root {
      --sidebar-width: var(--sidebar-width-desktop);
    }
  }
}

/**
 * 🌊 ANIMAÇÕES PREMIUM WELLWAVE (OpenAI + Wave Effects)
 */
@layer components {
  /* Animações Suaves Premium */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    }
    50% {
      box-shadow: 0 0 40px rgba(102, 126, 234, 0.8);
    }
  }

  @keyframes rotateIn {
    from {
      opacity: 0;
      transform: rotate(-180deg) scale(0.5);
    }
    to {
      opacity: 1;
      transform: rotate(0) scale(1);
    }
  }

  @keyframes heartbeat {
    0%, 100% {
      transform: scale(1);
    }
    14% {
      transform: scale(1.1);
    }
    28% {
      transform: scale(1);
    }
    42% {
      transform: scale(1.1);
    }
    70% {
      transform: scale(1);
    }
  }

  /* Wave Motion para logo */
  @keyframes waveMotion {
    0%,
    100% {
      transform: translateY(0) scaleY(1);
    }
    25% {
      transform: translateY(-2px) scaleY(0.98);
    }
    50% {
      transform: translateY(-4px) scaleY(0.95);
    }
    75% {
      transform: translateY(-2px) scaleY(0.98);
    }
  }

  @keyframes waveFlow {
    0% {
      transform: translateX(-100%) skewX(-15deg);
    }
    100% {
      transform: translateX(100%) skewX(-15deg);
    }
  }

  @keyframes wavePulse {
    0%,
    100% {
      box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.4);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
    }
  }

  /* ECG/Pulse médico */
  @keyframes pulseECG {
    0% {
      stroke-dasharray: 0 1000;
    }
    50% {
      stroke-dasharray: 100 1000;
    }
    100% {
      stroke-dasharray: 0 1000;
      stroke-dashoffset: -1000;
    }
  }

  /* DNA Helix loader médico */
  @keyframes dnaRotate {
    0% {
      transform: rotateY(0deg);
    }
    100% {
      transform: rotateY(360deg);
    }
  }

  /* Shimmer loading WellWave */
  @keyframes shimmerWave {
    0% {
      background-position: -200% center;
    }
    100% {
      background-position: 200% center;
    }
  }

  /* OpenAI Animations adaptadas */
  @keyframes openai-fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes openai-slide-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes openai-slide-in-left {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes openai-scale-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes blink {
    0%,
    49% {
      opacity: 1;
    }
    50%,
    100% {
      opacity: 0;
    }
  }

  @keyframes pulse-gentle {
    0%,
    100% {
      opacity: 0.95;
    }
    50% {
      opacity: 1;
    }
  }

  /* Ripple effect WellWave */
  @keyframes rippleWave {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  /* Typing indicator médico */
  @keyframes typingDot {
    0%,
    60%,
    100% {
      transform: translateY(0);
      opacity: 0.5;
    }
    30% {
      transform: translateY(-10px);
      opacity: 1;
    }
  }

  /* Classes de Animação Premium */
  .animate-entrance {
    animation: slideInUp 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  }

  .animate-scale {
    animation: fadeInScale 0.4s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  }

  .animate-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  .animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
  }

  /* CLASSES DE ANIMAÇÃO WELLWAVE */
  .wave-motion {
    animation: waveMotion 3s ease-in-out infinite;
  }

  .wave-flow {
    position: relative;
    overflow: hidden;
  }

  .wave-flow::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-wave-subtle);
    animation: waveFlow 2s linear infinite;
  }

  .wave-pulse {
    animation: wavePulse 2s infinite;
  }

  .pulse-ecg {
    animation: pulseECG 2s ease-in-out infinite;
  }

  .dna-helix {
    position: relative;
    width: 60px;
    height: 60px;
  }

  .dna-helix::before,
  .dna-helix::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top-color: var(--wave-primary);
    border-radius: 50%;
    animation: dnaRotate 1.5s linear infinite;
  }

  .dna-helix::after {
    border-top-color: var(--wave-medium);
    animation-delay: 0.25s;
    transform: scale(0.8);
  }

  /* Hover Premium Effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    cursor: pointer;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
  }

  .hover-grow {
    transition: transform 0.2s ease;
  }

  .hover-grow:hover {
    transform: scale(1.05);
  }

  .hover-glow:hover {
    box-shadow: var(--shadow-glow);
  }

  /* Glass Morphism Premium */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    -webkit-backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  /* OpenAI classes adaptadas */
  .openai-fade-in {
    animation: openai-fade-in var(--openai-duration-slow)
      var(--openai-ease-out);
  }

  .openai-slide-up {
    animation: openai-slide-up var(--openai-duration-slow)
      var(--openai-ease-out);
  }

  .openai-slide-in-left {
    animation: openai-slide-in-left var(--openai-duration-slow)
      var(--openai-ease-out);
  }

  .openai-scale-in {
    animation: openai-scale-in var(--openai-duration-normal)
      var(--openai-ease-out);
  }

  .typing-cursor {
    animation: blink 1s infinite;
    color: rgba(74, 144, 226, 0.7);
    font-weight: 300;
  }

  .dark .typing-cursor {
    color: rgba(135, 206, 235, 0.7);
  }

  .pulse-gentle {
    animation: pulse-gentle 3s ease-in-out infinite;
  }

  /* WellWave Button Styles Premium */
  .openai-button {
    @apply relative overflow-hidden;
    transition: all var(--openai-duration-normal)
      var(--openai-ease-in-out);
    border-radius: var(--radius);
  }

  .openai-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-wave-subtle);
    opacity: 0;
    transition: all var(--openai-duration-slow)
      var(--openai-ease-out);
  }

  .openai-button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-wave-md);
    border-color: var(--wave-primary);
  }

  .openai-button:hover::before {
    left: 0;
    opacity: 1;
  }

  .openai-button:active {
    transform: translateY(1px) scale(0.98);
    transition-duration: var(--openai-duration-fast);
  }

  /* Ripple effect */
  .ripple {
    position: relative;
    overflow: hidden;
  }

  .ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .ripple:active::before {
    width: 300px;
    height: 300px;
  }

  /* WellWave Card Styles Premium */
  .openai-card {
    @apply relative overflow-hidden;
    transition: all var(--openai-duration-slow)
      var(--openai-ease-in-out);
    box-shadow: var(--shadow-wave-sm);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
  }

  .openai-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--gradient-wave);
    opacity: 0;
    transition: opacity var(--openai-duration-normal)
      var(--openai-ease-out);
  }

  .openai-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-wave-lg);
    border-color: var(--border-hover);
  }

  .openai-card:hover::before {
    opacity: 1;
  }

  /* Stats Card Premium */
  .stat-card {
    @apply relative overflow-hidden;
    transition: all var(--openai-duration-slow) var(--openai-ease-in-out);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    border: 1px solid var(--border-default);
    padding: var(--spacing-lg);
    min-height: 120px;
  }

  .stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-wave);
    opacity: 0;
    transition: opacity var(--openai-duration-normal);
  }

  .stat-card:hover::before {
    opacity: 1;
  }

  .stat-bg {
    position: absolute;
    top: 0;
    right: 0;
    width: 80px;
    height: 80px;
    opacity: 0.1;
    border-radius: 0 var(--radius-lg) 0 100%;
  }

  .stat-content {
    position: relative;
    z-index: 2;
  }

  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
  }

  .stat-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .stat-icon {
    font-size: 20px;
  }

  .stat-value {
    font-size: 32px;
    font-weight: 700;
    line-height: 1;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }

  .stat-footer {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  .stat-change {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .stat-change.positive {
    color: var(--medical-success);
    background: rgba(0, 200, 150, 0.1);
  }

  .stat-change.negative {
    color: var(--medical-danger);
    background: rgba(255, 90, 95, 0.1);
  }

  .stat-change.neutral {
    color: var(--text-secondary);
    background: var(--neutral-100);
  }

  .stat-period {
    font-size: 11px;
    color: var(--text-tertiary);
  }

  /* Sidebar WellWave Premium */
  .openai-sidebar {
    width: var(--sidebar-width);
    box-shadow: var(--sidebar-shadow);
    transition: width var(--openai-duration-slower)
      var(--openai-ease-out);
    backdrop-filter: blur(20px);
    border: 1px solid var(--sidebar-border);
    background: var(--sidebar);
    position: relative;
  }

  .openai-sidebar::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: var(--gradient-wave);
    opacity: 0.3;
  }

  .openai-sidebar.collapsed {
    width: var(--sidebar-width-collapsed);
  }

  .openai-sidebar-item {
    @apply relative overflow-hidden;
    transition: all var(--openai-duration-fast)
      var(--openai-ease-in-out);
    border-radius: 8px;
    margin: 2px 8px;
    position: relative;
  }

  .openai-sidebar-item::after {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 0;
    background: var(--gradient-wave);
    transition: height var(--openai-duration-normal)
      var(--openai-ease-out);
    border-radius: 0 3px 3px 0;
  }

  .openai-sidebar-item:hover {
    background-color: var(--sidebar-accent);
    transform: translateX(2px);
    color: var(--sidebar-accent-foreground);
  }

  .openai-sidebar-item:active {
    transform: translateX(1px) scale(0.98);
    transition-duration: var(--openai-duration-fast);
  }

  .openai-sidebar-item.active {
    background-color: var(--sidebar-accent);
    color: var(--sidebar-primary);
    font-weight: 500;
  }

  .openai-sidebar-item.active::after {
    height: 24px;
  }

  /* Quick Actions Premium */
  .quick-actions {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .quick-btn {
    flex: 1;
    height: 44px;
    border-radius: var(--radius);
    border: 1px solid var(--border-default);
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 12px;
    font-weight: 600;
    transition: all var(--openai-duration-normal);
    position: relative;
    overflow: hidden;
  }

  .quick-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--openai-duration-slow);
  }

  .quick-btn:hover::before {
    left: 100%;
  }

  .quick-btn.emergency {
    border-color: var(--medical-danger);
    color: var(--medical-danger);
  }

  .quick-btn.emergency:hover {
    background: var(--medical-danger);
    color: white;
    box-shadow: 0 0 20px rgba(255, 90, 95, 0.3);
  }

  .quick-btn.ai-chat {
    border-color: var(--medical-info);
    color: var(--medical-info);
  }

  .quick-btn.ai-chat:hover {
    background: var(--medical-info);
    color: white;
    box-shadow: 0 0 20px rgba(139, 127, 255, 0.3);
  }

  /* Navigation Groups Premium */
  .nav-group {
    margin-bottom: var(--spacing-lg);
  }

  .nav-group-title {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--text-tertiary);
    margin: 0 var(--spacing-md) var(--spacing-sm);
    opacity: 0.8;
  }

  .nav-items {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .nav-item {
    margin-bottom: 2px;
  }

  .nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--openai-duration-fast);
    position: relative;
    overflow: hidden;
  }

  .nav-link:hover {
    background: var(--sidebar-accent);
    color: var(--sidebar-accent-foreground);
    transform: translateX(2px);
  }

  .nav-link.active {
    background: var(--sidebar-accent);
    color: var(--sidebar-primary);
    font-weight: 500;
  }

  .nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  .nav-label {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
  }

  .nav-badge {
    font-size: 10px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
    background: var(--neutral-200);
    color: var(--text-secondary);
    min-width: 18px;
    text-align: center;
  }

  .nav-badge.highlight {
    background: var(--gradient-wave);
    color: white;
    animation: pulse-gentle 2s infinite;
  }

  /* Focus states WellWave */
  .using-keyboard *:focus {
    outline: 2px solid var(--wave-primary) !important;
    outline-offset: 2px !important;
    border-radius: var(--radius);
    box-shadow: var(--shadow-wave-focus);
  }

  :not(.using-keyboard) *:focus {
    outline: none !important;
  }

  /* Scrollbar WellWave */
  .openai-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--wave-light) transparent;
  }

  .openai-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .openai-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .openai-scrollbar::-webkit-scrollbar-thumb {
    background: var(--wave-light);
    border-radius: 2px;
  }

  .openai-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--wave-primary);
  }

  .dark .openai-glass {
    background: rgba(26, 35, 50, 0.3);
    border: 1px solid var(--dark-border);
  }

  /* Loading states WellWave Premium */
  .openai-skeleton {
    background: linear-gradient(
      90deg,
      var(--wave-lightest) 25%,
      #ffffff 50%,
      var(--wave-lightest) 75%
    );
    background-size: 200% 100%;
    animation: shimmerWave 1.5s infinite;
    border-radius: var(--radius);
  }

  .dark .openai-skeleton {
    background: linear-gradient(
      90deg,
      var(--dark-bg-secondary) 25%,
      var(--dark-bg-tertiary) 50%,
      var(--dark-bg-secondary) 75%
    );
    background-size: 200% 100%;
  }

  /* Skeleton Loaders Premium */
  .skeleton {
    background: linear-gradient(
      90deg,
      var(--neutral-100) 0%,
      var(--neutral-200) 20%,
      var(--neutral-100) 40%,
      var(--neutral-100) 100%
    );
    background-size: 200% 100%;
    animation: shimmerWave 1.5s infinite;
    border-radius: var(--radius);
  }

  /* Typing indicator WellWave */
  .typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
  }

  .typing-indicator .dot {
    width: 8px;
    height: 8px;
    background: var(--wave-medium);
    border-radius: 50%;
    animation: typingDot 1.4s infinite;
  }

  .typing-indicator .dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-indicator .dot:nth-child(3) {
    animation-delay: 0.4s;
  }

  /* Ripple effect */
  .ripple {
    position: absolute;
    background: radial-gradient(
      circle,
      rgba(74, 144, 226, 0.5) 0%,
      transparent 70%
    );
    transform: scale(0);
    animation: rippleWave 0.6s ease-out;
    pointer-events: none;
    border-radius: 50%;
  }

  /* Medical status indicators */
  .status-critical {
    color: #ef4444;
    animation: wavePulse 2s infinite;
  }

  .status-urgent {
    color: #f59e0b;
    animation: pulse-gentle 3s infinite;
  }

  .status-stable {
    color: var(--wave-primary);
  }

  .status-resolved {
    color: #10b981;
  }

  /* Header Premium Flutuante */
  .dashboard-header {
    height: var(--header-height);
    background: var(--header-bg);
    backdrop-filter: var(--backdrop-blur);
    -webkit-backdrop-filter: var(--backdrop-blur);
    border-bottom: 1px solid var(--header-border);
    box-shadow: var(--header-shadow);
    position: sticky;
    top: 0;
    z-index: 40;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-xl);
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .title-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    background: var(--gradient-wave);
    color: white;
  }

  /* Breadcrumbs Premium */
  .breadcrumbs {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 13px;
    color: var(--text-tertiary);
    margin-top: 2px;
  }

  .breadcrumbs a {
    color: var(--text-tertiary);
    text-decoration: none;
    transition: color var(--openai-duration-fast);
  }

  .breadcrumbs a:hover {
    color: var(--wave-primary);
  }

  .breadcrumbs .separator {
    color: var(--text-disabled);
    margin: 0 2px;
  }

  .breadcrumbs .current {
    color: var(--text-secondary);
    font-weight: 500;
  }

  /* Search Universal Premium */
  .universal-search {
    position: relative;
    width: 400px;
    max-width: 50vw;
  }

  .search-input {
    width: 100%;
    height: 40px;
    padding: 0 var(--spacing-md);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-lg);
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    font-size: 14px;
    transition: all var(--openai-duration-normal);
  }

  .search-input:focus {
    outline: none;
    border-color: var(--wave-primary);
    box-shadow: var(--shadow-wave-focus);
  }

  .search-input::placeholder {
    color: var(--text-tertiary);
  }

  /* Header Actions Premium */
  .header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  .action-btn {
    position: relative;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-default);
    border-radius: var(--radius);
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--openai-duration-normal);
    cursor: pointer;
  }

  .action-btn:hover {
    background: var(--bg-accent);
    border-color: var(--wave-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  .notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: var(--medical-danger);
    border-radius: 50%;
    border: 2px solid var(--bg-primary);
    animation: pulse-gentle 2s infinite;
  }

  /* Stats Grid Premium */
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
  }

  /* Content Grid Premium */
  .content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
  }

  .card-large {
    grid-column: span 1;
  }

  .card-scroll {
    max-height: 600px;
    overflow-y: auto;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-default);
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .card-badge {
    font-size: 11px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    background: var(--bg-accent);
    color: var(--wave-primary);
  }

  .card-body {
    padding: var(--spacing-lg);
  }

  /* Filter Select Premium */
  .filter-select {
    padding: 6px 12px;
    border: 1px solid var(--border-default);
    border-radius: var(--radius);
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    font-size: 13px;
    color: var(--text-primary);
    cursor: pointer;
  }

  .filter-select:focus {
    outline: none;
    border-color: var(--wave-primary);
    box-shadow: var(--shadow-wave-focus);
  }
}

/**
 * Typography WellWave Premium
 */
@layer base {
  :where(
    :not(:has([class*=" text-"]), :not(:has([class^="text-"])))
  ) {
    h1 {
      font-size: 32px;
      font-weight: 700;
      line-height: 1.2;
      color: var(--text-primary);
      font-family: var(--font-family-heading);
    }

    h2 {
      font-size: 28px;
      font-weight: 600;
      line-height: 1.3;
      color: var(--text-primary);
      font-family: var(--font-family-heading);
    }

    h3 {
      font-size: 24px;
      font-weight: 600;
      line-height: 1.3;
      color: var(--text-primary);
      font-family: var(--font-family-heading);
    }

    h4 {
      font-size: 20px;
      font-weight: 600;
      line-height: 1.4;
      color: var(--text-primary);
      font-family: var(--font-family-heading);
    }

    h5 {
      font-size: 16px;
      font-weight: 600;
      line-height: 1.4;
      color: var(--text-primary);
      font-family: var(--font-family-heading);
    }

    h6 {
      font-size: 14px;
      font-weight: 600;
      line-height: 1.4;
      color: var(--text-primary);
      font-family: var(--font-family-heading);
    }

    p {
      font-size: var(--font-size);
      font-weight: var(--font-weight-normal);
      line-height: 1.6;
      color: var(--text-secondary);
    }

    label {
      font-size: var(--font-size);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
      color: var(--text-primary);
    }

    button {
      font-size: var(--font-size);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
      font-family: var(--font-family-base);
    }

    input,
    textarea {
      font-size: var(--font-size);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
      font-family: var(--font-family-base);
    }
  }
}

html {
  font-size: var(--font-size);
}

/* Mobile optimizations WellWave */
@media (max-width: 768px) {
  :root {
    --sidebar-width: 100vw;
    --grid-container-padding: var(--spacing-md);
  }

  .openai-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform var(--openai-duration-slower)
      var(--openai-ease-out);
  }

  .openai-sidebar.mobile-open {
    transform: translateX(0);
    box-shadow: var(--shadow-wave-xl);
  }

  .openai-sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(4px);
    z-index: 40;
  }

  .mobile-menu-button {
    display: flex;
    position: fixed;
    top: 16px;
    left: 16px;
    z-index: 999;
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    border: 1px solid var(--border-default);
    border-radius: var(--radius);
    box-shadow: var(--shadow-wave-md);
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .universal-search {
    width: 200px;
  }

  .dashboard-header {
    padding: 0 var(--spacing-md);
  }

  .header-left {
    flex: 1;
  }

  .page-title {
    font-size: 20px;
  }
}

/* Tablet optimizations */
@media (max-width: 1024px) and (min-width: 769px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .content-grid {
    grid-template-columns: 1fr;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  :root {
    --openai-duration-instant: 0ms;
    --openai-duration-fast: 0ms;
    --openai-duration-normal: 0ms;
    --openai-duration-slow: 0ms;
    --openai-duration-slower: 0ms;
    --openai-duration-slowest: 0ms;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Custom utilities WellWave Premium */
@layer utilities {
  .transition-wave {
    transition: all var(--openai-duration-normal)
      var(--openai-ease-in-out);
  }

  .transition-wave-fast {
    transition: all var(--openai-duration-fast)
      var(--openai-ease-in-out);
  }

  .transition-wave-slow {
    transition: all var(--openai-duration-slow)
      var(--openai-ease-out);
  }

  .shadow-wave-xs {
    box-shadow: var(--shadow-wave-xs);
  }

  .shadow-wave-sm {
    box-shadow: var(--shadow-wave-sm);
  }

  .shadow-wave-md {
    box-shadow: var(--shadow-wave-md);
  }

  .shadow-wave-lg {
    box-shadow: var(--shadow-wave-lg);
  }

  .shadow-wave-xl {
    box-shadow: var(--shadow-wave-xl);
  }

  .shadow-wave-glow {
    box-shadow: var(--shadow-wave-glow);
  }

  .bg-gradient-wave {
    background: var(--gradient-wave);
  }

  .bg-gradient-wave-soft {
    background: var(--gradient-wave-soft);
  }

  .bg-gradient-wave-reverse {
    background: var(--gradient-wave-reverse);
  }

  .bg-gradient-premium {
    background: var(--gradient-premium);
  }

  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-success {
    background: var(--gradient-success);
  }

  .bg-gradient-warning {
    background: var(--gradient-warning);
  }

  .bg-gradient-danger {
    background: var(--gradient-danger);
  }

  .text-wave-primary {
    color: var(--wave-primary);
  }

  .text-wave-medium {
    color: var(--wave-medium);
  }

  .text-wave-deep {
    color: var(--wave-deep);
  }

  .text-medical-primary {
    color: var(--medical-primary);
  }

  .text-medical-success {
    color: var(--medical-success);
  }

  .text-medical-warning {
    color: var(--medical-warning);
  }

  .text-medical-danger {
    color: var(--medical-danger);
  }

  .text-medical-info {
    color: var(--medical-info);
  }

  .border-wave {
    border-color: var(--wave-primary);
  }

  .border-wave-light {
    border-color: var(--wave-light);
  }

  .border-medical-primary {
    border-color: var(--medical-primary);
  }

  /* Font utilities */
  .font-inter {
    font-family: var(--font-family-base);
  }

  .font-mono {
    font-family: var(--font-family-mono);
  }

  .font-optimized {
    font-family: "Inter-Optimized", var(--font-family-base);
  }

  /* Spacing utilities baseados no spacing system */
  .p-spacing-xs { padding: var(--spacing-xs); }
  .p-spacing-sm { padding: var(--spacing-sm); }
  .p-spacing-md { padding: var(--spacing-md); }
  .p-spacing-lg { padding: var(--spacing-lg); }
  .p-spacing-xl { padding: var(--spacing-xl); }
  .p-spacing-2xl { padding: var(--spacing-2xl); }
  .p-spacing-3xl { padding: var(--spacing-3xl); }

  .m-spacing-xs { margin: var(--spacing-xs); }
  .m-spacing-sm { margin: var(--spacing-sm); }
  .m-spacing-md { margin: var(--spacing-md); }
  .m-spacing-lg { margin: var(--spacing-lg); }
  .m-spacing-xl { margin: var(--spacing-xl); }
  .m-spacing-2xl { margin: var(--spacing-2xl); }
  .m-spacing-3xl { margin: var(--spacing-3xl); }

  .gap-spacing-xs { gap: var(--spacing-xs); }
  .gap-spacing-sm { gap: var(--spacing-sm); }
  .gap-spacing-md { gap: var(--spacing-md); }
  .gap-spacing-lg { gap: var(--spacing-lg); }
  .gap-spacing-xl { gap: var(--spacing-xl); }
  .gap-spacing-2xl { gap: var(--spacing-2xl); }
  .gap-spacing-3xl { gap: var(--spacing-3xl); }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --wave-primary: #1e5ba8;
    --wave-medium: #0f3a6b;
    --wave-deep: #000000;
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-default: #000000;
  }

  .dark {
    --wave-primary: #87ceeb;
    --wave-medium: #e3f5ff;
    --wave-light: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --border-default: #ffffff;
  }
}

/* Font loading optimization for critical web vitals */
@supports (font-display: swap) {
  @font-face {
    font-family: "Inter-Local";
    font-style: normal;
    font-weight: 400 700;
    font-display: swap;
    src: local("Inter"), local("Inter-Regular"),
      local("Inter-Medium"), local("Inter-SemiBold"),
      local("Inter-Bold");
  }
}

/* Prefers reduced data */
@media (prefers-reduced-data: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }

  .wave-flow::before,
  .openai-skeleton,
  .wave-motion,
  .wave-pulse {
    animation: none !important;
  }
}

/* Print styles */
@media print {
  .app-container {
    display: block !important;
    height: auto !important;
  }

  .openai-sidebar,
  .mobile-menu-button,
  [data-no-print] {
    display: none !important;
  }

  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/* Medical Typography Classes - Enhanced for Medical Readability */
.font-medical-heading { font-family: var(--font-medical-heading); }
.font-medical-body { font-family: var(--font-medical-body); }
.font-medical-mono { font-family: var(--font-medical-mono); }

.leading-medical-tight { line-height: var(--leading-medical-tight); }
.leading-medical-normal { line-height: var(--leading-medical-normal); }
.leading-medical-relaxed { line-height: var(--leading-medical-relaxed); }
.leading-medical-loose { line-height: var(--leading-medical-loose); }

/* Medical Text Hierarchy Classes */
.text-medical-display { 
  font-size: var(--text-4xl); 
  font-weight: 700; 
  line-height: var(--leading-medical-tight);
  color: var(--medical-text-primary);
}

.text-medical-heading-1 { 
  font-size: var(--text-3xl); 
  font-weight: 600; 
  line-height: var(--leading-medical-tight);
  color: var(--medical-text-primary);
}

.text-medical-heading-2 { 
  font-size: var(--text-2xl); 
  font-weight: 600; 
  line-height: var(--leading-medical-normal);
  color: var(--medical-text-primary);
}

.text-medical-heading-3 { 
  font-size: var(--text-xl); 
  font-weight: 600; 
  line-height: var(--leading-medical-normal);
  color: var(--medical-text-primary);
}

.text-medical-body { 
  font-size: var(--text-base); 
  font-weight: 400; 
  line-height: var(--leading-medical-relaxed);
  color: var(--medical-text-primary);
}

.text-medical-body-small { 
  font-size: var(--text-sm); 
  font-weight: 400; 
  line-height: var(--leading-medical-relaxed);
  color: var(--medical-text-secondary);
}

.text-medical-caption { 
  font-size: var(--text-xs); 
  font-weight: 400; 
  line-height: var(--leading-medical-normal);
  color: var(--medical-text-tertiary);
}

.text-medical-mono { 
  font-family: var(--font-medical-mono); 
  font-size: var(--text-sm); 
  line-height: var(--leading-medical-normal);
  color: var(--medical-text-primary);
}

/* Medical Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .transition-medical-fast,
  .transition-medical-normal,
  .transition-medical-slow {
    transition-duration: 0.01ms !important;
  }
  
  .ease-medical-out,
  .ease-medical-in,
  .ease-medical-in-out {
    transition-timing-function: linear !important;
  }
}

/* Medical Focus Management */
.medical-focus-visible {
  outline: 2px solid var(--medical-focus-ring);
  outline-offset: 2px;
}

.medical-focus-visible-critical {
  outline: 2px solid var(--medical-focus-ring-critical);
  outline-offset: 2px;
}

.medical-focus-visible-urgent {
  outline: 2px solid var(--medical-focus-ring-urgent);
  outline-offset: 2px;
}

/* Medical Touch Target Optimization */
.medical-touch-target {
  min-height: 44px;
  min-width: 44px;
}

.medical-touch-target-large {
  min-height: 48px;
  min-width: 48px;
}