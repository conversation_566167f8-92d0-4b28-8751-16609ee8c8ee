import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "./utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all duration-200 focus-visible:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-medical-color-primary disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 touch-manipulation",
  {
    variants: {
      variant: {
        default:
          "bg-medical-color-primary text-white shadow-medical-button hover:bg-medical-color-primary-700 hover:shadow-medical-button-hover active:scale-[0.98] rounded-lg",
        destructive:
          "bg-medical-color-critical text-white shadow-medical-button hover:bg-medical-color-critical-600 hover:shadow-medical-button-hover active:scale-[0.98] rounded-lg",
        outline:
          "border border-medical-border-base bg-white shadow-medical-button hover:bg-medical-bg-muted hover:border-medical-color-primary-300 hover:shadow-medical-button-hover active:scale-[0.98] rounded-lg",
        secondary:
          "bg-medical-bg-secondary text-medical-text-primary shadow-medical-button hover:bg-medical-bg-subtle hover:shadow-medical-button-hover active:scale-[0.98] rounded-lg",
        ghost: "hover:bg-medical-bg-muted hover:text-medical-color-primary rounded-lg active:scale-[0.98]",
        link: "text-medical-color-primary underline-offset-4 hover:underline",
        medical: "bg-medical-color-primary text-white shadow-medical-md hover:shadow-medical-lg hover:bg-medical-color-primary-600 hover:scale-[1.02] active:scale-[0.98] rounded-lg",
        medicalCritical: "bg-medical-color-critical text-white shadow-medical-md hover:shadow-medical-lg hover:bg-medical-color-critical-600 hover:scale-[1.02] active:scale-[0.98] rounded-lg",
        medicalUrgent: "bg-medical-color-urgent text-white shadow-medical-md hover:shadow-medical-lg hover:bg-medical-color-urgent-600 hover:scale-[1.02] active:scale-[0.98] rounded-lg",
        medicalSuccess: "bg-medical-color-success text-white shadow-medical-md hover:shadow-medical-lg hover:bg-medical-color-success-600 hover:scale-[1.02] active:scale-[0.98] rounded-lg",
        medicalGlass: "bg-medical-glass-card border border-medical-glass-border backdrop-blur-sm text-medical-text-primary shadow-medical-sm hover:shadow-medical-md hover:scale-[1.01] active:scale-[0.99] rounded-lg",
        medicalOutline: "border-2 border-medical-color-primary bg-transparent text-medical-color-primary hover:bg-medical-color-primary hover:text-white shadow-medical-sm hover:shadow-medical-md active:scale-[0.98] rounded-lg",
      },
      size: {
        default: "h-11 px-6 py-2 text-sm min-w-[88px]", /* 44px+ touch target */
        sm: "h-9 px-4 text-xs min-w-[72px]",
        lg: "h-12 px-8 text-base min-w-[120px]",
        icon: "h-11 w-11 rounded-lg", /* Square touch target */
        medical: "h-12 px-8 text-base min-w-[140px]", /* Medical action buttons */
        medicalLarge: "h-14 px-10 text-lg min-w-[160px]", /* Large medical buttons */
        medicalSmall: "h-10 px-5 text-sm min-w-[100px]", /* Small medical buttons */
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }