import React from 'react'
import { cn } from './utils'
import { motion } from 'motion/react'

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'premium' | 'hero'
  blur?: 'sm' | 'md' | 'lg' | 'xl'
  opacity?: number
  animated?: boolean
  hoverEffect?: boolean
  children: React.ReactNode
}

const glassVariants = {
  default: {
    background: 'var(--medical-glass-card)',
    border: '1px solid var(--medical-glass-border)',
    backdropFilter: 'blur(12px)',
    boxShadow: 'var(--medical-shadow-glass)',
  },
  elevated: {
    background: 'var(--medical-glass-premium)',
    border: '1px solid var(--medical-glass-border)',
    backdropFilter: 'blur(16px)',
    boxShadow: 'var(--medical-shadow-glass-hover)',
  },
  premium: {
    background: 'linear-gradient(135deg, var(--medical-glass-hero) 0%, var(--medical-glass-premium) 100%)',
    border: '1px solid var(--medical-color-primary-200)',
    backdropFilter: 'blur(20px)',
    boxShadow: 'var(--medical-shadow-glass-elevated), inset 0 1px 0 rgba(255, 255, 255, 0.3)',
  },
  hero: {
    background: 'linear-gradient(135deg, var(--medical-color-primary-50) 0%, var(--medical-glass-hero) 100%)',
    border: '1px solid var(--medical-color-primary-200)',
    backdropFilter: 'blur(24px)',
    boxShadow: 'var(--medical-shadow-2xl), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
  }
}

const blurLevels = {
  sm: 'blur(8px)',
  md: 'blur(12px)',
  lg: 'blur(16px)',
  xl: 'blur(24px)'
}

export function GlassCard({ 
  variant = 'default',
  blur = 'md',
  opacity,
  animated = true,
  hoverEffect = true,
  className,
  children,
  ...props 
}: GlassCardProps) {
  const styles = glassVariants[variant]
  
  const cardStyles = {
    ...styles,
    backdropFilter: blurLevels[blur],
    background: opacity 
      ? `rgba(255, 255, 255, ${opacity})` 
      : styles.background
  }

  const animationVariants = {
    initial: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    animate: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: hoverEffect ? {
      scale: 1.02,
      y: -4,
      boxShadow: [
        styles.boxShadow,
        '0 32px 80px 0 rgba(29, 78, 216, 0.25)'
      ],
      transition: {
        duration: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    } : {},
    tap: {
      scale: 0.98,
      transition: {
        duration: 0.1
      }
    }
  }

  const CardComponent = animated ? motion.div : 'div'
  const motionProps = animated ? {
    variants: animationVariants,
    initial: 'initial',
    animate: 'animate',
    whileHover: 'hover',
    whileTap: 'tap'
  } : {}

  return (
    <CardComponent
      className={cn(
        'relative overflow-hidden rounded-xl',
        // Dark mode adjustments
        'dark:bg-slate-900/80 dark:border-slate-700/50',
        className
      )}
      style={cardStyles}
      {...motionProps}
      {...props}
    >
      {/* Inner glow effect for premium variants */}
      {(variant === 'premium' || variant === 'hero') && (
        <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/10 via-transparent to-transparent pointer-events-none" />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Bottom highlight for hero variant */}
      {variant === 'hero' && (
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent" />
      )}
    </CardComponent>
  )
}

// Preset components for common use cases
export function HeroGlassCard({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) {
  return (
    <GlassCard 
      variant="hero" 
      className={cn('p-8', className)} 
      {...props}
    >
      {children}
    </GlassCard>
  )
}

export function PremiumGlassCard({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) {
  return (
    <GlassCard 
      variant="premium" 
      className={cn('p-6', className)} 
      {...props}
    >
      {children}
    </GlassCard>
  )
}

export function ElevatedGlassCard({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) {
  return (
    <GlassCard 
      variant="elevated" 
      className={cn('p-4', className)} 
      {...props}
    >
      {children}
    </GlassCard>
  )
}