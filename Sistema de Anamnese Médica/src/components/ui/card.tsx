import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "./utils";

const cardVariants = cva(
  "flex flex-col gap-6 rounded-xl border transition-all duration-200 ease-out",
  {
    variants: {
      variant: {
        default: "bg-white text-slate-900 shadow-sm border-slate-200 hover:shadow-md",
        medical: "bg-white text-medical-text-primary shadow-medical-sm border-medical-border-base hover:shadow-medical-md hover:border-medical-border-strong",
        medicalGlass: "bg-medical-glass-card border-medical-glass-border backdrop-blur-sm text-medical-text-primary shadow-medical-sm hover:shadow-medical-md",
        medicalPremium: "bg-medical-glass-premium border-medical-glass-border backdrop-blur-md text-medical-text-primary shadow-medical-lg hover:shadow-medical-xl hover:bg-medical-glass-hero",
        medicalHero: "bg-medical-glass-hero border-medical-glass-border backdrop-blur-lg text-medical-text-primary shadow-medical-xl hover:shadow-medical-2xl",
        medicalCritical: "bg-medical-color-critical-50 border-medical-color-critical-200 text-medical-text-primary shadow-medical-critical hover:shadow-medical-lg",
        medicalPatient: "bg-medical-bg-elevated text-medical-text-primary shadow-medical-sm border-medical-border-base hover:shadow-medical-md hover:border-medical-color-primary-200",
        medicalData: "bg-medical-bg-muted text-medical-text-primary shadow-medical-xs border-medical-border-base hover:shadow-medical-sm",
        medicalDashboard: "bg-white text-medical-text-primary shadow-medical-md border-medical-border-base hover:shadow-medical-lg hover:border-medical-color-primary-100",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        medical: "p-6",
        medicalLarge: "p-8",
        medicalSmall: "p-4",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface CardProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof cardVariants> {}

function Card({ className, variant, size, ...props }: CardProps) {
  return (
    <div
      data-slot="card"
      className={cn(cardVariants({ variant, size, className }))}
      {...props}
    />
  );
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",
        className,
      )}
      {...props}
    />
  );
}

function CardTitle({ className, children, ...props }: React.ComponentProps<"div">) {
  return (
    <h4
      data-slot="card-title"
      className={cn("leading-none", className)}
      {...props}
    >
      {children}
    </h4>
  );
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <p
      data-slot="card-description"
      className={cn("text-muted-foreground", className)}
      {...props}
    />
  );
}

function CardAction({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className,
      )}
      {...props}
    />
  );
}

function CardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-content"
      className={cn("px-6 [&:last-child]:pb-6", className)}
      {...props}
    />
  );
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-footer"
      className={cn("flex items-center px-6 pb-6 [.border-t]:pt-6", className)}
      {...props}
    />
  );
}

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
  cardVariants,
};
