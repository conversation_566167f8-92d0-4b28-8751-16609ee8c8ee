import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Search, 
  Plus,
  Activity,
  Users,
  Calendar,
  TrendingUp,
  Stethoscope,
  Heart,
  Brain,
  Zap,
  ChevronRight,
  Bell,
  Star,
  Clock,
  BarChart3
} from 'lucide-react';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { HeaderActions } from '../HeaderActions';
import { HeroGlassCard, PremiumGlassCard, ElevatedGlassCard } from '../ui/glass-card';
import { StatusIndicator, MedicalStatusGrid } from '../ui/status-indicator';
import { PremiumButton, QuickActionButton, HeroButton } from '../ui/premium-button';
import wellwaveLogo from 'figma:asset/0861b8c218cc0038134a72897c76dab4ae1a5c19.png';

interface MinimalDashboardProps {
  darkMode?: boolean;
  onToggleTheme?: () => void;
  onModuleChange?: (module: string) => void;
  onOpenSettings?: () => void;
}



const PREMIUM_STATS = [
  {
    id: 'patients_today',
    label: 'Pacientes Hoje',
    value: 24,
    change: '+12%',
    trend: 'up' as const,
    icon: Users,
    color: 'blue'
  },
  {
    id: 'consultations',
    label: 'Consultas',
    value: 18,
    change: '+8%',
    trend: 'up' as const,
    icon: Stethoscope,
    color: 'emerald'
  },
  {
    id: 'efficiency',
    label: 'Eficiência',
    value: '94%',
    change: '+2%',
    trend: 'up' as const,
    icon: TrendingUp,
    color: 'violet'
  },
  {
    id: 'waiting',
    label: 'Aguardando',
    value: 6,
    change: '-15%',
    trend: 'down' as const,
    icon: Clock,
    color: 'amber'
  }
];

const MEDICAL_STATUS_DATA = [
  { type: 'stable' as const, label: 'Estáveis', count: 18, trend: 'stable' as const },
  { type: 'monitoring' as const, label: 'Monitorando', count: 4, trend: 'up' as const },
  { type: 'urgent' as const, label: 'Urgente', count: 2, trend: 'down' as const },
  { type: 'critical' as const, label: 'Crítico', count: 0, trend: 'stable' as const }
];

const RECENT_ACTIVITIES = [
  { 
    type: 'anamnese', 
    title: 'Nova anamnese criada', 
    subtitle: 'Paciente adulto, sexo feminino', 
    time: '2 min atrás',
    status: 'normal' as const,
    priority: 'normal'
  },
  { 
    type: 'prescricao', 
    title: 'Prescrição emitida', 
    subtitle: 'Dipirona 500mg, Ibuprofeno 600mg', 
    time: '5 min atrás',
    status: 'stable' as const,
    priority: 'normal'
  },
  { 
    type: 'chat', 
    title: 'Consulta IA finalizada', 
    subtitle: 'Diagnóstico diferencial para cefaleia', 
    time: '8 min atrás',
    status: 'active' as const,
    priority: 'high'
  },
  { 
    type: 'kanban', 
    title: 'Paciente transferido', 
    subtitle: 'Movido para "Em atendimento"', 
    time: '12 min atrás',
    status: 'monitoring' as const,
    priority: 'normal'
  },
  { 
    type: 'urgent', 
    title: 'Atendimento urgente', 
    subtitle: 'Paciente com dor torácica', 
    time: '15 min atrás',
    status: 'urgent' as const,
    priority: 'urgent'
  }
];

const PREMIUM_QUICK_ACTIONS = [
  { 
    id: 'anamnese', 
    label: 'Nova Anamnese', 
    description: 'Avaliação médica completa',
    icon: <Stethoscope className="w-5 h-5" />,
    shortcut: '⌘ N'
  },
  { 
    id: 'chat', 
    label: 'Assistente IA', 
    description: 'Consulta inteligente',
    icon: <Brain className="w-5 h-5" />,
    shortcut: '⌘ I'
  },
  { 
    id: 'prescricao', 
    label: 'Prescrição', 
    description: 'Receituário digital',
    icon: <Heart className="w-5 h-5" />,
    shortcut: '⌘ P'
  },
  { 
    id: 'emergency', 
    label: 'Emergência', 
    description: 'Atendimento urgente',
    icon: <Zap className="w-5 h-5" />,
    shortcut: '⌘ E'
  }
];

export const MinimalDashboard: React.FC<MinimalDashboardProps> = ({
  darkMode = false,
  onToggleTheme = () => {},
  onModuleChange,
  onOpenSettings
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-svh min-h-svh-fix bg-gradient-to-br from-medical-bg-primary via-medical-color-primary-50/30 to-medical-color-primary-100/20 dark:from-slate-900 dark:via-slate-800/50 dark:to-slate-900">
      {/* Premium Hero Header */}
      <motion.div 
        className="relative overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        {/* Background gradient effects - Enhanced with medical colors */}
        <div className="absolute inset-0 bg-gradient-to-r from-medical-color-primary/5 via-medical-color-secondary/5 to-medical-color-primary-800/5" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_0%,var(--medical-color-primary-400)/10,transparent_50%)]" />
        
        <HeroGlassCard className="rounded-none border-0 shadow-none mb-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
            {/* Top Header Bar */}
            <div className="flex items-center justify-between mb-8">
              {/* Logo & Branding */}
              <motion.div 
                className="flex items-center gap-4"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <motion.div
                  className="p-3 rounded-xl bg-gradient-to-br from-medical-color-primary to-medical-color-primary-700 shadow-medical-lg shadow-medical-color-primary/25"
                  whileHover={{ scale: 1.05, rotate: 3 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <img 
                    src={wellwaveLogo}
                    alt="WellWave"
                    className="h-6 w-6 sm:h-8 sm:w-8 object-contain brightness-0 invert"
                  />
                </motion.div>
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                    WellWave Premium
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Sistema Médico Inteligente
                  </p>
                </div>
              </motion.div>

              {/* Action Bar */}
              <motion.div 
                className="flex items-center gap-3"
                initial={{ x: 20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                <Badge variant="outline" className="hidden sm:flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  Online
                </Badge>
                <PremiumButton
                  variant="glass"
                  size="sm"
                  className="gap-2"
                >
                  <Bell className="w-4 h-4" />
                  <Badge className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
                    3
                  </Badge>
                </PremiumButton>
                <HeaderActions
                  darkMode={darkMode}
                  onToggleTheme={onToggleTheme}
                  onOpenSettings={onOpenSettings}
                />
              </motion.div>
            </div>

            {/* Search Bar Premium */}
            <motion.div 
              className="max-w-2xl mx-auto mb-8"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="Pesquisar pacientes, medicamentos, diagnósticos..."
                  className="w-full pl-12 pr-4 py-3 text-base h-12 bg-white/80 dark:bg-slate-800/80 border-white/20 dark:border-slate-700/50 rounded-xl backdrop-blur-sm focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 shadow-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </motion.div>

            {/* Premium Stats Grid */}
            <motion.div 
              className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {PREMIUM_STATS.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <motion.div
                    key={stat.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                  >
                    <ElevatedGlassCard className="p-4 text-center">
                      <div className={`inline-flex p-3 rounded-xl bg-${stat.color}-100 dark:bg-${stat.color}-900/20 mb-3`}>
                        <IconComponent className={`w-6 h-6 text-${stat.color}-600 dark:text-${stat.color}-400`} />
                      </div>
                      <div className={`text-2xl font-bold text-${stat.color}-600 dark:text-${stat.color}-400 mb-1`}>
                        {stat.value}
                      </div>
                      <div className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                        {stat.label}
                      </div>
                      <div className={`text-xs flex items-center justify-center gap-1 ${
                        stat.trend === 'up' ? 'text-emerald-600' : 
                        stat.trend === 'down' ? 'text-red-600' : 'text-slate-500'
                      }`}>
                        {stat.trend === 'up' ? '↗' : stat.trend === 'down' ? '↘' : '→'} {stat.change}
                      </div>
                    </ElevatedGlassCard>
                  </motion.div>
                );
              })}
            </motion.div>
          </div>
        </HeroGlassCard>
      </motion.div>

      {/* Main Content Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Left Column - Quick Actions & Status */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Premium Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <PremiumGlassCard>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                    <Zap className="w-5 h-5 text-blue-600" />
                    Ações Rápidas
                  </h2>
                  <HeroButton size="sm" onClick={() => onModuleChange?.('anamnese')}>
                    <Plus className="w-4 h-4" />
                    Novo
                  </HeroButton>
                </div>
                
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                  {PREMIUM_QUICK_ACTIONS.map((action, index) => (
                    <motion.div
                      key={action.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.8 + index * 0.1 }}
                    >
                      <QuickActionButton
                        icon={action.icon}
                        label={action.label}
                        description={action.description}
                        shortcut={action.shortcut}
                        onClick={() => onModuleChange?.(action.id)}
                        className="h-auto min-h-[100px]"
                      />
                    </motion.div>
                  ))}
                </div>
              </PremiumGlassCard>
            </motion.div>

            {/* Medical Status Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
            >
              <PremiumGlassCard>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                    <Activity className="w-5 h-5 text-green-600" />
                    Status dos Pacientes
                  </h2>
                  <PremiumButton variant="ghost" size="sm">
                    Ver Detalhes <ChevronRight className="w-4 h-4 ml-1" />
                  </PremiumButton>
                </div>
                
                <MedicalStatusGrid 
                  statuses={MEDICAL_STATUS_DATA}
                  className="grid-cols-2 lg:grid-cols-4"
                />
              </PremiumGlassCard>
            </motion.div>
          </div>

          {/* Right Column - Recent Activities */}
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.0 }}
            >
              <PremiumGlassCard>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                    <Clock className="w-5 h-5 text-violet-600" />
                    Atividades Recentes
                  </h2>
                  <Badge variant="outline" className="bg-violet-50 dark:bg-violet-900/20 border-violet-200 dark:border-violet-800">
                    {RECENT_ACTIVITIES.length} itens
                  </Badge>
                </div>
                
                <div className="space-y-4">
                  <AnimatePresence>
                    {RECENT_ACTIVITIES.map((activity, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ delay: 1.1 + index * 0.1 }}
                        className="flex items-start gap-3 p-3 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors cursor-pointer group"
                      >
                        <StatusIndicator
                          status={activity.status}
                          size="sm"
                          variant="minimal"
                          showLabel={false}
                          showPulse={activity.priority === 'urgent'}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
                              {activity.title}
                            </p>
                            {activity.priority === 'urgent' && (
                              <Badge className="bg-red-500 text-white text-xs">Urgente</Badge>
                            )}
                            {activity.priority === 'high' && (
                              <Badge className="bg-amber-500 text-white text-xs">Alta</Badge>
                            )}
                          </div>
                          <p className="text-xs text-slate-600 dark:text-slate-400 line-clamp-2 mb-2">
                            {activity.subtitle}
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-500">
                            {activity.time}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
                
                <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                  <PremiumButton variant="ghost" size="sm" className="w-full justify-center">
                    Ver Todas as Atividades
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </PremiumButton>
                </div>
              </PremiumGlassCard>
            </motion.div>

            {/* Performance Insight */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.2 }}
            >
              <PremiumGlassCard>
                <div className="text-center py-4">
                  <div className="inline-flex p-3 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 mb-4">
                    <Star className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-slate-900 dark:text-white mb-2">
                    Excelente Performance
                  </h3>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Você atendeu 94% dos pacientes no tempo ideal hoje.
                  </p>
                  <PremiumButton variant="gradient" size="sm" className="w-full">
                    <BarChart3 className="w-4 h-4" />
                    Ver Relatório Completo
                  </PremiumButton>
                </div>
              </PremiumGlassCard>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MinimalDashboard;
