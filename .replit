modules = ["nodejs-20", "web"]
[agent]
expertMode = true

[nix]
channel = "stable-25_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Medical Anamnesis App"

[[workflows.workflow]]
name = "Medical Anamnesis App"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd \"Sistema de Anamnese Médica\" && npm run dev"
waitForPort = 5000

[workflows.workflow.metadata]
outputType = "webview"

[deployment]
deploymentTarget = "autoscale"
run = ["bash", "-c", "cd 'Sistema de Anamnese Médica' && npm run build && npx serve build -p 5000"]
build = ["npm", "run", "build"]
