openapi: 3.0.3
info:
  title: Sistema de Anamnese Médica API
  description: API for medical anamnesis system
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: https://api.anamnese.med/v1
    description: Production server
  - url: http://localhost:3000/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  /auth/login:
    post:
      summary: Authenticate medical professional
      operationId: login
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /anamnesis:
    get:
      summary: List anamnesis records
      operationId: listAnamnesis
      tags:
        - Anamnesis
      parameters:
        - name: patient_id
          in: query
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          schema:
            type: string
            enum: [draft, in_progress, completed, archived]
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 25
            maximum: 100
      responses:
        '200':
          description: List of anamnesis records
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Anamnesis'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
    
    post:
      summary: Create new anamnesis
      operationId: createAnamnesis
      tags:
        - Anamnesis
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - patient_id
              properties:
                patient_id:
                  type: string
                  format: uuid
      responses:
        '201':
          description: Anamnesis created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Anamnesis'
        '400':
          $ref: '#/components/responses/BadRequest'

  /anamnesis/{id}:
    get:
      summary: Get anamnesis by ID
      operationId: getAnamnesis
      tags:
        - Anamnesis
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Anamnesis details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnamnesisDetail'
        '404':
          $ref: '#/components/responses/NotFound'
    
    put:
      summary: Update anamnesis
      operationId: updateAnamnesis
      tags:
        - Anamnesis
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [draft, in_progress, completed]
      responses:
        '200':
          description: Anamnesis updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Anamnesis'
        '409':
          $ref: '#/components/responses/Conflict'

  /anamnesis/{id}/sections:
    post:
      summary: Add section to anamnesis
      operationId: addSection
      tags:
        - Anamnesis Sections
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnamnesisSection'
      responses:
        '201':
          description: Section added
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnamnesisSection'

  /anamnesis/{id}/sections/{sectionId}:
    put:
      summary: Update anamnesis section
      operationId: updateSection
      tags:
        - Anamnesis Sections
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: sectionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: object
                is_completed:
                  type: boolean
      responses:
        '200':
          description: Section updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnamnesisSection'

  /anamnesis/{id}/export:
    post:
      summary: Export anamnesis
      operationId: exportAnamnesis
      tags:
        - Export
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - format
              properties:
                format:
                  type: string
                  enum: [PDF, CSV, JSON]
      responses:
        '200':
          description: Export generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  url:
                    type: string
                    format: uri
                  expires_at:
                    type: string
                    format: date-time

  /patients:
    get:
      summary: Search patients
      operationId: searchPatients
      tags:
        - Patients
      parameters:
        - name: q
          in: query
          description: Search query (name or medical record number)
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 25
      responses:
        '200':
          description: Patient search results
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Patient'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
    
    post:
      summary: Create new patient
      operationId: createPatient
      tags:
        - Patients
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatientInput'
      responses:
        '201':
          description: Patient created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'

  /patients/{id}:
    get:
      summary: Get patient by ID
      operationId: getPatient
      tags:
        - Patients
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Patient details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'
        '404':
          $ref: '#/components/responses/NotFound'
    
    put:
      summary: Update patient
      operationId: updatePatient
      tags:
        - Patients
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatientInput'
      responses:
        '200':
          description: Patient updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        user:
          $ref: '#/components/schemas/MedicalProfessional'

    MedicalProfessional:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        name:
          type: string
        license_number:
          type: string
        specialty:
          type: string
        is_active:
          type: boolean

    Patient:
      type: object
      properties:
        id:
          type: string
          format: uuid
        medical_record_number:
          type: string
        name:
          type: string
        birth_date:
          type: string
          format: date
        gender:
          type: string
          enum: [M, F, Other]
        phone:
          type: string
        email:
          type: string
          format: email
        blood_type:
          type: string
          enum: [A+, A-, B+, B-, AB+, AB-, O+, O-]
        allergies:
          type: array
          items:
            type: string
        chronic_conditions:
          type: array
          items:
            type: string

    PatientInput:
      type: object
      required:
        - medical_record_number
        - name
        - birth_date
        - gender
      properties:
        medical_record_number:
          type: string
          pattern: '^[A-Z0-9]{8,12}$'
        name:
          type: string
          minLength: 2
          maxLength: 100
        birth_date:
          type: string
          format: date
        gender:
          type: string
          enum: [M, F, Other]
        phone:
          type: string
        email:
          type: string
          format: email
        address:
          type: string
        blood_type:
          type: string
          enum: [A+, A-, B+, B-, AB+, AB-, O+, O-]
        allergies:
          type: array
          items:
            type: string
        chronic_conditions:
          type: array
          items:
            type: string

    Anamnesis:
      type: object
      properties:
        id:
          type: string
          format: uuid
        patient_id:
          type: string
          format: uuid
        professional_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [draft, in_progress, completed, archived]
        started_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
        last_saved_at:
          type: string
          format: date-time

    AnamnesisDetail:
      allOf:
        - $ref: '#/components/schemas/Anamnesis'
        - type: object
          properties:
            patient:
              $ref: '#/components/schemas/Patient'
            professional:
              $ref: '#/components/schemas/MedicalProfessional'
            sections:
              type: array
              items:
                $ref: '#/components/schemas/AnamnesisSection'

    AnamnesisSection:
      type: object
      required:
        - section_type
        - title
        - content
        - order_index
      properties:
        id:
          type: string
          format: uuid
        anamnesis_id:
          type: string
          format: uuid
        section_type:
          type: string
          enum: 
            - chief_complaint
            - present_illness
            - past_medical
            - medications
            - allergies
            - family_history
            - social_history
            - review_systems
            - physical_exam
            - assessment_plan
        title:
          type: string
        content:
          type: object
        order_index:
          type: integer
        is_completed:
          type: boolean
        completed_at:
          type: string
          format: date-time

    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        total_pages:
          type: integer

    Error:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    Conflict:
      description: Conflict (e.g., concurrent modification)
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'