# Data Model: Sistema de Anamnese Médica

**Feature**: Sistema de Anamnese Médica - Interface Principal  
**Date**: 2025-01-10  
**Version**: 1.0.0

## Entity Relationship Diagram

```mermaid
erDiagram
    MedicalProfessional ||--o{ Anamnesis : creates
    Patient ||--o{ Anamnesis : has
    Anamnesis ||--o{ AnamnesisSection : contains
    Anamnesis ||--o{ AnamnesisExport : generates
    
    MedicalProfessional {
        uuid id PK
        string email UK
        string name
        string license_number UK
        string specialty
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    Patient {
        uuid id PK
        string medical_record_number UK
        string name
        date birth_date
        string gender
        string phone
        string email
        text address
        string blood_type
        text allergies
        text chronic_conditions
        timestamp created_at
        timestamp updated_at
    }
    
    Anamnesis {
        uuid id PK
        uuid patient_id FK
        uuid professional_id FK
        string status
        timestamp started_at
        timestamp completed_at
        timestamp last_saved_at
        json metadata
        timestamp created_at
        timestamp updated_at
    }
    
    AnamnesisSection {
        uuid id PK
        uuid anamnesis_id FK
        string section_type
        string title
        json content
        integer order_index
        boolean is_completed
        timestamp completed_at
        timestamp created_at
        timestamp updated_at
    }
    
    AnamnesisExport {
        uuid id PK
        uuid anamnesis_id FK
        string format
        string file_url
        timestamp exported_at
        uuid exported_by FK
    }
```

## Entity Definitions

### MedicalProfessional
Represents healthcare providers who create and manage anamnesis records.

**Fields**:
- `id`: UUID, Primary Key
- `email`: String, Unique, Required
- `name`: String, Required
- `license_number`: String, Unique, Required
- `specialty`: String, Required
- `is_active`: Boolean, Default true
- `created_at`: Timestamp, Auto-generated
- `updated_at`: Timestamp, Auto-updated

**Validation Rules**:
- Email must be valid format
- License number must match pattern: /^[A-Z]{2}\d{6}$/
- Name length: 2-100 characters

### Patient
Represents individuals receiving medical care.

**Fields**:
- `id`: UUID, Primary Key
- `medical_record_number`: String, Unique, Required
- `name`: String, Required
- `birth_date`: Date, Required
- `gender`: String, Enum ['M', 'F', 'Other']
- `phone`: String, Optional
- `email`: String, Optional
- `address`: Text, Optional
- `blood_type`: String, Enum ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
- `allergies`: Text Array, Optional
- `chronic_conditions`: Text Array, Optional
- `created_at`: Timestamp, Auto-generated
- `updated_at`: Timestamp, Auto-updated

**Validation Rules**:
- Birth date must be in the past
- Medical record number: 8-12 characters
- Phone must be valid format
- Email must be valid format if provided

### Anamnesis
Represents a medical interview/consultation record.

**Fields**:
- `id`: UUID, Primary Key
- `patient_id`: UUID, Foreign Key to Patient, Required
- `professional_id`: UUID, Foreign Key to MedicalProfessional, Required
- `status`: String, Enum ['draft', 'in_progress', 'completed', 'archived']
- `started_at`: Timestamp, Required
- `completed_at`: Timestamp, Optional
- `last_saved_at`: Timestamp, Auto-updated
- `metadata`: JSON, Optional (version, browser, IP for audit)
- `created_at`: Timestamp, Auto-generated
- `updated_at`: Timestamp, Auto-updated

**Validation Rules**:
- Status transitions: draft → in_progress → completed → archived
- Cannot modify if status is 'archived'
- Completed_at required when status = 'completed'

**State Transitions**:
```
draft → in_progress (on first edit)
in_progress → completed (on finalization)
completed → archived (after 30 days or manual)
any → draft (on duplication)
```

### AnamnesisSection
Represents different categories of medical information within an anamnesis.

**Fields**:
- `id`: UUID, Primary Key
- `anamnesis_id`: UUID, Foreign Key to Anamnesis, Required
- `section_type`: String, Enum, Required
- `title`: String, Required
- `content`: JSON, Required
- `order_index`: Integer, Required
- `is_completed`: Boolean, Default false
- `completed_at`: Timestamp, Optional
- `created_at`: Timestamp, Auto-generated
- `updated_at`: Timestamp, Auto-updated

**Section Types**:
- `chief_complaint`: Main reason for visit
- `present_illness`: Current symptoms and timeline
- `past_medical`: Previous conditions and surgeries
- `medications`: Current medications
- `allergies`: Known allergies
- `family_history`: Hereditary conditions
- `social_history`: Lifestyle factors
- `review_systems`: Systematic symptom review
- `physical_exam`: Examination findings
- `assessment_plan`: Diagnosis and treatment

**Content Schema** (JSON):
```json
{
  "fields": [
    {
      "name": "string",
      "value": "string | number | boolean | array",
      "type": "text | number | boolean | select | multiselect",
      "required": "boolean"
    }
  ],
  "notes": "string",
  "attachments": ["url"]
}
```

**Validation Rules**:
- Section_type must be from allowed enum
- Order_index must be unique within anamnesis
- Content must match schema for section_type

### AnamnesisExport
Tracks exported versions of anamnesis records.

**Fields**:
- `id`: UUID, Primary Key
- `anamnesis_id`: UUID, Foreign Key to Anamnesis, Required
- `format`: String, Enum ['PDF', 'CSV', 'JSON'], Required
- `file_url`: String, Required
- `exported_at`: Timestamp, Required
- `exported_by`: UUID, Foreign Key to MedicalProfessional, Required

**Validation Rules**:
- File_url must be valid URL
- Format must be from allowed enum

## Indexes

### Performance Indexes
- `idx_anamnesis_patient_id`: On Anamnesis.patient_id
- `idx_anamnesis_professional_id`: On Anamnesis.professional_id
- `idx_anamnesis_status`: On Anamnesis.status
- `idx_anamnesis_section_anamnesis_id`: On AnamnesisSection.anamnesis_id
- `idx_patient_medical_record`: On Patient.medical_record_number

### Unique Constraints
- `uq_professional_email`: On MedicalProfessional.email
- `uq_professional_license`: On MedicalProfessional.license_number
- `uq_patient_medical_record`: On Patient.medical_record_number
- `uq_section_order`: On (anamnesis_id, order_index)

## Row Level Security (RLS) Policies

### MedicalProfessional
- SELECT: Authenticated users can view all active professionals
- INSERT: Only admins
- UPDATE: Self or admins
- DELETE: Only admins (soft delete via is_active)

### Patient
- SELECT: Authenticated professionals only
- INSERT: Authenticated professionals only
- UPDATE: Authenticated professionals only
- DELETE: Only admins (compliance requirement)

### Anamnesis
- SELECT: Creator or patient's assigned professionals
- INSERT: Authenticated professionals only
- UPDATE: Creator only, and only if status != 'archived'
- DELETE: Prohibited (compliance requirement)

### AnamnesisSection
- SELECT: Same as parent anamnesis
- INSERT: Creator of parent anamnesis
- UPDATE: Creator of parent anamnesis, if parent not archived
- DELETE: Prohibited (compliance requirement)

## Audit Considerations

### Tracked Operations
All CRUD operations on:
- Patient records
- Anamnesis records
- AnamnesisSection modifications

### Audit Fields
Every table includes:
- `created_at`: Record creation timestamp
- `updated_at`: Last modification timestamp
- `created_by`: User who created (via RLS context)
- `updated_by`: User who last modified (via RLS context)

## Data Retention

### Policies
- Patient records: Indefinite (legal requirement)
- Anamnesis records: 7 years minimum (HIPAA requirement)
- Exports: 1 year (can be regenerated)
- Audit logs: 3 years

### Archival Strategy
- Move completed anamnesis older than 1 year to cold storage
- Compress JSON content in archived records
- Maintain indexes for search capability

---
*Data model ready for implementation*