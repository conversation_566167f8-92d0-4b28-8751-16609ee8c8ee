# Quickstart Guide: Sistema de Anamnese Médica

## Prerequisites
- Node.js 18+ and npm
- Supabase account
- Git

## Setup Instructions

### 1. <PERSON><PERSON> and Install
```bash
git clone <repository-url>
cd "Sistema de Anamnese Médica"
npm install
```

### 2. Environment Configuration
```bash
cp .env.example .env
```

Edit `.env` with your Supabase credentials:
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Database Setup
Run the migrations in Supabase SQL editor:
```sql
-- Create tables (see data-model.md for complete schema)
-- Enable RLS policies
-- Set up initial data
```

### 4. Start Development Server
```bash
npm run dev
```

Access the application at `http://localhost:5173`

## Validation Scenarios

### Scenario 1: Medical Professional Login
1. Navigate to login page
2. Enter credentials:
   - Email: <EMAIL>
   - Password: SecurePass123!
3. Click "Login"
4. **Expected**: Redirected to dashboard with greeting message

### Scenario 2: Create New Anamnesis
1. From dashboard, click "Nova Anamnese"
2. Search for patient by name or medical record
3. Select patient from results
4. **Expected**: New anamnesis form opens with patient info pre-filled

### Scenario 3: Fill Anamnesis Form
1. In anamnesis form, complete "Chief Complaint" section:
   - Enter: "Patient reports chest pain for 2 days"
2. Move to "Present Illness" section:
   - Fill in symptom details
3. Click "Save" button
4. **Expected**: "Saved successfully" notification appears
5. **Expected**: Last saved timestamp updates

### Scenario 4: Search Existing Anamnesis
1. From dashboard, use search bar
2. Enter patient name: "João Silva"
3. Apply filter: Status = "In Progress"
4. **Expected**: List shows filtered anamnesis records
5. Click on a record
6. **Expected**: Opens anamnesis in edit mode

### Scenario 5: Export Anamnesis
1. Open completed anamnesis
2. Click "Export" button
3. Select format: "PDF"
4. Click "Generate"
5. **Expected**: PDF downloads with formatted anamnesis data

### Scenario 6: Concurrent Access Handling
1. Open same anamnesis in two browser tabs
2. Edit in Tab 1 and save
3. Try to edit in Tab 2
4. **Expected**: Conflict warning appears with options to refresh or overwrite

### Scenario 7: Offline Mode
1. Load anamnesis form
2. Disconnect internet
3. Continue filling form
4. Click save
5. **Expected**: "Saved locally" indicator appears
6. Reconnect internet
7. **Expected**: Auto-sync occurs with confirmation

### Scenario 8: Data Validation
1. Try to create patient without required fields
2. **Expected**: Validation errors highlight missing fields
3. Enter invalid email format
4. **Expected**: "Invalid email format" error appears
5. Enter birth date in future
6. **Expected**: "Birth date must be in the past" error

### Scenario 9: Auto-save Feature
1. Start filling anamnesis form
2. Wait 30 seconds without clicking save
3. **Expected**: Auto-save indicator appears
4. Refresh page
5. **Expected**: Form data persists

### Scenario 10: Accessibility Check
1. Navigate using only keyboard (Tab, Enter, Arrow keys)
2. **Expected**: All interactive elements are reachable
3. Enable screen reader
4. **Expected**: All form fields have proper labels announced

## Testing Commands

### Run All Tests
```bash
npm test
```

### Run Specific Test Suite
```bash
npm test -- anamnesis.test
```

### Test Coverage
```bash
npm run test:coverage
```

## Common Issues & Solutions

### Issue: Supabase connection failed
**Solution**: Verify `.env` credentials and Supabase project status

### Issue: Form not saving
**Solution**: Check browser console for errors, verify RLS policies

### Issue: Export not working
**Solution**: Ensure Supabase Storage is configured with proper CORS

## Performance Benchmarks

- Page load: < 2 seconds
- Form save: < 500ms
- Search results: < 1 second
- Export generation: < 3 seconds

## Security Checklist

- [ ] HTTPS enabled in production
- [ ] Session timeout after 15 minutes
- [ ] Audit logs recording all actions
- [ ] Data encrypted at rest
- [ ] HIPAA compliance verified

## Support

For issues or questions:
- Check documentation in `/docs`
- Review error logs in browser console
- Contact: <EMAIL>

---
*Last updated: 2025-01-10*