# Implementation Plan: Siste<PERSON> de <PERSON> Médica - Interface Principal

**Branch**: `005-sistema-de-anamnese` | **Date**: 2025-01-10 | **Spec**: [spec.md](./spec.md)
**Input**: Feature specification from `/Users/<USER>/specs/005-sistema-de-anamnese/spec.md`

## Execution Flow (/plan command scope)
```
1. Load feature spec from Input path
   → If not found: ERROR "No feature spec at {path}"
2. Fill Technical Context (scan for NEEDS CLARIFICATION)
   → Detect Project Type from context (web=frontend+backend, mobile=app+api)
   → Set Structure Decision based on project type
3. Evaluate Constitution Check section below
   → If violations exist: Document in Complexity Tracking
   → If no justification possible: ERROR "Simplify approach first"
   → Update Progress Tracking: Initial Constitution Check
4. Execute Phase 0 → research.md
   → If NEEDS CLARIFICATION remain: ERROR "Resolve unknowns"
5. Execute Phase 1 → contracts, data-model.md, quickstart.md, agent-specific template file (e.g., `CLAUDE.md` for Claude Code, `.github/copilot-instructions.md` for GitHub Copilot, or `GEMINI.md` for Gemini CLI).
6. Re-evaluate Constitution Check section
   → If new violations: Refactor design, return to Phase 1
   → Update Progress Tracking: Post-Design Constitution Check
7. Plan Phase 2 → Describe task generation approach (DO NOT create tasks.md)
8. STOP - Ready for /tasks command
```

**IMPORTANT**: The /plan command STOPS at step 7. Phases 2-4 are executed by other commands:
- Phase 2: /tasks command creates tasks.md
- Phase 3-4: Implementation execution (manual or via tools)

## Summary
Building a medical anamnesis interface that enables healthcare professionals to efficiently capture structured patient information through an intuitive dashboard with anamnesis forms, patient records management, and auxiliary tools.

## Technical Context
**Language/Version**: TypeScript 5.9 / React 18.3  
**Primary Dependencies**: React, Vite, Tailwind CSS, Radix UI, React Hook Form  
**Storage**: Supabase (PostgreSQL) for backend persistence  
**Testing**: Vitest with React Testing Library  
**Target Platform**: Web browsers (Chrome, Firefox, Safari, Edge)
**Project Type**: web - frontend React application with backend services  
**Performance Goals**: <200ms interface response time, smooth form interactions  
**Constraints**: HIPAA compliance for medical data, offline data persistence capability NEEDS CLARIFICATION  
**Scale/Scope**: Support 100+ concurrent medical professionals, manage 10k+ patient records

## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

**Simplicity**:
- Projects: 2 (frontend, tests) - within max 3 limit ✓
- Using framework directly? YES - React/Radix UI without wrappers ✓
- Single data model? YES - direct entity models ✓
- Avoiding patterns? YES - no unnecessary abstractions ✓

**Architecture**:
- EVERY feature as library? Planning modular components (anamnesis-lib, patient-lib) ✓
- Libraries listed: 
  - anamnesis-lib: Form handling and validation
  - patient-lib: Patient record management
  - export-lib: Data export functionality
- CLI per library: Will expose via npm scripts ✓
- Library docs: llms.txt format planned ✓

**Testing (NON-NEGOTIABLE)**:
- RED-GREEN-Refactor cycle enforced? YES - tests first ✓
- Git commits show tests before implementation? Will enforce ✓
- Order: Contract→Integration→E2E→Unit strictly followed? YES ✓
- Real dependencies used? YES - real Supabase instance ✓
- Integration tests for: new libraries, contract changes, shared schemas? YES ✓
- FORBIDDEN: Implementation before test, skipping RED phase - ACKNOWLEDGED ✓

**Observability**:
- Structured logging included? YES - console.log with context ✓
- Frontend logs → backend? Planning unified logging ✓
- Error context sufficient? Will include user/action/timestamp ✓

**Versioning**:
- Version number assigned? 1.0.0 for initial release ✓
- BUILD increments on every change? Will use semantic versioning ✓
- Breaking changes handled? Will maintain backwards compatibility ✓

## Project Structure

### Documentation (this feature)
```
specs/[###-feature]/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```

### Source Code (repository root)
```
# Option 1: Single project (DEFAULT)
src/
├── models/
├── services/
├── cli/
└── lib/

tests/
├── contract/
├── integration/
└── unit/

# Option 2: Web application (when "frontend" + "backend" detected)
backend/
├── src/
│   ├── models/
│   ├── services/
│   └── api/
└── tests/

frontend/
├── src/
│   ├── components/
│   ├── pages/
│   └── services/
└── tests/

# Option 3: Mobile + API (when "iOS/Android" detected)
api/
└── [same as backend above]

ios/ or android/
└── [platform-specific structure]
```

**Structure Decision**: Option 2 - Web application (frontend + backend structure)

## Phase 0: Outline & Research
1. **Extract unknowns from Technical Context** above:
   - For each NEEDS CLARIFICATION → research task
   - For each dependency → best practices task
   - For each integration → patterns task

2. **Generate and dispatch research agents**:
   ```
   For each unknown in Technical Context:
     Task: "Research {unknown} for {feature context}"
   For each technology choice:
     Task: "Find best practices for {tech} in {domain}"
   ```

3. **Consolidate findings** in `research.md` using format:
   - Decision: [what was chosen]
   - Rationale: [why chosen]
   - Alternatives considered: [what else evaluated]

**Output**: research.md with all NEEDS CLARIFICATION resolved

## Phase 1: Design & Contracts
*Prerequisites: research.md complete*

1. **Extract entities from feature spec** → `data-model.md`:
   - Entity name, fields, relationships
   - Validation rules from requirements
   - State transitions if applicable

2. **Generate API contracts** from functional requirements:
   - For each user action → endpoint
   - Use standard REST/GraphQL patterns
   - Output OpenAPI/GraphQL schema to `/contracts/`

3. **Generate contract tests** from contracts:
   - One test file per endpoint
   - Assert request/response schemas
   - Tests must fail (no implementation yet)

4. **Extract test scenarios** from user stories:
   - Each story → integration test scenario
   - Quickstart test = story validation steps

5. **Update agent file incrementally** (O(1) operation):
   - Run `/scripts/update-agent-context.sh [claude|gemini|copilot]` for your AI assistant
   - If exists: Add only NEW tech from current plan
   - Preserve manual additions between markers
   - Update recent changes (keep last 3)
   - Keep under 150 lines for token efficiency
   - Output to repository root

**Output**: data-model.md, /contracts/*, failing tests, quickstart.md, agent-specific file

## Phase 2: Task Planning Approach
*This section describes what the /tasks command will do - DO NOT execute during /plan*

**Task Generation Strategy**:
- Load `/templates/tasks-template.md` as base
- Generate tasks from Phase 1 design docs (contracts, data model, quickstart)
- Database setup tasks (tables, RLS policies)
- Contract test tasks for each endpoint [P]
- Entity model creation tasks [P]
- React component tasks for each UI section
- Integration test tasks from user stories
- Implementation tasks to make tests pass

**Ordering Strategy**:
1. Database and environment setup
2. Contract tests (RED phase) [P]
3. Backend models and services
4. Frontend components and forms
5. Integration tests
6. E2E validation scenarios

**Task Categories**:
- **Setup** (5 tasks): Database, environment, dependencies
- **Contract Tests** (10 tasks): One per API endpoint [P]
- **Models** (5 tasks): Patient, Anamnesis, Sections [P]
- **Services** (5 tasks): Auth, CRUD operations
- **UI Components** (10 tasks): Forms, dashboard, search
- **Integration** (8 tasks): User scenarios from quickstart
- **Validation** (5 tasks): E2E tests, performance checks

**Estimated Output**: 45-50 numbered, ordered tasks in tasks.md

**Parallel Execution Opportunities**:
- All contract tests can run in parallel [P]
- Model creation tasks are independent [P]
- UI components can be developed concurrently [P]

**IMPORTANT**: This phase is executed by the /tasks command, NOT by /plan

## Phase 3+: Future Implementation
*These phases are beyond the scope of the /plan command*

**Phase 3**: Task execution (/tasks command creates tasks.md)  
**Phase 4**: Implementation (execute tasks.md following constitutional principles)  
**Phase 5**: Validation (run tests, execute quickstart.md, performance validation)

## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
| [e.g., 4th project] | [current need] | [why 3 projects insufficient] |
| [e.g., Repository pattern] | [specific problem] | [why direct DB access insufficient] |


## Progress Tracking
*This checklist is updated during execution flow*

**Phase Status**:
- [x] Phase 0: Research complete (/plan command)
- [x] Phase 1: Design complete (/plan command)
- [x] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [x] Initial Constitution Check: PASS
- [x] Post-Design Constitution Check: PASS
- [x] All NEEDS CLARIFICATION resolved (except offline persistence details)
- [x] Complexity deviations documented (none required)

---
*Based on Constitution v2.1.1 - See `/memory/constitution.md`*