# Research & Decisions — Integrar Spec Kit

Date: 2025-09-10
Context: Adotar Spec‑Driven Development (SDD) no "Sistema de Anamnese Médica" aproveitando artefatos já existentes no repositório.

## Decisions

1) Use Specify CLI via uvx (sem instalação global)
- Decision: Executar `specify` com `uvx --from git+https://github.com/github/spec-kit.git`.
- Rationale: Evita setup global; mantém versão controlada e reproduzível.
- Alternatives: `pipx/uv tool install specify` (maior atrito/estado global).

2) Não re-inicializar projeto com `specify init`
- Decision: Não rodar `specify init` — templates/scripts já estão presentes no repo.
- Rationale: Evita sobrescrever arquivos existentes.
- Alternatives: Rodar com `--ignore-agent-tools` apenas se faltar algum artefato.

3) Local dos artefatos SDD
- Decision: Centralizar specs no diretório raiz `specs/` do repositório (fora da pasta do app).
- Rationale: Compatível com scripts existentes (calculam `REPO_ROOT/specs`), facilita integração com hooks.
- Alternatives: Manter specs dentro da pasta do app (diverge dos scripts atuais).

4) Convenção de nome/numeração
- Decision: Usar prefixo numérico de 3 dígitos + slug curto (ex.: `003-medical-reports-system`).
- Rationale: Ordenação cronológica e leitura em PRs.
- Alternatives: Data-based; não preferido.

5) Scripts auxiliares
- Decision: Manter `scripts/create-new-feature.sh` como caminho padrão (cria branch + pasta + spec.md) — executado a partir da pasta do app, porém cria `specs/` no root do repo.
- Rationale: Automatiza padrão; reduz erro humano.
- Alternatives: Criação manual (usar `templates/spec-template.md`).

6) Comando de verificação
- Decision: Adicionar script npm `specify:check` no app para conveniência.
- Rationale: Comando único para sanity-check local do SDD.
- Alternatives: Executar `uvx specify check` diretamente.

## Verified Environment
- `uv` instalado: OK
- `specify check` via `uvx`: OK ("Specify CLI is ready to use!")
- Templates presentes: `Sistema de Anamnese Médica/templates/*`
- Scripts presentes: `Sistema de Anamnese Médica/scripts/*.sh`

## Open Questions (NEEDS CLARIFICATION)
- Branch policy: Script cria branch automaticamente — padrão do time?
- Husky/pre-commit: Validar presença de `spec.md`/`plan.md` em PRs de novas features?
- PR template: Incluir seção “Spec/Plan/Tasks links” no template de PR do repo?
- Tarefas: Sempre gerar `tasks.md` antes de iniciar implementação?

## Next Actions
- Confirmar Branch policy e se haverá validações via Husky.
- Consolidar este documento e publicar `quickstart.md` para o time.
- Após o primeiro uso, revisar fluxo e ajustar checklists.

