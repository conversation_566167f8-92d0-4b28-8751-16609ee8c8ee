# Tasks — Integrar Spec Kit ao projeto

Order: TDD-friendly docs first → automation → guidance

1. Confirmar política de branches do time (padr<PERSON> `NNN-meu-titulo-curto`).
2. Consolidar decisões em `specs/001-integrar-spec-kit/research.md` (atualizar seção Open Questions conforme respostas).
3. Publicar `specs/001-integrar-spec-kit/quickstart.md` (validar comandos/path de templates e scripts).
4. Adicionar script de conveniência (`specify:check`) no `Sistema de Anamnese Médica/package.json` — j<PERSON> adicionado; validar execução local.
5. Configurar hook <PERSON><PERSON> `pre-commit` no root do repo para validar features:
   - Se branch combinar `^[0-9]{3}-`, executar `Sistema de Anamnese Médica/scripts/check-task-prerequisites.sh`.
   - Não bloquear commits em `main`/`develop`.
6. Garantir que os specs residem em `specs/` (raiz do repositório) e referenciam templates do app.
7. Atualizar `Sistema de Anamnese Médica/CLAUDE.md` → seção Recent Changes com itens desta integração e nota sobre localização de `specs/`.
8. Validar fluxo completo criando uma feature sandbox (ex.: `./scripts/create-new-feature.sh "Sandbox fluxo SDD"`) e conferindo checklist.
9. (Opcional) Adicionar `npm run specify:init` caso decidam re-bootstrap em repositórios novos (não recomendado aqui).

Completion Criteria
- [ ] research.md atualizado e aprovado pelo time
- [ ] quickstart.md validado por 1 dev (copy-paste funciona)
- [ ] pre-commit verifica `spec.md` e `plan.md` apenas em branches de feature
- [ ] `npm run specify:check` retorna OK
- [ ] CLAUDE.md atualizado com mudanças recentes

