#!/usr/bin/env markdown
# Implementation Plan: Integrar Spec Kit ao projeto

**Branch**: `[001-integrar-spec-kit]` | **Date**: 2025-09-10 | **Spec**: specs/001-integrar-spec-kit/spec.md  
**Input**: Feature specification from `/specs/001-integrar-spec-kit/spec.md`

## Execution Flow (/plan command scope)
```
1. Load feature spec from Input path
   → If not found: ERROR "No feature spec at {path}"
2. Fill Technical Context (scan for NEEDS CLARIFICATION)
   → Detect Project Type from context (web=frontend only)
   → Set Structure Decision accordingly
3. Evaluate Constitution Check section below
   → If violations exist: Document in Complexity Tracking
   → If no justification possible: ERROR "Simplify approach first"
   → Update Progress Tracking: Initial Constitution Check
4. Execute Phase 0 → research.md (outline unknowns + decisions)
5. Execute Phase 1 → data-model.md (N/A for this feature), quickstart.md (adoption guide), agent file update (CLAUDE.md)
6. Re-evaluate Constitution Check section
   → If new violations: Refactor design, return to Phase 1
7. Plan Phase 2 → Describe task generation approach (DO NOT create tasks.md)
8. STOP - Ready for /tasks command
```

**IMPORTANT**: The /plan command STOPS at step 7. Phase 2 is executed by the /tasks command.

## Summary
Integrar e operacionalizar o Spec‑Driven Development no "Sistema de Anamnese Médica", aproveitando os artefatos já presentes no repositório: `templates/`, `scripts/`, `.claude/commands`, `memory/`, `CLAUDE.md` e a pasta `spec-kit/`. O fluxo adotado será: especificar (`spec.md`) → planejar (`plan.md`) → detalhar tarefas (`tasks.md`) → implementar, usando o CLI `specify` via `uvx` quando necessário e os scripts locais para automações opcionais.

## Technical Context
**Language/Version**: TypeScript (TS) + React 18  
**Primary Dependencies**: Vite 7, Radix UI, Tailwind (util classes em `src/index.css`), lucide-react, react-hook-form, etc.  
**Storage/Backend**: Supabase (chaves via `VITE_*` em `import.meta.env`)  
**Testing**: Vitest 3 + React Testing Library (config: `vitest.config.ts`)  
**Target Platform**: Web (Vite dev server `:3000`)  
**Project Type**: Web (frontend único neste repo)  
**Performance Goals**: N/A para esta feature  
**Constraints**: Respeitar diretrizes do repositório (aliases `@`, sem libs desnecessárias, UI via `components/ui/`)  
**Scale/Scope**: Processo de engenharia (docs e automações) — sem alterações funcionais no app

## Constitution Check

**Simplicity**:
- Projects: 1 (frontend + testes) — OK
- Framework direto: sim (React/Vite) — OK
- Evitar over‑engineering: manter apenas docs/fluxos necessários — OK

**Architecture**:
- Biblioteca por feature: N/A (frontend app); foco é processo e documentação — OK
- CLI: uso de `uvx specify` (sem criar wrapper local) — OK
- Docs: `specs/*`, `CLAUDE.md` atualizados incrementalmente — OK

**Testing (NON-NEGOTIABLE)**:
- TDD aplica-se a features de código; aqui, apenas garantiremos scripts e docs — OK
- Vitest já configurado (nenhum teste requerido para integração do processo) — OK

**Observability**:
- Irrelevante para esta feature — OK

**Versioning**:
- Sem breaking changes; sem versionamento adicional — OK

## Project Structure

### Documentation (esta feature)
```
specs/001-integrar-spec-kit/
├── plan.md        # Este arquivo (/plan)
├── spec.md        # Já criado
├── quickstart.md  # Foco: como adotar o fluxo no time (Phase 1)
└── research.md    # Decisões: uvx, scripts, branches (Phase 0)
```

### Source Code (repository)
```
Sistema de Anamnese Médica/
├── src/
├── scripts/
├── templates/
├── memory/
└── vitest.config.ts

specs/                 # Armazenamento central dos specs (raiz do repo)
```

**Structure Decision**: Single project (frontend único) — nenhuma mudança estrutural.

## Phase 0: Outline & Research
1. Levantar desconhecidos relevantes ao time:
   - Política de branches: usar `scripts/create-new-feature.sh` (cria branch) ou manter criação manual?  
     [NEEDS CLARIFICATION]
   - Padronização de nomes para features: `NNN-titulo-curto` (já seguido pelos scripts)?  
     [NEEDS CLARIFICATION]
   - Uso oficial do `specify`: via `uvx` (sem instalar globalmente) — adotado.  
   - Integração com Husky/pre-commit para validar presença de `spec.md`/`plan.md`?  
     [NEEDS CLARIFICATION]

2. Decisões iniciais (propostas):
   - Manter `spec-kit` como vendorado na pasta do projeto; usar `uvx` para o CLI.
   - Centralizar specs no diretório raiz `specs/` do repositório.
   - Atualizar `CLAUDE.md` via `scripts/update-agent-context.sh` após novas features/plans.

3. Output: Criar `research.md` com decisões e pendências (seguindo formato do template) — a ser produzido após validações do time.

## Phase 1: Design & Contracts
Para esta feature (processo), não há entidades/contratos de API. Em vez disso, produzir:
1. `quickstart.md` (adaptação do guia do README do spec-kit ao nosso projeto):
   - Como verificar requisitos: `uvx --from git+https://github.com/github/spec-kit.git specify check`
   - Como criar nova feature: script local vs. manual
   - Como gerar `plan.md` e `tasks.md`
   - Como atualizar `CLAUDE.md`
2. Atualização incremental do `CLAUDE.md` (já presente) com notas curtas de “Recent Changes”.

Reavaliar Constitution Check (mantendo simplicidade e evitando automações desnecessárias).

## Phase 2: Task Planning Approach
- Base: `/templates/tasks-template.md`
- Tarefas previstas (exemplos; geradas no `/tasks`):
  - Documentar decisões em `research.md` [P]
  - Escrever `quickstart.md` com comandos reais do projeto [P]
  - Opcional: adicionar script `specify:check` no `package.json` do app
  - Opcional: padronizar criação de branches via script e documentar politica
  - Atualizar `CLAUDE.md` com mudanças

Ordering Strategy:
- Docs primeiro (research → quickstart)  
- Automação depois (scripts/npm)  
- Atualização de `CLAUDE.md` por último

Estimated Output: 8–12 tarefas focadas em docs e adoção do fluxo.

---

## Next Steps (ação)
- Validar pendências marcadas como [NEEDS CLARIFICATION] com o time.
- Autorizar criação de `research.md` e `quickstart.md` nesta feature.
- Confirmar se desejam script `"specify:check"` em `package.json` para conveniência.

