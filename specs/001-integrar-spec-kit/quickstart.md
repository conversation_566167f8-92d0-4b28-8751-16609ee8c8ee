# Quickstart — Spec‑Driven Development no Sistema de Anamnese Médica

Este guia descreve como usar o fluxo de Spec‑Driven Development (SDD) neste projeto.

## Pré‑requisitos
- Node.js (ver `.nvmrc` na raiz do workspace)
- Git configurado
- `uv` instalado (verificado)

## 1) Checar ferramentas do Spec Kit
```
cd "Sistema de Anamnese Médica"
uvx --from git+https://github.com/github/spec-kit.git specify check
```
Saída esperada: “Specify CLI is ready to use!”.

## 2) Criar uma nova feature (spec)
Opção A — Script (cria branch automaticamente):
```
cd "Sistema de Anamnese Médica"
./scripts/create-new-feature.sh "Adicionar exportação PDF dos relatórios"
```
Resultado: cria branch `NNN-adicionar-exportacao-pdf-dos-relatorios` e o diretório `specs/NNN-adicionar-exportacao-pdf-dos-relatorios/` no raiz do repositório, com `spec.md` a partir do template.

Opção B — Manual (sem criar branch):
```
# Executar a partir da RAIZ do repositório
FEATURE="001-minha-feature"
mkdir -p "specs/$FEATURE"
cp "Sistema de Anamnese Médica/templates/spec-template.md" "specs/$FEATURE/spec.md"
```

Preencha `spec.md` focando em “o que” e “por quê” (sem detalhes de implementação). Use os marcadores [NEEDS CLARIFICATION] para dúvidas.

## 3) Gerar o plano técnico (plan)
Crie `plan.md` a partir do template e detalhe abordagem técnica e fases:
```
cp "Sistema de Anamnese Médica/templates/plan-template.md" "specs/$FEATURE/plan.md"
```
No fluxo automatizado, o agente preencherá `plan.md`. Garanta que o plano trate pendências marcadas no spec.

## 4) Quebrar em tarefas (tasks)
Após o plano, gere `tasks.md` com base no template:
```
cp "Sistema de Anamnese Médica/templates/tasks-template.md" "specs/$FEATURE/tasks.md"
```
Ordem sugerida: TDD (testes antes da implementação), modelos → serviços → UI.

## 5) Atualizar o contexto do agente
Mantém o arquivo `CLAUDE.md` conciso e atualizado:
```
cd "Sistema de Anamnese Médica"
./scripts/update-agent-context.sh claude
```

## 6) Executar e testar o app
```
cd "Sistema de Anamnese Médica"
npm run dev      # Vite em http://localhost:3000
npm run test     # Vitest
```

## 7) Conveniências
- Verificação rápida do Spec Kit (na pasta do app):
```
cd "Sistema de Anamnese Médica"
npm run specify:check
```

## Referências úteis
- Documentos de comando: `.claude/commands/specify.md`, `.claude/commands/plan.md`, `.claude/commands/tasks.md`
- Templates: `Sistema de Anamnese Médica/templates/spec-template.md`, `Sistema de Anamnese Médica/templates/plan-template.md`, `Sistema de Anamnese Médica/templates/tasks-template.md`
- Scripts: `Sistema de Anamnese Médica/scripts/create-new-feature.sh`, `Sistema de Anamnese Médica/scripts/update-agent-context.sh`

