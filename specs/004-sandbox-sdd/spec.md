#!/usr/bin/env markdown
# Feature Specification: Sandbox SDD Flow

**Feature Branch**: `[004-sandbox-sdd]`  
**Created**: 2025-09-10  
**Status**: Draft  
**Input**: User description: "Validar o fluxo fim‑a‑fim do Spec‑Driven Development no repositório."

## User Scenarios & Testing

### Primary User Story
Como desenvolvedor, quero validar o fluxo SDD (spec → plan → tasks) em uma feature sandbox para assegurar que hooks e comandos funcionem corretamente.

### Acceptance Scenarios
1. Dado que estou na branch `004-sandbox-sdd`, quando faço commit, então o hook `pre-commit` executa validações e permite o commit se `spec.md` e `plan.md` existirem.
2. Dado um spec simples, quando gero `plan.md` e `tasks.md`, então os documentos são revisáveis e seguem os templates do projeto.

### Edge Cases
- Commit em feature branch sem `plan.md` → hook deve falhar e orientar a criar o arquivo.
- Diretórios de spec fora de `specs/BRANCH_NAME/` → script não deve encontrar os arquivos.

## Requirements
- **FR-001**: O spec DEVE existir em `specs/004-sandbox-sdd/spec.md`.
- **FR-002**: O plano DEVE existir em `specs/004-sandbox-sdd/plan.md`.
- **FR-003**: O hook `pre-commit` DEVE validar a presença de `plan.md` na feature branch.

## Review & Acceptance Checklist
- [ ] Sem detalhes de implementação além do fluxo de validação
- [ ] Cenários de aceitação claros
- [ ] Itens de verificação do hook contemplados

