openapi: 3.0.3
info:
  title: Medical UI Design System Contracts
  description: Component and design system contracts for medical interface enhancement
  version: 2.0.0
  contact:
    name: Medical UI Team
    email: <EMAIL>

servers:
  - url: http://localhost:6006
    description: Storybook development server
  - url: https://ui-components.medical.system
    description: Component documentation server

paths:
  /components/Button:
    get:
      summary: Get Button component variants
      operationId: getButtonVariants
      tags:
        - Components
      parameters:
        - name: variant
          in: query
          schema:
            type: string
            enum: [primary, secondary, medical, emergency, outline, ghost]
        - name: size
          in: query
          schema:
            type: string
            enum: [sm, md, lg, xl]
        - name: medical_context
          in: query
          schema:
            type: string
            enum: [routine, urgent, critical, emergency]
      responses:
        '200':
          description: Button component specifications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentVariant'

  /components/Card:
    get:
      summary: Get Card component variants
      operationId: getCardVariants
      tags:
        - Components
      parameters:
        - name: variant
          in: query
          schema:
            type: string
            enum: [default, elevated, premium, hero, glass]
        - name: medical_context
          in: query
          schema:
            type: string
            enum: [patient-info, medical-data, dashboard, emergency]
      responses:
        '200':
          description: Card component specifications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentVariant'

  /components/Input:
    get:
      summary: Get Input component variants
      operationId: getInputVariants
      tags:
        - Components
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [text, email, password, number, tel, medical-id]
        - name: validation_level
          in: query
          schema:
            type: string
            enum: [standard, medical-strict, hipaa-compliant]
      responses:
        '200':
          description: Input component specifications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentVariant'

  /design-tokens:
    get:
      summary: Get design system tokens
      operationId: getDesignTokens
      tags:
        - Design System
      parameters:
        - name: category
          in: query
          schema:
            type: string
            enum: [color, typography, spacing, shadow, radius, animation]
        - name: theme
          in: query
          schema:
            type: string
            enum: [light, dark, high-contrast]
      responses:
        '200':
          description: Design tokens collection
          content:
            application/json:
              schema:
                type: object
                properties:
                  tokens:
                    type: array
                    items:
                      $ref: '#/components/schemas/DesignToken'

  /layout-patterns:
    get:
      summary: Get layout patterns
      operationId: getLayoutPatterns
      tags:
        - Layout Patterns
      parameters:
        - name: workflow
          in: query
          schema:
            type: string
            enum: [dashboard, patient-entry, review, emergency, list, detail]
        - name: breakpoint
          in: query
          schema:
            type: string
            enum: [mobile, tablet, desktop, large-display]
      responses:
        '200':
          description: Layout pattern specifications
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LayoutPattern'

  /accessibility/audit:
    post:
      summary: Run accessibility audit
      operationId: runAccessibilityAudit
      tags:
        - Accessibility
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                component_id:
                  type: string
                wcag_level:
                  type: string
                  enum: [A, AA, AAA]
                medical_context:
                  type: string
                  enum: [routine, urgent, critical, emergency]
      responses:
        '200':
          description: Accessibility audit results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessibilityAuditResult'

  /performance/metrics:
    get:
      summary: Get component performance metrics
      operationId: getPerformanceMetrics
      tags:
        - Performance
      parameters:
        - name: component_type
          in: query
          schema:
            type: string
        - name: metric_type
          in: query
          schema:
            type: string
            enum: [render-time, animation-fps, memory-usage, bundle-size]
      responses:
        '200':
          description: Performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceMetrics'

components:
  schemas:
    DesignToken:
      type: object
      required:
        - id
        - category
        - name
        - value
        - css_custom_property
      properties:
        id:
          type: string
          example: "medical-color-primary-600"
        category:
          type: string
          enum: [color, typography, spacing, shadow, radius, animation]
        name:
          type: string
          example: "Primary Blue 600"
        value:
          type: string
          example: "#0A6EBD"
        semantic_meaning:
          type: string
          example: "Primary action color for medical interface"
        wcag_compliant:
          type: boolean
          example: true
        css_custom_property:
          type: string
          example: "--medical-color-primary-600"
        contrast_ratios:
          type: object
          properties:
            white:
              type: number
              example: 4.52
            black:
              type: number
              example: 4.64

    ComponentVariant:
      type: object
      required:
        - id
        - base_component
        - variant_name
        - medical_context
        - styling_properties
        - accessibility_attributes
      properties:
        id:
          type: string
          example: "button-medical-emergency"
        base_component:
          type: string
          example: "Button"
        variant_name:
          type: string
          example: "emergency"
        medical_context:
          type: string
          enum: [routine, urgent, critical, emergency]
        styling_properties:
          type: object
          example:
            background: "var(--medical-color-critical)"
            color: "white"
            border: "none"
            borderRadius: "var(--medical-radius-base)"
        accessibility_attributes:
          type: object
          example:
            role: "button"
            aria-label: "Emergency action button"
            tabindex: "0"
        interaction_states:
          type: array
          items:
            $ref: '#/components/schemas/InteractionState'
        is_premium:
          type: boolean
          default: true

    InteractionState:
      type: object
      required:
        - state_name
        - visual_properties
      properties:
        state_name:
          type: string
          enum: [default, hover, focus, active, disabled, loading]
        visual_properties:
          type: object
          example:
            transform: "translateY(-2px)"
            boxShadow: "var(--medical-shadow-elevated)"
        animation_properties:
          type: object
          example:
            duration: "var(--medical-duration-fast)"
            easing: "var(--medical-ease-out)"
        requires_focus_management:
          type: boolean
          example: true

    LayoutPattern:
      type: object
      required:
        - id
        - pattern_name
        - medical_workflow
        - grid_configuration
      properties:
        id:
          type: string
          example: "dashboard-medical-overview"
        pattern_name:
          type: string
          example: "Medical Dashboard"
        medical_workflow:
          type: string
          enum: [dashboard, patient-entry, review, emergency, list, detail]
        grid_configuration:
          type: object
          properties:
            columns:
              type: integer
              example: 12
            rows:
              type: string
              example: "auto"
            gap:
              type: string
              example: "var(--medical-spacing-grid)"
        responsive_behavior:
          type: object
          properties:
            mobile:
              $ref: '#/components/schemas/ResponsiveConfig'
            tablet:
              $ref: '#/components/schemas/ResponsiveConfig'
            desktop:
              $ref: '#/components/schemas/ResponsiveConfig'
        accessibility_requirements:
          type: string
          example: "Keyboard navigation, screen reader support, focus management"

    ResponsiveConfig:
      type: object
      properties:
        grid_columns:
          type: string
          example: "1fr"
        component_sizes:
          type: object
        spacing_adjustments:
          type: object

    AccessibilityAuditResult:
      type: object
      properties:
        component_id:
          type: string
        wcag_compliance:
          type: object
          properties:
            level_a:
              type: boolean
            level_aa:
              type: boolean
            level_aaa:
              type: boolean
        violations:
          type: array
          items:
            type: object
            properties:
              criterion:
                type: string
              severity:
                type: string
                enum: [minor, moderate, serious, critical]
              description:
                type: string
              remediation:
                type: string
        medical_specific_checks:
          type: object
          properties:
            touch_target_size:
              type: boolean
            contrast_ratios:
              type: boolean
            keyboard_navigation:
              type: boolean
            screen_reader_support:
              type: boolean

    PerformanceMetrics:
      type: object
      properties:
        component_type:
          type: string
        metrics:
          type: object
          properties:
            render_time_ms:
              type: number
              example: 16.7
            animation_fps:
              type: number
              example: 60
            memory_usage_kb:
              type: number
              example: 128
            bundle_size_kb:
              type: number
              example: 45
        benchmarks:
          type: object
          properties:
            target_render_time:
              type: number
              example: 16.7
            target_fps:
              type: number
              example: 60
            passes_performance_budget:
              type: boolean
              example: true

  responses:
    BadRequest:
      description: Invalid request parameters
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
              message:
                type: string

    NotFound:
      description: Component or resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
              message:
                type: string

    ValidationError:
      description: Design system validation failed
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
              validation_failures:
                type: array
                items:
                  type: string