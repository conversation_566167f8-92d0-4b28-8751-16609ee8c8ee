# Quickstart Guide: Sistema de Relatórios Médicos Inteligentes

**Feature**: Sistema de Relatórios Médicos Inteligentes  
**Date**: 2025-01-10  
**Status**: Complete

## Overview

Este guia demonstra como usar o sistema de relatórios médicos inteligentes para gerar relatórios automáticos baseados na anamnese, com sugestões de diagnósticos e recomendações de tratamento.

## Prerequisites

- Sistema de anamnese preenchido
- Paciente cadastrado no sistema
- Template de relatório disponível

## Basic Usage

### 1. Gerar Relatório Básico

```typescript
import { useReportGenerator } from '@/hooks/useReportGenerator';

function AnamneseComponent() {
  const { generateReport, isLoading } = useReportGenerator();
  
  const handleGenerateReport = async () => {
    try {
      const report = await generateReport({
        anamneseId: 'anamnese-123',
        templateId: 'clinica-geral',
        includeSuggestions: true
      });
      
      console.log('Relatório gerado:', report);
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
    }
  };
  
  return (
    <button 
      onClick={handleGenerateReport}
      disabled={isLoading}
    >
      {isLoading ? 'Gerando...' : 'Gerar Relatório'}
    </button>
  );
}
```

### 2. Visualizar Relatório

```typescript
import { ReportViewer } from '@/components/medical-reports/ReportViewer';

function ReportPage() {
  const reportId = 'report-123';
  
  return (
    <ReportViewer 
      reportId={reportId}
      onEdit={(report) => console.log('Editando:', report)}
      onExport={(format) => console.log('Exportando:', format)}
    />
  );
}
```

### 3. Exportar Relatório

```typescript
import { useReportExporter } from '@/hooks/useReportExporter';

function ExportButton({ reportId }: { reportId: string }) {
  const { exportReport } = useReportExporter();
  
  const handleExport = async (format: 'pdf' | 'docx') => {
    try {
      const file = await exportReport(reportId, format);
      
      // Download automático
      const url = URL.createObjectURL(file);
      const a = document.createElement('a');
      a.href = url;
      a.download = `relatorio-${reportId}.${format}`;
      a.click();
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar:', error);
    }
  };
  
  return (
    <div>
      <button onClick={() => handleExport('pdf')}>
        Exportar PDF
      </button>
      <button onClick={() => handleExport('docx')}>
        Exportar DOCX
      </button>
    </div>
  );
}
```

## Advanced Usage

### 1. Personalizar Template

```typescript
import { useReportTemplates } from '@/hooks/useReportTemplates';

function TemplateCustomizer() {
  const { templates, updateTemplate } = useReportTemplates();
  
  const handleCustomizeTemplate = async (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    
    if (template) {
      // Adicionar campo personalizado
      const updatedTemplate = {
        ...template,
        estrutura: {
          ...template.estrutura,
          secoes: [
            ...template.estrutura.secoes,
            {
              id: 'observacoes-personalizadas',
              nome: 'Observações Personalizadas',
              obrigatoria: false,
              ordem: 10,
              campos: [
                {
                  id: 'obs-medico',
                  nome: 'Observações do Médico',
                  tipo: 'textarea',
                  obrigatorio: false
                }
              ]
            }
          ]
        }
      };
      
      await updateTemplate(templateId, updatedTemplate);
    }
  };
  
  return (
    <div>
      {templates.map(template => (
        <div key={template.id}>
          <h3>{template.nome}</h3>
          <button onClick={() => handleCustomizeTemplate(template.id)}>
            Personalizar
          </button>
        </div>
      ))}
    </div>
  );
}
```

### 2. Integrar com Anamnese Existente

```typescript
import { useMedical } from '@/components/MedicalContext';
import { useReportGenerator } from '@/hooks/useReportGenerator';

function AnamneseRapida() {
  const { anamneseData, patientData } = useMedical();
  const { generateReport } = useReportGenerator();
  
  const handleCompleteAnamnese = async () => {
    // Validar dados da anamnese
    if (!anamneseData.sintomas || anamneseData.sintomas.length === 0) {
      alert('Por favor, preencha pelo menos um sintoma');
      return;
    }
    
    // Gerar relatório automaticamente
    try {
      const report = await generateReport({
        anamneseId: anamneseData.id,
        templateId: 'clinica-geral',
        includeSuggestions: true,
        autoSave: true
      });
      
      // Mostrar relatório gerado
      setActiveModule('report');
      setReportId(report.id);
      
      Toast.success('Relatório gerado com sucesso!');
    } catch (error) {
      Toast.error('Erro ao gerar relatório');
    }
  };
  
  return (
    <div>
      {/* Formulário de anamnese existente */}
      
      <button onClick={handleCompleteAnamnese}>
        Finalizar Anamnese e Gerar Relatório
      </button>
    </div>
  );
}
```

### 3. Sistema de Sugestões Inteligentes

```typescript
import { useDiagnosisSuggestions } from '@/hooks/useDiagnosisSuggestions';

function DiagnosisSuggestions({ symptoms }: { symptoms: string[] }) {
  const { suggestions, isLoading } = useDiagnosisSuggestions(symptoms);
  
  if (isLoading) {
    return <div>Analisando sintomas...</div>;
  }
  
  return (
    <div>
      <h3>Sugestões de Diagnóstico</h3>
      {suggestions.map(suggestion => (
        <div key={suggestion.id} className="suggestion-card">
          <h4>{suggestion.descricao}</h4>
          <p>Código: {suggestion.codigo}</p>
          <p>Confiança: {suggestion.confianca}%</p>
          
          <div>
            <h5>Evidências:</h5>
            <ul>
              {suggestion.evidencia.map(ev => (
                <li key={ev}>{ev}</li>
              ))}
            </ul>
          </div>
          
          <div>
            <h5>Exames Recomendados:</h5>
            <ul>
              {suggestion.examesRecomendados.map(exame => (
                <li key={exame}>{exame}</li>
              ))}
            </ul>
          </div>
        </div>
      ))}
    </div>
  );
}
```

## Testing Scenarios

### 1. Teste de Geração de Relatório

```typescript
// Teste de integração
describe('Report Generation', () => {
  it('should generate report with valid anamnesis data', async () => {
    const anamneseData = {
      id: 'test-anamnese',
      sintomas: ['dor de cabeça', 'febre'],
      historico: 'Paciente com histórico de hipertensão',
      exameFisico: 'PA: 140/90, FC: 80 bpm'
    };
    
    const report = await generateReport({
      anamneseId: anamneseData.id,
      templateId: 'clinica-geral'
    });
    
    expect(report).toBeDefined();
    expect(report.sugestoesDiagnostico).toHaveLength(2);
    expect(report.recomendacoesTratamento).toHaveLength(1);
  });
});
```

### 2. Teste de Exportação

```typescript
describe('Report Export', () => {
  it('should export PDF successfully', async () => {
    const reportId = 'test-report';
    const file = await exportReport(reportId, 'pdf');
    
    expect(file).toBeInstanceOf(Blob);
    expect(file.type).toBe('application/pdf');
  });
  
  it('should export DOCX successfully', async () => {
    const reportId = 'test-report';
    const file = await exportReport(reportId, 'docx');
    
    expect(file).toBeInstanceOf(Blob);
    expect(file.type).toBe('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
  });
});
```

### 3. Teste de Validação

```typescript
describe('Report Validation', () => {
  it('should validate required fields', () => {
    const invalidReport = {
      pacienteId: '',
      anamneseId: 'test',
      templateId: 'test'
    };
    
    expect(() => validateReport(invalidReport)).toThrow('Paciente ID é obrigatório');
  });
  
  it('should validate diagnosis suggestions', () => {
    const suggestion = {
      codigo: 'INVALID',
      descricao: 'Test',
      confianca: 150 // Invalid confidence
    };
    
    expect(() => validateDiagnosisSuggestion(suggestion)).toThrow('Confiança deve estar entre 0 e 100');
  });
});
```

## Error Handling

### 1. Tratamento de Erros de Geração

```typescript
const handleGenerateReport = async () => {
  try {
    const report = await generateReport(data);
    // Sucesso
  } catch (error) {
    if (error.code === 'INSUFFICIENT_DATA') {
      Toast.warning('Dados insuficientes para gerar relatório');
    } else if (error.code === 'TEMPLATE_NOT_FOUND') {
      Toast.error('Template não encontrado');
    } else {
      Toast.error('Erro interno do sistema');
    }
  }
};
```

### 2. Fallback para Modo Offline

```typescript
const generateReportOffline = async (data: AnamneseData) => {
  // Gerar relatório básico sem sugestões
  const basicReport = {
    id: generateId(),
    conteudo: generateBasicContent(data),
    sugestoesDiagnostico: [],
    recomendacoesTratamento: [],
    status: 'DRAFT'
  };
  
  return basicReport;
};
```

## Performance Tips

1. **Cache de Templates**: Templates são carregados uma vez e mantidos em cache
2. **Lazy Loading**: Componentes de relatório são carregados sob demanda
3. **Debounce**: Geração de sugestões usa debounce para evitar chamadas excessivas
4. **Web Workers**: Processamento pesado em background thread

## Troubleshooting

### Problemas Comuns

1. **Relatório não gera**: Verificar se anamnese está completa
2. **Exportação falha**: Verificar permissões do browser
3. **Sugestões não aparecem**: Verificar conexão com API médica
4. **Template não carrega**: Verificar se template está ativo

### Logs e Debug

```typescript
// Habilitar logs detalhados
localStorage.setItem('debug-reports', 'true');

// Ver logs no console
console.log('Report generation logs:', reportLogs);
```
