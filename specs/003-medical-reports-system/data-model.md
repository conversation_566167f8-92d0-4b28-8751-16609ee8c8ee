# Data Model: Sistema de Relatórios Médicos Inteligentes

**Feature**: Sistema de Relatórios Médicos Inteligentes  
**Date**: 2025-01-10  
**Status**: Complete

## Core Entities

### RelatorioMedico
Representa um relatório médico gerado automaticamente baseado na anamnese.

```typescript
interface RelatorioMedico {
  id: string;                    // UUID único do relatório
  pacienteId: string;            // ID do paciente
  anamneseId: string;            // ID da anamnese base
  templateId: string;            // ID do template utilizado
  conteudo: RelatorioConteudo;   // Conteúdo estruturado do relatório
  sugestoesDiagnostico: SugestaoDiagnostico[];  // Sugestões geradas
  recomendacoesTratamento: RecomendacaoTratamento[];  // Recomendações
  status: RelatorioStatus;       // Status do relatório
  dataCriacao: Date;            // Data de criação
  dataModificacao: Date;        // Data da última modificação
  versao: number;               // Versão do relatório
  observacoesMedico?: string;   // Observações adicionais do médico
}
```

**Validation Rules**:
- `id`: Obrigatório, formato UUID
- `pacienteId`: Obrigatório, referência válida
- `anamneseId`: Obrigatório, referência válida
- `templateId`: Obrigatório, template válido
- `dataCriacao`: Obrigatório, data válida
- `dataModificacao`: Obrigatório, >= dataCriacao

**State Transitions**:
```
DRAFT → GENERATED → REVIEWED → FINALIZED
  ↓         ↓          ↓
CANCELLED  CANCELLED  CANCELLED
```

### TemplateRelatorio
Representa um template para geração de relatórios por especialidade.

```typescript
interface TemplateRelatorio {
  id: string;                   // ID único do template
  nome: string;                 // Nome do template
  especialidade: string;        // Especialidade médica
  descricao: string;            // Descrição do template
  estrutura: TemplateEstrutura; // Estrutura do template
  camposObrigatorios: string[]; // Campos obrigatórios
  camposOpcionais: string[];    // Campos opcionais
  ativo: boolean;               // Template ativo
  dataCriacao: Date;           // Data de criação
  versao: number;              // Versão do template
}
```

**Validation Rules**:
- `id`: Obrigatório, formato UUID
- `nome`: Obrigatório, 3-100 caracteres
- `especialidade`: Obrigatório, especialidade válida
- `estrutura`: Obrigatório, estrutura válida
- `camposObrigatorios`: Array não vazio se template ativo

### SugestaoDiagnostico
Representa uma sugestão de diagnóstico baseada em sintomas e evidências.

```typescript
interface SugestaoDiagnostico {
  id: string;                   // ID único da sugestão
  codigo: string;               // Código CID-10
  descricao: string;            // Descrição do diagnóstico
  confianca: number;            // Nível de confiança (0-100)
  evidencia: string[];          // Evidências que suportam o diagnóstico
  sintomasRelacionados: string[]; // Sintomas relacionados
  examesRecomendados: string[]; // Exames recomendados
  observacoes?: string;         // Observações adicionais
}
```

**Validation Rules**:
- `id`: Obrigatório, formato UUID
- `codigo`: Obrigatório, formato CID-10 válido
- `descricao`: Obrigatório, 10-500 caracteres
- `confianca`: Obrigatório, 0-100
- `evidencia`: Array não vazio

### RecomendacaoTratamento
Representa uma recomendação de tratamento baseada em evidências médicas.

```typescript
interface RecomendacaoTratamento {
  id: string;                   // ID único da recomendação
  tipo: TipoTratamento;         // Tipo de tratamento
  descricao: string;            // Descrição da recomendação
  medicamentos: MedicamentoRecomendado[]; // Medicamentos recomendados
  exames: ExameRecomendado[];   // Exames recomendados
  observacoes: string;          // Observações importantes
  prioridade: Prioridade;       // Prioridade da recomendação
  duracao?: string;             // Duração do tratamento
  contraindicacoes?: string[];  // Contraindicações
}
```

**Validation Rules**:
- `id`: Obrigatório, formato UUID
- `tipo`: Obrigatório, tipo válido
- `descricao`: Obrigatório, 10-1000 caracteres
- `prioridade`: Obrigatório, prioridade válida

### RelatorioConteudo
Representa o conteúdo estruturado do relatório.

```typescript
interface RelatorioConteudo {
  cabecalho: CabecalhoRelatorio;     // Cabeçalho do relatório
  anamnese: ResumoAnamnese;         // Resumo da anamnese
  exameFisico?: ExameFisico;        // Exame físico
  diagnostico: DiagnosticoRelatorio; // Diagnóstico
  tratamento: TratamentoRelatorio;  // Tratamento
  observacoes?: string;             // Observações gerais
  assinatura: AssinaturaMedico;     // Assinatura do médico
}
```

## Supporting Types

### Enums

```typescript
enum RelatorioStatus {
  DRAFT = 'draft',
  GENERATED = 'generated',
  REVIEWED = 'reviewed',
  FINALIZED = 'finalized',
  CANCELLED = 'cancelled'
}

enum TipoTratamento {
  MEDICAMENTOSO = 'medicamentoso',
  CIRURGICO = 'cirurgico',
  FISIOTERAPIA = 'fisioterapia',
  PSICOLOGICO = 'psicologico',
  PREVENTIVO = 'preventivo',
  PALIATIVO = 'palliativo'
}

enum Prioridade {
  ALTA = 'alta',
  MEDIA = 'media',
  BAIXA = 'baixa'
}
```

### Complex Types

```typescript
interface TemplateEstrutura {
  secoes: SecaoTemplate[];
  layout: LayoutTemplate;
  estilos: EstilosTemplate;
}

interface SecaoTemplate {
  id: string;
  nome: string;
  obrigatoria: boolean;
  ordem: number;
  campos: CampoTemplate[];
}

interface CampoTemplate {
  id: string;
  nome: string;
  tipo: TipoCampo;
  obrigatorio: boolean;
  validacao?: RegraValidacao;
}

interface MedicamentoRecomendado {
  nome: string;
  dosagem: string;
  frequencia: string;
  duracao: string;
  observacoes?: string;
}

interface ExameRecomendado {
  nome: string;
  tipo: string;
  urgencia: Prioridade;
  observacoes?: string;
}
```

## Relationships

### RelatorioMedico Relationships
- **belongs to** TemplateRelatorio (templateId)
- **belongs to** Paciente (pacienteId)
- **belongs to** Anamnese (anamneseId)
- **has many** SugestaoDiagnostico
- **has many** RecomendacaoTratamento

### TemplateRelatorio Relationships
- **has many** RelatorioMedico
- **belongs to** EspecialidadeMedica

### SugestaoDiagnostico Relationships
- **belongs to** RelatorioMedico
- **belongs to** Diagnostico (codigo CID-10)

### RecomendacaoTratamento Relationships
- **belongs to** RelatorioMedico
- **has many** MedicamentoRecomendado
- **has many** ExameRecomendado

## Data Validation Rules

### Business Rules
1. **Relatório deve ter pelo menos uma sugestão de diagnóstico**
2. **Template deve estar ativo para ser usado**
3. **Confiança da sugestão deve ser > 50% para ser exibida**
4. **Recomendações de alta prioridade devem ser destacadas**
5. **Relatório finalizado não pode ser modificado**

### Technical Rules
1. **IDs devem ser UUIDs válidos**
2. **Datas devem estar em formato ISO 8601**
3. **Códigos CID-10 devem seguir padrão brasileiro**
4. **Campos obrigatórios não podem ser nulos/vazios**
5. **Versões devem incrementar sequencialmente**

## Performance Considerations

### Indexing Strategy
- **Primary Keys**: Todos os IDs
- **Foreign Keys**: pacienteId, anamneseId, templateId
- **Search Fields**: nome do template, especialidade
- **Date Fields**: dataCriacao, dataModificacao

### Caching Strategy
- **Templates**: Cache em memória (raramente mudam)
- **Relatórios**: Cache por 1 hora após geração
- **Sugestões**: Cache por 24 horas (base de conhecimento)

### Data Retention
- **Relatórios**: Manter por 5 anos (compliance médico)
- **Templates**: Manter histórico de versões
- **Logs**: Manter por 1 ano para auditoria
