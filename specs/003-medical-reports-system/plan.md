# Implementation Plan: Sistema de Relatórios Médicos Inteligentes

**Branch**: `003-medical-reports-system` | **Date**: 2025-01-10 | **Spec**: specs/003-medical-reports-system/spec.md
**Input**: Feature specification from `/specs/003-medical-reports-system/spec.md`

## Execution Flow (/plan command scope)
```
1. Load feature spec from Input path
   → If not found: ERROR "No feature spec at {path}"
2. Fill Technical Context (scan for NEEDS CLARIFICATION)
   → Detect Project Type from context (web=frontend+backend, mobile=app+api)
   → Set Structure Decision based on project type
3. Evaluate Constitution Check section below
   → If violations exist: Document in Complexity Tracking
   → If no justification possible: ERROR "Simplify approach first"
   → Update Progress Tracking: Initial Constitution Check
4. Execute Phase 0 → research.md
   → If NEEDS CLARIFICATION remain: ERROR "Resolve unknowns"
5. Execute Phase 1 → contracts, data-model.md, quickstart.md, agent-specific template file
6. Re-evaluate Constitution Check section
   → If new violations: Refactor design, return to Phase 1
   → Update Progress Tracking: Post-Design Constitution Check
7. Plan Phase 2 → Describe task generation approach (DO NOT create tasks.md)
8. STOP - Ready for /tasks command
```

## Summary
Implementar um sistema de relatórios médicos inteligentes que gere automaticamente relatórios baseados na anamnese, com sugestões de diagnósticos e recomendações de tratamento, integrado ao sistema React existente.

## Technical Context
**Language/Version**: TypeScript 5.0, React 18, Node.js 18  
**Primary Dependencies**: React, TypeScript, Vite, Tailwind CSS, Framer Motion  
**Storage**: LocalStorage (temporário), Supabase (futuro)  
**Testing**: Vitest, React Testing Library  
**Target Platform**: Web browser (Chrome, Firefox, Safari)  
**Project Type**: web (frontend integrado ao sistema existente)  
**Performance Goals**: Geração de relatório < 2 segundos, renderização < 500ms  
**Constraints**: Deve integrar com sistema existente sem quebrar funcionalidades  
**Scale/Scope**: 1-10 usuários simultâneos, 100+ relatórios por dia  

## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

**Simplicity**:
- Projects: 1 (sistema web integrado)
- Using framework directly? Sim (React direto, sem wrappers)
- Single data model? Sim (modelo de relatório unificado)
- Avoiding patterns? Sim (sem Repository/UoW desnecessários)

**Architecture**:
- EVERY feature as library? Sim (módulo de relatórios como biblioteca)
- Libraries listed: medical-reports (geração de relatórios médicos)
- CLI per library: N/A (interface web)
- Library docs: llms.txt format planned? Sim

**Testing (NON-NEGOTIABLE)**:
- RED-GREEN-Refactor cycle enforced? Sim
- Git commits show tests before implementation? Sim
- Order: Contract→Integration→E2E→Unit strictly followed? Sim
- Real dependencies used? Sim (dados reais de anamnese)
- Integration tests for: geração de relatórios, exportação, validação
- FORBIDDEN: Implementation before test, skipping RED phase

**Observability**:
- Structured logging included? Sim
- Frontend logs → backend? N/A (frontend only)
- Error context sufficient? Sim

**Versioning**:
- Version number assigned? 1.0.0
- BUILD increments on every change? Sim
- Breaking changes handled? Sim (compatibilidade com sistema existente)

## Project Structure

### Documentation (this feature)
```
specs/003-medical-reports-system/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```

### Source Code (repository root)
```
src/
├── components/
│   ├── medical-reports/     # Componentes de relatórios
│   │   ├── ReportGenerator.tsx
│   │   ├── ReportViewer.tsx
│   │   ├── ReportTemplates.tsx
│   │   └── ReportExporter.tsx
│   └── [existing components]
├── services/
│   ├── report-generation.service.ts
│   ├── diagnosis-suggestions.service.ts
│   └── treatment-recommendations.service.ts
├── types/
│   └── medical-reports.types.ts
└── utils/
    └── report-formatters.ts

tests/
├── contract/
│   └── medical-reports.test.ts
├── integration/
│   └── report-generation.test.ts
└── unit/
    └── report-services.test.ts
```

**Structure Decision**: Option 1 (Single project) - integrado ao sistema React existente

## Phase 0: Outline & Research

### Research Tasks
1. **Investigar bibliotecas de geração de PDF/DOCX em React**
   - Task: "Research PDF/DOCX generation libraries for React/TypeScript"
   - Foco: jsPDF, html2pdf, docx, react-pdf

2. **Analisar padrões de sugestões de diagnóstico**
   - Task: "Research medical diagnosis suggestion patterns and algorithms"
   - Foco: Base de conhecimento médico, algoritmos de matching

3. **Estudar templates de relatórios médicos**
   - Task: "Research medical report templates and standards"
   - Foco: Padrões brasileiros, estruturas de relatórios médicos

4. **Investigar integração com sistema de anamnese existente**
   - Task: "Research integration patterns with existing anamnesis system"
   - Foco: MedicalContext, AnamneseRapida component

### Output: research.md com decisões técnicas

## Phase 1: Design & Contracts

### Data Model (data-model.md)
- **RelatorioMedico**: id, pacienteId, anamneseId, conteudo, sugestoesDiagnostico, recomendacoesTratamento, template, dataCriacao, dataModificacao
- **TemplateRelatorio**: id, nome, especialidade, estrutura, camposObrigatorios
- **SugestaoDiagnostico**: id, codigo, descricao, confianca, evidencia
- **RecomendacaoTratamento**: id, tipo, descricao, medicamentos, exames, observacoes

### API Contracts (/contracts/)
- POST /api/reports/generate - Gerar relatório
- GET /api/reports/{id} - Obter relatório
- PUT /api/reports/{id} - Atualizar relatório
- POST /api/reports/{id}/export - Exportar relatório
- GET /api/templates - Listar templates

### Contract Tests
- Testes para cada endpoint com schemas de request/response
- Validação de dados de entrada e saída
- Testes de erro e edge cases

### Quickstart Guide
- Como gerar um relatório básico
- Como personalizar templates
- Como exportar em diferentes formatos
- Exemplos de integração

## Phase 2: Task Planning Approach

**Task Generation Strategy**:
- Carregar `/templates/tasks-template.md` como base
- Gerar tarefas a partir dos documentos de design (contracts, data model, quickstart)
- Cada contract → contract test task [P]
- Cada entidade → model creation task [P]
- Cada user story → integration test task
- Tarefas de implementação para fazer os testes passarem

**Ordering Strategy**:
- Ordem TDD: Testes antes da implementação
- Ordem de dependência: Models → Services → UI
- Marcar [P] para execução paralela (arquivos independentes)

**Estimated Output**: 20-25 tarefas numeradas e ordenadas em tasks.md

## Complexity Tracking
*Nenhuma violação da constituição detectada*

## Progress Tracking

**Phase Status**:
- [x] Phase 0: Research complete (/plan command)
- [x] Phase 1: Design complete (/plan command)
- [ ] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [x] Initial Constitution Check: PASS
- [x] Post-Design Constitution Check: PASS
- [x] All NEEDS CLARIFICATION resolved
- [x] Complexity deviations documented

---
*Based on Constitution v1.0.0 - See `/memory/constitution.md`*
