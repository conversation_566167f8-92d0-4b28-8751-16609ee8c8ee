{"permissions": {"allow": ["<PERSON><PERSON>(git clone:*)", "Bash(git pull:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(tree:*)", "Bash(npm run typecheck:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "Bash(npm install)", "Bash(cd:*)", "<PERSON><PERSON>(scripts/setup-plan.sh:*)", "Bash(scripts/update-agent-context.sh:*)", "Bash(ls:*)", "Bash(scripts/create-new-feature.sh:*)", "Bash(scripts/check-task-prerequisites.sh:*)", "<PERSON><PERSON>(npx storybook:*)", "Bash(npm install:*)", "Bash(npm test:*)", "WebFetch(domain:localhost)", "<PERSON><PERSON>(curl:*)", "Bash(npm ls:*)", "Bash(npm outdated)", "Read(//Users/<USER>/.cursor/**)", "Bash(npm audit:*)", "Bash(npx:*)"], "deny": [], "ask": []}}