import { render } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Input } from '../../src/components/ui/input';

// TDD: Estes testes devem FALHAR inicialmente pois os componentes aprimorados ainda não existem

describe('Input - Visual Regression Tests', () => {
  describe('Medical Input Types', () => {
    it('should render medical-id input with specialized styling', () => {
      const { container } = render(
        <Input 
          type="medical-id" 
          placeholder="Enter patient ID"
          medicalContext="patient-identification"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera styling para ID médico (deve falhar)
      expect(input).toHaveClass('border-medical-primary');
      expect(input).toHaveClass('bg-medical-input-bg');
      expect(input).toHaveClass('font-medical-mono'); // Monospace para IDs
      expect(input).toHaveAttribute('pattern', '[A-Z]{2}[0-9]{6}'); // Padrão médico
    });

    it('should render patient data input with medical validation', () => {
      const { container } = render(
        <Input 
          type="text" 
          medicalField="patient-name"
          validationLevel="medical-strict"
          required
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera validação médica rigorosa (deve falhar)
      expect(input).toHaveClass('border-medical-required');
      expect(input).toHaveAttribute('aria-required', 'true');
      expect(input).toHaveAttribute('data-medical-field', 'patient-name');
      expect(input).toHaveAttribute('data-validation-level', 'medical-strict');
    });

    it('should render HIPAA-compliant input with security styling', () => {
      const { container } = render(
        <Input 
          type="password"
          validationLevel="hipaa-compliant"
          medicalSensitive={true}
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera compliance HIPAA (deve falhar)
      expect(input).toHaveClass('border-medical-secure');
      expect(input).toHaveClass('bg-medical-secure-input');
      expect(input).toHaveAttribute('autocomplete', 'off');
      expect(input).toHaveAttribute('data-hipaa-compliant', 'true');
    });

    it('should render medication dosage input with medical units', () => {
      const { container } = render(
        <Input 
          type="number"
          medicalUnit="mg"
          medicalField="dosage"
          min={0}
          step={0.1}
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera input de dosagem médica (deve falhar)
      expect(input).toHaveClass('text-right'); // Números alinhados à direita
      expect(input).toHaveClass('font-medical-numeric');
      expect(input).toHaveAttribute('data-medical-unit', 'mg');
      expect(input).toHaveAttribute('inputmode', 'decimal');
    });
  });

  describe('Medical Validation States', () => {
    it('should show error state for medical validation failures', () => {
      const { container } = render(
        <Input 
          validationState="error"
          medicalError="Invalid patient ID format"
          aria-describedby="error-message"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera estado de erro médico (deve falhar)
      expect(input).toHaveClass('border-medical-error');
      expect(input).toHaveClass('bg-medical-error-subtle');
      expect(input).toHaveClass('focus:ring-medical-error');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    it('should show success state for validated medical data', () => {
      const { container } = render(
        <Input 
          validationState="success"
          medicalValidated={true}
          value="P001234"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera estado de sucesso médico (deve falhar)
      expect(input).toHaveClass('border-medical-success');
      expect(input).toHaveClass('bg-medical-success-subtle');
      expect(input).toHaveClass('focus:ring-medical-success');
      expect(input).toHaveAttribute('data-medical-validated', 'true');
    });

    it('should show warning state for medical data requiring attention', () => {
      const { container } = render(
        <Input 
          validationState="warning"
          medicalWarning="Unusual dosage - please confirm"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera estado de aviso médico (deve falhar)
      expect(input).toHaveClass('border-medical-warning');
      expect(input).toHaveClass('bg-medical-warning-subtle');
      expect(input).toHaveClass('focus:ring-medical-warning');
      expect(input).toHaveAttribute('aria-describedby', expect.stringContaining('warning'));
    });

    it('should show loading state during medical data validation', () => {
      const { container } = render(
        <Input 
          validationState="loading"
          medicalValidating={true}
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera estado de loading médico (deve falhar)
      expect(input).toHaveClass('border-medical-loading');
      expect(input).toHaveClass('animate-medical-pulse');
      expect(input).toHaveAttribute('aria-busy', 'true');
      expect(input).toHaveAttribute('readonly'); // Readonly durante validação
    });
  });

  describe('Medical Accessibility Features', () => {
    it('should support medical terminology for screen readers', () => {
      const { container } = render(
        <Input 
          medicalField="blood-pressure"
          aria-label="Systolic blood pressure in mmHg"
          medicalTerminology="systolic-bp"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera suporte a terminologia médica (deve falhar)
      expect(input).toHaveAttribute('aria-label', 'Systolic blood pressure in mmHg');
      expect(input).toHaveAttribute('data-medical-terminology', 'systolic-bp');
      expect(input).toHaveAttribute('role', 'textbox');
    });

    it('should have proper focus indicators for medical workflows', () => {
      const { container } = render(
        <Input medicalWorkflow="patient-intake" className="focus" />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera indicadores de foco médicos (deve falhar)
      expect(input).toHaveClass('focus:outline-medical-focus');
      expect(input).toHaveClass('focus:ring-medical-focus');
      expect(input).toHaveClass('focus:ring-2'); // WCAG 2.2 compliance
      expect(input).toHaveClass('focus:border-medical-focus');
    });

    it('should support keyboard shortcuts for medical efficiency', () => {
      const { container } = render(
        <Input 
          medicalShortcut="Ctrl+P"
          medicalAction="paste-patient-id"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera atalhos de teclado médicos (deve falhar)
      expect(input).toHaveAttribute('data-medical-shortcut', 'Ctrl+P');
      expect(input).toHaveAttribute('aria-keyshortcuts', 'Ctrl+P');
      expect(input).toHaveAttribute('title', expect.stringContaining('Ctrl+P'));
    });

    it('should have large touch targets for medical device compatibility', () => {
      const { container } = render(
        <Input size="medical-touch" medicalDevice="tablet" />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera touch targets de 44px+ (deve falhar)
      const styles = window.getComputedStyle(input);
      expect(parseInt(styles.minHeight)).toBeGreaterThanOrEqual(44);
      expect(input).toHaveClass('p-medical-touch'); // Padding adequado para toque
    });
  });

  describe('Medical Form Integration', () => {
    it('should integrate with medical form validation', () => {
      const { container } = render(
        <Input 
          formField="patient-weight"
          medicalRequired={true}
          medicalUnit="kg"
          validationRules="medical-weight"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera integração com formulários médicos (deve falhar)
      expect(input).toHaveAttribute('data-form-field', 'patient-weight');
      expect(input).toHaveAttribute('data-medical-required', 'true');
      expect(input).toHaveAttribute('data-validation-rules', 'medical-weight');
    });

    it('should support medical auto-completion', () => {
      const { container } = render(
        <Input 
          medicalAutoComplete="medication-name"
          medicalDatabase="drug-database"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera auto-completion médico (deve falhar)
      expect(input).toHaveAttribute('autocomplete', 'off'); // Por segurança médica
      expect(input).toHaveAttribute('data-medical-autocomplete', 'medication-name');
      expect(input).toHaveAttribute('data-medical-database', 'drug-database');
    });

    it('should support medical data formatting', () => {
      const { container } = render(
        <Input 
          medicalFormat="phone"
          medicalMask="(###) ###-####"
          medicalField="emergency-contact"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera formatação de dados médicos (deve falhar)
      expect(input).toHaveAttribute('data-medical-format', 'phone');
      expect(input).toHaveAttribute('data-medical-mask', '(###) ###-####');
      expect(input).toHaveAttribute('inputmode', 'tel');
    });
  });

  describe('Medical Context Styling', () => {
    it('should apply emergency context styling for critical inputs', () => {
      const { container } = render(
        <Input 
          medicalContext="emergency"
          medicalPriority="critical"
          placeholder="Emergency contact"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera styling de emergência (deve falhar)
      expect(input).toHaveClass('border-medical-emergency');
      expect(input).toHaveClass('bg-medical-emergency-bg');
      expect(input).toHaveClass('placeholder-medical-emergency');
      expect(input).toHaveAttribute('data-medical-priority', 'critical');
    });

    it('should apply routine context styling for standard inputs', () => {
      const { container } = render(
        <Input 
          medicalContext="routine"
          medicalPriority="normal"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera styling rotineiro (deve falhar)
      expect(input).toHaveClass('border-medical-routine');
      expect(input).toHaveClass('bg-medical-routine-bg');
      expect(input).toHaveClass('text-medical-routine-text');
    });

    it('should maintain professional appearance across all contexts', () => {
      const { container } = render(
        <Input 
          medicalProfessional={true}
          medicalInterface="premium"
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera aparência profissional (deve falhar)
      expect(input).toHaveClass('font-medical-professional');
      expect(input).toHaveClass('border-medical-professional');
      expect(input).toHaveClass('shadow-medical-subtle');
      expect(input).toHaveAttribute('data-medical-interface', 'premium');
    });
  });

  describe('Performance and Animation', () => {
    it('should have smooth focus transitions', () => {
      const { container } = render(
        <Input medicalSmooth={true} />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera transições suaves (deve falhar)
      expect(input).toHaveClass('transition-medical-input');
      expect(input).toHaveClass('duration-medical-fast');
      expect(input).toHaveClass('ease-medical-out');
    });

    it('should maintain performance during medical data entry', () => {
      const { container } = render(
        <Input 
          medicalOptimized={true}
          medicalRealtimeValidation={true}
        />
      );
      
      const input = container.firstChild as HTMLElement;
      
      // Espera otimizações de performance (deve falhar)
      expect(input).toHaveClass('will-change-medical-auto');
      expect(input).toHaveAttribute('data-medical-optimized', 'true');
      expect(input).toHaveAttribute('data-realtime-validation', 'true');
    });
  });
});