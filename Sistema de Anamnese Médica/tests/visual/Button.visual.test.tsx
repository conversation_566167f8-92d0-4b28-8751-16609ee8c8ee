import { render } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Button } from '../../src/components/ui/button';

// TDD: Estes testes devem FALHAR inicialmente pois os componentes aprimorados ainda não existem

describe('Button - Visual Regression Tests', () => {
  describe('Medical Context Variants', () => {
    it('should render emergency variant with red critical styling', () => {
      const { container } = render(
        <Button variant="emergency" medicalContext="emergency">
          Emergency Action
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera styling de emergência médica (deve falhar)
      expect(button).toHaveClass('bg-medical-critical'); // CSS custom property esperada
      expect(button).toHaveClass('text-white');
      expect(button).toHaveClass('shadow-medical-emergency');
      expect(button).toHaveAttribute('aria-label', 'Emergency action button');
    });

    it('should render urgent variant with amber styling', () => {
      const { container } = render(
        <Button variant="urgent" medicalContext="urgent">
          Urgent Action
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera styling urgente (deve falhar)
      expect(button).toHaveClass('bg-medical-urgent');
      expect(button).toHaveClass('text-medical-urgent-foreground');
      expect(button).toHaveClass('shadow-medical-elevated');
    });

    it('should render critical variant with high contrast styling', () => {
      const { container } = render(
        <Button variant="critical" medicalContext="critical">
          Critical Action
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera styling crítico (deve falhar)
      expect(button).toHaveClass('bg-medical-critical');
      expect(button).toHaveClass('border-medical-critical-border');
      expect(button).toHaveClass('text-white');
    });

    it('should render routine variant with professional blue styling', () => {
      const { container } = render(
        <Button variant="routine" medicalContext="routine">
          Routine Action
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera styling rotina médica (deve falhar)
      expect(button).toHaveClass('bg-medical-primary');
      expect(button).toHaveClass('text-white');
      expect(button).toHaveClass('shadow-medical-base');
    });
  });

  describe('Premium Styling', () => {
    it('should apply glass morphism effects to premium buttons', () => {
      const { container } = render(
        <Button variant="premium" premium={true}>
          Premium Action
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera efeitos glass morphism (deve falhar)
      expect(button).toHaveClass('backdrop-blur-medical');
      expect(button).toHaveClass('bg-medical-glass');
      expect(button).toHaveClass('border-medical-glass-border');
    });

    it('should have proper touch targets for medical device compatibility', () => {
      const { container } = render(
        <Button size="medical">Touch Target</Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera touch targets mínimos de 44px (deve falhar)
      const styles = window.getComputedStyle(button);
      expect(parseInt(styles.minHeight)).toBeGreaterThanOrEqual(44);
      expect(parseInt(styles.minWidth)).toBeGreaterThanOrEqual(44);
    });
  });

  describe('Interaction States', () => {
    it('should have medical-appropriate hover effects', () => {
      const { container } = render(
        <Button variant="routine" className="hover">
          Hover Test
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera efeitos de hover médicos (deve falhar)
      expect(button).toHaveClass('hover:bg-medical-primary-hover');
      expect(button).toHaveClass('hover:transform-medical-lift');
      expect(button).toHaveClass('hover:shadow-medical-elevated');
    });

    it('should have proper focus indicators for accessibility', () => {
      const { container } = render(
        <Button variant="routine" className="focus">
          Focus Test
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera indicadores de foco WCAG 2.2 (deve falhar)
      expect(button).toHaveClass('focus:outline-medical-focus');
      expect(button).toHaveClass('focus:ring-medical-focus');
      expect(button).toHaveClass('focus:ring-2');
    });

    it('should have loading state with medical-appropriate animation', () => {
      const { container } = render(
        <Button variant="routine" loading={true}>
          Loading Test
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera estado de loading médico (deve falhar)
      expect(button).toHaveClass('animate-medical-pulse');
      expect(button).toHaveAttribute('aria-busy', 'true');
      expect(button).toBeDisabled();
    });
  });

  describe('Accessibility Compliance', () => {
    it('should meet WCAG 2.2 Level AA contrast requirements', () => {
      const { container } = render(
        <Button variant="routine">Contrast Test</Button>
      );
      
      const button = container.firstChild as HTMLElement;
      const styles = window.getComputedStyle(button);
      
      // Simula verificação de contraste (deve falhar)
      expect(button).toHaveAttribute('role', 'button');
      expect(button).toHaveAttribute('tabindex', '0');
    });

    it('should support keyboard navigation in medical workflows', () => {
      const { container } = render(
        <Button variant="emergency" shortcut="Ctrl+E">
          Emergency
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera suporte a atalhos médicos (deve falhar)
      expect(button).toHaveAttribute('data-shortcut', 'Ctrl+E');
      expect(button).toHaveAttribute('aria-keyshortcuts', 'Ctrl+E');
    });

    it('should announce medical context to screen readers', () => {
      const { container } = render(
        <Button variant="emergency" medicalContext="emergency">
          Emergency Action
        </Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera anúncios para screen readers (deve falhar)
      expect(button).toHaveAttribute('aria-describedby');
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('emergency'));
    });
  });

  describe('Performance Requirements', () => {
    it('should maintain 60fps during hover animations', async () => {
      const { container } = render(
        <Button variant="routine">Performance Test</Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Simula teste de performance de animação (deve falhar)
      expect(button).toHaveClass('transition-medical-smooth');
      expect(button).toHaveClass('duration-medical-fast');
      expect(button).toHaveClass('ease-medical-out');
    });

    it('should have optimized rendering for medical workstations', () => {
      const { container } = render(
        <Button variant="routine">Render Test</Button>
      );
      
      const button = container.firstChild as HTMLElement;
      
      // Espera otimizações de rendering (deve falhar)
      expect(button).toHaveClass('will-change-medical-transform');
      expect(button).toHaveClass('contain-layout-medical');
    });
  });
});