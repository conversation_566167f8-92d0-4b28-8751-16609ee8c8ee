import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach } from 'vitest';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Button } from '../../src/components/ui/button';

// Extend expect to include axe matchers
expect.extend(toHaveNoViolations);

// TDD: Estes testes devem FALHAR inicialmente pois os recursos de acessibilidade ainda não existem

describe('Button - Accessibility Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;
  
  beforeEach(() => {
    user = userEvent.setup();
  });

  describe('WCAG 2.2 Level AA Compliance', () => {
    it('should pass axe accessibility tests', async () => {
      const { container } = render(
        <Button variant="routine">Accessible Button</Button>
      );

      // Testa compliance WCAG 2.2 (deve falhar)
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper contrast ratios for all variants', async () => {
      const variants = ['routine', 'urgent', 'critical', 'emergency'];
      
      for (const variant of variants) {
        const { container } = render(
          <Button variant={variant as any}>
            {variant} Button
          </Button>
        );

        // Testa contraste para cada variante (deve falhar)
        const results = await axe(container, {
          rules: {
            'color-contrast': { enabled: true },
            'color-contrast-enhanced': { enabled: true }
          }
        });
        
        expect(results).toHaveNoViolations();
      }
    });

    it('should maintain contrast in focus states', async () => {
      const { container } = render(
        <Button variant="routine">Focus Test</Button>
      );

      const button = screen.getByRole('button');
      await user.tab(); // Move focus to button

      // Testa contraste no estado de foco (deve falhar)
      const results = await axe(container, {
        rules: { 'color-contrast': { enabled: true } }
      });
      
      expect(results).toHaveNoViolations();
      expect(button).toHaveFocus();
    });

    it('should have minimum 44px touch target size', () => {
      render(
        <Button size="medical" variant="routine">
          Touch Target
        </Button>
      );

      const button = screen.getByRole('button');
      const rect = button.getBoundingClientRect();

      // Testa tamanho mínimo de toque (deve falhar)
      expect(rect.width).toBeGreaterThanOrEqual(44);
      expect(rect.height).toBeGreaterThanOrEqual(44);
    });
  });

  describe('Keyboard Navigation', () => {
    it('should be focusable via keyboard navigation', async () => {
      render(
        <div>
          <Button variant="routine">First Button</Button>
          <Button variant="urgent">Second Button</Button>
        </div>
      );

      const firstButton = screen.getByText('First Button');
      const secondButton = screen.getByText('Second Button');

      // Testa navegação por teclado (deve falhar)
      await user.tab();
      expect(firstButton).toHaveFocus();

      await user.tab();
      expect(secondButton).toHaveFocus();
    });

    it('should activate with keyboard (Space/Enter)', async () => {
      const handleClick = vi.fn();
      
      render(
        <Button onClick={handleClick} variant="routine">
          Keyboard Button
        </Button>
      );

      const button = screen.getByRole('button');
      
      // Testa ativação por teclado (deve falhar)
      await user.tab();
      expect(button).toHaveFocus();

      await user.keyboard(' '); // Space key
      expect(handleClick).toHaveBeenCalledTimes(1);

      await user.keyboard('{Enter}');
      expect(handleClick).toHaveBeenCalledTimes(2);
    });

    it('should support medical workflow keyboard shortcuts', async () => {
      const handleEmergency = vi.fn();
      
      render(
        <Button 
          variant="emergency"
          medicalShortcut="Ctrl+E"
          onClick={handleEmergency}
        >
          Emergency
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa atalhos médicos (deve falhar)
      expect(button).toHaveAttribute('aria-keyshortcuts', 'Control+E');
      
      fireEvent.keyDown(window, { key: 'e', ctrlKey: true });
      expect(handleEmergency).toHaveBeenCalled();
    });

    it('should maintain keyboard focus within medical workflows', async () => {
      render(
        <div data-medical-workflow="patient-intake">
          <Button variant="routine" medicalStep="1">Step 1</Button>
          <Button variant="routine" medicalStep="2">Step 2</Button>
          <Button variant="routine" medicalStep="3">Step 3</Button>
        </div>
      );

      const step1Button = screen.getByText('Step 1');
      const step2Button = screen.getByText('Step 2');

      // Testa foco em workflows médicos (deve falhar)
      await user.tab();
      expect(step1Button).toHaveFocus();

      await user.keyboard('{ArrowRight}'); // Navegação com setas
      expect(step2Button).toHaveFocus();
    });
  });

  describe('Screen Reader Support', () => {
    it('should have proper ARIA labels for medical contexts', () => {
      render(
        <Button 
          variant="emergency" 
          medicalContext="cardiac-arrest"
          medicalPriority="critical"
        >
          Code Blue
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa labels ARIA médicos (deve falhar)
      expect(button).toHaveAttribute('aria-label', 'Emergency: Code Blue - Critical Priority');
      expect(button).toHaveAttribute('data-medical-context', 'cardiac-arrest');
      expect(button).toHaveAttribute('data-medical-priority', 'critical');
    });

    it('should announce loading states to screen readers', () => {
      render(
        <Button 
          variant="routine" 
          loading={true}
          medicalAction="save-patient-data"
        >
          Saving...
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa anúncios de loading (deve falhar)
      expect(button).toHaveAttribute('aria-busy', 'true');
      expect(button).toHaveAttribute('aria-live', 'polite');
      expect(button).toHaveAttribute('aria-describedby', expect.stringContaining('loading'));
      expect(button).toBeDisabled();
    });

    it('should provide medical terminology context', () => {
      render(
        <Button 
          variant="routine"
          medicalTerminology="systolic-blood-pressure"
          medicalUnit="mmHg"
        >
          Record BP
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa contexto de terminologia médica (deve falhar)
      expect(button).toHaveAttribute('aria-describedby');
      const describedBy = button.getAttribute('aria-describedby');
      const descriptionElement = document.getElementById(describedBy!);
      
      expect(descriptionElement).toHaveTextContent('Record systolic blood pressure in mmHg');
    });

    it('should announce validation errors for medical safety', () => {
      render(
        <Button 
          variant="critical"
          medicalError="Invalid dosage - exceeds maximum safe limit"
          validationState="error"
        >
          Administer Medication
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa anúncios de erro médico (deve falhar)
      expect(button).toHaveAttribute('aria-invalid', 'true');
      expect(button).toHaveAttribute('aria-describedby', expect.stringContaining('error'));
      
      const errorId = button.getAttribute('aria-describedby');
      const errorElement = document.getElementById(errorId!);
      expect(errorElement).toHaveTextContent('Invalid dosage - exceeds maximum safe limit');
      expect(errorElement).toHaveAttribute('role', 'alert');
    });
  });

  describe('High Contrast Mode Support', () => {
    it('should maintain visibility in high contrast mode', () => {
      // Simula modo de alto contraste
      document.body.classList.add('high-contrast');
      
      render(
        <Button variant="routine">High Contrast Button</Button>
      );

      const button = screen.getByRole('button');

      // Testa visibilidade em alto contraste (deve falhar)
      expect(button).toHaveClass('high-contrast:border-2');
      expect(button).toHaveClass('high-contrast:bg-ButtonFace');
      expect(button).toHaveClass('high-contrast:text-ButtonText');

      document.body.classList.remove('high-contrast');
    });

    it('should provide alternative indicators for color-blind users', () => {
      render(
        <Button 
          variant="critical"
          medicalIndicator="icon"
          colorBlindSafe={true}
        >
          ⚠️ Critical Action
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa indicadores para daltônicos (deve falhar)
      expect(button).toHaveAttribute('data-color-blind-safe', 'true');
      expect(button.textContent).toContain('⚠️'); // Ícone visual adicional
      expect(button).toHaveClass('border-medical-critical-pattern'); // Padrão além da cor
    });
  });

  describe('Medical Device Compatibility', () => {
    it('should work with medical input devices', async () => {
      render(
        <Button 
          variant="emergency"
          medicalDevice="foot-pedal"
          medicalTrigger="pressure"
        >
          Emergency Stop
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa compatibilidade com dispositivos médicos (deve falhar)
      expect(button).toHaveAttribute('data-medical-device', 'foot-pedal');
      expect(button).toHaveAttribute('data-medical-trigger', 'pressure');
      
      // Simula ativação por dispositivo médico
      fireEvent(button, new CustomEvent('medical-device-activate', {
        detail: { device: 'foot-pedal', pressure: 10 }
      }));
    });

    it('should support voice commands for hands-free operation', () => {
      const handleVoiceCommand = vi.fn();
      
      render(
        <Button 
          variant="routine"
          voiceCommand="record vitals"
          onVoiceCommand={handleVoiceCommand}
        >
          Record Vitals
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa comandos de voz (deve falhar)
      expect(button).toHaveAttribute('data-voice-command', 'record vitals');
      
      // Simula comando de voz
      fireEvent(button, new CustomEvent('voice-command', {
        detail: { command: 'record vitals', confidence: 0.95 }
      }));
      
      expect(handleVoiceCommand).toHaveBeenCalledWith({
        command: 'record vitals',
        confidence: 0.95
      });
    });
  });

  describe('Error Recovery and Safety', () => {
    it('should provide clear error recovery for medical mistakes', () => {
      render(
        <Button 
          variant="critical"
          medicalError="Medication already administered"
          errorRecovery="undo-last-action"
        >
          Undo Last Action
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa recuperação de erros médicos (deve falhar)
      expect(button).toHaveAttribute('role', 'button');
      expect(button).toHaveAttribute('aria-describedby', expect.stringContaining('error-recovery'));
      expect(button).toHaveAttribute('data-error-recovery', 'undo-last-action');
    });

    it('should prevent accidental activation for critical actions', async () => {
      let clickCount = 0;
      const handleCriticalAction = () => clickCount++;
      
      render(
        <Button 
          variant="emergency"
          medicalSafety="double-confirm"
          medicalCritical={true}
          onClick={handleCriticalAction}
        >
          Delete Patient Record
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa prevenção de ativação acidental (deve falhar)
      expect(button).toHaveAttribute('data-medical-safety', 'double-confirm');
      
      await user.click(button);
      expect(clickCount).toBe(0); // Primeira tentativa bloqueada
      
      // Simula confirmação dupla
      await user.dblClick(button);
      expect(clickCount).toBe(1); // Só executa após confirmação
    });
  });

  describe('Internationalization and Localization', () => {
    it('should support RTL languages for international medical systems', () => {
      document.dir = 'rtl';
      
      render(
        <Button variant="routine" dir="rtl">
          زر طبي
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa suporte RTL (deve falhar)
      expect(button).toHaveAttribute('dir', 'rtl');
      expect(button).toHaveClass('rtl:pl-medical-base'); // Padding adequado para RTL
      expect(button).toHaveClass('rtl:text-right');

      document.dir = 'ltr';
    });

    it('should maintain accessibility in different locales', () => {
      render(
        <Button 
          variant="emergency"
          locale="es-ES"
          medicalContext="emergencia"
        >
          Emergencia
        </Button>
      );

      const button = screen.getByRole('button');

      // Testa acessibilidade multilíngue (deve falhar)
      expect(button).toHaveAttribute('lang', 'es');
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('Emergencia'));
      expect(button).toHaveAttribute('data-medical-context', 'emergencia');
    });
  });
});