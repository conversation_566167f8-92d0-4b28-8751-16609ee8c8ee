[project]
name = "specify-cli"
version = "0.0.2"
description = "Setup tool for Specify spec-driven development projects"
requires-python = ">=3.11"
dependencies = [
    "typer",
    "rich",
    "httpx",
    "platformdirs",
    "readchar",
]

[project.scripts]
specify = "specify_cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/specify_cli"]