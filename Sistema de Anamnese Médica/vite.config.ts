
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';

export default defineConfig({
  // Use root base locally, project base on GitHub Pages
  base: process.env.GITHUB_ACTIONS ? '/sistema-anamnese-medica/' : '/',
  plugins: [react()],
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    alias: {
      'figma:asset/0861b8c218cc0038134a72897c76dab4ae1a5c19.png': path.resolve(__dirname, './src/assets/0861b8c218cc0038134a72897c76dab4ae1a5c19.png'),
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    target: 'esnext',
    // Align with repository guideline: production build outputs to build/
    outDir: 'build',
  },
  server: {
    port: 3000,
    open: true,
  },
});
