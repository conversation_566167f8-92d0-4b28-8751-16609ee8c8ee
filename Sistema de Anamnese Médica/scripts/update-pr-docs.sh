#!/usr/bin/env bash
set -euo pipefail

# Updates the current PR with any modified spec documents.
# - Stages changes under specs/ and CLAUDE.md, commits, pushes.
# - Optionally posts a PR comment via GitHub CLI summarizing changed files.

REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT"

BRANCH=$(git rev-parse --abbrev-ref HEAD)

changed=$(git status --porcelain | awk '{print $2}' | grep -E '^(specs/|Sistema de Anamnese Médica/CLAUDE.md)' || true)
if [[ -z "$changed" ]]; then
  echo "No spec-related changes to commit."
  exit 0
fi

echo "Staging spec-related changes:"
echo "$changed" | sed 's/^/  - /'

git add $changed

msg="docs(specs): update spec documents on ${BRANCH}"
git commit -m "$msg"
git push -u origin "$BRANCH"

if command -v gh >/dev/null 2>&1; then
  # Try to detect an open PR for this branch and comment a summary
  if gh pr view "$BRANCH" --json number >/dev/null 2>&1; then
    summary=$(echo "$changed" | sed 's/^/- /')
    gh pr comment "$BRANCH" --body "Updated spec documents:\n\n${summary}"
  else
    echo "No PR found for branch $BRANCH (gh pr view failed)."
  fi
else
  echo "GitHub CLI not installed; skipping PR comment."
fi

