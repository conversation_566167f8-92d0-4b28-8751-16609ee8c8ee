{"name": "Sistema de Anamnese Médica", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "*", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "*", "cmdk": "^1.1.1", "docx": "^9.5.1", "embla-carousel-react": "^8.6.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jspdf": "^3.0.2", "lucide-react": "^0.542.0", "motion": "*", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dnd": "*", "react-dnd-html5-backend": "*", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^3.1.2", "sonner": "^2.0.3", "tailwind-merge": "*", "vaul": "^1.1.2"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@storybook/addon-a11y": "^9.1.5", "@storybook/addon-docs": "^9.1.5", "@storybook/addon-onboarding": "^9.1.5", "@storybook/addon-vitest": "^9.1.5", "@storybook/react-vite": "^9.1.5", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/file-saver": "^2.0.7", "@types/node": "^24.3.1", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "axe-core": "^4.10.3", "chromatic": "^13.1.4", "eslint": "^9.35.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^9.1.5", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.6", "playwright": "^1.55.0", "prettier": "^3.6.2", "storybook": "^9.1.5", "typescript": "^5.9.2", "vite": "7.1.4", "vitest": "^3.2.4"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint \"src/**/*.{ts,tsx,js,jsx}\"", "lint:fix": "npm run lint -- --fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit", "preview": "vite preview --port 4173", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run", "lint-staged": "lint-staged", "specify:check": "uvx --from git+https://github.com/github/spec-kit.git specify check", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx}": ["eslint --fix"], "*.{md,json,css,ts,tsx,js,jsx}": ["prettier --write --ignore-unknown"]}}