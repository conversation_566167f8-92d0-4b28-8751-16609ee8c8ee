import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { MedicalProvider } from '../components/MedicalContext'
import { ExtendedAnamneseData } from '../components/MedicalContext'

// Mock data para testes
export const mockAnamneseData: ExtendedAnamneseData = {
  paciente: {
    id: 'PAC-TEST-001',
    faixaEtaria: 'adulto',
    sexoBiologico: 'M',
    gestante: false,
    telefone: '11999999999',
    consentimentoWhatsApp: true,
    timestamp: new Date('2024-01-10T10:00:00Z')
  },
  queixaPrincipal: {
    queixaPrincipal: 'Dor torácica',
    duracaoSintomas: '2 horas',
    intensidade: 8,
    caracteristicas: ['em aperto', 'irradia para braço esquerdo'],
    fatoresAssociados: ['sudorese', 'nausea'],
    observacoes: 'Dor iniciou após esforço físico'
  },
  historicoMedico: {
    comorbidades: ['hipertensão arterial', 'diabetes mellitus'],
    medicamentosUso: ['enalapril 10mg', 'metformina 500mg'],
    alergias: ['penicilina'],
    cirurgiasAnteriores: ['apendicectomia'],
    historicoFamiliar: ['IAM paterno'],
    habitosVida: ['tabagismo', 'sedentarismo']
  },
  exameFisico: {
    sinaisVitais: {
      pa: '150/90',
      fc: '95',
      fr: '20',
      temp: '36.5',
      sat: '98',
      glicemia: '120'
    },
    exameFisico: {
      aspectoGeral: 'Consciente, orientado, em bom estado geral',
      cabecaPescoco: 'Sem alterações',
      torax: 'Simétrico, expansibilidade normal',
      cardiovascular: 'Ritmo regular, sem sopros',
      respiratorio: 'MV presente, sem ruídos adventícios',
      abdome: 'Plano, flácido, sem massas',
      extremidades: 'Sem edema, perfusão normal',
      neurologico: 'Consciente, orientado, sem déficits'
    },
    observacoes: 'Paciente estável, sem sinais de gravidade'
  },
  medicamentos: {
    prescricaoAtual: [
      {
        medicamento: 'AAS 100mg',
        dose: '100mg',
        via: 'oral',
        frequencia: '1x/dia',
        duracao: 'indefinido',
        orientacoes: 'Tomar com água'
      }
    ],
    medicamentosEmUso: ['enalapril', 'metformina']
  },
  avaliacaoConduta: {
    hipoteseDiagnostica: ['Síndrome coronariana aguda', 'Angina instável'],
    condutaImediata: ['ECG', 'Enzimas cardíacas', 'Monitorização cardíaca'],
    examesSolicitados: ['ECG', 'Troponina', 'CK-MB', 'Hemograma'],
    retorno: '24-48h',
    orientacoes: ['Repouso', 'Dieta hipossódica', 'Retorno imediato se piora']
  }
}

// Wrapper customizado para testes com MedicalProvider
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <MedicalProvider>
      {children}
    </MedicalProvider>
  )
}

// Função de render customizada
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Re-exporta tudo
export * from '@testing-library/react'
export { customRender as render }

// Utilitários para testes de relatórios médicos
export const createMockRelatorio = (overrides = {}) => ({
  id: 'REL-TEST-001',
  pacienteId: 'PAC-TEST-001',
  anamneseId: 'ANA-TEST-001',
  conteudo: 'Relatório médico de teste',
  sugestoesDiagnostico: [],
  recomendacoesTratamento: [],
  template: {
    id: 'TEMPLATE-001',
    nome: 'Template Clínica Geral',
    especialidade: 'clinica_geral' as const,
    estrutura: {
      secoes: [],
      ordem: [],
      estilos: {
        fonte: 'Arial',
        tamanhoFonte: 12,
        corPrimaria: '#2E7CD6',
        corSecundaria: '#87CEEB'
      }
    },
    camposObrigatorios: [],
    camposOpcionais: [],
    formatoExportacao: ['pdf', 'docx'] as const,
    ativo: true,
    dataCriacao: new Date(),
    dataModificacao: new Date()
  },
  dataCriacao: new Date(),
  dataModificacao: new Date(),
  status: 'rascunho' as const,
  versao: 1,
  ...overrides
})

export const createMockSugestaoDiagnostico = (overrides = {}) => ({
  id: 'SUG-TEST-001',
  codigo: 'I20.0',
  descricao: 'Angina instável',
  confianca: 85,
  evidencia: ['dor torácica', 'fatores de risco cardiovascular'],
  sintomasRelacionados: ['dor torácica', 'sudorese'],
  examesComplementares: ['ECG', 'Troponina'],
  observacoes: 'Sugestão baseada em sintomas e fatores de risco',
  fonte: 'algoritmo' as const,
  ...overrides
})

export const createMockRecomendacaoTratamento = (overrides = {}) => ({
  id: 'REC-TEST-001',
  tipo: 'medicamentoso' as const,
  descricao: 'Tratamento antiagregante plaquetário',
  medicamentos: [
    {
      nome: 'AAS',
      principioAtivo: 'Ácido acetilsalicílico',
      dosagem: '100mg',
      via: 'oral',
      frequencia: '1x/dia',
      duracao: 'indefinido',
      observacoes: 'Tomar com água',
      contraindicacoes: ['alergia a AAS']
    }
  ],
  exames: [
    {
      nome: 'ECG',
      tipo: 'eletrocardiograma',
      urgencia: 'urgente' as const,
      justificativa: 'Avaliar isquemia miocárdica',
      observacoes: 'Realizar imediatamente'
    }
  ],
  observacoes: 'Recomendação baseada em guidelines atuais',
  nivelEvidencia: 'A' as const,
  fonte: 'Diretrizes Brasileiras de Cardiologia',
  contraindicacoes: ['alergia a AAS'],
  interacoes: ['warfarina'],
  ...overrides
})

// Mock de funções de geração de relatórios
export const mockReportGenerationService = {
  gerarRelatorio: () => Promise.resolve({
    sucesso: true,
    relatorio: createMockRelatorio(),
    sugestoes: [createMockSugestaoDiagnostico()],
    recomendacoes: [createMockRecomendacaoTratamento()],
    tempoGeracao: 1500
  }),
  exportarRelatorio: () => Promise.resolve({
    sucesso: true,
    arquivo: new Blob(['mock content'], { type: 'application/pdf' }),
    nomeArquivo: 'relatorio_teste.pdf',
    tamanhoArquivo: 1024
  })
}

// Mock de funções de diagnóstico
export const mockDiagnosisService = {
  gerarSugestoes: () => Promise.resolve([
    createMockSugestaoDiagnostico(),
    createMockSugestaoDiagnostico({
      id: 'SUG-TEST-002',
      codigo: 'I21.9',
      descricao: 'Infarto agudo do miocárdio',
      confianca: 75
    })
  ])
}

// Mock de funções de tratamento
export const mockTreatmentService = {
  gerarRecomendacoes: () => Promise.resolve([
    createMockRecomendacaoTratamento(),
    createMockRecomendacaoTratamento({
      id: 'REC-TEST-002',
      tipo: 'observacao',
      descricao: 'Monitorização cardíaca contínua'
    })
  ])
}
