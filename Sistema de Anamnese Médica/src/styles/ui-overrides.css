/* Lightweight UI overrides to improve proportions and viewport behavior */

/* Dynamic viewport unit fallback (iOS/Android address bar) */
:root {
  --app-min-h: 100svh;
}
@supports not (height: 100svh) {
  :root { --app-min-h: 100vh; }
}

/* Prefer reduced motion for performance and accessibility */
@media (prefers-reduced-motion: reduce) {
  * { animation-duration: 0.01ms !important; animation-iteration-count: 1 !important; transition-duration: 0.01ms !important; }
  html:focus-within { scroll-behavior: auto; }
}

/* Base proportions */
html {
  hanging-punctuation: first last;
  text-wrap: pretty;
}

/* Ensure the app container fills the safe viewport height */
.app-container {
  height: auto;
  min-height: var(--app-min-h);
}

/* Fluid gutters and readable max width for text-heavy areas */
.content-gutters { padding-inline: clamp(16px, 4vw, 32px); }
.content-max { max-width: 1280px; margin-inline: auto; }

/* Safe-area helpers */
.safe-top { padding-top: env(safe-area-inset-top); }
.safe-bottom { padding-bottom: env(safe-area-inset-bottom); }
.safe-left { padding-left: env(safe-area-inset-left); }
.safe-right { padding-right: env(safe-area-inset-right); }

/* Utility to opt into dynamic viewport min-height when needed */
.min-h-svh-fix { min-height: var(--app-min-h); }
