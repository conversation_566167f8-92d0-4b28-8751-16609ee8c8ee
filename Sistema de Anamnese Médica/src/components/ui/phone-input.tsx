import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Phone, Check, X } from 'lucide-react';
import { Input } from './input';
import { Label } from './label';
import { Checkbox } from './checkbox';

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  consent: boolean;
  onConsentChange: (consent: boolean) => void;
  className?: string;
  color?: string;
}

export const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  consent,
  onConsentChange,
  className = '',
  color = '#2E86DE'
}) => {
  const [isValid, setIsValid] = useState(true);
  const [isFocused, setIsFocused] = useState(false);

  // Máscara para telefone brasileiro
  const formatPhoneNumber = (input: string) => {
    // Remove tudo que não é número
    const numbers = input.replace(/\D/g, '');
    
    // <PERSON>ita a 11 dígitos
    const limited = numbers.slice(0, 11);
    
    // Aplica a máscara
    if (limited.length <= 2) {
      return `(${limited}`;
    } else if (limited.length <= 7) {
      return `(${limited.slice(0, 2)}) ${limited.slice(2)}`;
    } else {
      return `(${limited.slice(0, 2)}) ${limited.slice(2, 7)}-${limited.slice(7)}`;
    }
  };

  // Validação do telefone
  const validatePhone = (phone: string) => {
    const numbers = phone.replace(/\D/g, '');
    return numbers.length >= 10; // Pelo menos 10 dígitos
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    const valid = validatePhone(formatted);
    
    onChange(formatted);
    setIsValid(valid || formatted === '');
  };

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  return (
    <motion.div 
      className={`space-y-4 ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Input do telefone */}
      <div className="relative">
        <Label className="flex items-center gap-2 mb-2">
          <Phone className="h-4 w-4" style={{ color }} />
          Telefone para contato
          <span className="text-xs text-muted-foreground">(opcional)</span>
        </Label>
        
        <div className="relative">
          <Input
            type="tel"
            placeholder="(11) 99999-9999"
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className={`pl-12 pr-10 transition-all duration-200 ${
              !isValid ? 'border-red-500 focus:border-red-500' : ''
            } ${
              isFocused ? 'ring-2 ring-offset-2' : ''
            }`}
            style={isFocused ? {
              borderColor: color,
              ringColor: `${color}40`
            } : {}}
          />
          
          {/* Ícone do telefone */}
          <div 
            className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center justify-center w-6 h-6 rounded-full"
            style={{ backgroundColor: `${color}20` }}
          >
            <Phone className="h-3 w-3" style={{ color }} />
          </div>
          
          {/* Indicador de validação */}
          <AnimatePresence>
            {value && (
              <motion.div
                className="absolute right-3 top-1/2 transform -translate-y-1/2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
              >
                {isValid ? (
                  <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                    <Check className="h-3 w-3 text-white" />
                  </div>
                ) : (
                  <div className="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center">
                    <X className="h-3 w-3 text-white" />
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        {/* Mensagem de erro */}
        <AnimatePresence>
          {!isValid && value && (
            <motion.p
              className="text-sm text-red-600 mt-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
            >
              Por favor, insira um telefone válido
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Checkbox de consentimento */}
      <AnimatePresence>
        {value && isValid && (
          <motion.div
            className="flex items-start space-x-3 p-4 rounded-xl bg-muted/30 border"
            style={{ 
              backgroundColor: `${color}05`,
              borderColor: `${color}20`
            }}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Checkbox
              id="phone-consent"
              checked={consent}
              onCheckedChange={onConsentChange}
              className="mt-0.5"
            />
            <div className="flex-1">
              <Label 
                htmlFor="phone-consent" 
                className="text-sm cursor-pointer leading-relaxed"
              >
                Autorizo o envio de informações médicas via WhatsApp para este número
              </Label>
              <p className="text-xs text-muted-foreground mt-1">
                Suas informações serão tratadas com total sigilo médico
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default PhoneInput;