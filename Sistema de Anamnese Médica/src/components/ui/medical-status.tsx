import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "./utils";
import { 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  AlertCircle, 
  Clock,
  Activity,
  Heart,
  Zap
} from "lucide-react";

const medicalStatusVariants = cva(
  "inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium transition-medical-normal ease-medical-out",
  {
    variants: {
      status: {
        critical: "bg-medical-color-critical-50 text-medical-color-critical-700 border border-medical-color-critical-200 shadow-medical-critical",
        urgent: "bg-medical-color-urgent-50 text-medical-color-urgent-700 border border-medical-color-urgent-200 shadow-medical-urgent",
        routine: "bg-medical-color-primary-50 text-medical-color-primary-700 border border-medical-color-primary-200 shadow-medical-glow",
        success: "bg-medical-color-success-50 text-medical-color-success-700 border border-medical-color-success-200 shadow-medical-success",
        info: "bg-medical-color-info-50 text-medical-color-info-700 border border-medical-color-info-200",
        warning: "bg-medical-color-urgent-50 text-medical-color-urgent-700 border border-medical-color-urgent-200",
        stable: "bg-medical-color-success-50 text-medical-color-success-700 border border-medical-color-success-200",
        pending: "bg-gray-50 text-gray-700 border border-gray-200",
        processing: "bg-medical-color-primary-50 text-medical-color-primary-700 border border-medical-color-primary-200",
      },
      size: {
        sm: "px-2 py-1 text-xs",
        default: "px-3 py-1.5 text-sm",
        lg: "px-4 py-2 text-base",
      },
      variant: {
        default: "",
        glass: "backdrop-blur-medical bg-opacity-80",
        solid: "border-0 text-white",
        outline: "bg-transparent border-2",
      },
    },
    compoundVariants: [
      {
        status: "critical",
        variant: "solid",
        class: "bg-medical-color-critical text-white",
      },
      {
        status: "urgent",
        variant: "solid",
        class: "bg-medical-color-urgent text-white",
      },
      {
        status: "routine",
        variant: "solid",
        class: "bg-medical-color-primary text-white",
      },
      {
        status: "success",
        variant: "solid",
        class: "bg-medical-color-success text-white",
      },
      {
        status: "info",
        variant: "solid",
        class: "bg-medical-color-info text-white",
      },
    ],
    defaultVariants: {
      status: "routine",
      size: "default",
      variant: "default",
    },
  }
);

const statusIcons = {
  critical: AlertTriangle,
  urgent: AlertCircle,
  routine: Activity,
  success: CheckCircle,
  info: Info,
  warning: AlertTriangle,
  stable: Heart,
  pending: Clock,
  processing: Zap,
};

export interface MedicalStatusProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof medicalStatusVariants> {
  label?: string;
  showIcon?: boolean;
  pulse?: boolean;
}

const MedicalStatus = React.forwardRef<HTMLDivElement, MedicalStatusProps>(
  ({ className, status, size, variant, label, showIcon = true, pulse = false, ...props }, ref) => {
    const Icon = status ? statusIcons[status] : Activity;
    
    return (
      <div
        ref={ref}
        className={cn(
          medicalStatusVariants({ status, size, variant, className }),
          pulse && "animate-pulse"
        )}
        {...props}
      >
        {showIcon && <Icon className="h-4 w-4" />}
        {label && <span>{label}</span>}
      </div>
    );
  }
);

MedicalStatus.displayName = "MedicalStatus";

// Preset status components for common medical contexts
export const CriticalStatus = React.forwardRef<HTMLDivElement, Omit<MedicalStatusProps, 'status'>>(
  (props, ref) => <MedicalStatus ref={ref} status="critical" {...props} />
);
CriticalStatus.displayName = "CriticalStatus";

export const UrgentStatus = React.forwardRef<HTMLDivElement, Omit<MedicalStatusProps, 'status'>>(
  (props, ref) => <MedicalStatus ref={ref} status="urgent" {...props} />
);
UrgentStatus.displayName = "UrgentStatus";

export const RoutineStatus = React.forwardRef<HTMLDivElement, Omit<MedicalStatusProps, 'status'>>(
  (props, ref) => <MedicalStatus ref={ref} status="routine" {...props} />
);
RoutineStatus.displayName = "RoutineStatus";

export const StableStatus = React.forwardRef<HTMLDivElement, Omit<MedicalStatusProps, 'status'>>(
  (props, ref) => <MedicalStatus ref={ref} status="stable" {...props} />
);
StableStatus.displayName = "StableStatus";

export { MedicalStatus, medicalStatusVariants };
