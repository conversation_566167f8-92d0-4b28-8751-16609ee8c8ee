import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "./utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all duration-200 focus-visible:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 touch-manipulation",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-md active:scale-[0.98] rounded-lg",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md active:scale-[0.98] rounded-lg",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md active:scale-[0.98] rounded-lg",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md active:scale-[0.98] rounded-lg",
        ghost: "hover:bg-accent hover:text-accent-foreground rounded-lg active:scale-[0.98]",
        link: "text-primary underline-offset-4 hover:underline",
        medical: "bg-medical-gradient-primary text-white shadow-medical-md hover:shadow-medical-lg hover:scale-[1.02] active:scale-[0.98] rounded-lg transition-medical-normal ease-medical-out",
        medicalCritical: "bg-medical-gradient-critical text-white shadow-medical-critical hover:shadow-medical-critical hover:scale-[1.02] active:scale-[0.98] rounded-lg transition-medical-normal ease-medical-out",
        medicalUrgent: "bg-medical-gradient-urgent text-white shadow-medical-urgent hover:shadow-medical-urgent hover:scale-[1.02] active:scale-[0.98] rounded-lg transition-medical-normal ease-medical-out",
        medicalSuccess: "bg-medical-gradient-success text-white shadow-medical-success hover:shadow-medical-success hover:scale-[1.02] active:scale-[0.98] rounded-lg transition-medical-normal ease-medical-out",
        medicalGlass: "bg-medical-glass border border-medical-glass-border backdrop-blur-medical text-medical-text-primary shadow-medical-sm hover:shadow-medical-md hover:scale-[1.01] active:scale-[0.99] rounded-lg transition-medical-normal ease-medical-out",
        medicalOutline: "border-2 border-medical-color-primary bg-transparent text-medical-color-primary hover:bg-medical-color-primary hover:text-white shadow-medical-sm hover:shadow-medical-md active:scale-[0.98] rounded-lg transition-medical-normal ease-medical-out",
      },
      size: {
        default: "h-11 px-6 py-2 text-sm min-w-[88px]", /* 44px+ touch target */
        sm: "h-9 px-4 text-xs min-w-[72px]",
        lg: "h-12 px-8 text-base min-w-[120px]",
        icon: "h-11 w-11 rounded-lg", /* Square touch target */
        medical: "h-12 px-8 text-base min-w-[140px]", /* Medical action buttons */
        medicalLarge: "h-14 px-10 text-lg min-w-[160px]", /* Large medical buttons */
        medicalSmall: "h-10 px-5 text-sm min-w-[100px]", /* Small medical buttons */
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }