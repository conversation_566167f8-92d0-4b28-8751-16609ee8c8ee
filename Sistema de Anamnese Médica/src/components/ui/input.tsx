import * as React from "react";

import { cn } from "./utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        // Medical UI base styles
        "medical-input",
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground",
        "flex min-w-0 text-base outline-none transition-all duration-200",
        "file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium",
        "disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
        // Medical focus states
        "focus-visible:border-primary focus-visible:ring-4 focus-visible:ring-primary/20",
        // Medical error states  
        "aria-invalid:border-destructive aria-invalid:ring-4 aria-invalid:ring-destructive/20",
        // Responsive text sizing
        "text-base md:text-sm",
        // Touch-friendly on mobile
        "touch-manipulation",
        className,
      )}
      {...props}
    />
  );
}

export { Input };
