import React from 'react';
import { motion } from 'motion/react';
import { cn } from './utils';

interface SegmentedControlOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface SegmentedControlProps {
  options: SegmentedControlOption[];
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export const SegmentedControl: React.FC<SegmentedControlProps> = ({
  options,
  value,
  onValueChange,
  className = '',
  size = 'md',
  color = '#2E86DE'
}) => {
  const sizeClasses = {
    sm: 'h-8 text-sm px-3',
    md: 'h-10 text-base px-4',
    lg: 'h-12 text-lg px-6'
  };

  return (
    <div 
      className={cn(
        "inline-flex rounded-2xl p-1 bg-muted/50 border backdrop-blur-sm",
        className
      )}
      style={{ 
        backgroundColor: `${color}08`,
        borderColor: `${color}20`
      }}
    >
      {options.map((option) => (
        <motion.button
          key={option.value}
          type="button"
          className={cn(
            "relative flex items-center justify-center gap-2 rounded-xl transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed",
            sizeClasses[size],
            value === option.value
              ? "text-white shadow-md"
              : "text-muted-foreground hover:text-foreground hover:bg-background/50"
          )}
          style={value === option.value ? {
            backgroundColor: color,
            boxShadow: `0 4px 12px ${color}30`
          } : {}}
          onClick={() => !option.disabled && onValueChange(option.value)}
          disabled={option.disabled}
          whileHover={!option.disabled ? { scale: 1.02 } : {}}
          whileTap={!option.disabled ? { scale: 0.98 } : {}}
        >
          {option.icon && (
            <span className="flex items-center justify-center">
              {option.icon}
            </span>
          )}
          <span className="truncate">{option.label}</span>
          
          {value === option.value && (
            <motion.div
              className="absolute inset-0 rounded-xl"
              style={{
                background: `linear-gradient(135deg, ${color}, ${color}dd)`
              }}
              layoutId="segmented-active-bg"
              initial={false}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
            />
          )}
          
          <span className="relative z-10 flex items-center gap-2">
            {option.icon}
            {option.label}
          </span>
        </motion.button>
      ))}
    </div>
  );
};

export default SegmentedControl;