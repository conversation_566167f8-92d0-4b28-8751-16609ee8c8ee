import React from 'react'
import { cn } from './utils'
import { motion } from 'motion/react'
import { 
  Heart, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Zap,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react'

type StatusType = 'stable' | 'critical' | 'urgent' | 'normal' | 'improving' | 'declining' | 'monitoring' | 'active'
type StatusSize = 'sm' | 'md' | 'lg'
type StatusVariant = 'default' | 'minimal' | 'detailed' | 'pulse'

interface StatusIndicatorProps {
  status: StatusType
  size?: StatusSize
  variant?: StatusVariant
  showIcon?: boolean
  showLabel?: boolean
  showPulse?: boolean
  label?: string
  className?: string
}

const statusConfig = {
  stable: {
    color: 'text-emerald-600 dark:text-emerald-400',
    bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
    borderColor: 'border-emerald-200 dark:border-emerald-800/50',
    icon: CheckCircle,
    label: 'Estável',
    gradient: 'from-emerald-500 to-green-600'
  },
  critical: {
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-50 dark:bg-red-900/20',
    borderColor: 'border-red-200 dark:border-red-800/50',
    icon: AlertTriangle,
    label: 'Crítico',
    gradient: 'from-red-500 to-red-600'
  },
  urgent: {
    color: 'text-amber-600 dark:text-amber-400',
    bgColor: 'bg-amber-50 dark:bg-amber-900/20',
    borderColor: 'border-amber-200 dark:border-amber-800/50',
    icon: Clock,
    label: 'Urgente',
    gradient: 'from-amber-500 to-orange-600'
  },
  normal: {
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800/50',
    icon: Heart,
    label: 'Normal',
    gradient: 'from-blue-500 to-blue-600'
  },
  improving: {
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800/50',
    icon: TrendingUp,
    label: 'Melhorando',
    gradient: 'from-green-500 to-emerald-600'
  },
  declining: {
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    borderColor: 'border-orange-200 dark:border-orange-800/50',
    icon: TrendingDown,
    label: 'Declínio',
    gradient: 'from-orange-500 to-red-500'
  },
  monitoring: {
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-800/50',
    icon: Activity,
    label: 'Monitorando',
    gradient: 'from-purple-500 to-violet-600'
  },
  active: {
    color: 'text-cyan-600 dark:text-cyan-400',
    bgColor: 'bg-cyan-50 dark:bg-cyan-900/20',
    borderColor: 'border-cyan-200 dark:border-cyan-800/50',
    icon: Zap,
    label: 'Ativo',
    gradient: 'from-cyan-500 to-blue-600'
  }
}

const sizeConfig = {
  sm: {
    container: 'px-2 py-1',
    icon: 'w-3 h-3',
    text: 'text-xs',
    dot: 'w-2 h-2'
  },
  md: {
    container: 'px-3 py-1.5',
    icon: 'w-4 h-4',
    text: 'text-sm',
    dot: 'w-3 h-3'
  },
  lg: {
    container: 'px-4 py-2',
    icon: 'w-5 h-5',
    text: 'text-base',
    dot: 'w-4 h-4'
  }
}

export function StatusIndicator({
  status,
  size = 'md',
  variant = 'default',
  showIcon = true,
  showLabel = true,
  showPulse = false,
  label,
  className
}: StatusIndicatorProps) {
  const config = statusConfig[status]
  const sizeStyles = sizeConfig[size]
  const IconComponent = config.icon
  
  const shouldPulse = showPulse || status === 'critical' || status === 'active'
  
  const pulseAnimation = {
    scale: [1, 1.1, 1],
    opacity: [1, 0.8, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: [0.455, 0.03, 0.515, 0.955] // heartbeat curve
    }
  }

  if (variant === 'minimal') {
    return (
      <motion.div 
        className={cn('flex items-center gap-2', className)}
        animate={shouldPulse ? pulseAnimation : {}}
      >
        <div className={cn(
          'rounded-full',
          config.color,
          sizeStyles.dot
        )}>
          <div className={cn(
            'w-full h-full rounded-full',
            `bg-gradient-to-br ${config.gradient}`
          )} />
        </div>
        {showLabel && (
          <span className={cn(
            'font-medium',
            config.color,
            sizeStyles.text
          )}>
            {label || config.label}
          </span>
        )}
      </motion.div>
    )
  }

  if (variant === 'pulse') {
    return (
      <motion.div 
        className={cn(
          'relative inline-flex items-center gap-2',
          className
        )}
      >
        <div className="relative">
          <motion.div 
            className={cn(
              'rounded-full',
              `bg-gradient-to-br ${config.gradient}`,
              sizeStyles.dot
            )}
            animate={pulseAnimation}
          />
          <motion.div
            className={cn(
              'absolute inset-0 rounded-full border-2',
              config.borderColor,
              'opacity-75'
            )}
            animate={{
              scale: [1, 1.5, 2],
              opacity: [0.7, 0.3, 0],
              transition: {
                duration: 2,
                repeat: Infinity,
                ease: "easeOut"
              }
            }}
          />
        </div>
        {showLabel && (
          <span className={cn(
            'font-medium',
            config.color,
            sizeStyles.text
          )}>
            {label || config.label}
          </span>
        )}
      </motion.div>
    )
  }

  if (variant === 'detailed') {
    return (
      <motion.div 
        className={cn(
          'inline-flex items-center gap-2 rounded-lg border',
          config.bgColor,
          config.borderColor,
          sizeStyles.container,
          'shadow-sm',
          className
        )}
        whileHover={{
          scale: 1.02,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
        }}
        animate={shouldPulse ? pulseAnimation : {}}
      >
        {showIcon && (
          <motion.div
            animate={status === 'critical' ? {
              rotate: [0, -5, 5, -5, 0],
              transition: { 
                duration: 0.5, 
                repeat: Infinity, 
                repeatDelay: 2 
              }
            } : {}}
          >
            <IconComponent className={cn(
              config.color,
              sizeStyles.icon
            )} />
          </motion.div>
        )}
        {showLabel && (
          <span className={cn(
            'font-semibold',
            config.color,
            sizeStyles.text
          )}>
            {label || config.label}
          </span>
        )}
      </motion.div>
    )
  }

  // Default variant
  return (
    <motion.div 
      className={cn(
        'inline-flex items-center gap-1.5',
        className
      )}
      animate={shouldPulse ? pulseAnimation : {}}
    >
      {showIcon && (
        <IconComponent className={cn(
          config.color,
          sizeStyles.icon
        )} />
      )}
      {showLabel && (
        <span className={cn(
          'font-medium',
          config.color,
          sizeStyles.text
        )}>
          {label || config.label}
        </span>
      )}
    </motion.div>
  )
}

// Medical Status Grid Component
interface MedicalStatusGridProps {
  statuses: Array<{
    type: StatusType
    label?: string
    count?: number
    trend?: 'up' | 'down' | 'stable'
  }>
  className?: string
}

export function MedicalStatusGrid({ statuses, className }: MedicalStatusGridProps) {
  return (
    <div className={cn(
      'grid grid-cols-2 md:grid-cols-4 gap-3',
      className
    )}>
      {statuses.map((status, index) => (
        <motion.div
          key={status.type}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className={cn(
            'p-3 rounded-lg border text-center',
            statusConfig[status.type].bgColor,
            statusConfig[status.type].borderColor
          )}
        >
          <StatusIndicator
            status={status.type}
            variant="minimal"
            size="sm"
            label={status.label}
            className="justify-center mb-2"
          />
          {status.count !== undefined && (
            <div className="space-y-1">
              <div className={cn(
                'text-2xl font-bold',
                statusConfig[status.type].color
              )}>
                {status.count}
              </div>
              {status.trend && (
                <div className="flex items-center justify-center gap-1">
                  {status.trend === 'up' && <TrendingUp className="w-3 h-3 text-green-500" />}
                  {status.trend === 'down' && <TrendingDown className="w-3 h-3 text-red-500" />}
                  {status.trend === 'stable' && <Minus className="w-3 h-3 text-gray-500" />}
                </div>
              )}
            </div>
          )}
        </motion.div>
      ))}
    </div>
  )
}