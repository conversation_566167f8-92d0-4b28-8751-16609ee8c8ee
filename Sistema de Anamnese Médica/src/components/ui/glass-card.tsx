import React from 'react'
import { cn } from './utils'
import { motion } from 'motion/react'

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'premium' | 'hero'
  blur?: 'sm' | 'md' | 'lg' | 'xl'
  opacity?: number
  animated?: boolean
  hoverEffect?: boolean
  children: React.ReactNode
}

const glassVariants = {
  default: {
    background: 'rgba(255, 255, 255, 0.8)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    backdropFilter: 'blur(12px)',
    boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
  },
  elevated: {
    background: 'rgba(255, 255, 255, 0.85)',
    border: '1px solid rgba(255, 255, 255, 0.25)',
    backdropFilter: 'blur(16px)',
    boxShadow: '0 12px 40px 0 rgba(31, 38, 135, 0.2)',
  },
  premium: {
    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%)',
    border: '1px solid rgba(29, 78, 216, 0.1)',
    backdropFilter: 'blur(20px)',
    boxShadow: '0 20px 50px 0 rgba(29, 78, 216, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3)',
  },
  hero: {
    background: 'linear-gradient(135deg, rgba(29, 78, 216, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%)',
    border: '1px solid rgba(59, 130, 246, 0.2)',
    backdropFilter: 'blur(24px)',
    boxShadow: '0 25px 60px 0 rgba(29, 78, 216, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
  }
}

const blurLevels = {
  sm: 'blur(8px)',
  md: 'blur(12px)',
  lg: 'blur(16px)',
  xl: 'blur(24px)'
}

export function GlassCard({ 
  variant = 'default',
  blur = 'md',
  opacity,
  animated = true,
  hoverEffect = true,
  className,
  children,
  ...props 
}: GlassCardProps) {
  const styles = glassVariants[variant]
  
  const cardStyles = {
    ...styles,
    backdropFilter: blurLevels[blur],
    background: opacity 
      ? `rgba(255, 255, 255, ${opacity})` 
      : styles.background
  }

  const animationVariants = {
    initial: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    animate: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: hoverEffect ? {
      scale: 1.02,
      y: -4,
      boxShadow: [
        styles.boxShadow,
        '0 32px 80px 0 rgba(29, 78, 216, 0.25)'
      ],
      transition: {
        duration: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    } : {},
    tap: {
      scale: 0.98,
      transition: {
        duration: 0.1
      }
    }
  }

  const CardComponent = animated ? motion.div : 'div'
  const motionProps = animated ? {
    variants: animationVariants,
    initial: 'initial',
    animate: 'animate',
    whileHover: 'hover',
    whileTap: 'tap'
  } : {}

  return (
    <CardComponent
      className={cn(
        'relative overflow-hidden rounded-xl',
        // Dark mode adjustments
        'dark:bg-slate-900/80 dark:border-slate-700/50',
        className
      )}
      style={cardStyles}
      {...motionProps}
      {...props}
    >
      {/* Inner glow effect for premium variants */}
      {(variant === 'premium' || variant === 'hero') && (
        <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/10 via-transparent to-transparent pointer-events-none" />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Bottom highlight for hero variant */}
      {variant === 'hero' && (
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent" />
      )}
    </CardComponent>
  )
}

// Preset components for common use cases
export function HeroGlassCard({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) {
  return (
    <GlassCard 
      variant="hero" 
      className={cn('p-8', className)} 
      {...props}
    >
      {children}
    </GlassCard>
  )
}

export function PremiumGlassCard({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) {
  return (
    <GlassCard 
      variant="premium" 
      className={cn('p-6', className)} 
      {...props}
    >
      {children}
    </GlassCard>
  )
}

export function ElevatedGlassCard({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) {
  return (
    <GlassCard 
      variant="elevated" 
      className={cn('p-4', className)} 
      {...props}
    >
      {children}
    </GlassCard>
  )
}