import React from 'react'
import { cn } from './utils'
import { motion } from 'motion/react'
import { Slot } from '@radix-ui/react-slot'
import { Loader2 } from 'lucide-react'

interface PremiumButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'glass' | 'gradient' | 'medical' | 'hero'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  loadingText?: string
  asChild?: boolean
  ripple?: boolean
  glow?: boolean
  children: React.ReactNode
}

const buttonVariants = {
  primary: {
    base: 'bg-blue-600 hover:bg-blue-700 text-white border-transparent',
    shadow: 'shadow-md hover:shadow-lg',
    focus: 'focus:ring-blue-500/50'
  },
  secondary: {
    base: 'bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 text-slate-900 dark:text-slate-100 border-slate-200 dark:border-slate-700',
    shadow: 'shadow-sm hover:shadow-md',
    focus: 'focus:ring-slate-500/50'
  },
  ghost: {
    base: 'bg-transparent hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-900 dark:text-slate-100 border-transparent',
    shadow: 'shadow-none hover:shadow-sm',
    focus: 'focus:ring-slate-500/50'
  },
  glass: {
    base: 'bg-white/80 hover:bg-white/90 dark:bg-slate-900/80 dark:hover:bg-slate-900/90 text-slate-900 dark:text-slate-100 border-white/20 dark:border-slate-700/50 backdrop-blur-md',
    shadow: 'shadow-lg hover:shadow-xl',
    focus: 'focus:ring-blue-500/50'
  },
  gradient: {
    base: 'bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white border-transparent',
    shadow: 'shadow-lg hover:shadow-xl shadow-blue-500/25',
    focus: 'focus:ring-blue-500/50'
  },
  medical: {
    base: 'bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white border-transparent',
    shadow: 'shadow-lg hover:shadow-xl shadow-emerald-500/25',
    focus: 'focus:ring-emerald-500/50'
  },
  hero: {
    base: 'bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 hover:from-blue-700 hover:via-purple-700 hover:to-blue-900 text-white border-transparent relative overflow-hidden',
    shadow: 'shadow-2xl hover:shadow-3xl shadow-blue-500/30',
    focus: 'focus:ring-blue-500/50'
  }
}

const sizeVariants = {
  sm: 'px-3 py-1.5 text-sm rounded-md',
  md: 'px-4 py-2 text-sm rounded-lg',
  lg: 'px-6 py-3 text-base rounded-lg',
  xl: 'px-8 py-4 text-lg rounded-xl'
}

export function PremiumButton({
  variant = 'primary',
  size = 'md',
  loading = false,
  loadingText = 'Carregando...',
  asChild = false,
  ripple = true,
  glow = false,
  className,
  children,
  disabled,
  onClick,
  ...props
}: PremiumButtonProps) {
  const [isRippling, setIsRippling] = React.useState(false)
  const buttonRef = React.useRef<HTMLButtonElement>(null)
  
  const variantStyles = buttonVariants[variant]
  const sizeStyles = sizeVariants[size]

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return
    
    if (ripple && buttonRef.current) {
      setIsRippling(true)
      setTimeout(() => setIsRippling(false), 400)
    }
    
    onClick?.(e)
  }

  const buttonClasses = cn(
    // Base styles
    'relative inline-flex items-center justify-center font-medium transition-all duration-200 ease-out',
    'border focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    
    // Variant styles
    variantStyles.base,
    variantStyles.shadow,
    variantStyles.focus,
    
    // Size styles
    sizeStyles,
    
    // Glow effect
    glow && 'animate-pulse',
    
    // Loading state
    loading && 'cursor-wait',
    
    className
  )

  const buttonContent = (
    <>
      {/* Hero variant background effect */}
      {variant === 'hero' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent"
          initial={{ x: '-100%' }}
          whileHover={{
            x: '100%',
            transition: { duration: 0.6, ease: 'easeInOut' }
          }}
        />
      )}
      
      {/* Ripple effect */}
      {ripple && isRippling && (
        <motion.div
          className="absolute inset-0 bg-white/30 rounded-[inherit]"
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 0.4, ease: 'easeOut' }}
        />
      )}
      
      {/* Content */}
      <motion.div 
        className="relative z-10 flex items-center justify-center gap-2"
        animate={{
          x: loading ? -10 : 0,
        }}
        transition={{ duration: 0.2 }}
      >
        {loading && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            <Loader2 className="w-4 h-4 animate-spin" />
          </motion.div>
        )}
        <span className={cn(loading && 'opacity-70')}>
          {loading ? loadingText : children}
        </span>
      </motion.div>
    </>
  )

  const MotionButton = motion.button

  const buttonMotionProps = {
    whileHover: disabled || loading ? {} : {
      scale: 1.02,
      y: -1,
      transition: { duration: 0.15, ease: 'easeOut' }
    },
    whileTap: disabled || loading ? {} : {
      scale: 0.98,
      y: 0,
      transition: { duration: 0.1 }
    },
    initial: { scale: 1 },
    animate: { scale: 1 }
  }

  if (asChild) {
    return (
      <Slot className={buttonClasses} onClick={handleClick} {...props}>
        {children}
      </Slot>
    )
  }

  return (
    <MotionButton
      ref={buttonRef}
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={handleClick}
      {...buttonMotionProps}
      {...props}
    >
      {buttonContent}
    </MotionButton>
  )
}

// Preset button components for common medical use cases
export function MedicalButton(props: Omit<PremiumButtonProps, 'variant'>) {
  return <PremiumButton variant="medical" {...props} />
}

export function HeroButton(props: Omit<PremiumButtonProps, 'variant'>) {
  return <PremiumButton variant="hero" glow {...props} />
}

export function GlassButton(props: Omit<PremiumButtonProps, 'variant'>) {
  return <PremiumButton variant="glass" {...props} />
}

export function GradientButton(props: Omit<PremiumButtonProps, 'variant'>) {
  return <PremiumButton variant="gradient" {...props} />
}

// Quick Action Button with icon
interface QuickActionButtonProps extends Omit<PremiumButtonProps, 'children'> {
  icon: React.ReactNode
  label: string
  description?: string
  shortcut?: string
}

export function QuickActionButton({
  icon,
  label,
  description,
  shortcut,
  className,
  ...props
}: QuickActionButtonProps) {
  return (
    <PremiumButton
      variant="glass"
      className={cn(
        'flex flex-col items-center gap-2 p-4 min-h-[80px] w-full',
        className
      )}
      {...props}
    >
      <div className="flex items-center justify-center w-6 h-6 text-blue-600 dark:text-blue-400">
        {icon}
      </div>
      <div className="text-center">
        <div className="font-semibold text-xs">{label}</div>
        {description && (
          <div className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">
            {description}
          </div>
        )}
        {shortcut && (
          <div className="text-xs text-slate-400 dark:text-slate-500 mt-1 font-mono">
            {shortcut}
          </div>
        )}
      </div>
    </PremiumButton>
  )
}