import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "./utils";

const cardVariants = cva(
  "flex flex-col gap-6 rounded-xl border transition-medical-normal ease-medical-out",
  {
    variants: {
      variant: {
        default: "bg-card text-card-foreground shadow-sm",
        medical: "bg-medical-card-bg text-medical-text-primary shadow-medical-md hover:shadow-medical-lg",
        medicalGlass: "bg-medical-glass border-medical-glass-border backdrop-blur-medical text-medical-text-primary shadow-medical-sm hover:shadow-medical-md",
        medicalPremium: "bg-medical-glass-premium border-medical-glass-border backdrop-blur-medical-strong text-medical-text-primary shadow-medical-lg hover:shadow-medical-xl",
        medicalHero: "bg-medical-glass-hero border-medical-glass-border backdrop-blur-medical-strong text-medical-text-primary shadow-medical-xl",
        medicalCritical: "bg-medical-card-emergency border-medical-border-critical text-medical-text-primary shadow-medical-critical",
        medicalPatient: "bg-medical-card-patient text-medical-text-primary shadow-medical-md hover:shadow-medical-lg",
        medicalData: "bg-medical-card-data text-medical-text-primary shadow-medical-sm hover:shadow-medical-md",
        medicalDashboard: "bg-medical-card-dashboard text-medical-text-primary shadow-medical-md hover:shadow-medical-lg",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        medical: "p-6",
        medicalLarge: "p-8",
        medicalSmall: "p-4",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface CardProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof cardVariants> {}

function Card({ className, variant, size, ...props }: CardProps) {
  return (
    <div
      data-slot="card"
      className={cn(cardVariants({ variant, size, className }))}
      {...props}
    />
  );
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",
        className,
      )}
      {...props}
    />
  );
}

function CardTitle({ className, children, ...props }: React.ComponentProps<"div">) {
  return (
    <h4
      data-slot="card-title"
      className={cn("leading-none", className)}
      {...props}
    >
      {children}
    </h4>
  );
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <p
      data-slot="card-description"
      className={cn("text-muted-foreground", className)}
      {...props}
    />
  );
}

function CardAction({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className,
      )}
      {...props}
    />
  );
}

function CardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-content"
      className={cn("px-6 [&:last-child]:pb-6", className)}
      {...props}
    />
  );
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-footer"
      className={cn("flex items-center px-6 pb-6 [.border-t]:pt-6", className)}
      {...props}
    />
  );
}

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
  cardVariants,
};
