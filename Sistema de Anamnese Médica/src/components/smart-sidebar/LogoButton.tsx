import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { useMedical } from '../MedicalContext';
import { sidebarConfig } from '../../config/ui/sidebar';
import wellwaveLogo from 'figma:asset/0861b8c218cc0038134a72897c76dab4ae1a5c19.png';

interface LogoButtonProps {
  collapsed: boolean;
  onToggle: () => void;
  className?: string;
}

export const LogoButton: React.FC<LogoButtonProps> = ({
  collapsed,
  onToggle,
  className = ""
}) => {
  const { medicalColors } = useMedical();
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onToggle();
    }
  };
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.button
            className={`group relative flex items-center gap-4 p-6 w-full text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-sidebar-ring rounded-2xl transition-all smart-sidebar-logo ${className}`}
            onClick={onToggle}
            onKeyDown={handleKeyDown}
            aria-pressed={collapsed}
            aria-label={collapsed ? "Expandir sidebar" : "Recolher sidebar"}
            whileHover={{ 
              scale: 1.02,
              transition: { duration: 0.2, ease: "easeOut" }
            }}
            whileTap={{ 
              scale: 0.98,
              transition: { duration: 0.1, ease: "easeInOut" }
            }}
            style={{
              transition: sidebarConfig.transitions.medium
            }}
          >
            {/* Background gradient overlay */}
            <motion.div
              className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100"
              style={{
                background: `linear-gradient(135deg, ${medicalColors.primary}08, ${medicalColors.primaryLight}05, transparent)`
              }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            />
            
            {/* Logo container with premium effects */}
            <motion.div 
              className="relative shrink-0 flex items-center justify-center"
              whileHover={{ 
                rotate: [0, -2, 2, 0],
                scale: 1.05,
                transition: { 
                  duration: 0.6, 
                  ease: "easeInOut",
                  rotate: {
                    duration: 0.8,
                    ease: "easeInOut"
                  }
                }
              }}
            >
              {/* Glow effect background */}
              <motion.div
                className="absolute inset-0 rounded-full"
                style={{
                  background: `conic-gradient(from 0deg, ${medicalColors.primary}30, ${medicalColors.primaryLight}20, ${medicalColors.primaryDark}30, ${medicalColors.primary}30)`,
                  filter: "blur(12px)",
                  zIndex: 0
                }}
                animate={{ 
                  rotate: 360,
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  rotate: {
                    duration: 20, 
                    repeat: Infinity, 
                    ease: "linear"
                  },
                  scale: {
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }
                }}
              />
              
              {/* Logo image with enhanced styling */}
              <motion.div
                className="relative z-10 w-12 h-12 rounded-full overflow-hidden shadow-lg"
                style={{
                  background: `linear-gradient(135deg, ${medicalColors.primaryLight}10, transparent)`,
                  border: `2px solid ${medicalColors.primary}20`
                }}
                animate={{ 
                  boxShadow: [
                    `0 4px 20px ${medicalColors.primary}20`,
                    `0 8px 30px ${medicalColors.primary}30`,
                    `0 4px 20px ${medicalColors.primary}20`
                  ]
                }}
                transition={{ 
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <motion.img
                  src={wellwaveLogo}
                  alt="WellWave Logo"
                  className="w-full h-full object-cover"
                  animate={{ 
                    filter: [
                      "brightness(1) contrast(1)",
                      "brightness(1.05) contrast(1.05)", 
                      "brightness(1) contrast(1)"
                    ]
                  }}
                  transition={{ 
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
              
              {/* Pulse ring effect */}
              <motion.div
                className="absolute inset-0 rounded-full border-2 opacity-0"
                style={{
                  borderColor: medicalColors.primary
                }}
                animate={{
                  scale: [1, 1.4, 1.8],
                  opacity: [0.6, 0.3, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeOut"
                }}
              />
            </motion.div>
            
            {/* Text content with sophisticated reveal animation */}
            <AnimatePresence mode="wait">
              {!collapsed && (
                <motion.div 
                  className="flex-1 min-w-0 overflow-hidden"
                  initial={{ 
                    opacity: 0, 
                    width: 0, 
                    x: -20,
                    clipPath: "inset(0 100% 0 0)" 
                  }}
                  animate={{ 
                    opacity: 1, 
                    width: "auto", 
                    x: 0,
                    clipPath: "inset(0 0% 0 0)" 
                  }}
                  exit={{ 
                    opacity: 0, 
                    width: 0, 
                    x: -10,
                    clipPath: "inset(0 100% 0 0)" 
                  }}
                  transition={{ 
                    duration: 0.4, 
                    ease: "easeInOut",
                    opacity: { duration: 0.3, delay: 0.1 },
                    width: { duration: 0.4 },
                    x: { duration: 0.3 },
                    clipPath: { duration: 0.4 }
                  }}
                >
                  <motion.h1 
                    className="text-2xl font-bold truncate mb-1"
                    style={{
                      background: medicalColors.gradients.primary,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                      backgroundSize: '200% 100%'
                    }}
                    animate={{
                      y: 0,
                      opacity: 1,
                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    initial={{ y: 5, opacity: 0 }}
                    exit={{ y: -5, opacity: 0 }}
                  >
                    WellWave
                  </motion.h1>
                  <motion.p 
                    className="text-sm text-sidebar-foreground/70 font-medium truncate"
                    initial={{ y: 5, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: -5, opacity: 0 }}
                    transition={{ delay: 0.05 }}
                  >
                    Sistema Médico Integrado
                  </motion.p>
                  
                  {/* Status indicator */}
                  <motion.div
                    className="flex items-center gap-2 mt-2"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ delay: 0.1 }}
                  >
                    <motion.div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: medicalColors.success }}
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [1, 0.7, 1]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                    <span className="text-xs text-sidebar-foreground/60 font-medium">
                      Sistema Online
                    </span>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Click ripple effect */}
            <motion.div
              className="absolute inset-0 rounded-2xl pointer-events-none"
              style={{
                background: `radial-gradient(circle, ${medicalColors.primary}20 0%, transparent 70%)`
              }}
              initial={{ scale: 0, opacity: 0 }}
              whileTap={{ 
                scale: 1.2, 
                opacity: [0, 0.5, 0],
                transition: { duration: 0.3 }
              }}
            />
          </motion.button>
        </TooltipTrigger>
        
        {collapsed && (
          <TooltipContent side="right" className="ml-3 font-medium">
            <div className="text-center">
              <p className="font-semibold">WellWave</p>
              <p className="text-xs opacity-70">Clique para expandir</p>
            </div>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
};