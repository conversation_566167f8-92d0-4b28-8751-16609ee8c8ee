import React, { useState, useCallback, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { GripVertical } from 'lucide-react';
import { sidebarConfig } from '../../config/ui/sidebar';
import { useMedical } from '../MedicalContext';

interface ResizeHandleProps {
  onResize: (width: number) => void;
  onResizeStart?: () => void;
  onResizeEnd?: () => void;
  currentWidth: number;
  collapsed: boolean;
}

export const ResizeHandle: React.FC<ResizeHandleProps> = ({
  onResize,
  onResizeStart,
  onResizeEnd,
  currentWidth,
  collapsed
}) => {
  const { medicalColors } = useMedical();
  const [isDragging, setIsDragging] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const startWidthRef = useRef(currentWidth);
  const startXRef = useRef(0);
  
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (collapsed) return;
    
    e.preventDefault();
    setIsDragging(true);
    setShowFeedback(true);
    startWidthRef.current = currentWidth;
    startXRef.current = e.clientX;
    onResizeStart?.();
    
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [collapsed, currentWidth, onResizeStart]);
  
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    
    const deltaX = e.clientX - startXRef.current;
    const newWidth = startWidthRef.current + deltaX;
    
    const clampedWidth = Math.max(
      sidebarConfig.minWidth,
      Math.min(sidebarConfig.maxWidth, newWidth)
    );
    
    onResize(clampedWidth);
  }, [isDragging, onResize]);
  
  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    setShowFeedback(false);
    onResizeEnd?.();
    
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [isDragging, onResizeEnd]);
  
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (collapsed) return;
    
    if (e.key === 'ArrowLeft') {
      e.preventDefault();
      const newWidth = Math.max(sidebarConfig.minWidth, currentWidth - sidebarConfig.resizeStep);
      onResize(newWidth);
      setShowFeedback(true);
      setTimeout(() => setShowFeedback(false), 1000);
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      const newWidth = Math.min(sidebarConfig.maxWidth, currentWidth + sidebarConfig.resizeStep);
      onResize(newWidth);
      setShowFeedback(true);
      setTimeout(() => setShowFeedback(false), 1000);
    }
  }, [collapsed, currentWidth, onResize]);
  
  // Mouse event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);
  
  // Cleanup cursor on unmount
  useEffect(() => {
    return () => {
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, []);
  
  if (collapsed) return null;
  
  return (
    <>
      {/* Main resize handle */}
      <motion.div
        className="group absolute top-0 right-0 w-2 h-full cursor-col-resize z-20 flex items-center justify-center resize-handle"
        onMouseDown={handleMouseDown}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="separator"
        aria-valuenow={currentWidth}
        aria-valuemin={sidebarConfig.minWidth}
        aria-valuemax={sidebarConfig.maxWidth}
        aria-label="Redimensionar sidebar"
        animate={{
          width: isHovering || isDragging ? 6 : 2
        }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
      >
        {/* Visual handle indicator */}
        <motion.div
          className="h-20 w-full rounded-full flex items-center justify-center relative overflow-hidden"
          style={{
            background: isHovering || isDragging 
              ? `linear-gradient(135deg, ${medicalColors.primary}80, ${medicalColors.primaryLight}60)`
              : `${medicalColors.primary}40`
          }}
          animate={{
            scale: isHovering || isDragging ? 1.1 : 1,
            boxShadow: isDragging 
              ? `0 4px 12px ${medicalColors.primary}40`
              : isHovering 
                ? `0 2px 8px ${medicalColors.primary}30`
                : 'none'
          }}
          transition={{ duration: 0.2 }}
        >
          {/* Grip icon for hover state */}
          <AnimatePresence>
            {(isHovering || isDragging) && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <GripVertical 
                  className="h-4 w-4 text-white"
                  style={{ filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))' }}
                />
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Shimmer effect when dragging */}
          <AnimatePresence>
            {isDragging && (
              <motion.div
                className="absolute inset-0 shimmer"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              />
            )}
          </AnimatePresence>
        </motion.div>
        
        {/* Invisible wider hit area */}
        <div className="absolute inset-y-0 -left-4 -right-4 cursor-col-resize" />
      </motion.div>
      
      {/* Width feedback tooltip */}
      <AnimatePresence>
        {showFeedback && (
          <motion.div
            className="fixed z-50 px-3 py-2 rounded-lg shadow-lg text-sm font-medium text-white pointer-events-none"
            style={{
              background: `linear-gradient(135deg, ${medicalColors.primary}, ${medicalColors.primaryDark})`,
              left: `${currentWidth - 50}px`,
              top: '50%',
              transform: 'translateY(-50%)'
            }}
            initial={{ opacity: 0, scale: 0.8, x: 10 }}
            animate={{ opacity: 1, scale: 1, x: 0 }}
            exit={{ opacity: 0, scale: 0.8, x: 10 }}
            transition={{ duration: 0.2 }}
          >
            {currentWidth}px
            
            {/* Arrow pointing to handle */}
            <div 
              className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 w-0 h-0"
              style={{
                borderLeft: `6px solid ${medicalColors.primary}`,
                borderTop: '6px solid transparent',
                borderBottom: '6px solid transparent'
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Resize guide lines */}
      <AnimatePresence>
        {isDragging && (
          <>
            {/* Min width guide */}
            <motion.div
              className="fixed top-0 bottom-0 w-0.5 z-10 pointer-events-none"
              style={{
                left: `${sidebarConfig.minWidth}px`,
                backgroundColor: `${medicalColors.warning}80`
              }}
              initial={{ opacity: 0, scaleY: 0 }}
              animate={{ opacity: 1, scaleY: 1 }}
              exit={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.2 }}
            />
            
            {/* Max width guide */}
            <motion.div
              className="fixed top-0 bottom-0 w-0.5 z-10 pointer-events-none"
              style={{
                left: `${sidebarConfig.maxWidth}px`,
                backgroundColor: `${medicalColors.danger}80`
              }}
              initial={{ opacity: 0, scaleY: 0 }}
              animate={{ opacity: 1, scaleY: 1 }}
              exit={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.2 }}
            />
          </>
        )}
      </AnimatePresence>
    </>
  );
};