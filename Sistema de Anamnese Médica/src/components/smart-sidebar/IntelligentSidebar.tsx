import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Badge } from '../ui/badge';
import { PremiumButton, QuickActionButton } from '../ui/premium-button';
import { StatusIndicator } from '../ui/status-indicator';
import { PremiumGlassCard, ElevatedGlassCard } from '../ui/glass-card';
import { 
  BarChart3,
  Stethoscope,
  Bot,
  Calendar,
  FileText,
  Pill,
  Activity,
  Sun,
  Moon,
  Menu,
  X,
  ChevronRight,
  ChevronLeft,
  Bell,
  Star,
  Zap,
  Heart,
  Brain,
  AlertTriangle,
  TrendingUp,
  Clock,
  User,
  Settings,
  LogOut
} from 'lucide-react';
import wellwaveLogo from 'figma:asset/0861b8c218cc0038134a72897c76dab4ae1a5c19.png';

interface SidebarModule {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
  color: string;
  badge?: {
    count: number;
    type: 'urgent' | 'normal' | 'success' | 'warning';
  };
  status?: 'active' | 'inactive' | 'pending';
  timestamp?: Date;
}

interface IntelligentSidebarProps {
  modules: SidebarModule[];
  activeModule: string;
  onModuleChange: (moduleId: string) => void;
  onNewAction: () => void;
  newActionLabel: string;
  darkMode: boolean;
  onToggleTheme: () => void;
  userName: string;
  userEmail: string;
  expanded: boolean;
  onToggle: (expanded: boolean) => void;
  isMobile?: boolean;
  isOpen?: boolean;
  onClose?: () => void;
}

const QUICK_MEDICAL_ACTIONS = [
  { id: 'emergency', icon: <AlertTriangle className="w-4 h-4" />, label: 'Emergência', urgent: true },
  { id: 'prescription', icon: <Heart className="w-4 h-4" />, label: 'Prescrever' },
  { id: 'ai_consult', icon: <Brain className="w-4 h-4" />, label: 'Consultar IA' },
  { id: 'schedule', icon: <Calendar className="w-4 h-4" />, label: 'Agendar' }
];

const SIDEBAR_NOTIFICATIONS = [
  { id: 1, type: 'urgent' as const, message: '2 pacientes aguardando', time: '5 min' },
  { id: 2, type: 'normal' as const, message: 'Relatório disponível', time: '1h' },
  { id: 3, type: 'success' as const, message: 'Backup completo', time: '2h' }
];

export const IntelligentSidebar: React.FC<IntelligentSidebarProps> = ({
  modules,
  activeModule,
  onModuleChange,
  onNewAction,
  newActionLabel,
  darkMode,
  onToggleTheme,
  userName,
  userEmail,
  expanded,
  onToggle,
  isMobile = false,
  isOpen = false,
  onClose
}) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const sidebarWidth = expanded ? 320 : 80; // Increased width for premium features

  const handleModuleClick = (moduleId: string) => {
    onModuleChange(moduleId);
    if (isMobile && onClose) {
      onClose();
    }
  };

  const ModuleNavigationItem = ({ module, index }: { module: SidebarModule; index: number }) => {
    const isActive = activeModule === module.id;
    const IconComponent = module.icon;
    
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.1 + index * 0.05 }}
        className="relative"
      >
        <motion.button
          onClick={() => handleModuleClick(module.id)}
          className={`
            w-full flex items-center gap-3 px-3 py-3 rounded-xl text-left transition-all duration-200 group relative overflow-hidden
            ${isActive 
              ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 text-blue-700 dark:text-blue-300 shadow-sm' 
              : 'text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800/50 hover:text-slate-900 dark:hover:text-slate-200'
            }
          `}
          whileHover={{ x: 2 }}
          whileTap={{ scale: 0.98 }}
        >
          {/* Active indicator */}
          {isActive && (
            <motion.div
              className="absolute left-0 top-2 bottom-2 w-1 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-r-full"
              initial={{ scaleY: 0 }}
              animate={{ scaleY: 1 }}
              transition={{ duration: 0.3 }}
            />
          )}
          
          {/* Icon */}
          <motion.div 
            className={`p-2 rounded-lg ${isActive ? 'bg-white dark:bg-slate-700 shadow-sm' : 'group-hover:bg-white dark:group-hover:bg-slate-700'}`}
            style={{ 
              backgroundColor: isActive ? `${module.color}20` : undefined,
              color: isActive ? module.color : undefined 
            }}
            whileHover={{ rotate: 5 }}
          >
            <IconComponent className="w-5 h-5" />
          </motion.div>
          
          {/* Content */}
          {(expanded || isMobile) && (
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div>
                  <div className={`font-medium truncate ${isActive ? 'text-slate-900 dark:text-white' : ''}`}>
                    {module.label}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400 truncate">
                    {module.description}
                  </div>
                </div>
                
                {/* Badge or Status */}
                <div className="flex items-center gap-2">
                  {module.badge && (
                    <Badge 
                      className={`text-xs px-2 py-0.5 ${
                        module.badge.type === 'urgent' ? 'bg-red-500 text-white' :
                        module.badge.type === 'warning' ? 'bg-amber-500 text-white' :
                        module.badge.type === 'success' ? 'bg-green-500 text-white' :
                        'bg-blue-500 text-white'
                      }`}
                    >
                      {module.badge.count}
                    </Badge>
                  )}
                  
                  {module.status && (
                    <StatusIndicator
                      status={module.status === 'active' ? 'active' : module.status === 'pending' ? 'monitoring' : 'normal'}
                      size="sm"
                      variant="minimal"
                      showLabel={false}
                    />
                  )}
                  
                  {isActive && (
                    <ChevronRight className="w-4 h-4 text-blue-500" />
                  )}
                </div>
              </div>
            </div>
          )}
        </motion.button>
      </motion.div>
    );
  };

  return (
    <>
      {/* Mobile Backdrop */}
      {isMobile && isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        className={`
          ${isMobile ? 'fixed' : 'relative'} 
          top-0 left-0 h-full
          flex flex-col z-50
          ${isMobile ? (isOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'}
        `}
        style={{ width: isMobile ? 320 : sidebarWidth }}
        animate={{ 
          width: isMobile ? 320 : sidebarWidth,
          x: isMobile ? (isOpen ? 0 : -320) : 0
        }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
      >
        <PremiumGlassCard className="h-full rounded-none border-0 border-r">
          {/* Header */}
          <div className="p-4 border-b border-slate-200/60 dark:border-slate-700/50">
            <div className="flex items-center justify-between mb-4">
              {expanded || isMobile ? (
                <motion.div 
                  className="flex items-center gap-3"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <motion.div
                    className="p-2 rounded-xl bg-gradient-to-br from-blue-600 to-indigo-600 shadow-md"
                    whileHover={{ scale: 1.05, rotate: 3 }}
                  >
                    <img 
                      src={wellwaveLogo} 
                      alt="WellWave" 
                      className="h-6 w-6 object-contain brightness-0 invert" 
                    />
                  </motion.div>
                  <div>
                    <div className="font-bold text-slate-900 dark:text-white">WellWave</div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">Sistema Inteligente</div>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  className="mx-auto p-2 rounded-xl bg-gradient-to-br from-blue-600 to-indigo-600"
                  whileHover={{ scale: 1.05 }}
                >
                  <img src={wellwaveLogo} alt="WellWave" className="h-6 w-6 brightness-0 invert" />
                </motion.div>
              )}
              
              {/* Controls */}
              <div className="flex items-center gap-2">
                {(expanded || isMobile) && (
                  <PremiumButton
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowNotifications(!showNotifications)}
                    className="relative"
                  >
                    <Bell className="w-4 h-4" />
                    <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full p-0 flex items-center justify-center">
                      3
                    </Badge>
                  </PremiumButton>
                )}
                
                {isMobile ? (
                  <PremiumButton variant="ghost" size="sm" onClick={onClose}>
                    <X className="w-4 h-4" />
                  </PremiumButton>
                ) : (
                  <PremiumButton variant="ghost" size="sm" onClick={() => onToggle(!expanded)}>
                    <motion.div
                      animate={{ rotate: expanded ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </motion.div>
                  </PremiumButton>
                )}
              </div>
            </div>

            {/* Quick Medical Actions - Only when expanded */}
            {(expanded || isMobile) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="grid grid-cols-2 gap-2 mb-4"
              >
                {QUICK_MEDICAL_ACTIONS.map((action, index) => (
                  <motion.button
                    key={action.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 + index * 0.05 }}
                    className={`
                      p-2 rounded-lg text-xs font-medium transition-all duration-200 flex flex-col items-center gap-1
                      ${action.urgent 
                        ? 'bg-red-50 hover:bg-red-100 dark:bg-red-900/20 dark:hover:bg-red-900/30 text-red-700 dark:text-red-300' 
                        : 'bg-slate-50 hover:bg-slate-100 dark:bg-slate-800/50 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300'
                      }
                    `}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => onModuleChange(action.id)}
                  >
                    {action.icon}
                    <span className="leading-none">{action.label}</span>
                    {action.urgent && (
                      <motion.div 
                        className="w-1 h-1 bg-red-500 rounded-full"
                        animate={{ scale: [1, 1.5, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      />
                    )}
                  </motion.button>
                ))}
              </motion.div>
            )}

            {/* New Action Button */}
            <PremiumButton
              onClick={onNewAction}
              variant="gradient"
              size="sm"
              className="w-full"
            >
              <Stethoscope className="w-4 h-4" />
              {(expanded || isMobile) ? newActionLabel : ''}
            </PremiumButton>
          </div>

          {/* Notifications Panel */}
          <AnimatePresence>
            {showNotifications && (expanded || isMobile) && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border-b border-slate-200/60 dark:border-slate-700/50"
              >
                <div className="p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-sm text-slate-900 dark:text-white">Notificações</h3>
                    <Badge variant="outline" className="text-xs">
                      {SIDEBAR_NOTIFICATIONS.length}
                    </Badge>
                  </div>
                  
                  {SIDEBAR_NOTIFICATIONS.map((notification) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="flex items-start gap-2 p-2 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800/50 cursor-pointer"
                    >
                      <StatusIndicator
                        status={notification.type === 'urgent' ? 'urgent' : notification.type === 'success' ? 'stable' : 'normal'}
                        size="sm"
                        variant="minimal"
                        showLabel={false}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-slate-900 dark:text-white">{notification.message}</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400">{notification.time}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation */}
          <nav className="flex-1 px-3 py-4 overflow-y-auto">
            <div className="space-y-2">
              {modules.map((module, index) => (
                <ModuleNavigationItem key={module.id} module={module} index={index} />
              ))}
            </div>
          </nav>

          {/* Footer */}
          <div className="border-t border-slate-200/60 dark:border-slate-700/50 p-4">
            {/* Theme Toggle */}
            <PremiumButton
              variant="ghost"
              size="sm"
              onClick={onToggleTheme}
              className="w-full mb-3 justify-start"
            >
              <motion.div
                animate={{ rotate: darkMode ? 180 : 0 }}
                transition={{ duration: 0.5 }}
              >
                {darkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </motion.div>
              {(expanded || isMobile) && (
                <span className="ml-2">
                  {darkMode ? 'Modo Claro' : 'Modo Escuro'}
                </span>
              )}
            </PremiumButton>

            {/* User Profile */}
            {(expanded || isMobile) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <ElevatedGlassCard className="p-3">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-md">
                      <span className="text-white text-sm font-bold">
                        {userName.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-semibold text-slate-900 dark:text-white truncate">
                        {userName}
                      </div>
                      <div className="text-xs text-slate-500 dark:text-slate-400 truncate">
                        {userEmail}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white text-xs border-0">
                      <Star className="w-3 h-3 mr-1" />
                      PREMIUM
                    </Badge>
                    <div className="flex gap-1">
                      <PremiumButton variant="ghost" size="sm" className="p-1 h-auto">
                        <Settings className="w-3 h-3" />
                      </PremiumButton>
                      <PremiumButton variant="ghost" size="sm" className="p-1 h-auto">
                        <LogOut className="w-3 h-3" />
                      </PremiumButton>
                    </div>
                  </div>
                </ElevatedGlassCard>
              </motion.div>
            )}
          </div>
        </PremiumGlassCard>
      </motion.aside>
    </>
  );
};

export default IntelligentSidebar;