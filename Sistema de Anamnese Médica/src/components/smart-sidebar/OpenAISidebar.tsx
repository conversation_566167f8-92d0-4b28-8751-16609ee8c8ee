import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  ChevronLeft, 
  ChevronRight, 
  Settings, 
  Bell,
  Zap,
  Bot,
  Activity,
  User,
  LogOut,
  Plus,
  Search,
  HelpCircle
} from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import wellwaveLogo from 'figma:asset/0861b8c218cc0038134a72897c76dab4ae1a5c19.png';

interface SidebarModule {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string; size?: number; style?: React.CSSProperties }>;
  description: string;
  color: string;
  timestamp?: Date;
}

interface OpenAISidebarProps {
  modules: SidebarModule[];
  activeModule: string;
  onModuleChange: (moduleId: string) => void;
  onNewAction?: () => void;
  newActionLabel?: string;
  darkMode?: boolean;
  onToggleTheme?: () => void;
  userName?: string;
  userEmail?: string;
  userAvatar?: string;
  expanded?: boolean;
  onToggle?: (expanded: boolean) => void;
}

export const OpenAISidebar: React.FC<OpenAISidebarProps> = ({
  modules,
  activeModule,
  onModuleChange,
  onNewAction,
  newActionLabel = 'Nova Ação',
  darkMode = false,
  onToggleTheme,
  userName = 'Dr. Silva',
  userEmail = '<EMAIL>',
  userAvatar,
  expanded = true,
  onToggle
}) => {
  const [isExpanded, setIsExpanded] = useState(expanded);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  useEffect(() => {
    setIsExpanded(expanded);
  }, [expanded]);

  const handleToggle = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    onToggle?.(newExpanded);
  };

  const formatTimeAgo = (timestamp?: Date) => {
    if (!timestamp) return '';
    
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days}d atrás`;
    if (hours > 0) return `${hours}h atrás`;
    if (minutes > 0) return `${minutes}m atrás`;
    return 'Agora';
  };

  return (
    <TooltipProvider>
      <motion.aside 
        className={`openai-sidebar premium-sidebar glass`}
        animate={{ 
          width: isExpanded ? 280 : 80
        }}
        transition={{ 
          duration: 0.3, 
          ease: [0.4, 0, 0.2, 1] 
        }}
        style={{
          background: darkMode 
            ? 'rgba(26, 35, 50, 0.95)' 
            : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          borderRight: '1px solid var(--border-default)',
          boxShadow: 'var(--shadow-wave-lg)'
        }}
      >
        {/* Logo Section Premium com WellWave Logo Oficial */}
        <div className="sidebar-header flex items-center justify-between p-4 border-b border-border">
          <motion.div 
            className="logo-wrapper flex items-center gap-3"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div 
              className="logo-icon wave-motion"
              animate={{ 
                rotate: [0, 2, -2, 0],
                scale: [1, 1.02, 1]
              }}
              transition={{ 
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <div 
                className="p-2 rounded-xl overflow-hidden"
                style={{ 
                  background: 'var(--gradient-wave)',
                  boxShadow: 'var(--shadow-wave-glow)' 
                }}
              >
                <motion.img 
                  src={wellwaveLogo}
                  alt="WellWave Logo"
                  className="w-6 h-6 animate-heartbeat object-contain"
                  style={{ 
                    filter: 'drop-shadow(0 0 4px rgba(255,255,255,0.5))',
                    borderRadius: '4px'
                  }}
                  animate={{
                    scale: [1, 1.05, 1],
                    rotate: [0, 1, -1, 0]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </div>
            </motion.div>
            
            <AnimatePresence>
              {isExpanded && (
                <motion.div 
                  className="logo-text"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <span 
                    className="brand block font-bold text-lg leading-none"
                    style={{ 
                      background: 'var(--gradient-wave)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}
                  >
                    WellWave
                  </span>
                  <span className="tagline text-xs text-muted-foreground">Medical Excellence</span>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.button 
                className="sidebar-toggle p-2 rounded-lg hover:bg-accent transition-colors"
                onClick={handleToggle}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                transition={{ duration: 0.15 }}
              >
                <motion.div
                  animate={{ rotate: isExpanded ? 0 : 180 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronLeft size={16} />
                </motion.div>
              </motion.button>
            </TooltipTrigger>
            <TooltipContent side="right">
              {isExpanded ? 'Retrair sidebar' : 'Expandir sidebar'}
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Quick Actions Premium */}
        <motion.div 
          className="quick-actions p-4 space-y-2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          {/* New Action Button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.button 
                className="quick-btn w-full h-12 rounded-lg border border-border bg-background hover:bg-accent flex items-center justify-center gap-2 text-sm font-medium transition-all duration-200 group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={onNewAction}
                style={{ borderColor: 'var(--medical-success)' }}
              >
                <Plus size={16} className="text-medical-success" />
                <AnimatePresence>
                  {isExpanded && (
                    <motion.span 
                      className="text-medical-success"
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                    >
                      {newActionLabel}
                    </motion.span>
                  )}
                </AnimatePresence>
                <motion.div
                  className="absolute inset-0 bg-medical-success/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity"
                  layoutId="quickActionHover"
                />
              </motion.button>
            </TooltipTrigger>
            <TooltipContent side="right">
              {newActionLabel}
            </TooltipContent>
          </Tooltip>

          {/* Emergency Button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.button 
                className="quick-btn w-full h-10 rounded-lg border border-medical-danger/30 bg-medical-danger/5 hover:bg-medical-danger hover:text-white flex items-center justify-center gap-2 text-sm font-medium transition-all duration-200"
                whileHover={{ scale: 1.02, boxShadow: '0 0 20px rgba(255, 90, 95, 0.3)' }}
                whileTap={{ scale: 0.98 }}
              >
                <Zap size={14} />
                <AnimatePresence>
                  {isExpanded && (
                    <motion.span 
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                    >
                      SOS
                    </motion.span>
                  )}
                </AnimatePresence>
              </motion.button>
            </TooltipTrigger>
            <TooltipContent side="right">
              Emergência (⌘E)
            </TooltipContent>
          </Tooltip>

          {/* AI Chat Button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.button 
                className="quick-btn w-full h-10 rounded-lg border border-medical-info/30 bg-medical-info/5 hover:bg-medical-info hover:text-white flex items-center justify-center gap-2 text-sm font-medium transition-all duration-200"
                whileHover={{ scale: 1.02, boxShadow: '0 0 20px rgba(139, 127, 255, 0.3)' }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onModuleChange('chat')}
              >
                <Bot size={14} />
                <AnimatePresence>
                  {isExpanded && (
                    <motion.span 
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                    >
                      IA
                    </motion.span>
                  )}
                </AnimatePresence>
              </motion.button>
            </TooltipTrigger>
            <TooltipContent side="right">
              IA Médica (⌘I)
            </TooltipContent>
          </Tooltip>
        </motion.div>

        {/* Navigation Modules */}
        <nav className="sidebar-nav flex-1 px-2 overflow-y-auto openai-scrollbar">
          <div className="space-y-1">
            {modules.map((module, index) => {
              const Icon = module.icon;
              const isActive = activeModule === module.id;
              
              return (
                <Tooltip key={module.id}>
                  <TooltipTrigger asChild>
                    <motion.button
                      className={`nav-item w-full group relative ${isActive ? 'active' : ''}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => onModuleChange(module.id)}
                      onMouseEnter={() => setHoveredItem(module.id)}
                      onMouseLeave={() => setHoveredItem(null)}
                      whileHover={{ x: isExpanded ? 4 : 0, scale: isExpanded ? 1.02 : 1.1 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div 
                        className={`nav-link flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                          isActive 
                            ? 'bg-accent text-accent-foreground' 
                            : 'hover:bg-accent/50 text-muted-foreground hover:text-foreground'
                        }`}
                      >
                        <div 
                          className="nav-icon flex-shrink-0"
                          style={{ color: isActive ? module.color : undefined }}
                        >
                          <Icon size={20} />
                        </div>
                        
                        <AnimatePresence>
                          {isExpanded && (
                            <motion.div 
                              className="nav-content flex-1 min-w-0"
                              initial={{ opacity: 0, width: 0 }}
                              animate={{ opacity: 1, width: 'auto' }}
                              exit={{ opacity: 0, width: 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <div className="nav-label font-medium text-sm truncate">
                                {module.label}
                              </div>
                              <div className="nav-description text-xs text-muted-foreground truncate">
                                {module.description}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        <AnimatePresence>
                          {isExpanded && module.timestamp && (
                            <motion.div
                              className="nav-timestamp text-xs text-muted-foreground flex-shrink-0"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                            >
                              {formatTimeAgo(module.timestamp)}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>

                      {/* Active indicator */}
                      {isActive && (
                        <motion.div
                          className="absolute left-0 top-1/2 w-1 h-6 bg-primary rounded-r-full"
                          layoutId="activeIndicator"
                          initial={{ height: 0 }}
                          animate={{ height: 24 }}
                          style={{ 
                            background: module.color,
                            transform: 'translateY(-50%)'
                          }}
                        />
                      )}

                      {/* Tooltip for collapsed state */}
                      <AnimatePresence>
                        {!isExpanded && hoveredItem === module.id && (
                          <motion.div 
                            className="nav-tooltip absolute left-full top-1/2 ml-2 px-3 py-2 bg-popover text-popover-foreground rounded-lg border shadow-lg z-50 whitespace-nowrap"
                            initial={{ opacity: 0, x: -10, scale: 0.9 }}
                            animate={{ opacity: 1, x: 0, scale: 1 }}
                            exit={{ opacity: 0, x: -10, scale: 0.9 }}
                            transition={{ duration: 0.15 }}
                            style={{ transform: 'translateY(-50%)' }}
                          >
                            <div className="font-medium">{module.label}</div>
                            <div className="text-xs text-muted-foreground">{module.description}</div>
                            {module.timestamp && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {formatTimeAgo(module.timestamp)}
                              </div>
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.button>
                  </TooltipTrigger>
                  <TooltipContent side="right" className={isExpanded ? 'hidden' : ''}>
                    <div className="space-y-1">
                      <div className="font-medium">{module.label}</div>
                      <div className="text-xs text-muted-foreground">{module.description}</div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>
        </nav>

        {/* User Section Premium */}
        <motion.div 
          className="sidebar-footer p-4 border-t border-border"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <motion.div 
            className="user-card glass hover-lift rounded-xl p-3"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center gap-3">
              <div className="relative">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={userAvatar} alt={userName} />
                  <AvatarFallback 
                    className="text-white font-semibold"
                    style={{ background: 'var(--gradient-wave)' }}
                  >
                    {userName.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                <motion.span 
                  className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-background rounded-full"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [1, 0.7, 1]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </div>
              
              <AnimatePresence>
                {isExpanded && (
                  <motion.div 
                    className="user-info flex-1 min-w-0"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="user-name font-semibold text-sm truncate">{userName}</div>
                    <div className="user-role text-xs text-muted-foreground truncate">{userEmail}</div>
                    <Badge 
                      className="user-badge mt-1 text-xs text-white font-semibold"
                      style={{ 
                        background: 'var(--gradient-wave)',
                        border: 'none',
                        fontSize: '10px'
                      }}
                    >
                      🌊 PREMIUM
                    </Badge>
                  </motion.div>
                )}
              </AnimatePresence>
              
              <div className="user-actions flex flex-col gap-1">
                {/* Settings */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.button 
                      className="p-1.5 rounded-lg hover:bg-accent text-muted-foreground hover:text-foreground transition-colors"
                      whileHover={{ scale: 1.1, rotate: 90 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Settings size={14} />
                    </motion.button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    Configurações
                  </TooltipContent>
                </Tooltip>

                {/* Help */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.button 
                      className="p-1.5 rounded-lg hover:bg-accent text-muted-foreground hover:text-foreground transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <HelpCircle size={14} />
                    </motion.button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    Ajuda
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.aside>
    </TooltipProvider>
  );
};

export default OpenAISidebar;