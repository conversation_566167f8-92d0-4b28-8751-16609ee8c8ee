import React from 'react';
import { motion } from 'motion/react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  BarChart3,
  Stethoscope,
  Bot,
  Calendar,
  FileText,
  Pill,
  Activity,
  Sun,
  Moon,
  Menu,
  X
} from 'lucide-react';
import wellwaveLogo from 'figma:asset/0861b8c218cc0038134a72897c76dab4ae1a5c19.png';

interface SidebarModule {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}

interface MinimalSidebarProps {
  modules: SidebarModule[];
  activeModule: string;
  onModuleChange: (moduleId: string) => void;
  onNewAction: () => void;
  newActionLabel: string;
  darkMode: boolean;
  onToggleTheme: () => void;
  userName: string;
  userEmail: string;
  expanded: boolean;
  onToggle: (expanded: boolean) => void;
  isMobile?: boolean;
  isOpen?: boolean;
  onClose?: () => void;
}

export const MinimalSidebar: React.FC<MinimalSidebarProps> = ({
  modules,
  activeModule,
  onModuleChange,
  onNewAction,
  newActionLabel,
  darkMode,
  onToggleTheme,
  userName,
  userEmail,
  expanded,
  onToggle,
  isMobile = false,
  isOpen = false,
  onClose
}) => {
  const sidebarWidth = expanded ? 280 : 72;

  const handleModuleClick = (moduleId: string) => {
    onModuleChange(moduleId);
    if (isMobile && onClose) {
      onClose();
    }
  };

  return (
    <>
      {/* Mobile Backdrop */}
      {isMobile && isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        className={`
          ${isMobile ? 'fixed' : 'relative'} 
          top-0 left-0 h-full bg-white dark:bg-gray-900 
          border-r border-gray-200 dark:border-gray-700 
          flex flex-col z-50
          ${isMobile ? (isOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'}
        `}
        style={{ width: isMobile ? 280 : sidebarWidth }}
        animate={{ 
          width: isMobile ? 280 : sidebarWidth,
          x: isMobile ? (isOpen ? 0 : -280) : 0
        }}
        transition={{ duration: 0.2, ease: 'easeInOut' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          {expanded || isMobile ? (
            <div className="flex items-center gap-3">
              <img src={wellwaveLogo} alt="WellWave" className="h-8 w-8" />
              <div>
                <div className="font-semibold text-gray-900 dark:text-white">WellWave</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Sistema Médico</div>
              </div>
            </div>
          ) : (
            <img src={wellwaveLogo} alt="WellWave" className="h-8 w-8 mx-auto" />
          )}
          
          {isMobile ? (
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X size={20} />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onToggle(!expanded)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Menu size={20} />
            </Button>
          )}
        </div>

        {/* New Action Button */}
        <div className="p-4">
          <Button
            onClick={onNewAction}
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            size={expanded || isMobile ? "default" : "icon"}
          >
            {expanded || isMobile ? newActionLabel : <Stethoscope size={20} />}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-2">
          <div className="space-y-1">
            {modules.map((module) => {
              const Icon = module.icon;
              const isActive = activeModule === module.id;
              
              return (
                <motion.button
                  key={module.id}
                  onClick={() => handleModuleClick(module.id)}
                  className={`
                    w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors group
                    ${isActive 
                      ? 'bg-primary/10 text-primary border border-primary/20' 
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon size={20} className="flex-shrink-0" />
                  {(expanded || isMobile) && (
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{module.label}</div>
                      <div className="text-xs text-muted-foreground/70 truncate group-hover:text-muted-foreground">
                        {module.description}
                      </div>
                    </div>
                  )}
                  {isActive && (
                    <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0"></div>
                  )}
                </motion.button>
              );
            })}
          </div>
        </nav>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4">
          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size={expanded || isMobile ? "default" : "icon"}
            onClick={onToggleTheme}
            className="w-full mb-3 text-muted-foreground hover:text-foreground hover:bg-accent"
          >
            {darkMode ? <Sun size={20} /> : <Moon size={20} />}
            {(expanded || isMobile) && (
              <span className="ml-2">
                {darkMode ? 'Modo Claro' : 'Modo Escuro'}
              </span>
            )}
          </Button>

          {/* User Info */}
          {(expanded || isMobile) && (
            <div className="flex items-center gap-3 p-3 rounded-lg bg-accent/50 border">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-medium">
                  {userName.split(' ').map(n => n[0]).join('')}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-foreground truncate">
                  {userName}
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {userEmail}
                </div>
              </div>
              <Badge variant="secondary" className="text-xs bg-primary/10 text-primary border-primary/20">
                PRO
              </Badge>
            </div>
          )}
        </div>
      </motion.aside>
    </>
  );
};

export default MinimalSidebar;