import React from 'react';
import { useSmartSidebar } from '../../hooks/useSmartSidebar';
import { useMedical } from '../MedicalContext';

export const SidebarDebug: React.FC = () => {
  const sidebarState = useSmartSidebar();
  const { medicalColors } = useMedical();

  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg z-50 text-xs">
      <h3 className="font-bold mb-2">Sidebar Debug</h3>
      <div>Collapsed: {sidebarState.collapsed ? 'true' : 'false'}</div>
      <div>Width: {sidebarState.width}px</div>
      <div>Is Mobile: {sidebarState.isMobile ? 'true' : 'false'}</div>
      <div>Is Hovering: {sidebarState.isHovering ? 'true' : 'false'}</div>
      <div>Primary Color: {medicalColors.primary}</div>
      <button 
        onClick={sidebarState.toggle}
        className="mt-2 px-2 py-1 bg-blue-500 rounded text-xs"
      >
        Toggle Sidebar
      </button>
    </div>
  );
};