import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Plus, Settings, HelpCircle, Menu, ChevronRight } from 'lucide-react';
import { Button } from '../ui/button';
import { Sheet, SheetContent, SheetTrigger } from '../ui/sheet';
import { useSmartSidebar } from '../../hooks/useSmartSidebar';
import { useMedical } from '../MedicalContext';
import { sidebarConfig } from '../../config/ui/sidebar';
import { LogoButton } from './LogoButton';
import { SidebarItem } from './SidebarItem';
import { ResizeHandle } from './ResizeHandle';

// Tokens de animação profissionais
const professionalAnimations = {
  duration: {
    fast: 0.12,
    normal: 0.15,
    slow: 0.18
  },
  easing: {
    smooth: [0.4, 0, 0.2, 1] as const,
    out: [0.0, 0, 0.2, 1] as const
  }
};

interface SmartSidebarProps {
  modules: Array<{
    id: string;
    label: string;
    icon: any;
    description: string;
    color: string;
  }>;
  activeModule: string;
  onModuleChange: (moduleId: string) => void;
  contextualItems?: Array<{
    label: string;
    icon: any;
    color: string;
  }>;
  onNewAction?: () => void;
  newActionLabel?: string;
}

export const SmartSidebar: React.FC<SmartSidebarProps> = ({
  modules,
  activeModule,
  onModuleChange,
  contextualItems = [],
  onNewAction,
  newActionLabel = "Nova Ação"
}) => {
  const { medicalColors } = useMedical();
  const {
    collapsed,
    width,
    isHovering,
    isTransitioning,
    isMobile,
    isDrawerOpen,
    toggle,
    setWidth,
    setResizing,
    openDrawer,
    closeDrawer,
    onMouseEnter,
    onMouseLeave,
    onFocusIn,
    onFocusOut
  } = useSmartSidebar();
  
  // Sidebar content component refinado
  const SidebarContent = ({ inDrawer = false }: { inDrawer?: boolean }) => (
    <div className="flex flex-col h-full relative">
      {/* Logo/Header com melhor espaçamento */}
      <div className="border-b border-sidebar-border/50">
        <LogoButton
          collapsed={collapsed && !inDrawer}
          onToggle={inDrawer ? closeDrawer : toggle}
        />
      </div>
      
      {/* Quick Action Button - profissional */}
      {onNewAction && (
        <div className={`${collapsed && !inDrawer ? 'p-3' : 'p-5'} transition-all duration-150`}>
          <motion.div
            whileHover={{ opacity: 0.9 }}
            whileTap={{ opacity: 0.8 }}
            transition={{ 
              duration: professionalAnimations.duration.fast,
              ease: professionalAnimations.easing.smooth
            }}
          >
            <Button
              onClick={onNewAction}
              className={`w-full justify-center gap-3 text-white shadow-sm hover:shadow-md transition-all duration-150 border-0 font-semibold ${
                collapsed && !inDrawer 
                  ? 'h-12 px-3 rounded-xl' 
                  : 'h-14 px-6 rounded-2xl'
              }`}
              style={{ 
                background: medicalColors.gradients.primary,
                boxShadow: `0 4px 12px ${medicalColors.primary}20`
              }}
            >
              <Plus className={`${collapsed && !inDrawer ? 'h-5 w-5' : 'h-4 w-4'} shrink-0`} />
              
              <AnimatePresence mode="wait">
                {(!collapsed || inDrawer) && (
                  <motion.span
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    transition={{ 
                      duration: professionalAnimations.duration.normal,
                      ease: professionalAnimations.easing.smooth
                    }}
                    className="truncate"
                  >
                    {newActionLabel}
                  </motion.span>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </div>
      )}
      
      {/* Navigation com scroll melhorado */}
      <div className={`flex-1 overflow-y-auto smart-sidebar-scroll ${
        collapsed && !inDrawer ? 'px-3' : 'px-5'
      } transition-all duration-300`}>
        <div className="space-y-3">
          {/* Modules section header */}
          <div className={`${collapsed && !inDrawer ? 'px-2 py-1' : 'px-3 py-2'} transition-all duration-300`}>
            <AnimatePresence mode="wait">
              {(!collapsed || inDrawer) && (
                <motion.h3 
                  className="text-xs text-sidebar-foreground/70 uppercase tracking-wider font-bold"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                  transition={{ duration: 0.25, delay: 0.1 }}
                >
                  Módulos Médicos
                </motion.h3>
              )}
            </AnimatePresence>
          </div>
          
          {/* Modules list com animações escalonadas */}
          <div className="space-y-2">
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 4 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ 
                  delay: index * 0.03,
                  duration: professionalAnimations.duration.normal,
                  ease: professionalAnimations.easing.out
                }}
              >
                <SidebarItem
                  icon={module.icon}
                  label={module.label}
                  description={module.description}
                  color={module.color}
                  isActive={activeModule === module.id}
                  collapsed={collapsed && !inDrawer}
                  onClick={() => onModuleChange(module.id)}
                />
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Contextual items com melhor separação */}
        <AnimatePresence>
          {contextualItems.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ 
                duration: professionalAnimations.duration.slow,
                ease: professionalAnimations.easing.smooth
              }}
              className="mt-8 overflow-hidden"
            >
              {/* Divider elegante */}
              <motion.div 
                className="relative my-6"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
              >
                <div className="absolute inset-0 flex items-center">
                  <div 
                    className="w-full border-t"
                    style={{ 
                      borderImage: `linear-gradient(90deg, transparent, ${medicalColors.primary}30, transparent) 1`
                    }}
                  />
                </div>
              </motion.div>
              
              <div className={`${collapsed && !inDrawer ? 'px-2 py-1' : 'px-3 py-2'} transition-all duration-300`}>
                <AnimatePresence mode="wait">
                  {(!collapsed || inDrawer) && (
                    <motion.h3 
                      className="text-xs text-sidebar-foreground/70 uppercase tracking-wider font-bold"
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -5 }}
                      transition={{ duration: 0.25, delay: 0.1 }}
                    >
                      Acesso Rápido
                    </motion.h3>
                  )}
                </AnimatePresence>
              </div>
              
              <div className="space-y-2 mt-3">
                {contextualItems.map((item, index) => (
                  <motion.div
                    key={`contextual-${index}`}
                    initial={{ opacity: 0, y: 4 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ 
                      delay: (index * 0.03) + 0.1,
                      duration: professionalAnimations.duration.normal,
                      ease: professionalAnimations.easing.out
                    }}
                  >
                    <SidebarItem
                      icon={item.icon}
                      label={item.label}
                      color={item.color}
                      collapsed={collapsed && !inDrawer}
                    />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Bottom Section com visual premium */}
      <div 
        className={`border-t border-sidebar-border/50 backdrop-blur-sm ${
          collapsed && !inDrawer ? 'p-3' : 'p-5'
        } transition-all duration-300`}
        style={{ 
          background: `linear-gradient(135deg, ${medicalColors.primary}08, ${medicalColors.primaryLight}05, transparent)`
        }}
      >
        <div className="space-y-2">
          {[
            { icon: Settings, label: "Configurações", color: medicalColors.primary },
            { icon: HelpCircle, label: "Suporte Médico", color: medicalColors.success }
          ].map((item, index) => (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, y: 4 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: 0.1 + (index * 0.03),
                duration: professionalAnimations.duration.normal,
                ease: professionalAnimations.easing.out
              }}
            >
              <SidebarItem
                icon={item.icon}
                label={item.label}
                color={item.color}
                collapsed={collapsed && !inDrawer}
              />
            </motion.div>
          ))}
        </div>
      </div>
      
      {/* Collapsed state expand hint */}
      <AnimatePresence>
        {collapsed && !inDrawer && (
          <motion.div
            className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ 
              delay: 0.2,
              duration: professionalAnimations.duration.normal
            }}
          >
            <motion.div
              className="w-8 h-1 rounded-full"
              style={{ backgroundColor: `${medicalColors.primary}30` }}
              animate={{
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
  
  // Mobile drawer
  if (isMobile) {
    return (
      <>
        {/* Mobile trigger button premium */}
        <Sheet open={isDrawerOpen} onOpenChange={closeDrawer}>
          <SheetTrigger asChild>
            <motion.div
              className="fixed top-6 left-6 z-50 md:hidden"
              whileHover={{ opacity: 0.9 }}
              whileTap={{ opacity: 0.8 }}
              transition={{ 
                duration: professionalAnimations.duration.fast,
                ease: professionalAnimations.easing.smooth
              }}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={openDrawer}
                className="h-12 w-12 rounded-2xl backdrop-blur-md border shadow-lg"
                style={{
                  background: `linear-gradient(135deg, ${medicalColors.primary}15, ${medicalColors.primaryLight}10)`,
                  borderColor: `${medicalColors.primary}20`
                }}
              >
                <Menu className="h-5 w-5" style={{ color: medicalColors.primary }} />
              </Button>
            </motion.div>
          </SheetTrigger>
          
          <SheetContent 
            side="left" 
            className="p-0 w-80 bg-sidebar border-sidebar-border mobile-drawer-content backdrop-blur-xl"
            style={{
              background: `linear-gradient(135deg, ${medicalColors.primaryLight}05, ${medicalColors.primary}05)`
            }}
          >
            <SidebarContent inDrawer />
          </SheetContent>
        </Sheet>
      </>
    );
  }
  
  // Desktop sidebar premium
  return (
    <motion.div
      className="relative bg-sidebar border-r border-sidebar-border/50 flex flex-col smart-sidebar shadow-premium backdrop-blur-xl"
      style={{ 
        width: `${width}px`,
        minWidth: `${width}px`,
        maxWidth: `${width}px`,
        background: `linear-gradient(135deg, ${medicalColors.primaryLight}03, ${medicalColors.primary}03, transparent)`,
        height: 'var(--app-min-h)',
        transition: sidebarConfig.transitions.width
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onFocus={onFocusIn}
      onBlur={onFocusOut}
    >
      <SidebarContent />
      
      {/* Resize handle premium */}
      <ResizeHandle
        onResize={setWidth}
        onResizeStart={() => setResizing(true)}
        onResizeEnd={() => setResizing(false)}
        currentWidth={width}
        collapsed={collapsed}
      />
      
      {/* Hover glow effect */}
      <motion.div
        className="absolute inset-0 pointer-events-none rounded-l-3xl"
        style={{
          background: `linear-gradient(135deg, ${medicalColors.primary}06, ${medicalColors.primaryLight}03, transparent)`,
          filter: "blur(1px)"
        }}
        animate={{
          opacity: isHovering ? 1 : 0
        }}
        transition={{ duration: 0.4, ease: "easeInOut" }}
      />
      
      {/* Active transition indicator */}
      <AnimatePresence>
        {isTransitioning && (
          <motion.div
            className="absolute top-0 left-0 w-1 h-full"
            style={{
              background: `linear-gradient(180deg, ${medicalColors.primary}, ${medicalColors.primaryLight})`
            }}
            initial={{ scaleY: 0 }}
            animate={{ scaleY: 1 }}
            exit={{ scaleY: 0 }}
            transition={{ duration: 0.3 }}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};
