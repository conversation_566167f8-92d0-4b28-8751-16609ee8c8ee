import React from 'react';
import { motion } from 'motion/react';
import { Stethoscope, Bot, Calendar } from 'lucide-react';
import { useMedical } from '../MedicalContext';

export const SimpleSidebar: React.FC = () => {
  const { medicalColors } = useMedical();
  
  return (
    <motion.aside
      className="w-64 bg-white border-r border-gray-200 flex flex-col min-h-svh"
      style={{ 
        background: `linear-gradient(135deg, ${medicalColors.primaryLight}10, ${medicalColors.primary}05)`,
        minWidth: '256px',
        maxWidth: '256px'
      }}
      initial={{ opacity: 1 }}
      animate={{ opacity: 1 }}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div 
            className="w-10 h-10 rounded-lg flex items-center justify-center"
            style={{ backgroundColor: medicalColors.primary }}
          >
            <span className="text-white font-bold">W</span>
          </div>
          <div>
            <h1 className="font-bold text-lg">WellWave</h1>
            <p className="text-xs text-gray-500">Sistema Médico</p>
          </div>
        </div>
      </div>
      
      {/* Navigation */}
      <div className="flex-1 p-4">
        <div className="space-y-2">
          <button 
            className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 transition-colors"
            style={{ backgroundColor: `${medicalColors.primary}10` }}
          >
            <Stethoscope className="h-5 w-5" style={{ color: medicalColors.primary }} />
            <span>Anamnese</span>
          </button>
          
          <button className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 transition-colors">
            <Bot className="h-5 w-5" style={{ color: medicalColors.neurology }} />
            <span>Chat Médico</span>
          </button>
          
          <button className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 transition-colors">
            <Calendar className="h-5 w-5" style={{ color: medicalColors.warning }} />
            <span>Plantão</span>
          </button>
        </div>
      </div>
      
      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <p className="text-xs text-gray-500">Sidebar Test</p>
      </div>
    </motion.aside>
  );
};
