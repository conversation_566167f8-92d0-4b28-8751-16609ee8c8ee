import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { LucideIcon } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { sidebarConfig } from '../../config/ui/sidebar';

interface SidebarItemProps {
  icon: LucideIcon;
  label: string;
  description?: string;
  color: string;
  isActive?: boolean;
  collapsed: boolean;
  onClick?: () => void;
  className?: string;
}

export const SidebarItem: React.FC<SidebarItemProps> = ({
  icon: Icon,
  label,
  description,
  color,
  isActive = false,
  collapsed,
  onClick,
  className = ""
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.button
            className={`group w-full flex items-center gap-4 px-4 py-4 rounded-2xl text-left transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-sidebar-ring smart-sidebar-item relative overflow-hidden ${
              isActive
                ? 'shadow-lg'
                : 'text-sidebar-foreground hover:shadow-md'
            } ${className}`}
            style={isActive ? {
              background: `linear-gradient(135deg, ${color}15, ${color}08, transparent)`,
              borderLeft: `4px solid ${color}`,
              color: color
            } : {}}
            onClick={onClick}
            whileHover={{ 
              scale: 1.02, 
              x: collapsed ? 0 : 3,
              transition: { 
                duration: 0.2, 
                ease: "easeOut" 
              }
            }}
            whileTap={{ 
              scale: 0.98,
              transition: { duration: 0.1 }
            }}
            layout
          >
            {/* Background gradient for hover */}
            <motion.div
              className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100"
              style={{
                background: isActive 
                  ? `linear-gradient(135deg, ${color}20, ${color}10, transparent)`
                  : `linear-gradient(135deg, ${color}08, transparent)`
              }}
              transition={{ duration: 0.3 }}
            />
            
            {/* Icon container - sempre visível com melhor design */}
            <motion.div
              className={`shrink-0 relative z-10 flex items-center justify-center rounded-xl transition-all duration-300 ${
                collapsed ? 'w-12 h-12' : 'w-10 h-10'
              }`}
              style={isActive ? {
                backgroundColor: `${color}20`,
                boxShadow: `0 4px 12px ${color}25`
              } : {
                backgroundColor: `${color}10`,
              }}
              whileHover={{ 
                rotate: isActive ? 0 : 5,
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              animate={collapsed ? {
                scale: [1, 1.05, 1],
                rotate: [0, 2, -2, 0]
              } : {}}
              transition={collapsed ? {
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              } : {}}
            >
              <Icon 
                className={`transition-all duration-300 ${
                  collapsed ? 'h-5 w-5' : 'h-4 w-4'
                }`}
                style={{ color: isActive ? color : undefined }}
              />
              
              {/* Active indicator pulse */}
              {isActive && (
                <motion.div
                  className="absolute inset-0 rounded-xl border-2 opacity-40"
                  style={{ borderColor: color }}
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.4, 0.1, 0.4]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              )}
            </motion.div>
            
            {/* Label content with sophisticated reveal animation */}
            <AnimatePresence mode="wait">
              {!collapsed && (
                <motion.div 
                  className="flex-1 min-w-0 relative z-10"
                  initial={{ 
                    opacity: 0, 
                    width: 0, 
                    x: -15,
                    clipPath: "inset(0 100% 0 0)" 
                  }}
                  animate={{ 
                    opacity: 1, 
                    width: "auto", 
                    x: 0,
                    clipPath: "inset(0 0% 0 0)" 
                  }}
                  exit={{ 
                    opacity: 0, 
                    width: 0, 
                    x: -10,
                    clipPath: "inset(0 100% 0 0)" 
                  }}
                  transition={{ 
                    duration: 0.35, 
                    ease: "easeInOut",
                    opacity: { duration: 0.25, delay: 0.05 },
                    width: { duration: 0.35 },
                    x: { duration: 0.25 },
                    clipPath: { duration: 0.35 }
                  }}
                >
                  <motion.div 
                    className="font-semibold truncate text-base mb-0.5"
                    initial={{ y: 3, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: -3, opacity: 0 }}
                    transition={{ delay: 0.05 }}
                  >
                    {label}
                  </motion.div>
                  {description && (
                    <motion.div 
                      className="text-xs opacity-70 truncate"
                      initial={{ y: 3, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      exit={{ y: -3, opacity: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      {description}
                    </motion.div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Active state side indicator */}
            {isActive && !collapsed && (
              <motion.div
                className="w-1 h-8 rounded-full shrink-0"
                style={{ backgroundColor: color }}
                initial={{ scaleY: 0 }}
                animate={{ scaleY: 1 }}
                exit={{ scaleY: 0 }}
                transition={{ duration: 0.3 }}
              />
            )}
            
            {/* Hover ripple effect */}
            <motion.div
              className="absolute inset-0 rounded-2xl pointer-events-none"
              style={{
                background: `radial-gradient(circle at center, ${color}15 0%, transparent 70%)`
              }}
              initial={{ scale: 0, opacity: 0 }}
              whileHover={{ 
                scale: 1.2, 
                opacity: [0, 0.3, 0],
                transition: { duration: 0.4 }
              }}
            />
            
            {/* Click feedback */}
            <motion.div
              className="absolute inset-0 rounded-2xl pointer-events-none"
              style={{
                background: `linear-gradient(135deg, ${color}20, transparent)`
              }}
              initial={{ scale: 0.8, opacity: 0 }}
              whileTap={{ 
                scale: 1, 
                opacity: [0, 0.4, 0],
                transition: { duration: 0.2 }
              }}
            />
          </motion.button>
        </TooltipTrigger>
        
        {collapsed && (
          <TooltipContent side="right" className="ml-3 font-medium">
            <div>
              <p className="font-semibold text-sm">{label}</p>
              {description && (
                <p className="text-xs opacity-70 mt-0.5">{description}</p>
              )}
            </div>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
};