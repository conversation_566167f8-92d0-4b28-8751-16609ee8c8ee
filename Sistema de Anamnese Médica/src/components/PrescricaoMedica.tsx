import React from 'react';
import { motion } from 'motion/react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from './ui/card';
import { FileText, Pill, User, Calendar } from 'lucide-react';
import { useMedical } from './MedicalContext';

export function PrescricaoMedica() {
  const { medicalColors } = useMedical();
  
  return (
    <div 
      className="flex flex-col h-full"
      style={{ 
        background: `linear-gradient(135deg, ${medicalColors.primaryLight}05 0%, ${medicalColors.primary}05 50%, ${medicalColors.primaryDark}05 100%)`
      }}
    >
      {/* Header */}
      <div className="border-b bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm shadow-sm">
        <div className="content-max px-4 md:px-6 py-4 md:py-6 flex items-center gap-3">
          <div className="p-3 rounded-xl" style={{ backgroundColor: `${medicalColors.success}20` }}>
            <FileText className="h-6 w-6" style={{ color: medicalColors.success }} />
          </div>
          <div>
            <h1 
              className="text-2xl font-semibold"
              style={{
                background: `linear-gradient(135deg, ${medicalColors.success}, ${medicalColors.primary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              Prescrições Médicas
            </h1>
            <p className="text-sm text-muted-foreground">
              Sistema de receituários digitais
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1">
        <div className="content-max px-4 md:px-6 py-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Pill className="h-5 w-5" style={{ color: medicalColors.success }} />
                Módulo em Desenvolvimento
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <div className="mb-4">
                  <div 
                    className="w-16 h-16 mx-auto rounded-full flex items-center justify-center"
                    style={{ backgroundColor: `${medicalColors.success}20` }}
                  >
                    <FileText className="h-8 w-8" style={{ color: medicalColors.success }} />
                  </div>
                </div>
                <h3 className="text-lg font-semibold mb-2">Prescrições Médicas</h3>
                <p className="text-muted-foreground mb-6">
                  Este módulo permitirá criar receituários digitais com integração 
                  à base de medicamentos e histórico do paciente.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <User className="h-4 w-4" style={{ color: medicalColors.primary }} />
                      <span className="font-medium">Dados do Paciente</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Integração automática com dados da anamnese
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Pill className="h-4 w-4" style={{ color: medicalColors.gastro }} />
                      <span className="font-medium">Base de Medicamentos</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Autocomplete com medicamentos do SUS
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <FileText className="h-4 w-4" style={{ color: medicalColors.success }} />
                      <span className="font-medium">Templates</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Modelos de receita por especialidade
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4" style={{ color: medicalColors.warning }} />
                      <span className="font-medium">Histórico</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Controle de prescrições anteriores
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
        </div>
      </div>
    </div>
  );
}
