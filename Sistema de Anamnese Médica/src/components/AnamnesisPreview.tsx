import { motion } from 'motion/react'
import { FileText, Copy, Download, Eye, CheckCircle, AlertCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Separator } from './ui/separator'
import { useMedical } from './MedicalContext'
import { toast } from 'sonner'

export function AnamnesisPreview() {
  const { anamnesisData, exportAnamnese, validateData, medicalColors } = useMedical()

  const handleCopy = async () => {
    try {
      const anamnesisText = exportAnamnese()
      await navigator.clipboard.writeText(anamnesisText)
      toast.success('Anamnese copiada para a área de transferência!')
    } catch (error) {
      toast.error('Erro ao copiar anamnese')
    }
  }

  const handleDownload = () => {
    try {
      const anamnesisText = exportAnamnese()
      const blob = new Blob([anamnesisText], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      
      const timestamp = new Date().toISOString().slice(0, 10)
      link.download = `anamnese_${anamnesisData.paciente.id}_${timestamp}.txt`
      link.href = url
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast.success('Anamnese baixada com sucesso!')
    } catch (error) {
      toast.error('Erro ao baixar anamnese')
    }
  }

  const getCompletionStatus = () => {
    const sections = [
      { name: 'Dados do Paciente', completed: !!(anamnesisData.paciente.faixaEtaria && anamnesisData.paciente.sexoBiologico) },
      { name: 'Queixa Principal', completed: !!(anamnesisData.queixaPrincipal.queixaPrincipal && anamnesisData.queixaPrincipal.duracaoSintomas) },
      { name: 'História Médica', completed: !!(anamnesisData.historicoMedico.comorbidades.length || anamnesisData.historicoMedico.medicamentosUso.length) },
      { name: 'Exame Físico', completed: !!(Object.values(anamnesisData.exameFisico.sinaisVitais).some(v => v.trim()) || Object.values(anamnesisData.exameFisico.exameFisico).some(v => v.trim())) },
      { name: 'Medicamentos', completed: !!(anamnesisData.medicamentos.prescricaoAtual.length > 0) }
    ]
    
    return sections
  }

  const isComplete = validateData()
  const sections = getCompletionStatus()
  const completedSections = sections.filter(s => s.completed).length

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-l-4 openai-card" style={{ borderLeftColor: medicalColors.neurology }}>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <div 
              className="p-2 rounded-lg"
              style={{ backgroundColor: `${medicalColors.neurology}20` }}
            >
              <FileText className="h-5 w-5" style={{ color: medicalColors.neurology }} />
            </div>
            <div>
              <CardTitle className="text-lg">Revisão Final</CardTitle>
              <CardDescription>Anamnese completa pronta para uso clínico</CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Completion Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-base">Status de Completude</CardTitle>
            <Badge variant={isComplete ? "default" : "secondary"} className="gap-1">
              {isComplete ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
              {completedSections}/{sections.length} Seções
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {sections.map((section, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className={`p-3 rounded-lg border ${
                  section.completed 
                    ? 'bg-green-50 border-green-200 text-green-800' 
                    : 'bg-muted border-muted-foreground/20'
                }`}
              >
                <div className="flex items-center gap-2">
                  {section.completed ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-medium">{section.name}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Patient Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Identificação do Paciente</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">ID:</span>
              <div className="font-mono">{anamnesisData.paciente.id}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Faixa Etária:</span>
              <div>{anamnesisData.paciente.faixaEtaria || 'Não informado'}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Sexo:</span>
              <div>{anamnesisData.paciente.sexoBiologico === 'F' ? 'Feminino' : anamnesisData.paciente.sexoBiologico === 'M' ? 'Masculino' : 'Não informado'}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Data/Hora:</span>
              <div>{anamnesisData.paciente.timestamp.toLocaleString('pt-BR')}</div>
            </div>
          </div>
          {anamnesisData.paciente.gestante && (
            <Badge className="bg-pink-100 text-pink-800">Gestante</Badge>
          )}
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-base">Visualização da Anamnese</CardTitle>
            <CardDescription>Preview do documento final</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCopy} className="gap-2 openai-button">
              <Copy className="h-4 w-4" />
              Copiar
            </Button>
            <Button onClick={handleDownload} className="gap-2 openai-button">
              <Download className="h-4 w-4" />
              Baixar TXT
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="max-h-96 overflow-y-auto openai-scrollbar p-4 bg-muted/30 rounded-lg font-mono text-sm whitespace-pre-wrap">
            {exportAnamnese()}
          </div>
        </CardContent>
      </Card>

      {/* Validation Warning */}
      {!isComplete && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
        >
          <Card className="bg-yellow-50 border-yellow-200">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div className="space-y-1">
                  <div className="font-medium text-yellow-800">Anamnese Incompleta</div>
                  <div className="text-sm text-yellow-700">
                    Algumas seções ainda não foram preenchidas. Complete as informações obrigatórias para finalizar a anamnese.
                  </div>
                  <div className="text-xs text-yellow-600 mt-2">
                    Seções obrigatórias: Dados do Paciente e Queixa Principal
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Success Message */}
      {isComplete && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
        >
          <Card className="bg-green-50 border-green-200">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
                <div className="space-y-1">
                  <div className="font-medium text-green-800">Anamnese Completa</div>
                  <div className="text-sm text-green-700">
                    A anamnese foi preenchida com sucesso e está pronta para ser utilizada na prática clínica.
                  </div>
                  <div className="text-xs text-green-600 mt-2">
                    Você pode copiar ou baixar o documento final usando os botões acima.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}

export default AnamnesisPreview