import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import { Ava<PERSON>, AvatarFallback } from "./ui/avatar";
import { 
  Bot,
  User,
  Send,
  Sparkles,
  Heart,
  Brain,
  Stethoscope,
  FileText,
  Pill,
  AlertTriangle,
  Plus,
  MessageSquare
} from "lucide-react";
import { motion, AnimatePresence } from "motion/react";
import { useMedical } from "./MedicalContext";

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  category?: 'diagnostic' | 'treatment' | 'general';
}

export function ChatMedico() {
  const { medicalColors } = useMedical();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Olá! Sou seu assistente médico inteligente. Como posso ajudá-lo hoje? Posso auxiliar com diagnósticos, protocolos, medicamentos e orientações clínicas.',
      timestamp: new Date(),
      category: 'general'
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const quickQuestions = [
    { 
      icon: Heart, 
      text: "Protocolo para dor torácica aguda",
      color: medicalColors.cardiology,
      category: 'diagnostic' as const
    },
    { 
      icon: Brain, 
      text: "Avaliação de cefaleia secundária", 
      color: medicalColors.neurology,
      category: 'diagnostic' as const
    },
    { 
      icon: Pill, 
      text: "Dosagem de antibióticos em pediatria",
      color: medicalColors.gastro,
      category: 'treatment' as const
    },
    { 
      icon: AlertTriangle, 
      text: "Sinais de alarme em emergência",
      color: medicalColors.danger,
      category: 'general' as const
    }
  ];

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simular resposta do assistente
    setTimeout(() => {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: generateResponse(content),
        timestamp: new Date(),
        category: categorizeMessage(content)
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const generateResponse = (question: string): string => {
    const lowerQ = question.toLowerCase();
    
    if (lowerQ.includes('dor torácica') || lowerQ.includes('peito')) {
      return `**Protocolo para Dor Torácica Aguda:**

**Avaliação Inicial:**
• ECG em até 10 minutos
• Sinais vitais e oximetria
• História focada: início, duração, caráter, irradiação
• Exame físico cardiovascular e pulmonar

**Estratificação de Risco:**
• Troponina seriada (0h, 3h, 6h)
• Escore HEART ou GRACE
• Radiografia de tórax

**Condutas por Probabilidade:**
• **Alta probabilidade**: AAS, clopidogrel, heparina, estatina
• **Probabilidade intermediária**: Observação, teste de esforço
• **Baixa probabilidade**: Alta com orientações

**Critérios de Internação:**
• Alterações no ECG
• Troponina elevada
• Instabilidade hemodinâmica
• Escore HEART ≥ 4`;
    }
    
    if (lowerQ.includes('cefaleia') || lowerQ.includes('dor de cabeça')) {
      return `**Avaliação de Cefaleia - Sinais de Alarme:**

**Sinais de Alarme (Red Flags):**
• Início súbito e intenso ("pior dor da vida")
• Cefaleia progressiva ou mudança do padrão
• Febre, rigidez nucal, alterações neurológicas
• Idade > 50 anos com cefaleia nova
• Cefaleia pós-traumatismo

**Investigação:**
• TC crânio se sinais de alarme
• Punção lombar se suspeita de HSA
• Exames laboratoriais conforme contexto

**Diagnósticos Diferenciais:**
• HSA, meningite, tumor cerebral
• Hipertensão intracraniana
• Arterite temporal (> 50 anos)`;
    }
    
    if (lowerQ.includes('antibiótico') || lowerQ.includes('pediatria')) {
      return `**Dosagem de Antibióticos em Pediatria:**

**Amoxicilina:**
• Leve-moderada: 25-45 mg/kg/dia, 8/8h
• Grave: 80-90 mg/kg/dia, 8/8h

**Azitromicina:**
• 10 mg/kg/dia, 1x/dia por 3 dias

**Cefalexina:**
• 25-50 mg/kg/dia, 6/6h

**Ceftriaxona (EV):**
• 50-75 mg/kg/dia, 1x/dia
• Meningite: 100 mg/kg/dia

**Importante:**
• Sempre ajustar pela função renal
• Considerar peso ideal em obesos
• Duração conforme protocolo institucional`;
    }
    
    return `Obrigado pela sua pergunta sobre "${question}". 

Como assistente médico, posso ajudá-lo com:
• Protocolos e guidelines atualizados
• Dosagens medicamentosas
• Diagnósticos diferenciais
• Condutas em emergência
• Interpretação de exames

Para uma resposta mais específica, você poderia reformular sua pergunta ou fornecer mais detalhes sobre o caso clínico?

Lembre-se: Esta é uma ferramenta de apoio. Decisões clínicas devem sempre considerar a avaliação completa do paciente e seu julgamento médico.`;
  };

  const categorizeMessage = (content: string): 'diagnostic' | 'treatment' | 'general' => {
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('diagnóstico') || lowerContent.includes('sintomas') || lowerContent.includes('exame')) {
      return 'diagnostic';
    }
    if (lowerContent.includes('tratamento') || lowerContent.includes('medicamento') || lowerContent.includes('dose')) {
      return 'treatment';
    }
    return 'general';
  };

  const getCategoryColor = (category?: string) => {
    switch (category) {
      case 'diagnostic': return medicalColors.neurology;
      case 'treatment': return medicalColors.success;
      default: return medicalColors.primary;
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputMessage);
    }
  };

  return (
    <div 
      className="flex flex-col h-full"
      style={{ 
        background: `linear-gradient(135deg, ${medicalColors.primaryLight}05 0%, ${medicalColors.primary}05 50%, ${medicalColors.primaryDark}05 100%)`
      }}
    >
      {/* Header */}
      <div className="border-b bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm p-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div 
              className="p-3 rounded-xl"
              style={{ 
                background: `linear-gradient(135deg, ${medicalColors.neurology}20, ${medicalColors.primary}20)`
              }}
            >
              <Bot className="h-6 w-6" style={{ color: medicalColors.neurology }} />
            </div>
            <div>
              <h1 
                className="text-2xl font-semibold"
                style={{
                  background: `linear-gradient(135deg, ${medicalColors.neurology}, ${medicalColors.primary})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >
                Assistente Médico IA
              </h1>
              <p className="text-sm text-muted-foreground">
                Suporte inteligente para decisões clínicas
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge 
              className="text-xs"
              style={{ 
                backgroundColor: `${medicalColors.success}20`, 
                color: medicalColors.success 
              }}
            >
              <Sparkles className="h-3 w-3 mr-1" />
              Online
            </Badge>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.type === 'assistant' && (
                <Avatar className="h-8 w-8 mt-2">
                  <AvatarFallback 
                    style={{ backgroundColor: `${medicalColors.neurology}20` }}
                  >
                    <Bot className="h-4 w-4" style={{ color: medicalColors.neurology }} />
                  </AvatarFallback>
                </Avatar>
              )}
              
              <div className={`max-w-2xl ${message.type === 'user' ? 'order-1' : ''}`}>
                <motion.div
                  whileHover={{ scale: 1.01 }}
                  className={`p-4 rounded-2xl shadow-sm ${
                    message.type === 'user'
                      ? 'text-white'
                      : 'bg-white dark:bg-gray-800 border'
                  }`}
                  style={message.type === 'user' ? {
                    background: `linear-gradient(135deg, ${medicalColors.primary}, ${medicalColors.neurology})`
                  } : {}}
                >
                  {message.category && message.type === 'assistant' && (
                    <Badge 
                      className="text-xs mb-3"
                      style={{ 
                        backgroundColor: `${getCategoryColor(message.category)}20`,
                        color: getCategoryColor(message.category)
                      }}
                    >
                      {message.category === 'diagnostic' && <Stethoscope className="h-3 w-3 mr-1" />}
                      {message.category === 'treatment' && <Pill className="h-3 w-3 mr-1" />}
                      {message.category === 'general' && <MessageSquare className="h-3 w-3 mr-1" />}
                      {message.category === 'diagnostic' ? 'Diagnóstico' : 
                       message.category === 'treatment' ? 'Tratamento' : 'Geral'}
                    </Badge>
                  )}
                  
                  <div className="text-sm whitespace-pre-line leading-relaxed">
                    {message.content}
                  </div>
                  
                  <div className={`text-xs mt-2 ${
                    message.type === 'user' ? 'text-white/70' : 'text-muted-foreground'
                  }`}>
                    {message.timestamp.toLocaleTimeString('pt-BR', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </motion.div>
              </div>
              
              {message.type === 'user' && (
                <Avatar className="h-8 w-8 mt-2">
                  <AvatarFallback 
                    style={{ backgroundColor: `${medicalColors.primary}20` }}
                  >
                    <User className="h-4 w-4" style={{ color: medicalColors.primary }} />
                  </AvatarFallback>
                </Avatar>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Typing indicator */}
        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex gap-3 justify-start"
          >
            <Avatar className="h-8 w-8 mt-2">
              <AvatarFallback 
                style={{ backgroundColor: `${medicalColors.neurology}20` }}
              >
                <Bot className="h-4 w-4" style={{ color: medicalColors.neurology }} />
              </AvatarFallback>
            </Avatar>
            <div className="bg-white dark:bg-gray-800 border p-4 rounded-2xl shadow-sm">
              <div className="flex space-x-1">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: medicalColors.primary }}
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1, repeat: Infinity, delay: i * 0.3 }}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {/* Quick Questions */}
        {messages.length === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-dashed">
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Sparkles className="h-4 w-4" style={{ color: medicalColors.primary }} />
                  Perguntas Frequentes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {quickQuestions.map((question, index) => (
                    <motion.button
                      key={index}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => sendMessage(question.text)}
                      className="flex items-center gap-3 p-3 rounded-lg text-left border hover:shadow-md transition-all duration-200"
                      style={{ borderColor: `${question.color}30` }}
                    >
                      <div 
                        className="p-2 rounded-lg"
                        style={{ backgroundColor: `${question.color}20` }}
                      >
                        <question.icon className="h-4 w-4" style={{ color: question.color }} />
                      </div>
                      <span className="text-sm font-medium">{question.text}</span>
                    </motion.button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>

      {/* Input Area */}
      <div className="border-t bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm p-6">
        <div className="flex gap-3 max-w-4xl mx-auto">
          <div className="flex-1 relative">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Digite sua pergunta médica..."
              className="pr-12 h-12 text-sm"
              disabled={isTyping}
            />
            <motion.div 
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Button
                size="sm"
                variant="ghost"
                onClick={() => sendMessage(inputMessage)}
                disabled={!inputMessage.trim() || isTyping}
                className="h-8 w-8 p-0"
              >
                <Send className="h-4 w-4" style={{ color: medicalColors.primary }} />
              </Button>
            </motion.div>
          </div>
        </div>
        
        <div className="text-xs text-muted-foreground text-center mt-3">
          Esta é uma ferramenta de apoio. Sempre considere sua avaliação clínica completa.
        </div>
      </div>
    </div>
  );
}