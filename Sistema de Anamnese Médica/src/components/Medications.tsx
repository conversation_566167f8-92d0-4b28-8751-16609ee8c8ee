import { motion } from 'motion/react'
import { Pill, Plus, X } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'
import { Input } from './ui/input'
import { Button } from './ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Textarea } from './ui/textarea'
import { useMedical } from './MedicalContext'

export function Medications() {
  const { anamnesisData, updateNestedAnamnesisData, medicalColors } = useMedical()

  const addMedication = () => {
    const newMedication = {
      medicamento: '',
      dose: '',
      via: '',
      frequencia: '',
      duracao: '',
      orientacoes: ''
    }
    
    const updatedPrescricao = [...anamnesisData.medicamentos.prescricaoAtual, newMedication]
    updateNestedAnamnesisData('medicamentos', 'prescricaoAtual', updatedPrescricao)
  }

  const removeMedication = (index: number) => {
    const updatedPrescricao = anamnesisData.medicamentos.prescricaoAtual.filter((_, i) => i !== index)
    updateNestedAnamnesisData('medicamentos', 'prescricaoAtual', updatedPrescricao)
  }

  const updateMedication = (index: number, field: string, value: string) => {
    const updatedPrescricao = [...anamnesisData.medicamentos.prescricaoAtual]
    updatedPrescricao[index] = { ...updatedPrescricao[index], [field]: value }
    updateNestedAnamnesisData('medicamentos', 'prescricaoAtual', updatedPrescricao)
  }

  const viaOptions = ['VO', 'IV', 'IM', 'SC', 'SL', 'Tópica', 'Inalatória', 'Retal']
  const frequenciaOptions = ['1x/dia', '2x/dia', '3x/dia', '4x/dia', '6/6h', '8/8h', '12/12h', 'SOS', 'Se necessário']

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-l-4 openai-card" style={{ borderLeftColor: medicalColors.success }}>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <div 
              className="p-2 rounded-lg"
              style={{ backgroundColor: `${medicalColors.success}20` }}
            >
              <Pill className="h-5 w-5" style={{ color: medicalColors.success }} />
            </div>
            <div>
              <CardTitle className="text-lg">Medicamentos</CardTitle>
              <CardDescription>Prescrição médica e orientações</CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Current Prescription */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-base">Prescrição Atual</CardTitle>
            <CardDescription>Medicamentos prescritos na consulta</CardDescription>
          </div>
          <Button onClick={addMedication} size="sm" className="gap-2 openai-button">
            <Plus className="h-4 w-4" />
            Adicionar Medicamento
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {anamnesisData.medicamentos.prescricaoAtual.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Pill className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Nenhum medicamento prescrito ainda</p>
              <p className="text-sm">Clique em "Adicionar Medicamento" para começar</p>
            </div>
          ) : (
            <div className="space-y-6">
              {anamnesisData.medicamentos.prescricaoAtual.map((medication, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-4 border rounded-lg space-y-4"
                >
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Medicamento {index + 1}</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMedication(index)}
                      className="text-destructive hover:text-destructive"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Medicamento *</Label>
                      <Input
                        placeholder="Nome do medicamento"
                        value={medication.medicamento}
                        onChange={(e) => updateMedication(index, 'medicamento', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Dose *</Label>
                      <Input
                        placeholder="500mg, 10ml, etc."
                        value={medication.dose}
                        onChange={(e) => updateMedication(index, 'dose', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Via de Administração</Label>
                      <Select
                        value={medication.via}
                        onValueChange={(value) => updateMedication(index, 'via', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a via" />
                        </SelectTrigger>
                        <SelectContent>
                          {viaOptions.map((via) => (
                            <SelectItem key={via} value={via}>{via}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Frequência *</Label>
                      <Select
                        value={medication.frequencia}
                        onValueChange={(value) => updateMedication(index, 'frequencia', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a frequência" />
                        </SelectTrigger>
                        <SelectContent>
                          {frequenciaOptions.map((freq) => (
                            <SelectItem key={freq} value={freq}>{freq}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Duração</Label>
                      <Input
                        placeholder="7 dias, 30 dias, contínuo, etc."
                        value={medication.duracao}
                        onChange={(e) => updateMedication(index, 'duracao', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Orientações Especiais</Label>
                    <Textarea
                      placeholder="Tomar com alimentos, não dirigir após uso, etc."
                      value={medication.orientacoes}
                      onChange={(e) => updateMedication(index, 'orientacoes', e.target.value)}
                      rows={2}
                      className="resize-none"
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary */}
      {anamnesisData.medicamentos.prescricaoAtual.length > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-gradient-to-r from-success/5 to-success/10 border-success/20">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <div 
                  className="p-2 rounded-full flex-shrink-0"
                  style={{ backgroundColor: medicalColors.success }}
                >
                  <Pill className="h-4 w-4 text-white" />
                </div>
                <div className="space-y-2 flex-1">
                  <div className="font-medium">Resumo da Prescrição</div>
                  <div className="text-sm space-y-1">
                    {anamnesisData.medicamentos.prescricaoAtual.map((med, index) => (
                      med.medicamento && (
                        <div key={index}>
                          <strong>{index + 1}. {med.medicamento}</strong>
                          {med.dose && <> - {med.dose}</>}
                          {med.via && <> ({med.via})</>}
                          {med.frequencia && <> - {med.frequencia}</>}
                          {med.duracao && <> por {med.duracao}</>}
                        </div>
                      )
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}

export default Medications