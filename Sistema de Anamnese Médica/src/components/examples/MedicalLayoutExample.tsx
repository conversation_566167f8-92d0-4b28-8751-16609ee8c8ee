import * as React from "react";
import { But<PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { MedicalStatus, CriticalStatus, UrgentStatus, RoutineStatus, StableStatus } from "../ui/medical-status";
import { 
  MedicalLayout, 
  MedicalDashboardLayout, 
  PatientEntryLayout,
  EmergencyLayout 
} from "../layouts/MedicalLayout";
import { 
  Stethoscope, 
  Heart, 
  Activity, 
  AlertTriangle, 
  CheckCircle,
  User,
  Calendar,
  FileText
} from "lucide-react";

export function MedicalLayoutExample() {
  return (
    <div className="space-y-8 p-6">
      <h1 className="text-medical-heading-1">Sistema de Interface Premium - Exemplos</h1>
      
      {/* Medical Status Examples */}
      <section className="space-y-4">
        <h2 className="text-medical-heading-2">Indicadores de Status Médico</h2>
        <div className="flex flex-wrap gap-4">
          <CriticalStatus label="Crítico" />
          <UrgentStatus label="Urgente" />
          <RoutineStatus label="Rotina" />
          <StableStatus label="Estável" />
          <MedicalStatus status="processing" label="Processando" pulse />
        </div>
      </section>

      {/* Button Examples */}
      <section className="space-y-4">
        <h2 className="text-medical-heading-2">Botões Médicos Aprimorados</h2>
        <div className="flex flex-wrap gap-4">
          <Button variant="medical" size="medical">
            <Stethoscope className="h-4 w-4" />
            Ação Médica
          </Button>
          <Button variant="medicalCritical" size="medical">
            <AlertTriangle className="h-4 w-4" />
            Emergência
          </Button>
          <Button variant="medicalUrgent" size="medical">
            <Activity className="h-4 w-4" />
            Urgente
          </Button>
          <Button variant="medicalSuccess" size="medical">
            <CheckCircle className="h-4 w-4" />
            Concluído
          </Button>
          <Button variant="medicalGlass" size="medical">
            <Heart className="h-4 w-4" />
            Glass Effect
          </Button>
          <Button variant="medicalOutline" size="medical">
            <User className="h-4 w-4" />
            Outline
          </Button>
        </div>
      </section>

      {/* Card Examples */}
      <section className="space-y-4">
        <h2 className="text-medical-heading-2">Cards Médicos Premium</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card variant="medical" size="medical">
            <Card.Header>
              <Card.Title className="text-medical-heading-3">Card Médico Padrão</Card.Title>
              <Card.Description className="text-medical-body-small">
                Card com estilo médico profissional
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <p className="text-medical-body">
                Conteúdo do card com tipografia otimizada para leitura médica.
              </p>
            </Card.Content>
          </Card>

          <Card variant="medicalGlass" size="medical">
            <Card.Header>
              <Card.Title className="text-medical-heading-3">Card Glass Morphism</Card.Title>
              <Card.Description className="text-medical-body-small">
                Efeito glass morphism premium
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <p className="text-medical-body">
                Card com efeito de vidro e backdrop blur para profundidade visual.
              </p>
            </Card.Content>
          </Card>

          <Card variant="medicalCritical" size="medical">
            <Card.Header>
              <Card.Title className="text-medical-heading-3">Card Crítico</Card.Title>
              <Card.Description className="text-medical-body-small">
                Para informações críticas
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <p className="text-medical-body">
                Card com destaque visual para informações críticas do paciente.
              </p>
            </Card.Content>
          </Card>
        </div>
      </section>

      {/* Layout Examples */}
      <section className="space-y-4">
        <h2 className="text-medical-heading-2">Layouts Médicos Otimizados</h2>
        
        {/* Dashboard Layout */}
        <div className="border rounded-lg overflow-hidden">
          <div className="bg-medical-color-primary-50 p-4 border-b">
            <h3 className="text-medical-heading-3">Dashboard Médico</h3>
          </div>
          <MedicalDashboardLayout
            title="Dashboard Principal"
            subtitle="Visão geral do sistema médico"
            status="routine"
            statusLabel="Sistema Operacional"
            actions={
              <Button variant="medical" size="sm">
                <Activity className="h-4 w-4" />
                Atualizar
              </Button>
            }
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card variant="medical" size="medicalSmall">
                <Card.Content className="text-center">
                  <Heart className="h-8 w-8 mx-auto mb-2 text-medical-color-primary" />
                  <p className="text-medical-body-small">Pacientes Ativos</p>
                  <p className="text-medical-heading-2">24</p>
                </Card.Content>
              </Card>
              <Card variant="medical" size="medicalSmall">
                <Card.Content className="text-center">
                  <Calendar className="h-8 w-8 mx-auto mb-2 text-medical-color-success" />
                  <p className="text-medical-body-small">Consultas Hoje</p>
                  <p className="text-medical-heading-2">12</p>
                </Card.Content>
              </Card>
              <Card variant="medical" size="medicalSmall">
                <Card.Content className="text-center">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-medical-color-urgent" />
                  <p className="text-medical-body-small">Pendências</p>
                  <p className="text-medical-heading-2">3</p>
                </Card.Content>
              </Card>
              <Card variant="medical" size="medicalSmall">
                <Card.Content className="text-center">
                  <Stethoscope className="h-8 w-8 mx-auto mb-2 text-medical-color-info" />
                  <p className="text-medical-body-small">Exames</p>
                  <p className="text-medical-heading-2">8</p>
                </Card.Content>
              </Card>
            </div>
          </MedicalDashboardLayout>
        </div>

        {/* Emergency Layout */}
        <div className="border rounded-lg overflow-hidden">
          <div className="bg-medical-color-critical-50 p-4 border-b">
            <h3 className="text-medical-heading-3">Layout de Emergência</h3>
          </div>
          <EmergencyLayout
            title="Protocolo de Emergência"
            subtitle="Ações críticas para situações de emergência"
            status="critical"
            statusLabel="EMERGÊNCIA ATIVA"
            actions={
              <Button variant="medicalCritical" size="medical">
                <AlertTriangle className="h-4 w-4" />
                Ação Imediata
              </Button>
            }
          >
            <Card variant="medicalCritical" size="medicalLarge">
              <Card.Header>
                <Card.Title className="text-medical-heading-2 text-medical-color-critical">
                  ⚠️ Protocolo de Emergência Ativado
                </Card.Title>
              </Card.Header>
              <Card.Content>
                <p className="text-medical-body mb-4">
                  Sistema em modo de emergência. Todas as funcionalidades críticas estão disponíveis.
                </p>
                <div className="flex gap-4">
                  <Button variant="medicalCritical" size="medical">
                    <Heart className="h-4 w-4" />
                    Reanimação
                  </Button>
                  <Button variant="medicalUrgent" size="medical">
                    <Activity className="h-4 w-4" />
                    Monitoramento
                  </Button>
                </div>
              </Card.Content>
            </Card>
          </EmergencyLayout>
        </div>
      </section>

      {/* Typography Examples */}
      <section className="space-y-4">
        <h2 className="text-medical-heading-2">Tipografia Médica Otimizada</h2>
        <div className="space-y-4">
          <div>
            <h1 className="text-medical-display">Display Text</h1>
            <p className="text-medical-caption">Para títulos principais e destaques</p>
          </div>
          <div>
            <h2 className="text-medical-heading-1">Heading 1</h2>
            <p className="text-medical-caption">Para seções principais</p>
          </div>
          <div>
            <h3 className="text-medical-heading-2">Heading 2</h3>
            <p className="text-medical-caption">Para subseções</p>
          </div>
          <div>
            <h4 className="text-medical-heading-3">Heading 3</h4>
            <p className="text-medical-caption">Para títulos de componentes</p>
          </div>
          <div>
            <p className="text-medical-body">
              Texto do corpo otimizado para leitura médica com line-height de 1.6 para melhor legibilidade.
            </p>
            <p className="text-medical-caption">Para texto principal e descrições</p>
          </div>
          <div>
            <p className="text-medical-body-small">
              Texto pequeno para informações secundárias e metadados.
            </p>
            <p className="text-medical-caption">Para informações secundárias</p>
          </div>
          <div>
            <p className="text-medical-mono">
              Texto monospace para valores numéricos e códigos médicos.
            </p>
            <p className="text-medical-caption">Para valores numéricos e códigos</p>
          </div>
        </div>
      </section>
    </div>
  );
}
