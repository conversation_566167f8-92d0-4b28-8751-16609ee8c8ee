import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { 
  ChevronRight, 
  ChevronLeft, 
  User, 
  MessageSquare, 
  History, 
  Stethoscope, 
  Pill,
  FileText,
  CheckCircle,
  Circle,
  Download,
  Copy,
  RotateCcw,
  Heart,
  Brain,
  Thermometer,
  Activity,
  Zap,
  Clock,
  AlertCircle
} from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Separator } from './ui/separator'
import { Progress } from './ui/progress'
import { useMedical } from './MedicalContext'
import { PatientData } from './PatientData'
import { ChiefComplaint } from './ChiefComplaint'
import { MedicalHistory } from './MedicalHistory'
import { PhysicalExam } from './PhysicalExam'
import { Medications } from './Medications'
import { AnamnesisPreview } from './AnamnesisPreview'
import { LiveAnamnesisPreview } from './LiveAnamnesisPreview'
import { ReportViewer } from './reports/ReportViewer'
import { toast } from 'sonner'
import { useDeduplicated } from '../hooks/useDeduplicated'

type Step = 'patient' | 'complaint' | 'history' | 'exam' | 'medications' | 'preview' | 'report'

interface StepConfig {
  id: Step
  label: string
  icon: any
  description: string
  color: string
}

export function AnamneseRapida() {
  const [currentStep, setCurrentStep] = useState<Step>('patient')
  const { 
    anamnesisData, 
    medicalColors, 
    exportAnamnese, 
    validateData, 
    resetAnamnese 
  } = useMedical()
  
  // Use the deduplication hook
  const { deduplicateText, clearDeduplicationCache } = useDeduplicated()

  const steps: StepConfig[] = [
    { 
      id: 'patient', 
      label: 'Dados do Paciente', 
      icon: User, 
      description: 'Informações básicas anônimas',
      color: medicalColors.primary
    },
    { 
      id: 'complaint', 
      label: 'Queixa Principal', 
      icon: MessageSquare, 
      description: 'HDA e sintomas atuais',
      color: medicalColors.warning
    },
    { 
      id: 'history', 
      label: 'História Médica', 
      icon: History, 
      description: 'Antecedentes e comorbidades',
      color: medicalColors.neurology
    },
    { 
      id: 'exam', 
      label: 'Exame Físico', 
      icon: Stethoscope, 
      description: 'Sinais vitais e exame',
      color: medicalColors.cardiology
    },
    { 
      id: 'medications', 
      label: 'Medicamentos', 
      icon: Pill, 
      description: 'Prescrições e orientações',
      color: medicalColors.success
    },
    { 
      id: 'preview', 
      label: 'Revisão Final', 
      icon: FileText, 
      description: 'Revisão e exportação',
      color: medicalColors.gastro
    },
    { 
      id: 'report', 
      label: 'Relatório & IA', 
      icon: Brain, 
      description: 'Análise avançada e relatórios',
      color: medicalColors.neurology
    }
  ]

  const isStepCompleted = (stepId: Step): boolean => {
    switch (stepId) {
      case 'patient':
        return !!(
          anamnesisData?.paciente?.faixaEtaria && 
          anamnesisData?.paciente?.sexoBiologico
        )
      case 'complaint':
        return !!(
          anamnesisData?.queixaPrincipal?.queixaPrincipal?.trim() &&
          anamnesisData?.queixaPrincipal?.duracaoSintomas?.trim()
        )
      case 'history':
        return !!(
          anamnesisData?.historicoMedico?.comorbidades?.length ||
          anamnesisData?.historicoMedico?.medicamentosUso?.length ||
          anamnesisData?.historicoMedico?.alergias?.length
        )
      case 'exam':
        return !!(
          anamnesisData?.exameFisico?.sinaisVitais?.pa ||
          anamnesisData?.exameFisico?.sinaisVitais?.fc ||
          anamnesisData?.exameFisico?.exameFisico?.aspectoGeral
        )
      case 'medications':
        return !!(anamnesisData?.medicamentos?.prescricaoAtual?.length)
      case 'preview':
        return validateData()
      case 'report':
        return validateData() // Can access report if preview is complete
      default:
        return false
    }
  }

  const getCurrentStepIndex = () => steps.findIndex(step => step.id === currentStep)
  
  const getProgressPercentage = () => {
    const completedSteps = steps.filter(step => isStepCompleted(step.id)).length
    return (completedSteps / steps.length) * 100
  }

  const canProceedToNext = () => {
    return isStepCompleted(currentStep) || currentStep === 'preview'
  }

  const handleNext = () => {
    const currentIndex = getCurrentStepIndex()
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id)
    }
  }

  const handlePrevious = () => {
    const currentIndex = getCurrentStepIndex()
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1].id)
    }
  }

  const handleStepClick = (stepId: Step) => {
    const stepIndex = steps.findIndex(s => s.id === stepId)
    const currentIndex = getCurrentStepIndex()
    
    // Pode navegar para trás ou para próximo step se o atual estiver completo
    if (stepIndex <= currentIndex || (stepIndex === currentIndex + 1 && canProceedToNext())) {
      setCurrentStep(stepId)
    }
  }

  const handleExport = async () => {
    try {
      const anamnesisText = exportAnamnese()
      const deduplicatedText = deduplicateText(anamnesisText)
      
      // Tentar usar a API de clipboard moderna
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(deduplicatedText)
        toast.success('Anamnese copiada para a área de transferência!')
      } else {
        // Fallback para navegadores antigos
        const textArea = document.createElement('textarea')
        textArea.value = deduplicatedText
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        toast.success('Anamnese copiada!')
      }
    } catch (error) {
      console.error('Erro ao copiar:', error)
      toast.error('Erro ao copiar anamnese')
    }
  }

  const handleDownload = () => {
    try {
      const anamnesisText = exportAnamnese()
      const deduplicatedText = deduplicateText(anamnesisText)
      const blob = new Blob([deduplicatedText], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      
      const patientId = anamnesisData.paciente?.id?.slice(-8) || 'novo'
      const timestamp = new Date().toISOString().slice(0, 10)
      link.download = `anamnese_${patientId}_${timestamp}.txt`
      link.href = url
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast.success('Anamnese baixada com sucesso!')
    } catch (error) {
      console.error('Erro ao baixar:', error)
      toast.error('Erro ao baixar anamnese')
    }
  }

  const handleReset = () => {
    resetAnamnese()
    setCurrentStep('patient')
    clearDeduplicationCache()
    toast.success('Nova anamnese iniciada')
  }

  const renderStepContent = () => {
    const contentVariants = {
      hidden: { opacity: 0, x: 20 },
      visible: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -20 }
    }

    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{ duration: 0.2, ease: "easeInOut" }}
          className="w-full openai-fade-in"
        >
          {(() => {
            switch (currentStep) {
              case 'patient':
                return <PatientData />
              case 'complaint':
                return <ChiefComplaint />
              case 'history':
                return <MedicalHistory />
              case 'exam':
                return <PhysicalExam />
              case 'medications':
                return <Medications />
              case 'preview':
                return <AnamnesisPreview />
              case 'report':
                return <ReportViewer />
              default:
                return <PatientData />
            }
          })()}
        </motion.div>
      </AnimatePresence>
    )
  }

  const currentStepConfig = steps.find(step => step.id === currentStep)
  const progressPercentage = getProgressPercentage()

  return (
    <div className="min-h-svh min-h-svh-fix w-full flex flex-col bg-gradient-to-br from-slate-50 via-blue-50/20 to-indigo-50/30 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Modern Header com Progress Premium */}
      <motion.header 
        className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/60 dark:border-slate-700/50 shadow-xl shadow-blue-500/5"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
      >
        {/* Top Header Bar */}
        <div className="px-6 py-6 w-full">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Title Section */}
            <div className="flex items-center gap-4">
              <motion.div 
                className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg"
                whileHover={{ scale: 1.05, rotate: 3 }}
                whileTap={{ scale: 0.95 }}
              >
                <Stethoscope className="h-6 w-6 text-white" />
              </motion.div>
              <div>
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-slate-900 dark:text-white">
                  Nova Anamnese
                </h1>
                <p className="text-sm sm:text-base text-slate-600 dark:text-slate-400 mt-1">
                  Preencha os campos para gerar a anamnese do paciente
                </p>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center gap-2 sm:gap-3">
              <Badge variant="outline" className="gap-1.5 text-xs sm:text-sm bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600">
                <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                {anamnesisData.paciente?.timestamp ? 
                  new Date(anamnesisData.paciente.timestamp).toLocaleTimeString('pt-BR', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  }) : 
                  'Nova'
                }
              </Badge>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleReset}
                className="gap-2 hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-400"
              >
                <RotateCcw className="h-4 w-4" />
                <span className="hidden sm:inline">Nova Anamnese</span>
                <span className="sm:hidden">Nova</span>
              </Button>
            </div>
          </div>

          {/* Progress Section */}
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-slate-700 dark:text-slate-300 uppercase tracking-wide">
                Progresso da Anamnese
              </h3>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {Math.round(progressPercentage)}
                </span>
                <span className="text-sm text-slate-500 dark:text-slate-400">%</span>
              </div>
            </div>
            <div className="relative">
              <div className="absolute left-0 top-1/2 h-2 w-full bg-slate-200 dark:bg-slate-700 rounded-full -translate-y-1/2"></div>
              <motion.div 
                className="absolute left-0 top-1/2 h-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full -translate-y-1/2 shadow-lg shadow-blue-500/25"
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 0.8, ease: [0.4, 0, 0.2, 1] }}
              ></motion.div>
            </div>
          </div>
        </div>

        {/* Step Navigation Premium */}
        <div className="px-4 sm:px-6 lg:px-8 pb-4 sm:pb-6">
          <div className="relative">
            {/* Connection Line */}
            <div className="absolute left-0 top-1/2 h-0.5 w-full bg-slate-200 dark:bg-slate-700 -translate-y-1/2 hidden sm:block"></div>
            <motion.div 
              className="absolute left-0 top-1/2 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-600 -translate-y-1/2 hidden sm:block"
              initial={{ width: 0 }}
              animate={{ width: `${(getCurrentStepIndex() / (steps.length - 1)) * 100}%` }}
              transition={{ duration: 0.6, ease: [0.4, 0, 0.2, 1] }}
            ></motion.div>

            {/* Steps */}
            <div className="relative flex justify-between gap-2 overflow-x-auto pb-2 sm:pb-0">
              {steps.map((step, index) => {
                const isActive = step.id === currentStep
                const isCompleted = isStepCompleted(step.id)
                const isAccessible = index <= getCurrentStepIndex() || (index === getCurrentStepIndex() + 1 && canProceedToNext())
                
                return (
                  <div key={step.id} className="text-center flex-shrink-0 min-w-0">
                    <motion.button
                      onClick={() => handleStepClick(step.id)}
                      disabled={!isAccessible}
                      className={`mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full ring-4 sm:ring-8 ring-white dark:ring-slate-900 transition-all duration-300 ${
                        isActive
                          ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-xl shadow-blue-500/25'
                          : isCompleted
                          ? 'bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-xl shadow-green-500/25'
                          : isAccessible
                          ? 'bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-300 dark:hover:bg-slate-600'
                          : 'bg-slate-100 dark:bg-slate-800 text-slate-400 dark:text-slate-600 cursor-not-allowed'
                      }`}
                      whileHover={isAccessible ? { scale: 1.05 } : {}}
                      whileTap={isAccessible ? { scale: 0.95 } : {}}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: index * 0.1, duration: 0.4 }}
                    >
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6" />
                      ) : (
                        <step.icon className="h-4 w-4 sm:h-5 sm:w-5" />
                      )}
                    </motion.button>
                    <p className={`mt-2 text-xs sm:text-sm font-semibold max-w-[80px] sm:max-w-none truncate sm:whitespace-normal ${
                      isActive
                        ? 'text-blue-600 dark:text-blue-400'
                        : isCompleted
                        ? 'text-green-600 dark:text-green-400'
                        : isAccessible
                        ? 'text-slate-600 dark:text-slate-400'
                        : 'text-slate-400 dark:text-slate-600'
                    }`}>
                      {step.label}
                    </p>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content Area - Layout com duas colunas - Medical Full Width */}
      <div className="flex-1 w-full">
        <div className="h-full flex gap-6 p-4 sm:p-6 lg:p-8 max-w-none">
          {/* Coluna Principal - Formulário */}
          <div className="flex-1 overflow-y-auto w-full">
            <div className="w-full max-w-none space-y-6">
              {/* Step Header Card */}
              {currentStepConfig && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1, duration: 0.5 }}
                >
                  <Card className="p-4 sm:p-6 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-xl shadow-blue-500/5 rounded-xl">
                    <div className="flex items-center gap-3 sm:gap-4">
                      <motion.div 
                        className="p-3 rounded-xl"
                        style={{ backgroundColor: `${currentStepConfig.color}20` }}
                        whileHover={{ scale: 1.05, rotate: 3 }}
                      >
                        <currentStepConfig.icon 
                          className="h-5 w-5 sm:h-6 sm:w-6" 
                          style={{ color: currentStepConfig.color }} 
                        />
                      </motion.div>
                      <div className="flex-1">
                        <h2 className="text-lg sm:text-xl font-bold text-slate-900 dark:text-white">
                          {currentStepConfig.label}
                        </h2>
                        <p className="text-sm sm:text-base text-slate-600 dark:text-slate-400 mt-1">
                          {currentStepConfig.description}
                        </p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )}

              {/* Step Content */}
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
              >
                {renderStepContent()}
              </motion.div>
            </div>
          </div>

          {/* Coluna Lateral - Preview em Tempo Real */}
          <div className="hidden xl:block w-96 overflow-y-auto">
            <div className="sticky top-6">
              <LiveAnamnesisPreview />
            </div>
          </div>
        </div>
        
        {/* Preview Mobile - Aparece em telas menores */}
        <div className="xl:hidden px-4 sm:px-6 lg:px-8 pb-6">
          <LiveAnamnesisPreview />
        </div>
      </div>

      {/* Premium Footer Navigation */}
      <motion.footer 
        className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-t border-slate-200/60 dark:border-slate-700/50 shadow-2xl shadow-slate-900/5"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <div className="max-w-screen-2xl mx-auto p-4 sm:p-6">
          <div className="flex items-center justify-between gap-4">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={getCurrentStepIndex() === 0}
              className="gap-2 bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
              size="sm"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Anterior</span>
            </Button>

            <div className="flex items-center gap-2 sm:gap-3">
              {currentStep === 'preview' || currentStep === 'report' ? (
                <>
                  <Button
                    variant="outline"
                    onClick={handleExport}
                    className="gap-2 bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700"
                    size="sm"
                  >
                    <Copy className="h-4 w-4" />
                    <span className="hidden sm:inline">Copiar</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleDownload}
                    className="gap-2 bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700"
                    size="sm"
                  >
                    <Download className="h-4 w-4" />
                    <span className="hidden sm:inline">Baixar TXT</span>
                  </Button>
                </>
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={!canProceedToNext() || getCurrentStepIndex() === steps.length - 1}
                  className="gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg shadow-blue-500/25 disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none"
                  size="sm"
                >
                  <span className="hidden sm:inline">Próximo</span>
                  <span className="sm:hidden">Próx.</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Progress Summary */}
          <div className="mt-4 pt-4 border-t border-slate-200/60 dark:border-slate-700/50">
            <div className="flex items-center justify-center gap-4 text-xs sm:text-sm text-slate-600 dark:text-slate-400">
              <span>
                Etapa {getCurrentStepIndex() + 1} de {steps.length}
              </span>
              <span className="hidden sm:inline">•</span>
              <span className="hidden sm:inline">
                {Math.round(progressPercentage)}% concluído
              </span>
            </div>
          </div>
        </div>
      </motion.footer>

      {/* Validation Alert */}
      <AnimatePresence>
        {!validateData() && progressPercentage > 50 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed bottom-20 sm:bottom-24 right-4 sm:right-6 z-50 max-w-sm"
          >
            <Card className="p-4 bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800/50 shadow-xl shadow-amber-500/10">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                    Campos obrigatórios
                  </p>
                  <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                    Alguns campos obrigatórios não foram preenchidos
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default AnamneseRapida
