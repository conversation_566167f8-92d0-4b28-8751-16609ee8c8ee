import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { FileText, Copy, Download, Eye, EyeOff, RotateCcw } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Textarea } from './ui/textarea'
import { Switch } from './ui/switch'
import { Label } from './ui/label'
import { useMedical } from './MedicalContext'
import { toast } from 'sonner'

export function LiveAnamnesisPreview() {
  const { anamnesisData, exportAnamnese, medicalColors } = useMedical()
  const [previewText, setPreviewText] = useState('')
  const [isLive, setIsLive] = useState(true)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [wordCount, setWordCount] = useState(0)

  // Atualiza o preview em tempo real
  useEffect(() => {
    if (isLive) {
      const text = exportAnamnese()
      setPreviewText(text)
      setWordCount(text.split(/\s+/).filter(word => word.length > 0).length)
    }
  }, [anamnesisData, isLive, exportAnamnese])

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(previewText)
      toast.success('✓ Anamnese copiada!')
    } catch (error) {
      toast.error('Erro ao copiar')
    }
  }

  const handleDownload = () => {
    try {
      const blob = new Blob([previewText], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      
      const timestamp = new Date().toISOString().slice(0, 10)
      link.download = `anamnese_${anamnesisData.paciente.id}_${timestamp}.txt`
      link.href = url
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast.success('✓ Download iniciado!')
    } catch (error) {
      toast.error('Erro no download')
    }
  }

  const handleRefresh = () => {
    const text = exportAnamnese()
    setPreviewText(text)
    setWordCount(text.split(/\s+/).filter(word => word.length > 0).length)
    toast.success('✓ Preview atualizado!')
  }

  const completionPercentage = () => {
    const sections = [
      anamnesisData.paciente.faixaEtaria && anamnesisData.paciente.sexoBiologico,
      anamnesisData.queixaPrincipal.queixaPrincipal,
      anamnesisData.historicoMedico.comorbidades.length > 0 || anamnesisData.historicoMedico.medicamentosUso.length > 0,
      Object.values(anamnesisData.exameFisico.sinaisVitais).some(v => v.trim()) || Object.values(anamnesisData.exameFisico.exameFisico).some(v => v.trim())
    ]
    
    const completed = sections.filter(Boolean).length
    return Math.round((completed / sections.length) * 100)
  }

  return (
    <motion.div 
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header Compacto */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div 
                className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md"
                whileHover={{ scale: 1.05 }}
              >
                <FileText className="h-4 w-4 text-white" />
              </motion.div>
              <div>
                <CardTitle className="text-base font-semibold text-slate-900 dark:text-white">
                  Anamnese em Tempo Real
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {completionPercentage()}% completa
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {wordCount} palavras
                  </Badge>
                </div>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Controles */}
      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="bg-slate-50/80 dark:bg-slate-800/50 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50">
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  {/* Toggle Preview Automático */}
                  <div className="flex items-center gap-2">
                    <Switch
                      id="live-preview"
                      checked={isLive}
                      onCheckedChange={setIsLive}
                      className="data-[state=checked]:bg-blue-500"
                    />
                    <Label htmlFor="live-preview" className="text-sm font-medium">
                      Preview automático
                    </Label>
                  </div>

                  {/* Ações */}
                  <div className="flex items-center gap-2">
                    {!isLive && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRefresh}
                        className="gap-1.5 text-xs"
                      >
                        <RotateCcw className="h-3 w-3" />
                        Atualizar
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCopy}
                      className="gap-1.5 text-xs"
                    >
                      <Copy className="h-3 w-3" />
                      Copiar
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleDownload}
                      className="gap-1.5 text-xs"
                    >
                      <Download className="h-3 w-3" />
                      Baixar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Preview Area */}
            <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg">
              <CardContent className="p-0">
                <Textarea
                  value={previewText}
                  onChange={(e) => setPreviewText(e.target.value)}
                  placeholder="A anamnese aparecerá aqui conforme você preenche os dados..."
                  className="min-h-[300px] max-h-[500px] resize-none border-0 bg-transparent font-mono text-xs leading-relaxed focus-visible:ring-0 focus-visible:ring-offset-0"
                  style={{
                    fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace'
                  }}
                />
              </CardContent>
            </Card>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs text-slate-600 dark:text-slate-400">
                <span>Progresso da anamnese</span>
                <span>{completionPercentage()}%</span>
              </div>
              <div className="relative h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                <motion.div
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${completionPercentage()}%` }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export default LiveAnamnesisPreview