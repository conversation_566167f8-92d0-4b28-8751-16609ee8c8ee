import React, { useState } from 'react';
import { motion } from 'motion/react';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { 
  Bell, 
  User, 
  Settings, 
  Moon, 
  Sun,
  Filter,
  Search,
  MoreHorizontal,
  LogOut,
  HelpCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { useToastHelpers } from './Toast';

interface HeaderActionsProps {
  darkMode?: boolean;
  onToggleTheme?: () => void;
  onOpenSettings?: () => void;
  userName?: string;
  userEmail?: string;
}

export const HeaderActions: React.FC<HeaderActionsProps> = ({
  darkMode = false,
  onToggleTheme = () => {},
  onOpenSettings,
  userName = "<PERSON><PERSON>",
  userEmail = "<EMAIL>"
}) => {
  const [notifications] = useState([
    { id: 1, type: 'emergency', message: 'Paciente crítico aguardando', time: '2min' },
    { id: 2, type: 'info', message: 'Nova prescrição aprovada', time: '5min' },
    { id: 3, type: 'warning', message: 'Sistema será atualizado em 1h', time: '10min' }
  ]);
  
  const toast = useToastHelpers();

  const handleNotificationClick = (notification: any) => {
    switch (notification.type) {
      case 'emergency':
        toast.emergencyAlert(notification.message);
        break;
      case 'warning':
        toast.warning('Aviso do Sistema', notification.message);
        break;
      default:
        toast.info('Notificação', notification.message);
    }
  };

  const handleLogout = () => {
    toast.info('Saindo do Sistema', 'Até logo, Dr. Silva!');
    // Implementar logout
  };

  const handleHelp = () => {
    toast.info('Ajuda', 'Abrindo central de ajuda...');
    // Implementar ajuda
  };

  return (
    <div className="flex items-center gap-1 sm:gap-2 lg:gap-3">
      {/* Search - Hide on very small screens */}
      <div className="hidden lg:flex relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <input
          type="text"
          placeholder="Buscar pacientes, receitas..."
          className="
            pl-10 pr-4 py-2 w-48 xl:w-64 text-sm rounded-lg border border-border
            bg-background text-foreground placeholder:text-muted-foreground
            focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary
            transition-all duration-200
          "
        />
      </div>

      {/* Mobile Search Button */}
      <Button 
        variant="ghost" 
        size="icon" 
        className="lg:hidden text-muted-foreground hover:text-foreground w-8 h-8 sm:w-9 sm:h-9"
      >
        <Search size={16} className="sm:w-5 sm:h-5" />
      </Button>

      {/* Filter - Smaller on mobile */}
      <Button 
        variant="ghost" 
        size="icon" 
        className="hidden sm:flex text-muted-foreground hover:text-foreground w-8 h-8 sm:w-9 sm:h-9"
      >
        <Filter size={16} className="sm:w-5 sm:h-5" />
      </Button>

      {/* Notifications - Mobile Optimized */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-muted-foreground hover:text-foreground relative w-8 h-8 sm:w-9 sm:h-9"
          >
            <Bell size={16} className="sm:w-5 sm:h-5" />
            {notifications.length > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-4 h-4 sm:w-5 sm:h-5 bg-red-500 rounded-full flex items-center justify-center"
              >
                <span className="text-xs text-white font-medium">
                  {notifications.length > 9 ? '9+' : notifications.length}
                </span>
              </motion.div>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-72 sm:w-80 mr-2">
          <DropdownMenuLabel>
            <div className="flex items-center justify-between">
              <span>Notificações</span>
              <Badge variant="secondary" className="text-xs">
                {notifications.length}
              </Badge>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {notifications.map((notification) => (
            <DropdownMenuItem 
              key={notification.id}
              className="p-3 cursor-pointer"
              onClick={() => handleNotificationClick(notification)}
            >
              <div className="flex items-start gap-3 w-full">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  notification.type === 'emergency' ? 'bg-red-500 animate-pulse' :
                  notification.type === 'warning' ? 'bg-amber-500' : 'bg-blue-500'
                }`} />
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-foreground">{notification.message}</p>
                  <p className="text-xs text-muted-foreground mt-1">{notification.time} atrás</p>
                </div>
              </div>
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <DropdownMenuItem className="text-center p-3">
            <span className="text-sm text-primary">Ver todas as notificações</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Theme Toggle - Mobile Optimized */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onToggleTheme}
        className="text-muted-foreground hover:text-foreground w-8 h-8 sm:w-9 sm:h-9"
      >
        <motion.div
          animate={{ rotate: darkMode ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {darkMode ? <Sun size={16} className="sm:w-5 sm:h-5" /> : <Moon size={16} className="sm:w-5 sm:h-5" />}
        </motion.div>
      </Button>

      {/* Settings - Hide on smallest screens */}
      <Button 
        variant="ghost" 
        size="icon" 
        className="hidden sm:flex text-muted-foreground hover:text-foreground w-8 h-8 sm:w-9 sm:h-9"
        onClick={onOpenSettings}
      >
        <Settings size={16} className="sm:w-5 sm:h-5" />
      </Button>

      {/* User Menu - Mobile Responsive */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="flex items-center gap-1 sm:gap-2 h-8 sm:h-10 px-2 sm:px-3">
            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary rounded-full flex items-center justify-center">
              <span className="text-primary-foreground text-xs sm:text-sm font-medium">
                {userName.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            <div className="hidden lg:block text-left">
              <div className="text-sm font-medium text-foreground">
                {userName}
              </div>
              <div className="text-xs text-muted-foreground">
                Online
              </div>
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48 sm:w-56 mr-1 sm:mr-2">
          <DropdownMenuLabel>
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium">{userName}</p>
              <p className="text-xs text-muted-foreground">{userEmail}</p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="cursor-pointer">
            <User className="mr-2 h-4 w-4" />
            <span>Perfil</span>
          </DropdownMenuItem>
          <DropdownMenuItem className="cursor-pointer" onClick={onOpenSettings}>
            <Settings className="mr-2 h-4 w-4" />
            <span>Configurações</span>
          </DropdownMenuItem>
          <DropdownMenuItem className="cursor-pointer" onClick={handleHelp}>
            <HelpCircle className="mr-2 h-4 w-4" />
            <span>Ajuda</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="cursor-pointer text-red-600" onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Sair</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* More Options - Show Settings on mobile */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            className="sm:hidden text-muted-foreground hover:text-foreground w-8 h-8"
          >
            <MoreHorizontal size={16} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          <DropdownMenuItem className="cursor-pointer" onClick={onOpenSettings}>
            <Settings className="mr-2 h-4 w-4" />
            <span>Configurações</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};