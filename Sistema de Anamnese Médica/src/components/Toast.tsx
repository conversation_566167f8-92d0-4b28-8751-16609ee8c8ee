import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Check, X, AlertTriangle, Info, Loader2 } from 'lucide-react';
import { designSystem } from '../utils/design-system';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export interface ToastProps {
  id: string;
  type: ToastType;
  title: string;
  description?: string;
  duration?: number;
  onClose: (id: string) => void;
}

export interface ToastContextType {
  toasts: ToastProps[];
  addToast: (toast: Omit<ToastProps, 'id' | 'onClose'>) => void;
  removeToast: (id: string) => void;
  clearAll: () => void;
}

// Context para gerenciar toasts globalmente
export const ToastContext = React.createContext<ToastContextType | null>(null);

// Hook para usar o toast context
export const useToast = () => {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Componente individual do Toast
export const Toast: React.FC<ToastProps> = ({ 
  id, 
  type, 
  title, 
  description, 
  duration = 4000, 
  onClose 
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [progress, setProgress] = useState(100);

  const handleClose = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => onClose(id), 200);
  }, [onClose, id]);

  useEffect(() => {
    if (type === 'loading') return; // Loading toasts não desaparecem automaticamente

    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev - (100 / (duration / 50));
        if (newProgress <= 0) {
          clearInterval(progressInterval);
          handleClose();
          return 0;
        }
        return newProgress;
      });
    }, 50);

    return () => clearInterval(progressInterval);
  }, [duration, type, handleClose]);

  const getToastStyles = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800',
          icon: <Check className="h-5 w-5 text-green-600" />,
          progressColor: '#22c55e'
        };
      case 'error':
        return {
          bg: 'bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800',
          icon: <X className="h-5 w-5 text-red-600" />,
          progressColor: '#ef4444'
        };
      case 'warning':
        return {
          bg: 'bg-amber-50 dark:bg-amber-950 border-amber-200 dark:border-amber-800',
          icon: <AlertTriangle className="h-5 w-5 text-amber-600" />,
          progressColor: '#f59e0b'
        };
      case 'info':
        return {
          bg: 'bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800',
          icon: <Info className="h-5 w-5 text-blue-600" />,
          progressColor: designSystem.colors.primary[500]
        };
      case 'loading':
        return {
          bg: 'bg-gray-50 dark:bg-gray-950 border-gray-200 dark:border-gray-800',
          icon: <Loader2 className="h-5 w-5 text-gray-600 animate-spin" />,
          progressColor: '#6b7280'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-950 border-gray-200 dark:border-gray-800',
          icon: <Info className="h-5 w-5 text-gray-600" />,
          progressColor: '#6b7280'
        };
    }
  };

  const styles = getToastStyles();

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.3 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
      className={`
        relative p-4 rounded-lg border shadow-lg backdrop-blur-sm max-w-md w-full
        ${styles.bg}
      `}
      style={{
        backdropFilter: 'blur(20px)',
        background: `${styles.bg.includes('bg-') ? '' : styles.bg}`
      }}
    >
      {/* Progress bar */}
      {type !== 'loading' && (
        <div className="absolute top-0 left-0 h-1 bg-black/10 rounded-t-lg w-full overflow-hidden">
          <div 
            className="h-full transition-all duration-75 ease-linear"
            style={{ 
              width: `${progress}%`,
              backgroundColor: styles.progressColor
            }}
          />
        </div>
      )}

      {/* Content */}
      <div className="flex items-start gap-3">
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          {styles.icon}
        </div>

        {/* Text content */}
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold text-foreground mb-1">
            {title}
          </h4>
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>

        {/* Close button */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 transition-colors"
        >
          <X className="h-4 w-4 text-muted-foreground" />
        </button>
      </div>
    </motion.div>
  );
};

// Provider para gerenciar toasts
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const addToast = (toast: Omit<ToastProps, 'id' | 'onClose'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: ToastProps = {
      ...toast,
      id,
      onClose: removeToast
    };
    setToasts(prev => [...prev, newToast]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearAll = () => {
    setToasts([]);
  };

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearAll }}>
      {children}
      
      {/* Toast container */}
      <div className="fixed bottom-4 right-4 z-50 space-y-2">
        <AnimatePresence>
          {toasts.map(toast => (
            <Toast key={toast.id} {...toast} />
          ))}
        </AnimatePresence>
      </div>
    </ToastContext.Provider>
  );
};

// Utility functions para facilitar o uso
export const toast = {
  success: (title: string, description?: string, duration?: number) => {
    // Esta função será definida no context
    console.log('Toast success:', title, description);
  },
  error: (title: string, description?: string, duration?: number) => {
    console.log('Toast error:', title, description);
  },
  warning: (title: string, description?: string, duration?: number) => {
    console.log('Toast warning:', title, description);
  },
  info: (title: string, description?: string, duration?: number) => {
    console.log('Toast info:', title, description);
  },
  loading: (title: string, description?: string) => {
    console.log('Toast loading:', title, description);
  }
};

// Custom hook com funções utilitárias
export const useToastHelpers = () => {
  const { addToast } = useToast();

  return {
    success: (title: string, description?: string, duration?: number) => 
      addToast({ type: 'success', title, description, duration }),
    
    error: (title: string, description?: string, duration?: number) => 
      addToast({ type: 'error', title, description, duration }),
    
    warning: (title: string, description?: string, duration?: number) => 
      addToast({ type: 'warning', title, description, duration }),
    
    info: (title: string, description?: string, duration?: number) => 
      addToast({ type: 'info', title, description, duration }),
    
    loading: (title: string, description?: string) => 
      addToast({ type: 'loading', title, description }),

    // Utility para operações médicas
    patientUpdated: (patientName: string) => 
      addToast({ 
        type: 'success', 
        title: 'Paciente Atualizado', 
        description: `Informações de ${patientName} foram salvas com sucesso` 
      }),
    
    prescriptionSaved: () => 
      addToast({ 
        type: 'success', 
        title: 'Prescrição Salva', 
        description: 'Receituário foi gerado e salvo com sucesso' 
      }),
    
    emergencyAlert: (message: string) => 
      addToast({ 
        type: 'error', 
        title: '🚨 Alerta de Emergência', 
        description: message,
        duration: 8000 
      }),
    
    aiResponse: (message: string) => 
      addToast({ 
        type: 'info', 
        title: '🤖 Assistente IA', 
        description: message 
      })
  };
};