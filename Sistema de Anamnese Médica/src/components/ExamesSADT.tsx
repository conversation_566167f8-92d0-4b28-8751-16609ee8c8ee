import React from 'react';
import { motion } from 'motion/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Activity, FileText, Calendar, Check } from 'lucide-react';
import { useMedical } from './MedicalContext';

export function ExamesSADT() {
  const { medicalColors } = useMedical();
  
  return (
    <div 
      className="flex flex-col h-full"
      style={{ 
        background: `linear-gradient(135deg, ${medicalColors.primaryLight}05 0%, ${medicalColors.primary}05 50%, ${medicalColors.primaryDark}05 100%)`
      }}
    >
      {/* Header */}
      <div className="border-b bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm p-6 shadow-sm">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-xl" style={{ backgroundColor: `${medicalColors.cardiology}20` }}>
            <Activity className="h-6 w-6" style={{ color: medicalColors.cardiology }} />
          </div>
          <div>
            <h1 
              className="text-2xl font-semibold"
              style={{
                background: `linear-gradient(135deg, ${medicalColors.cardiology}, ${medicalColors.primary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              Solicitações SADT
            </h1>
            <p className="text-sm text-muted-foreground">
              Exames, consultas e procedimentos médicos
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" style={{ color: medicalColors.cardiology }} />
                Módulo em Desenvolvimento
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <div className="mb-4">
                  <div 
                    className="w-16 h-16 mx-auto rounded-full flex items-center justify-center"
                    style={{ backgroundColor: `${medicalColors.cardiology}20` }}
                  >
                    <Activity className="h-8 w-8" style={{ color: medicalColors.cardiology }} />
                  </div>
                </div>
                <h3 className="text-lg font-semibold mb-2">Solicitações SADT</h3>
                <p className="text-muted-foreground mb-6">
                  Sistema para solicitação de exames complementares, 
                  consultas especializadas e procedimentos médicos.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Activity className="h-4 w-4" style={{ color: medicalColors.cardiology }} />
                      <span className="font-medium">Exames Laboratoriais</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Hemograma, bioquímica, hormônios e mais
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <FileText className="h-4 w-4" style={{ color: medicalColors.neurology }} />
                      <span className="font-medium">Exames de Imagem</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Radiografia, ultrassom, tomografia, ressonância
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4" style={{ color: medicalColors.warning }} />
                      <span className="font-medium">Consultas Especializadas</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Encaminhamento para especialistas
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Check className="h-4 w-4" style={{ color: medicalColors.success }} />
                      <span className="font-medium">Acompanhamento</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Status e resultados dos exames solicitados
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}