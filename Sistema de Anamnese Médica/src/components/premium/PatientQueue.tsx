import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Clock, AlertCircle, User } from 'lucide-react';
import { MedicalIcon, IconVariant } from '../../utils/medical-icons-premium';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Patient, PRIORITY_COLORS, STATUS_COLORS } from '../../utils/dashboard-constants';

interface PatientQueueProps {
  patients: Patient[];
  onPatientSelect?: (patient: Patient) => void;
  onCallPatient?: (patientId: string) => void;
  darkMode?: boolean;
}

const PatientQueue: React.FC<PatientQueueProps> = ({
  patients,
  onPatientSelect,
  onCallPatient,
  darkMode = false
}) => {
  const getPriorityIcon = (priority: Patient['priority']) => {
    switch (priority) {
      case 'emergencia':
        return 'critical';
      case 'alta':
        return 'urgent';
      case 'media':
        return 'moderate';
      default:
        return 'stable';
    }
  };

  const getStatusIcon = (status: Patient['status']) => {
    switch (status) {
      case 'chamado':
        return 'inProgress';
      case 'atendimento':
        return 'approve';
      default:
        return 'pending';
    }
  };

  const formatTime = (time: string) => {
    return time;
  };

  return (
    <div className="space-y-3">
      <AnimatePresence>
        {patients.map((patient, index) => (
          <motion.div
            key={patient.id}
            className="patient-queue-item openai-card glass hover-lift cursor-pointer"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ delay: index * 0.1 }}
            onClick={() => onPatientSelect?.(patient)}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
          >
            <div className="p-4">
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  {/* Avatar */}
                  <motion.div
                    className="w-10 h-10 rounded-full bg-gradient-wave flex items-center justify-center text-white font-semibold"
                    whileHover={{ scale: 1.1 }}
                  >
                    {patient.avatar ? (
                      <img 
                        src={patient.avatar} 
                        alt={patient.name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <User size={20} />
                    )}
                  </motion.div>

                  {/* Patient info */}
                  <div>
                    <div className="font-semibold text-sm">{patient.name}</div>
                    <div className="text-xs text-muted-foreground">{patient.age} anos</div>
                  </div>
                </div>

                {/* Priority and Status badges */}
                <div className="flex items-center gap-2">
                  <Badge
                    className="text-xs px-2 py-1 border-0"
                    style={{
                      backgroundColor: PRIORITY_COLORS[patient.priority].bg,
                      color: PRIORITY_COLORS[patient.priority].text,
                      borderColor: PRIORITY_COLORS[patient.priority].border
                    }}
                  >
                    <MedicalIcon
                      name={getPriorityIcon(patient.priority)}
                      variant={IconVariant.FILLED}
                      size={12}
                      category="status"
                    />
                    <span className="ml-1 capitalize">{patient.priority}</span>
                  </Badge>

                  <Badge
                    className="text-xs px-2 py-1 border-0"
                    style={{
                      backgroundColor: STATUS_COLORS[patient.status].bg,
                      color: STATUS_COLORS[patient.status].text
                    }}
                  >
                    <MedicalIcon
                      name={getStatusIcon(patient.status)}
                      variant={IconVariant.OUTLINE}
                      size={12}
                      category="status"
                    />
                    <span className="ml-1 capitalize">{patient.status}</span>
                  </Badge>
                </div>
              </div>

              {/* Complaint */}
              <div className="mb-3">
                <div className="text-sm text-muted-foreground mb-1">Queixa principal:</div>
                <div className="text-sm font-medium">{patient.complaint}</div>
              </div>

              {/* Time info and actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock size={12} />
                    <span>Chegada: {formatTime(patient.arrivalTime)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <AlertCircle size={12} />
                    <span>Estimativa: {patient.estimatedTime}</span>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex items-center gap-2">
                  {patient.status === 'aguardando' && (
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 px-3 text-xs openai-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          onCallPatient?.(patient.id);
                        }}
                      >
                        Chamar
                      </Button>
                    </motion.div>
                  )}

                  {patient.status === 'chamado' && (
                    <motion.div 
                      className="flex items-center gap-1 text-xs text-medical-warning"
                      animate={{ opacity: [1, 0.5, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <AlertCircle size={12} />
                      <span>Chamado</span>
                    </motion.div>
                  )}

                  {patient.status === 'atendimento' && (
                    <div className="flex items-center gap-1 text-xs text-medical-success">
                      <div className="w-2 h-2 bg-medical-success rounded-full animate-pulse" />
                      <span>Em atendimento</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Priority indicator bar */}
            <div 
              className="h-1 w-full"
              style={{ 
                backgroundColor: PRIORITY_COLORS[patient.priority].border,
                opacity: patient.priority === 'emergencia' ? 1 : 0.6
              }}
            />
          </motion.div>
        ))}
      </AnimatePresence>

      {patients.length === 0 && (
        <motion.div
          className="text-center py-8 text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <User size={48} className="mx-auto mb-2 opacity-50" />
          <div>Nenhum paciente na fila</div>
        </motion.div>
      )}
    </div>
  );
};

export default PatientQueue;