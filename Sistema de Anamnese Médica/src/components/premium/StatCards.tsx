import React from 'react';
import { motion } from 'motion/react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { MedicalIcon, IconVariant } from '../../utils/medical-icons-premium';
import { StatCard } from '../../utils/dashboard-constants';

interface StatCardsProps {
  stats: StatCard[];
  darkMode?: boolean;
}

const StatCards: React.FC<StatCardsProps> = ({ stats, darkMode = false }) => {
  const renderSparkline = (data: number[], color: string) => {
    if (!data || data.length < 2) return null;

    const max = Math.max(...data);
    const min = Math.min(...data);
    const range = max - min || 1;
    const width = 60;
    const height = 20;

    const points = data.map((value, index) => {
      const x = (index / (data.length - 1)) * width;
      const y = height - ((value - min) / range) * height;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="stat-sparkline">
        <svg width={width} height={height} className="overflow-visible">
          <polyline
            points={points}
            fill="none"
            stroke={color}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.7"
          />
          <defs>
            <linearGradient id={`sparkline-gradient-${stats.indexOf(stats.find(s => s.sparklineData === data) || stats[0])}`} x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={color} stopOpacity="0.2"/>
              <stop offset="100%" stopColor={color} stopOpacity="0"/>
            </linearGradient>
          </defs>
          <polygon
            points={`0,${height} ${points} ${width},${height}`}
            fill={`url(#sparkline-gradient-${stats.indexOf(stats.find(s => s.sparklineData === data) || stats[0])})`}
          />
        </svg>
      </div>
    );
  };

  const renderTrendIcon = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={12} />;
      case 'down':
        return <TrendingDown size={12} />;
      default:
        return <Minus size={12} />;
    }
  };

  return (
    <div className="stats-grid">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.title}
          className="stat-card glass hover-lift openai-card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.3 }}
          whileHover={{ 
            scale: 1.02,
            transition: { duration: 0.2 }
          }}
        >
          {/* Background gradient */}
          <div 
            className="stat-bg" 
            style={{ background: stat.gradient }}
          />

          <div className="stat-content">
            {/* Header */}
            <div className="stat-header">
              <span className="stat-title">{stat.title}</span>
              <motion.span 
                className="stat-icon"
                style={{ color: stat.color }}
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.15 }}
              >
                <MedicalIcon
                  name={stat.icon}
                  variant={IconVariant.DUOTONE}
                  size={24}
                  category={stat.category}
                />
              </motion.span>
            </div>

            {/* Value */}
            <motion.div 
              className="stat-value"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ delay: index * 0.1 + 0.2, duration: 0.3 }}
            >
              {stat.value}
            </motion.div>

            {/* Footer with change indicator */}
            <div className="stat-footer">
              <motion.span 
                className={`stat-change ${stat.trend === 'up' ? 'positive' : stat.trend === 'down' ? 'negative' : 'neutral'}`}
                whileHover={{ scale: 1.05 }}
              >
                {renderTrendIcon(stat.trend)}
                {stat.change}
              </motion.span>
              <span className="stat-period">vs. ontem</span>
            </div>
          </div>

          {/* Sparkline */}
          {stat.sparklineData && (
            <motion.div
              className="absolute bottom-2 right-4 opacity-50 hover:opacity-100 transition-opacity"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.5 }}
              transition={{ delay: index * 0.1 + 0.4 }}
            >
              {renderSparkline(stat.sparklineData, stat.color)}
            </motion.div>
          )}

          {/* Hover overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity rounded-lg pointer-events-none"
            style={{
              background: `linear-gradient(135deg, ${stat.color}10 0%, transparent 100%)`
            }}
          />
        </motion.div>
      ))}
    </div>
  );
};

export default StatCards;