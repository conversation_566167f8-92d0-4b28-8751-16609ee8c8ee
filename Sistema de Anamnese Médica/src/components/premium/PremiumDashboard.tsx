import React, { useState } from 'react';
import { motion } from 'motion/react';
import { 
  Search, 
  Bell, 
  Filter,
  Download,
  MoreHorizontal,
  ChevronDown,
  BarChart3,
  Users,
  Activity
} from 'lucide-react';
import { MedicalIcon, IconVariant } from '../../utils/medical-icons-premium';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import StatCards from './StatCards';
import PatientQueue from './PatientQueue';
import { 
  DASHBOARD_STATS, 
  PATIENT_QUEUE, 
  DASHBOARD_FILTERS, 
  TIME_PERIODS,
  Patient 
} from '../../utils/dashboard-constants';

interface PremiumDashboardProps {
  darkMode?: boolean;
  onToggleTheme?: () => void;
}

export const PremiumDashboard: React.FC<PremiumDashboardProps> = ({
  darkMode = false,
  onToggleTheme
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('todos');
  const [selectedPeriod, setSelectedPeriod] = useState('hoje');
  const [notifications] = useState(3);

  const handlePatientSelect = (patient: Patient) => {
    console.log('Selected patient:', patient);
    // Here you would navigate to patient details or open a modal
  };

  const handleCallPatient = (patientId: string) => {
    console.log('Calling patient:', patientId);
    // Here you would handle calling the patient
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Implement search logic here
  };

  const filteredPatients = PATIENT_QUEUE.filter(patient => {
    if (selectedFilter === 'todos') return true;
    return patient.priority === selectedFilter;
  });

  return (
    <div className="dashboard-container grid-container">
      {/* Premium Header */}
      <motion.header 
        className="dashboard-header glass"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="header-left">
          <motion.h1 
            className="page-title"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            <span className="title-icon">
              <MedicalIcon 
                name="dashboard" 
                variant={IconVariant.FILLED} 
                size={24}
                category="navigation"
              />
            </span>
            Dashboard Médico
          </motion.h1>
          <div className="breadcrumbs">
            <a href="/">Home</a>
            <span className="separator">›</span>
            <span className="current">Dashboard</span>
          </div>
        </div>
        
        <div className="header-right">
          {/* Universal Search */}
          <motion.div 
            className="universal-search"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
              <Input
                type="text"
                placeholder="Buscar paciente, CID, medicamento... (⌘K)"
                className="search-input pl-10 glass"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </motion.div>
          
          {/* Header Actions */}
          <motion.div 
            className="header-actions"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <Button variant="ghost" size="icon" className="action-btn">
              <Bell size={16} />
              {notifications > 0 && <span className="notification-dot" />}
            </Button>
            <Button variant="ghost" size="icon" className="action-btn">
              <MoreHorizontal size={16} />
            </Button>
          </motion.div>
        </div>
      </motion.header>

      {/* Stats Cards Grid */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <StatCards stats={DASHBOARD_STATS} darkMode={darkMode} />
      </motion.div>

      {/* Main Content Grid */}
      <div className="content-grid">
        {/* Main Chart Card */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="card-large glass openai-card">
            <CardHeader className="card-header">
              <CardTitle className="card-title">
                <MedicalIcon 
                  name="analytics" 
                  variant={IconVariant.DUOTONE}
                  size={20}
                  category="wellwave"
                />
                Atendimentos - Últimos 30 dias
              </CardTitle>
              <div className="card-actions">
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="filter-select w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_PERIODS.map(period => (
                      <SelectItem key={period.value} value={period.value}>
                        {period.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="ghost" size="icon">
                  <Download size={16} />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="card-body">
              {/* Placeholder for chart */}
              <div className="h-64 flex items-center justify-center text-muted-foreground border-2 border-dashed border-border rounded-lg">
                <div className="text-center">
                  <BarChart3 size={48} className="mx-auto mb-2 opacity-50" />
                  <div>Gráfico de atendimentos</div>
                  <div className="text-sm">Dados em tempo real</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Patient Queue Card */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="card-scroll glass openai-card">
            <CardHeader className="card-header">
              <CardTitle className="card-title">
                <MedicalIcon 
                  name="patients" 
                  variant={IconVariant.DUOTONE}
                  size={20}
                  category="navigation"
                />
                Fila de Atendimento
              </CardTitle>
              <div className="flex items-center gap-2">
                <span className="card-badge">
                  {filteredPatients.length} {filteredPatients.length === 1 ? 'paciente' : 'pacientes'}
                </span>
                <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                  <SelectTrigger className="filter-select w-32">
                    <SelectValue />
                    <ChevronDown size={12} />
                  </SelectTrigger>
                  <SelectContent>
                    {DASHBOARD_FILTERS.map(filter => (
                      <SelectItem key={filter.value} value={filter.value}>
                        {filter.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent className="card-body p-0">
              <div className="p-4">
                <PatientQueue 
                  patients={filteredPatients}
                  onPatientSelect={handlePatientSelect}
                  onCallPatient={handleCallPatient}
                  darkMode={darkMode}
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Quick Stats Row */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7 }}
      >
        <Card className="openai-card glass">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Atendimentos Hoje</div>
                <div className="text-2xl font-bold">47</div>
              </div>
              <MedicalIcon 
                name="examine" 
                variant={IconVariant.DUOTONE}
                size={32}
                category="actions"
                color="var(--medical-primary)"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="openai-card glass">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Média de Espera</div>
                <div className="text-2xl font-bold">22min</div>
              </div>
              <MedicalIcon 
                name="measure" 
                variant={IconVariant.DUOTONE}
                size={32}
                category="actions"
                color="var(--medical-warning)"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="openai-card glass">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Satisfação</div>
                <div className="text-2xl font-bold">94%</div>
              </div>
              <MedicalIcon 
                name="approve" 
                variant={IconVariant.DUOTONE}
                size={32}
                category="actions"
                color="var(--medical-success)"
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default PremiumDashboard;