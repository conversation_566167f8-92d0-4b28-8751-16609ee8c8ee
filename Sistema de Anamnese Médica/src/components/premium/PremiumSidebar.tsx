import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  ChevronLeft, 
  ChevronRight, 
  Settings, 
  Bell,
  Zap,
  Bot,
  Activity,
  User,
  LogOut
} from 'lucide-react';
import { MedicalIcon, MedicalIconSystem, IconVariant, IconSize } from '../../utils/medical-icons-premium';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';

interface NavItem {
  id: string;
  icon: string;
  label: string;
  badge?: string | number;
  color: string;
  category?: string;
  shortcut?: string;
}

interface NavGroup {
  title: string;
  items: NavItem[];
}

interface PremiumSidebarProps {
  expanded?: boolean;
  onToggle?: (expanded: boolean) => void;
  activeModule?: string;
  onModuleChange?: (moduleId: string) => void;
  onNewAction?: () => void;
  darkMode?: boolean;
  onToggleTheme?: () => void;
  userName?: string;
  userEmail?: string;
  userAvatar?: string;
}

export const PremiumSidebar: React.FC<PremiumSidebarProps> = ({
  expanded = true,
  onToggle,
  activeModule = 'dashboard',
  onModuleChange,
  onNewAction,
  darkMode = false,
  onToggleTheme,
  userName = 'Dr. Silva',
  userEmail = '<EMAIL>',
  userAvatar
}) => {
  const [isExpanded, setIsExpanded] = useState(expanded);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  useEffect(() => {
    setIsExpanded(expanded);
  }, [expanded]);

  const handleToggle = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    onToggle?.(newExpanded);
  };

  // Grupos de navegação premium
  const navGroups: NavGroup[] = [
    {
      title: 'Atendimento',
      items: [
        { 
          id: 'dashboard', 
          icon: 'dashboard', 
          label: 'Dashboard', 
          badge: null, 
          color: 'var(--medical-primary)',
          category: 'navigation',
          shortcut: '⌘D'
        },
        { 
          id: 'anamnese', 
          icon: 'anamnesis', 
          label: 'Anamnese', 
          badge: null, 
          color: 'var(--medical-secondary)',
          category: 'navigation',
          shortcut: '⌘A'
        },
        { 
          id: 'consultas', 
          icon: 'examine', 
          label: 'Consultas', 
          badge: 3, 
          color: 'var(--medical-info)',
          category: 'actions',
          shortcut: '⌘C'
        },
        { 
          id: 'prescricao', 
          icon: 'prescribe', 
          label: 'Prescrições', 
          badge: 'NEW', 
          color: 'var(--medical-success)',
          category: 'actions',
          shortcut: '⌘P'
        }
      ]
    },
    {
      title: 'Gestão',
      items: [
        { 
          id: 'kanban', 
          icon: 'kanban', 
          label: 'Triagem', 
          badge: 12, 
          color: 'var(--medical-warning)',
          category: 'navigation',
          shortcut: '⌘T'
        },
        { 
          id: 'schedule', 
          icon: 'schedule', 
          label: 'Agenda', 
          badge: null, 
          color: 'var(--medical-primary)',
          category: 'navigation',
          shortcut: '⌘S'
        },
        { 
          id: 'reports', 
          icon: 'reports', 
          label: 'Relatórios', 
          badge: null, 
          color: 'var(--medical-secondary)',
          category: 'navigation',
          shortcut: '⌘R'
        },
        { 
          id: 'patients', 
          icon: 'patients', 
          label: 'Pacientes', 
          badge: null, 
          color: 'var(--medical-info)',
          category: 'navigation',
          shortcut: '⌘U'
        }
      ]
    },
    {
      title: 'Inteligência',
      items: [
        { 
          id: 'chat', 
          icon: 'ai', 
          label: 'Chat IA', 
          badge: 'PRO', 
          color: 'var(--medical-info)',
          category: 'wellwave',
          shortcut: '⌘I'
        },
        { 
          id: 'medicamentos', 
          icon: 'medications', 
          label: 'Base Medicamentos', 
          badge: null, 
          color: 'var(--medical-success)',
          category: 'navigation',
          shortcut: '⌘M'
        },
        { 
          id: 'exames', 
          icon: 'exams', 
          label: 'Exames/SADT', 
          badge: null, 
          color: 'var(--medical-warning)',
          category: 'navigation',
          shortcut: '⌘E'
        },
        { 
          id: 'protocolos', 
          icon: 'document', 
          label: 'Protocolos', 
          badge: null, 
          color: 'var(--medical-primary)',
          category: 'actions',
          shortcut: '⌘O'
        }
      ]
    }
  ];

  const renderNavGroups = () => {
    return navGroups.map((group, groupIndex) => (
      <motion.div 
        key={group.title}
        className="nav-group"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: groupIndex * 0.1 }}
      >
        <AnimatePresence>
          {isExpanded && (
            <motion.h4 
              className="nav-group-title"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.2 }}
            >
              {group.title}
            </motion.h4>
          )}
        </AnimatePresence>
        
        <ul className="nav-items">
          {group.items.map((item, itemIndex) => (
            <motion.li 
              key={item.id}
              className="nav-item"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: (groupIndex * 0.1) + (itemIndex * 0.05) }}
            >
              <motion.a
                href={`#${item.id}`}
                className={`nav-link ${activeModule === item.id ? 'active' : ''}`}
                onClick={(e) => {
                  e.preventDefault();
                  onModuleChange?.(item.id);
                }}
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
                whileHover={{ x: isExpanded ? 4 : 0, scale: isExpanded ? 1.02 : 1.1 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.15 }}
              >
                <span className="nav-icon" style={{ color: item.color }}>
                  <MedicalIcon 
                    name={item.icon}
                    variant={activeModule === item.id ? IconVariant.FILLED : IconVariant.OUTLINE}
                    size={20}
                    category={item.category}
                    animated
                  />
                </span>
                
                <AnimatePresence>
                  {isExpanded && (
                    <motion.span 
                      className="nav-label"
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      {item.label}
                    </motion.span>
                  )}
                </AnimatePresence>
                
                {item.badge && (
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.span 
                        className={`nav-badge ${item.badge === 'NEW' || item.badge === 'PRO' ? 'highlight' : ''}`}
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        {item.badge}
                      </motion.span>
                    )}
                  </AnimatePresence>
                )}

                {/* Tooltip para sidebar colapsada */}
                <AnimatePresence>
                  {!isExpanded && hoveredItem === item.id && (
                    <motion.div 
                      className="nav-tooltip glass"
                      initial={{ opacity: 0, x: -10, scale: 0.9 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      exit={{ opacity: 0, x: -10, scale: 0.9 }}
                      transition={{ duration: 0.15 }}
                    >
                      <div className="tooltip-content">
                        <div className="tooltip-title">{item.label}</div>
                        {item.shortcut && (
                          <div className="tooltip-shortcut">{item.shortcut}</div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.a>
            </motion.li>
          ))}
        </ul>
      </motion.div>
    ));
  };

  return (
    <motion.aside 
      className={`openai-sidebar premium-sidebar ${isExpanded ? 'expanded' : 'collapsed'} glass`}
      animate={{ 
        width: isExpanded ? 'var(--sidebar-width)' : 'var(--sidebar-width-collapsed)' 
      }}
      transition={{ 
        duration: 0.3, 
        ease: [0.4, 0, 0.2, 1] 
      }}
    >
      {/* Logo Section Premium */}
      <div className="sidebar-header">
        <motion.div 
          className="logo-wrapper"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div 
            className="logo-icon wave-motion"
            animate={{ 
              rotate: [0, 5, -5, 0],
              scale: [1, 1.05, 1]
            }}
            transition={{ 
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <div 
              className="p-2 rounded-xl"
              style={{ 
                background: 'var(--gradient-wave)',
                boxShadow: 'var(--shadow-wave-glow)' 
              }}
            >
              <Activity 
                size={24} 
                className="text-white animate-heartbeat" 
                style={{ filter: 'drop-shadow(0 0 4px rgba(255,255,255,0.5))' }}
              />
            </div>
          </motion.div>
          
          <AnimatePresence>
            {isExpanded && (
              <motion.div 
                className="logo-text"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
              >
                <span 
                  className="brand"
                  style={{ 
                    background: 'var(--gradient-wave)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    fontSize: '18px',
                    fontWeight: 700
                  }}
                >
                  WellWave
                </span>
                <span className="tagline">Medical Excellence</span>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
        
        <motion.button 
          className="sidebar-toggle"
          onClick={handleToggle}
          whileHover={{ scale: 1.1, backgroundColor: 'var(--bg-accent)' }}
          whileTap={{ scale: 0.9 }}
          transition={{ duration: 0.15 }}
        >
          <motion.div
            animate={{ rotate: isExpanded ? 0 : 180 }}
            transition={{ duration: 0.3 }}
          >
            <ChevronLeft size={16} />
          </motion.div>
        </motion.button>
      </div>

      {/* Quick Actions Premium */}
      <motion.div 
        className="quick-actions"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <motion.button 
          className="quick-btn emergency ripple"
          whileHover={{ scale: 1.05, boxShadow: '0 0 20px rgba(255, 90, 95, 0.3)' }}
          whileTap={{ scale: 0.95 }}
          title="Emergência (⌘E)"
        >
          <Zap size={16} />
          <AnimatePresence>
            {isExpanded && (
              <motion.span 
                className="label"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                SOS
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>
        
        <motion.button 
          className="quick-btn ai-chat ripple"
          whileHover={{ scale: 1.05, boxShadow: '0 0 20px rgba(139, 127, 255, 0.3)' }}
          whileTap={{ scale: 0.95 }}
          title="IA Médica (⌘I)"
          onClick={() => onModuleChange?.('chat')}
        >
          <Bot size={16} />
          <AnimatePresence>
            {isExpanded && (
              <motion.span 
                className="label"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                IA
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>

        <motion.button 
          className="quick-btn new-action ripple"
          whileHover={{ scale: 1.05, boxShadow: '0 0 20px rgba(0, 200, 150, 0.3)' }}
          whileTap={{ scale: 0.95 }}
          title="Nova Anamnese (⌘N)"
          onClick={onNewAction}
        >
          <MedicalIcon 
            name="anamnesis" 
            variant={IconVariant.FILLED} 
            size={16}
            category="navigation"
          />
          <AnimatePresence>
            {isExpanded && (
              <motion.span 
                className="label"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                NOVA
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>
      </motion.div>

      {/* Navigation Groups Premium */}
      <nav className="sidebar-nav openai-scrollbar">
        {renderNavGroups()}
      </nav>

      {/* User Section Premium */}
      <motion.div 
        className="sidebar-footer"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <motion.div 
          className="user-card glass hover-lift"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <div className="user-avatar">
            <Avatar className="w-10 h-10">
              <AvatarImage src={userAvatar} alt={userName} />
              <AvatarFallback 
                className="text-white font-semibold"
                style={{ background: 'var(--gradient-wave)' }}
              >
                {userName.split(' ').map(n => n[0]).join('').slice(0, 2)}
              </AvatarFallback>
            </Avatar>
            <motion.span 
              className="status-indicator online"
              animate={{ 
                scale: [1, 1.2, 1],
                opacity: [1, 0.7, 1]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
          
          <AnimatePresence>
            {isExpanded && (
              <motion.div 
                className="user-info"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
              >
                <span className="user-name">{userName}</span>
                <span className="user-role">{userEmail}</span>
                <Badge 
                  className="user-badge"
                  style={{ 
                    background: 'var(--gradient-wave)',
                    color: 'white',
                    border: 'none',
                    fontSize: '10px'
                  }}
                >
                  🌊 PREMIUM
                </Badge>
              </motion.div>
            )}
          </AnimatePresence>
          
          <div className="user-actions">
            {/* Theme Toggle */}
            <motion.button 
              className="action-btn theme-toggle"
              onClick={onToggleTheme}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              title="Alternar tema"
            >
              <motion.div
                animate={{ rotate: darkMode ? 180 : 0 }}
                transition={{ duration: 0.5 }}
              >
                {darkMode ? (
                  <Sun size={14} />
                ) : (
                  <Moon size={14} />
                )}
              </motion.div>
            </motion.button>

            {/* Notifications */}
            <motion.button 
              className="action-btn notifications"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              title="Notificações"
            >
              <Bell size={14} />
              <motion.span 
                className="notification-dot"
                animate={{ 
                  scale: [1, 1.2, 1],
                  opacity: [1, 0.7, 1]
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </motion.button>

            {/* Settings */}
            <motion.button 
              className="action-btn settings-btn"
              whileHover={{ 
                scale: 1.1,
                rotate: 90
              }}
              whileTap={{ scale: 0.9 }}
              title="Configurações"
            >
              <Settings size={14} />
            </motion.button>

            {/* Logout */}
            <motion.button 
              className="action-btn logout-btn"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              title="Sair"
            >
              <LogOut size={14} />
            </motion.button>
          </div>
        </motion.div>
      </motion.div>
    </motion.aside>
  );
};

export default PremiumSidebar;