import { useState } from 'react'
import { motion } from 'motion/react'
import { History, Heart, Pill, AlertCircle, Plus, X, Check } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'
import { Input } from './ui/input'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Checkbox } from './ui/checkbox'
import { useMedical } from './MedicalContext'

export function MedicalHistory() {
  const { anamnesisData, updateNestedAnamnesisData, medicalColors } = useMedical()
  const [newComorbidity, setNewComorbidity] = useState('')
  const [newMedication, setNewMedication] = useState('')
  const [newAllergy, setNewAllergy] = useState('')

  // Common comorbidities
  const commonComorbidities = [
    'Hipertensão Arterial', 'Diabetes Mellitus', 'Dislipidemia', 'Tabagismo',
    'Etilismo', 'Obesidade', 'Insuficiência Cardíaca', 'DPOC', 'Asma',
    'Hipotireoidismo', 'Fibrilação Atrial', 'Insuficiência Renal'
  ]

  // Common medications
  const commonMedications = [
    'AAS 100mg', 'Losartana 50mg', 'Atenolol 50mg', 'Metformina 850mg',
    'Omeprazol 20mg', 'Sinvastatina 20mg', 'Dipirona 500mg', 'Paracetamol 750mg'
  ]

  const addItem = (
    item: string, 
    currentArray: string[], 
    field: 'comorbidades' | 'medicamentosUso' | 'alergias',
    setter: (value: string) => void
  ) => {
    if (item && !currentArray.includes(item)) {
      const newArray = [...currentArray, item]
      updateNestedAnamnesisData('historicoMedico', field, newArray)
      setter('')
    }
  }

  const removeItem = (
    index: number, 
    currentArray: string[], 
    field: 'comorbidades' | 'medicamentosUso' | 'alergias'
  ) => {
    const newArray = currentArray.filter((_, i) => i !== index)
    updateNestedAnamnesisData('historicoMedico', field, newArray)
  }

  return (
    <div className="space-y-4 max-w-4xl mx-auto">
      {/* Header Minimalista */}
      <motion.div 
        className="mb-6"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 shadow-lg">
            <History className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-slate-900 dark:text-white">História Médica</h2>
            <p className="text-sm text-slate-600 dark:text-slate-400">Antecedentes e comorbidades</p>
          </div>
        </div>
      </motion.div>

      {/* Comorbidades - Checkboxes Minimalistas */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Heart className="h-4 w-4 text-red-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Comorbidades
              </Label>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {commonComorbidities.map((comorbidity) => {
                const isSelected = anamnesisData.historicoMedico.comorbidades.includes(comorbidity)
                
                return (
                  <motion.div
                    key={comorbidity}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <label className={`
                      flex items-center gap-3 p-3 rounded-xl border-2 cursor-pointer transition-all
                      ${isSelected 
                        ? 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300' 
                        : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800'
                      }
                    `}>
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            addItem(comorbidity, anamnesisData.historicoMedico.comorbidades, 'comorbidades', setNewComorbidity)
                          } else {
                            const index = anamnesisData.historicoMedico.comorbidades.indexOf(comorbidity)
                            if (index > -1) removeItem(index, anamnesisData.historicoMedico.comorbidades, 'comorbidades')
                          }
                        }}
                        className="w-4 h-4"
                      />
                      <span className="text-sm font-medium flex-1">
                        {comorbidity}
                      </span>
                    </label>
                  </motion.div>
                )
              })}
            </div>

            {/* Comorbidades selecionadas */}
            {anamnesisData.historicoMedico.comorbidades.length > 0 && (
              <div className="mt-4 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-xl">
                <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">Comorbidades selecionadas:</p>
                <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {anamnesisData.historicoMedico.comorbidades.join(' • ')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Medicamentos - Checkboxes Minimalistas */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Pill className="h-4 w-4 text-green-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Medicamentos em Uso
              </Label>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {commonMedications.map((medication) => {
                const isSelected = anamnesisData.historicoMedico.medicamentosUso.includes(medication)
                
                return (
                  <motion.div
                    key={medication}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <label className={`
                      flex items-center gap-3 p-3 rounded-xl border-2 cursor-pointer transition-all
                      ${isSelected 
                        ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' 
                        : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800'
                      }
                    `}>
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            addItem(medication, anamnesisData.historicoMedico.medicamentosUso, 'medicamentosUso', setNewMedication)
                          } else {
                            const index = anamnesisData.historicoMedico.medicamentosUso.indexOf(medication)
                            if (index > -1) removeItem(index, anamnesisData.historicoMedico.medicamentosUso, 'medicamentosUso')
                          }
                        }}
                        className="w-4 h-4"
                      />
                      <span className="text-sm font-medium flex-1">
                        {medication}
                      </span>
                    </label>
                  </motion.div>
                )
              })}
            </div>

            {/* Medicamentos selecionados */}
            {anamnesisData.historicoMedico.medicamentosUso.length > 0 && (
              <div className="mt-4 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-xl">
                <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">Medicamentos em uso:</p>
                <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {anamnesisData.historicoMedico.medicamentosUso.join(' • ')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Alergias - Input Simples */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <AlertCircle className="h-4 w-4 text-orange-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Alergias (Opcional)
              </Label>
            </div>
            
            <div className="flex gap-2">
              <Input
                placeholder="Ex: Penicilina, Frutos do mar, Pólen..."
                value={newAllergy}
                onChange={(e) => setNewAllergy(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addItem(newAllergy, anamnesisData.historicoMedico.alergias, 'alergias', setNewAllergy)}
                className="flex-1 rounded-xl border-slate-300 dark:border-slate-600 focus:border-orange-500 focus:ring-orange-500/20 bg-white dark:bg-slate-800"
              />
              <Button 
                onClick={() => addItem(newAllergy, anamnesisData.historicoMedico.alergias, 'alergias', setNewAllergy)}
                disabled={!newAllergy.trim()}
                className="rounded-xl bg-orange-500 hover:bg-orange-600 text-white"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Alergias registradas */}
            {anamnesisData.historicoMedico.alergias.length > 0 && (
              <div className="space-y-2">
                <div className="flex flex-wrap gap-2">
                  {anamnesisData.historicoMedico.alergias.map((allergy, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                    >
                      <Badge 
                        variant="secondary" 
                        className="gap-1 cursor-pointer hover:bg-destructive hover:text-destructive-foreground bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300"
                        onClick={() => removeItem(index, anamnesisData.historicoMedico.alergias, 'alergias')}
                      >
                        {allergy}
                        <X className="h-3 w-3" />
                      </Badge>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default MedicalHistory