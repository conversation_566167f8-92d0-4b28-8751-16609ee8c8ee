import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "../ui/utils";
import { Card } from "../ui/card";
import { MedicalStatus } from "../ui/medical-status";

const medicalLayoutVariants = cva(
  "w-full transition-medical-normal ease-medical-out",
  {
    variants: {
      variant: {
        dashboard: "grid grid-cols-1 lg:grid-cols-12 gap-6 p-6",
        patientEntry: "max-w-4xl mx-auto p-6 space-y-6",
        review: "max-w-6xl mx-auto p-6 space-y-6",
        emergency: "max-w-2xl mx-auto p-4 space-y-4",
        list: "max-w-7xl mx-auto p-6 space-y-4",
        detail: "max-w-5xl mx-auto p-6 space-y-6",
      },
      density: {
        compact: "gap-4 p-4",
        normal: "gap-6 p-6",
        spacious: "gap-8 p-8",
      },
    },
    defaultVariants: {
      variant: "dashboard",
      density: "normal",
    },
  }
);

export interface MedicalLayoutProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof medicalLayoutVariants> {
  title?: string;
  subtitle?: string;
  status?: "critical" | "urgent" | "routine" | "success" | "info" | "warning" | "stable" | "pending" | "processing";
  statusLabel?: string;
  actions?: React.ReactNode;
  sidebar?: React.ReactNode;
  children: React.ReactNode;
}

const MedicalLayout = React.forwardRef<HTMLDivElement, MedicalLayoutProps>(
  ({ 
    className, 
    variant, 
    density, 
    title, 
    subtitle, 
    status, 
    statusLabel, 
    actions, 
    sidebar, 
    children, 
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(medicalLayoutVariants({ variant, density, className }))}
        {...props}
      >
        {/* Header Section */}
        {(title || subtitle || status || actions) && (
          <div className="col-span-full flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div className="flex flex-col gap-2">
              {title && (
                <h1 className="text-2xl font-bold text-medical-text-primary">
                  {title}
                </h1>
              )}
              {subtitle && (
                <p className="text-medical-text-secondary">
                  {subtitle}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              {status && (
                <MedicalStatus 
                  status={status} 
                  label={statusLabel}
                  variant="glass"
                />
              )}
              {actions && (
                <div className="flex items-center gap-2">
                  {actions}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className={cn(
          "flex gap-6",
          sidebar ? "lg:grid lg:grid-cols-4" : "col-span-full"
        )}>
          {/* Sidebar */}
          {sidebar && (
            <aside className="lg:col-span-1 space-y-4">
              {sidebar}
            </aside>
          )}
          
          {/* Main Content */}
          <main className={cn(
            "space-y-6",
            sidebar ? "lg:col-span-3" : "w-full"
          )}>
            {children}
          </main>
        </div>
      </div>
    );
  }
);

MedicalLayout.displayName = "MedicalLayout";

// Specialized layout components for different medical workflows
export const MedicalDashboardLayout = React.forwardRef<HTMLDivElement, Omit<MedicalLayoutProps, 'variant'>>(
  (props, ref) => <MedicalLayout ref={ref} variant="dashboard" {...props} />
);
MedicalDashboardLayout.displayName = "MedicalDashboardLayout";

export const PatientEntryLayout = React.forwardRef<HTMLDivElement, Omit<MedicalLayoutProps, 'variant'>>(
  (props, ref) => <MedicalLayout ref={ref} variant="patientEntry" {...props} />
);
PatientEntryLayout.displayName = "PatientEntryLayout";

export const MedicalReviewLayout = React.forwardRef<HTMLDivElement, Omit<MedicalLayoutProps, 'variant'>>(
  (props, ref) => <MedicalLayout ref={ref} variant="review" {...props} />
);
MedicalReviewLayout.displayName = "MedicalReviewLayout";

export const EmergencyLayout = React.forwardRef<HTMLDivElement, Omit<MedicalLayoutProps, 'variant'>>(
  (props, ref) => <MedicalLayout ref={ref} variant="emergency" {...props} />
);
EmergencyLayout.displayName = "EmergencyLayout";

export const PatientListLayout = React.forwardRef<HTMLDivElement, Omit<MedicalLayoutProps, 'variant'>>(
  (props, ref) => <MedicalLayout ref={ref} variant="list" {...props} />
);
PatientListLayout.displayName = "PatientListLayout";

export const PatientDetailLayout = React.forwardRef<HTMLDivElement, Omit<MedicalLayoutProps, 'variant'>>(
  (props, ref) => <MedicalLayout ref={ref} variant="detail" {...props} />
);
PatientDetailLayout.displayName = "PatientDetailLayout";

export { MedicalLayout, medicalLayoutVariants };
