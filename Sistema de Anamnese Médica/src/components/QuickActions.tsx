import React, { useState } from 'react';
import { motion } from 'motion/react';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { 
  Plus, 
  Stethoscope, 
  Bot, 
  FileText, 
  Calendar,
  Clock,
  Users,
  Activity,
  Settings,
  Bell,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react';
import { useToastHelpers } from './Toast';

interface QuickActionsProps {
  onModuleChange?: (module: string) => void;
  onOpenSettings?: () => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ 
  onModuleChange, 
  onOpenSettings 
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const toast = useToastHelpers();

  const handleRefreshData = async () => {
    setIsRefreshing(true);
    toast.loading('Atualizando dados...', 'Sincronizando informações do sistema');
    
    // Simular refresh
    setTimeout(() => {
      setIsRefreshing(false);
      toast.success('Dados Atualizados', 'Todas as informações foram sincronizadas');
    }, 2000);
  };

  const handleExportData = () => {
    toast.info('Exportando Dados', 'Preparando arquivo para download...');
    
    // Simular export
    setTimeout(() => {
      const data = {
        timestamp: new Date().toISOString(),
        patients: 47,
        appointments: 312,
        prescriptions: 89
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `wellwave-data-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      
      toast.success('Dados Exportados', 'Arquivo baixado com sucesso');
    }, 1000);
  };

  const handleNewPatient = () => {
    toast.success('Novo Paciente', 'Formulário de cadastro aberto');
    onModuleChange?.('anamnese');
  };

  const handleEmergencyMode = () => {
    toast.emergencyAlert('Modo de emergência ativado! Prioridade máxima para novos atendimentos.');
  };

  const quickActions = [
    {
      id: 'new-patient',
      label: 'Novo Paciente',
      description: 'Cadastrar paciente',
      icon: Plus,
      color: 'bg-blue-500 hover:bg-blue-600',
      onClick: handleNewPatient
    },
    {
      id: 'anamnese',
      label: 'Anamnese',
      description: 'Iniciar avaliação',
      icon: Stethoscope,
      color: 'bg-green-500 hover:bg-green-600',
      onClick: () => onModuleChange?.('anamnese')
    },
    {
      id: 'ai-chat',
      label: 'Assistente IA',
      description: 'Consultar IA médica',
      icon: Bot,
      color: 'bg-purple-500 hover:bg-purple-600',
      onClick: () => onModuleChange?.('chat')
    },
    {
      id: 'prescription',
      label: 'Prescrição',
      description: 'Criar receituário',
      icon: FileText,
      color: 'bg-amber-500 hover:bg-amber-600',
      onClick: () => onModuleChange?.('prescricao')
    }
  ];

  const systemActions = [
    {
      id: 'refresh',
      label: 'Atualizar',
      icon: RefreshCw,
      loading: isRefreshing,
      onClick: handleRefreshData
    },
    {
      id: 'export',
      label: 'Exportar',
      icon: Download,
      onClick: handleExportData
    },
    {
      id: 'settings',
      label: 'Configurações',
      icon: Settings,
      onClick: onOpenSettings
    },
    {
      id: 'emergency',
      label: 'Emergência',
      icon: Bell,
      color: 'text-red-600 hover:bg-red-50 dark:hover:bg-red-950',
      onClick: handleEmergencyMode
    }
  ];

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Ações Principais - Mobile First */}
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h3 className="text-sm sm:text-base font-semibold text-foreground">Ações Rápidas</h3>
            <Badge variant="secondary" className="text-xs">
              <span className="hidden sm:inline">{quickActions.length} disponíveis</span>
              <span className="sm:hidden">{quickActions.length}</span>
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3">
            {quickActions.map((action) => {
              const IconComponent = action.icon;
              return (
                <motion.div
                  key={action.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="outline"
                    className={`
                      h-auto p-3 sm:p-4 flex flex-col items-center gap-1.5 sm:gap-2 w-full
                      border-2 border-dashed border-border
                      hover:border-primary/50 hover:bg-accent
                      transition-all duration-200 touch-manipulation
                      min-h-[80px] sm:min-h-[100px]
                    `}
                    onClick={action.onClick}
                  >
                    <div className={`
                      p-1.5 sm:p-2 rounded-lg text-white ${action.color}
                      group-hover:scale-110 transition-transform
                    `}>
                      <IconComponent className="h-3 w-3 sm:h-4 sm:w-4" />
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-xs sm:text-sm">{action.label}</div>
                      <div className="text-xs text-muted-foreground mt-0.5 sm:mt-1 line-clamp-1">
                        {action.description}
                      </div>
                    </div>
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Ações do Sistema - Mobile Optimized */}
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h3 className="text-sm sm:text-base font-semibold text-foreground">Sistema</h3>
            <div className="flex items-center gap-1.5 sm:gap-2">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-muted-foreground">Online</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 sm:flex sm:items-center gap-1.5 sm:gap-2 sm:flex-wrap">
            {systemActions.map((action) => {
              const IconComponent = action.icon;
              return (
                <motion.div
                  key={action.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`
                      flex items-center gap-1.5 sm:gap-2 h-8 sm:h-9 w-full sm:w-auto justify-start
                      ${action.color || 'hover:bg-accent'} touch-manipulation
                    `}
                    onClick={action.onClick}
                    disabled={action.loading}
                  >
                    <IconComponent 
                      className={`h-3 w-3 sm:h-4 sm:w-4 ${action.loading ? 'animate-spin' : ''}`} 
                    />
                    <span className="text-xs sm:text-sm">{action.label}</span>
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Status do Sistema */}
      <Card className="border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700 dark:text-green-300">
                  Sistema Operacional
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-4 text-xs text-green-600 dark:text-green-400">
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>47 pacientes</span>
              </div>
              <div className="flex items-center gap-1">
                <Activity className="h-3 w-3" />
                <span>98% uptime</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>2min sync</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};