import React from 'react';
import { motion } from 'motion/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Pill, Search, Database, Info } from 'lucide-react';
import { useMedical } from './MedicalContext';

export function MedicamentosSUS() {
  const { medicalColors } = useMedical();
  
  return (
    <div 
      className="flex flex-col h-full"
      style={{ 
        background: `linear-gradient(135deg, ${medicalColors.primaryLight}05 0%, ${medicalColors.primary}05 50%, ${medicalColors.primaryDark}05 100%)`
      }}
    >
      {/* Header */}
      <div className="border-b bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm p-6 shadow-sm">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-xl" style={{ backgroundColor: `${medicalColors.gastro}20` }}>
            <Pill className="h-6 w-6" style={{ color: medicalColors.gastro }} />
          </div>
          <div>
            <h1 
              className="text-2xl font-semibold"
              style={{
                background: `linear-gradient(135deg, ${medicalColors.gastro}, ${medicalColors.primary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              Base de Medicamentos SUS
            </h1>
            <p className="text-sm text-muted-foreground">
              Farmacologia e medicamentos disponíveis no Sistema Único de Saúde
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" style={{ color: medicalColors.gastro }} />
                Módulo em Desenvolvimento
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <div className="mb-4">
                  <div 
                    className="w-16 h-16 mx-auto rounded-full flex items-center justify-center"
                    style={{ backgroundColor: `${medicalColors.gastro}20` }}
                  >
                    <Pill className="h-8 w-8" style={{ color: medicalColors.gastro }} />
                  </div>
                </div>
                <h3 className="text-lg font-semibold mb-2">Base de Medicamentos SUS</h3>
                <p className="text-muted-foreground mb-6">
                  Base completa de medicamentos disponíveis no SUS com informações 
                  farmacológicas, posologia e contraindicações.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Search className="h-4 w-4" style={{ color: medicalColors.primary }} />
                      <span className="font-medium">Busca Inteligente</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Pesquisa por nome, princípio ativo ou indicação
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Database className="h-4 w-4" style={{ color: medicalColors.success }} />
                      <span className="font-medium">Base Atualizada</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Lista oficial atualizada do Ministério da Saúde
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Info className="h-4 w-4" style={{ color: medicalColors.neurology }} />
                      <span className="font-medium">Informações Completas</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Posologia, contraindicações e interações
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Pill className="h-4 w-4" style={{ color: medicalColors.gastro }} />
                      <span className="font-medium">Categorização</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Organizado por classe terapêutica
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}