import { useState } from 'react'
import { motion } from 'motion/react'
import { User, Users, Baby, Shield, Heart, Phone, AlertCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'
import { Input } from './ui/input'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Checkbox } from './ui/checkbox'
import { useMedical } from './MedicalContext'

export function PatientData() {
  const { anamnesisData, updateAnamnesisData, updateNestedAnamnesisData, medicalColors } = useMedical()
  const [phoneError, setPhoneError] = useState('')

  const handleAgeGroupChange = (faixaEtaria: 'pediatrico' | 'adulto' | 'idoso') => {
    updateNestedAnamnesisData('paciente', 'faixaEtaria', faixaEtaria)
    
    // Reset pregnancy if not adult female
    if (faixaEtaria !== 'adulto' && anamnesisData.paciente.gestante) {
      updateNestedAnamnesisData('paciente', 'gestante', false)
    }
  }

  const handleSexChange = (sexoBiologico: 'M' | 'F') => {
    updateNestedAnamnesisData('paciente', 'sexoBiologico', sexoBiologico)
    
    // Reset pregnancy if not female
    if (sexoBiologico !== 'F' && anamnesisData.paciente.gestante) {
      updateNestedAnamnesisData('paciente', 'gestante', false)
    }
  }

  const handlePhoneChange = (telefone: string) => {
    // Basic phone validation
    const phoneRegex = /^[\d\s\(\)\-\+]*$/
    if (telefone === '' || phoneRegex.test(telefone)) {
      updateNestedAnamnesisData('paciente', 'telefone', telefone)
      setPhoneError('')
    } else {
      setPhoneError('Digite apenas números, espaços, parênteses, hífen ou +')
    }
  }

  const ageGroups = [
    { 
      value: 'pediatrico', 
      label: 'Pediátrico', 
      description: '0-17 anos',
      icon: Baby,
      color: medicalColors.warning
    },
    { 
      value: 'adulto', 
      label: 'Adulto', 
      description: '18-64 anos',
      icon: User,
      color: medicalColors.primary
    },
    { 
      value: 'idoso', 
      label: 'Idoso', 
      description: '65+ anos',
      icon: Shield,
      color: medicalColors.neurology
    }
  ]

  const sexOptions = [
    { value: 'F', label: 'Feminino', color: medicalColors.endocrinology },
    { value: 'M', label: 'Masculino', color: medicalColors.cardiology }
  ]

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header Card Premium */}
      <Card className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-xl shadow-blue-500/5 rounded-xl">
        <CardHeader className="pb-3 sm:pb-4">
          <div className="flex items-center gap-3 sm:gap-4">
            <motion.div 
              className="p-2 sm:p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg"
              whileHover={{ scale: 1.05, rotate: 3 }}
            >
              <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </motion.div>
            <div className="flex-1">
              <CardTitle className="text-base sm:text-lg text-slate-900 dark:text-white">Identificação do Paciente</CardTitle>
              <CardDescription className="text-xs sm:text-sm">Sistema anônimo preservando privacidade médica</CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Patient ID Display */}
      <Card className="bg-slate-50/80 dark:bg-slate-800/50 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 rounded-xl">
        <CardContent className="pt-4 sm:pt-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex-1">
              <Label className="text-xs sm:text-sm text-slate-600 dark:text-slate-400">ID do Paciente</Label>
              <div className="font-mono text-lg sm:text-xl font-bold text-slate-900 dark:text-white mt-1">
                {anamnesisData.paciente.id}
              </div>
            </div>
            <Badge variant="outline" className="gap-1.5 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 w-fit">
              <Shield className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="text-xs sm:text-sm">Anônimo</span>
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Age Group Selection */}
      <Card className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-xl shadow-blue-500/5 rounded-xl">
        <CardHeader>
          <CardTitle className="text-base sm:text-lg text-slate-900 dark:text-white">Faixa Etária</CardTitle>
          <CardDescription className="text-xs sm:text-sm">Selecione a faixa etária apropriada</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
            {ageGroups.map((group) => {
              const Icon = group.icon
              const isSelected = anamnesisData.paciente.faixaEtaria === group.value
              
              return (
                <motion.button
                  key={group.value}
                  onClick={() => handleAgeGroupChange(group.value as any)}
                  className={`p-3 sm:p-4 rounded-xl border-2 transition-all text-left touch-manipulation ${
                    isSelected 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg shadow-blue-500/10' 
                      : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800/50 hover:bg-slate-50 dark:hover:bg-slate-800'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: group.value === 'pediatrico' ? 0 : group.value === 'adulto' ? 0.1 : 0.2 }}
                >
                  <div className="flex items-center gap-2 sm:gap-3">
                    <div className={`p-2 rounded-lg ${isSelected ? 'bg-blue-500 text-white' : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400'}`}>
                      <Icon className="h-4 w-4 sm:h-5 sm:w-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className={`font-semibold text-sm sm:text-base truncate ${isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-slate-900 dark:text-white'}`}>
                        {group.label}
                      </div>
                      <div className={`text-xs sm:text-sm mt-0.5 ${isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-slate-500 dark:text-slate-400'}`}>
                        {group.description}
                      </div>
                    </div>
                  </div>
                </motion.button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Sex Selection */}
      <Card className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-xl shadow-blue-500/5 rounded-xl">
        <CardHeader>
          <CardTitle className="text-base sm:text-lg text-slate-900 dark:text-white">Sexo Biológico</CardTitle>
          <CardDescription className="text-xs sm:text-sm">Importante para protocolos médicos específicos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3 sm:gap-4">
            {sexOptions.map((option, index) => {
              const isSelected = anamnesisData.paciente.sexoBiologico === option.value
              
              return (
                <motion.button
                  key={option.value}
                  onClick={() => handleSexChange(option.value as any)}
                  className={`p-3 sm:p-4 rounded-xl border-2 transition-all font-semibold touch-manipulation ${
                    isSelected 
                      ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 shadow-lg shadow-indigo-500/10' 
                      : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800/50 hover:bg-slate-50 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, x: index === 0 ? -10 : 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="text-sm sm:text-base text-center">{option.label}</div>
                </motion.button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Pregnancy Status - Only for adult females */}
      {anamnesisData.paciente.sexoBiologico === 'F' && 
       anamnesisData.paciente.faixaEtaria === 'adulto' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Heart className="h-4 w-4" style={{ color: medicalColors.endocrinology }} />
                Gestação
              </CardTitle>
              <CardDescription>Importante para protocolos de segurança</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="gestante"
                  checked={anamnesisData.paciente.gestante}
                  onCheckedChange={(checked) => 
                    updateNestedAnamnesisData('paciente', 'gestante', checked)
                  }
                />
                <Label htmlFor="gestante">Paciente gestante</Label>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Phone Contact */}
      <Card className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-xl shadow-blue-500/5 rounded-xl">
        <CardHeader>
          <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-slate-900 dark:text-white">
            <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30">
              <Phone className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 dark:text-green-400" />
            </div>
            Contato (Opcional)
          </CardTitle>
          <CardDescription className="text-xs sm:text-sm">
            Para comunicação via WhatsApp - apenas com consentimento
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="telefone" className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Telefone
            </Label>
            <Input
              id="telefone"
              type="tel"
              placeholder="(11) 99999-9999"
              value={anamnesisData.paciente.telefone}
              onChange={(e) => handlePhoneChange(e.target.value)}
              className={`rounded-xl transition-all duration-200 ${
                phoneError 
                  ? 'border-red-400 focus:border-red-500 focus:ring-red-500/20' 
                  : 'border-slate-300 dark:border-slate-600 focus:border-blue-500 focus:ring-blue-500/20 bg-white dark:bg-slate-800'
              }`}
            />
            {phoneError && (
              <motion.p 
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1"
              >
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                {phoneError}
              </motion.p>
            )}
          </div>

          {anamnesisData.paciente.telefone && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/50 rounded-xl"
            >
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="whatsapp-consent"
                  checked={anamnesisData.paciente.consentimentoWhatsApp}
                  onCheckedChange={(checked) => 
                    updateNestedAnamnesisData('paciente', 'consentimentoWhatsApp', checked)
                  }
                  className="mt-0.5 w-5 h-5 border-green-300 dark:border-green-700 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                />
                <div className="flex-1">
                  <Label htmlFor="whatsapp-consent" className="text-sm font-medium text-green-800 dark:text-green-200 cursor-pointer leading-relaxed">
                    Autorizo contato via WhatsApp para seguimento médico
                  </Label>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-1 leading-relaxed">
                    O número será usado apenas para comunicações médicas essenciais
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Summary Card */}
      {(anamnesisData.paciente.faixaEtaria && anamnesisData.paciente.sexoBiologico) && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4, duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
        >
          <Card className="bg-gradient-to-r from-emerald-50 via-blue-50 to-indigo-50 dark:from-emerald-900/20 dark:via-blue-900/20 dark:to-indigo-900/20 border-emerald-200 dark:border-emerald-800/50 shadow-xl shadow-emerald-500/10 rounded-xl">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center gap-3 sm:gap-4">
                <motion.div 
                  className="p-3 rounded-full bg-gradient-to-br from-emerald-500 to-blue-600 shadow-lg"
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <User className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                </motion.div>
                <div className="flex-1">
                  <div className="font-bold text-base sm:text-lg text-emerald-800 dark:text-emerald-200">
                    Identificação Confirmada
                  </div>
                  <div className="text-sm sm:text-base text-slate-600 dark:text-slate-300 mt-1">
                    <span className="font-medium">
                      {anamnesisData.paciente.faixaEtaria.charAt(0).toUpperCase() + anamnesisData.paciente.faixaEtaria.slice(1)}
                    </span>
                    <span className="mx-2">•</span>
                    <span className="font-medium">
                      {anamnesisData.paciente.sexoBiologico === 'F' ? 'Feminino' : 'Masculino'}
                    </span>
                    {anamnesisData.paciente.gestante && (
                      <>
                        <span className="mx-2">•</span>
                        <span className="font-medium text-pink-600 dark:text-pink-400">Gestante</span>
                      </>
                    )}
                    {anamnesisData.paciente.telefone && anamnesisData.paciente.consentimentoWhatsApp && (
                      <>
                        <span className="mx-2">•</span>
                        <span className="font-medium text-green-600 dark:text-green-400">WhatsApp autorizado</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}

export default PatientData