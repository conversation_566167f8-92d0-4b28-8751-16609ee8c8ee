import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON><PERSON> } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger, DialogFooter } from "./ui/dialog";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Label } from "./ui/label";
import { 
  Calendar, 
  Clock, 
  User, 
  Heart, 
  AlertTriangle, 
  CheckCircle,
  Plus,
  MoreHorizontal,
  Eye,
  Stethoscope,
  FileText,
  Activity,
  UserRound,
  Edit,
  ArrowRight,
  GripVertical,
  Save,
  X
} from "lucide-react";
import { motion, AnimatePresence } from "motion/react";
import { DndProvider, useDrag, useDrop, DragSourceMonitor } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useMedical, Patient } from "./MedicalContext";
import { useToastHelpers } from "./Toast";

export function KanbanPlantao() {
  const { patients, updatePatient, medicalColors } = useMedical();
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const toast = useToastHelpers();

  // Drag and Drop handlers
  const handlePatientMove = (patientId: string, newStatus: string) => {
    const patient = patients.find(p => p.id === patientId);
    updatePatient(patientId, { status: newStatus as any });
    
    if (patient) {
      const statusNames = {
        triagem: 'Triagem',
        atendimento: 'Em Atendimento',
        exames: 'Aguardando Exames',
        alta: 'Alta Médica',
        internacao: 'Internação'
      };
      
      toast.info(
        'Paciente Movido',
        `${patient.name} foi movido para ${statusNames[newStatus as keyof typeof statusNames]}`
      );
    }
  };

  const handlePatientEdit = (patient: Patient) => {
    setEditingPatient(patient);
  };

  const columns = [
    { 
      id: 'triagem', 
      title: 'Triagem', 
      color: medicalColors.warning,
      count: patients.filter(p => p.status === 'triagem').length 
    },
    { 
      id: 'atendimento', 
      title: 'Em Atendimento', 
      color: medicalColors.primary,
      count: patients.filter(p => p.status === 'atendimento').length 
    },
    { 
      id: 'exames', 
      title: 'Aguardando Exames', 
      color: medicalColors.neurology,
      count: patients.filter(p => p.status === 'exames').length 
    },
    { 
      id: 'alta', 
      title: 'Alta Médica', 
      color: medicalColors.success,
      count: patients.filter(p => p.status === 'alta').length 
    },
    { 
      id: 'internacao', 
      title: 'Internação', 
      color: medicalColors.danger,
      count: patients.filter(p => p.status === 'internacao').length 
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'emergencia': return medicalColors.danger;
      case 'alta': return medicalColors.cardiology;
      case 'media': return medicalColors.warning;
      case 'baixa': return medicalColors.success;
      default: return medicalColors.primary;
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'emergencia': return <AlertTriangle className="h-3 w-3" />;
      case 'alta': return <Heart className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const movePatient = (patientId: string, newStatus: 'triagem' | 'atendimento' | 'exames' | 'alta' | 'internacao') => {
    updatePatient(patientId, { status: newStatus });
  };

  const PatientCard = ({ patient }: { patient: Patient }) => (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.02 }}
      className="mb-3"
    >
      <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 border-l-4"
        style={{ borderLeftColor: getPriorityColor(patient.priority) }}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback style={{ backgroundColor: `${getPriorityColor(patient.priority)}20` }}>
                  <UserRound className="h-4 w-4" style={{ color: getPriorityColor(patient.priority) }} />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-sm">{patient.name}</p>
                <p className="text-xs text-muted-foreground">
                  {patient.age} anos • {patient.sex === 'M' ? 'Masculino' : 'Feminino'}
                </p>
              </div>
            </div>
            
            <Dialog>
              <DialogTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setSelectedPatient(patient)}
                  className="h-8 w-8 p-0"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <UserRound className="h-5 w-5" style={{ color: medicalColors.primary }} />
                    {patient.name} - Detalhes do Paciente
                  </DialogTitle>
                  <DialogDescription>
                    Visualize e gerencie as informações completas do paciente, incluindo dados básicos, anamnese e status de atendimento.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4">
                  {/* Informações básicas */}
                  <div className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Informações Básicas
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Nome:</span>
                            <p>{patient.name}</p>
                          </div>
                          <div>
                            <span className="font-medium">Idade:</span>
                            <p>{patient.age} anos</p>
                          </div>
                          <div>
                            <span className="font-medium">Sexo:</span>
                            <p>{patient.sex === 'M' ? 'Masculino' : 'Feminino'}</p>
                          </div>
                          <div>
                            <span className="font-medium">Chegada:</span>
                            <p>{patient.arrivalTime}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">Prioridade:</span>
                          <Badge 
                            className="text-xs text-white"
                            style={{ backgroundColor: getPriorityColor(patient.priority) }}
                          >
                            {getPriorityIcon(patient.priority)}
                            <span className="ml-1 capitalize">{patient.priority}</span>
                          </Badge>
                        </div>
                        
                        <div>
                          <span className="font-medium text-sm">Queixa Principal:</span>
                          <p className="text-sm">{patient.complaint}</p>
                        </div>
                        
                        {patient.room && (
                          <div>
                            <span className="font-medium text-sm">Localização:</span>
                            <p className="text-sm">{patient.room}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Ações rápidas */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <Activity className="h-4 w-4" />
                          Alterar Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-2">
                          {columns.map((column) => (
                            <motion.div key={column.id} whileTap={{ scale: 0.95 }}>
                              <Button
                                variant={patient.status === column.id ? "default" : "outline"}
                                size="sm"
                                onClick={() => movePatient(patient.id, column.id as any)}
                                className="w-full text-xs"
                                style={patient.status === column.id ? {
                                  backgroundColor: column.color,
                                  borderColor: column.color
                                } : { borderColor: `${column.color}40` }}
                              >
                                {column.title}
                              </Button>
                            </motion.div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Anamnese com cores atualizadas */}
                  {patient.anamnese && (
                    <div className="space-y-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base flex items-center gap-2">
                            <Stethoscope className="h-4 w-4" />
                            Anamnese Completa
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {/* História da Doença Atual */}
                          {patient.anamnese.informacoesAdicionais && (
                            <div>
                              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                                <Heart className="h-3 w-3" style={{ color: medicalColors.cardiology }} />
                                História da Doença Atual
                              </h4>
                              <div className="text-xs bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                <p className="whitespace-pre-wrap">{patient.anamnese.informacoesAdicionais}</p>
                                {patient.anamnese.duracaoDor && (
                                  <p className="mt-2"><span className="font-medium">Duração:</span> {patient.anamnese.duracaoDor}</p>
                                )}
                                {patient.anamnese.intensidadeDor && (
                                  <p><span className="font-medium">Intensidade:</span> {patient.anamnese.intensidadeDor[0]}/10</p>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Sinais Vitais */}
                          {(patient.anamnese.sinaisVitais.pressaoArterial || 
                            patient.anamnese.sinaisVitais.frequenciaCardiaca ||
                            patient.anamnese.sinaisVitais.temperatura) && (
                            <div>
                              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                                <Activity className="h-3 w-3" style={{ color: medicalColors.success }} />
                                Sinais Vitais
                              </h4>
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                {patient.anamnese.sinaisVitais.pressaoArterial && (
                                  <div 
                                    className="p-2 rounded"
                                    style={{ backgroundColor: `${medicalColors.primary}10` }}
                                  >
                                    <span className="font-medium">PA:</span> {patient.anamnese.sinaisVitais.pressaoArterial} mmHg
                                  </div>
                                )}
                                {patient.anamnese.sinaisVitais.frequenciaCardiaca && (
                                  <div 
                                    className="p-2 rounded"
                                    style={{ backgroundColor: `${medicalColors.cardiology}10` }}
                                  >
                                    <span className="font-medium">FC:</span> {patient.anamnese.sinaisVitais.frequenciaCardiaca} bpm
                                  </div>
                                )}
                                {patient.anamnese.sinaisVitais.temperatura && (
                                  <div 
                                    className="p-2 rounded"
                                    style={{ backgroundColor: `${medicalColors.warning}10` }}
                                  >
                                    <span className="font-medium">T:</span> {patient.anamnese.sinaisVitais.temperatura}°C
                                  </div>
                                )}
                                {patient.anamnese.sinaisVitais.glicemiaCapilar && (
                                  <div 
                                    className="p-2 rounded"
                                    style={{ backgroundColor: `${medicalColors.success}10` }}
                                  >
                                    <span className="font-medium">Glicemia:</span> {patient.anamnese.sinaisVitais.glicemiaCapilar} mg/dL
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Medicações */}
                          {patient.anamnese.medicamentos.length > 0 && (
                            <div>
                              <h4 className="font-medium text-sm mb-2">Medicações</h4>
                              <div className="flex flex-wrap gap-1">
                                {patient.anamnese.medicamentos.map((med, index) => (
                                  <Badge 
                                    key={index} 
                                    variant="secondary" 
                                    className="text-xs"
                                    style={{ backgroundColor: `${medicalColors.gastro}20`, color: medicalColors.gastro }}
                                  >
                                    {med}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Hipótese Diagnóstica */}
                          {patient.anamnese.hipoteseDiagnostica && (
                            <div>
                              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                                <FileText className="h-3 w-3" style={{ color: medicalColors.neurology }} />
                                Hipótese Diagnóstica
                              </h4>
                              <div 
                                className="text-xs p-3 rounded-lg"
                                style={{ backgroundColor: `${medicalColors.neurology}10` }}
                              >
                                {patient.anamnese.hipoteseDiagnostica}
                              </div>
                            </div>
                          )}

                          {/* Conduta */}
                          {patient.anamnese.conduta && (
                            <div>
                              <h4 className="font-medium text-sm mb-2">Conduta</h4>
                              <div 
                                className="text-xs p-3 rounded-lg"
                                style={{ backgroundColor: `${medicalColors.success}10` }}
                              >
                                {patient.anamnese.conduta}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Heart className="h-3 w-3" style={{ color: getPriorityColor(patient.priority) }} />
              <p className="text-sm font-medium">{patient.complaint}</p>
            </div>
            
            <div className="flex items-center justify-between">
              <Badge 
                className="text-xs text-white"
                style={{ backgroundColor: getPriorityColor(patient.priority) }}
              >
                {getPriorityIcon(patient.priority)}
                <span className="ml-1 capitalize">{patient.priority}</span>
              </Badge>
              
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                {patient.arrivalTime}
              </div>
            </div>
            
            {patient.room && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <User className="h-3 w-3" />
                {patient.room}
              </div>
            )}

            {patient.anamnese && (
              <div className="pt-2 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Anamnese disponível</span>
                  <Eye className="h-3 w-3" style={{ color: medicalColors.success }} />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex flex-col h-full bg-background">
        {/* Header com cores padronizadas */}
        <div className="border-b bg-card/95 backdrop-blur-sm p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 rounded-xl bg-primary/10">
                <Calendar className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-foreground">
                  Plantão Médico
                </h1>
                <p className="text-sm text-muted-foreground">
                  Gestão inteligente de pacientes em tempo real
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-right">
                <div className="text-sm font-semibold text-foreground">{patients.length} pacientes</div>
                <div className="text-xs text-muted-foreground">
                  {patients.filter(p => p.status === 'atendimento').length} em atendimento
                </div>
              </div>
              <Button 
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Novo Paciente
              </Button>
              <Button 
                variant="outline"
                size="icon"
                onClick={() => setShowSettings(true)}
              >
                <Edit className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div className="flex-1 p-6 overflow-hidden">
          <div className="grid grid-cols-5 gap-6 h-full">
            {columns.map((column) => (
              <KanbanColumn 
                key={column.id} 
                column={column}
                patients={patients.filter(patient => patient.status === column.id)}
                onPatientMove={handlePatientMove}
                onPatientEdit={handlePatientEdit}
              />
            ))}
          </div>
        </div>

        {/* Modal de Edição de Paciente */}
        <EditPatientModal 
          patient={editingPatient}
          onClose={() => setEditingPatient(null)}
          onSave={(updatedPatient) => {
            if (editingPatient) {
              updatePatient(editingPatient.id, updatedPatient);
              toast.patientUpdated(updatedPatient.name || editingPatient.name);
              setEditingPatient(null);
            }
          }}
        />

        {/* Modal de Configurações Profissionais */}
        <ProfessionalSettingsModal 
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    </DndProvider>
  );
}

// Componente KanbanColumn com Drag and Drop
interface KanbanColumnProps {
  column: {
    id: string;
    title: string;
    color: string;
    count: number;
  };
  patients: Patient[];
  onPatientMove: (patientId: string, newStatus: string) => void;
  onPatientEdit: (patient: Patient) => void;
}

const KanbanColumn: React.FC<KanbanColumnProps> = ({ column, patients, onPatientMove, onPatientEdit }) => {
  const [{ isOver }, drop] = useDrop({
    accept: 'patient',
    drop: (item: { id: string }) => {
      onPatientMove(item.id, column.id);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  const getColumnColor = (id: string) => {
    switch (id) {
      case 'triagem': return 'border-amber-500 bg-amber-50 dark:bg-amber-950';
      case 'atendimento': return 'border-blue-500 bg-blue-50 dark:bg-blue-950';
      case 'exames': return 'border-purple-500 bg-purple-50 dark:bg-purple-950';
      case 'alta': return 'border-green-500 bg-green-50 dark:bg-green-950';
      case 'internacao': return 'border-red-500 bg-red-50 dark:bg-red-950';
      default: return 'border-gray-500 bg-gray-50 dark:bg-gray-950';
    }
  };

  const getColumnIconColor = (id: string) => {
    switch (id) {
      case 'triagem': return 'text-amber-600';
      case 'atendimento': return 'text-blue-600';
      case 'exames': return 'text-purple-600';
      case 'alta': return 'text-green-600';
      case 'internacao': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <motion.div 
      ref={drop}
      className={`flex flex-col h-full border-2 border-dashed rounded-lg transition-colors ${
        isOver ? 'border-primary bg-primary/5' : getColumnColor(column.id)
      }`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
    >
      <div className="p-4 border-b border-border/50">
        <div className="flex items-center justify-between">
          <h3 className={`font-semibold text-sm ${getColumnIconColor(column.id)}`}>
            {column.title}
          </h3>
          <Badge variant="secondary" className="text-xs">
            {column.count}
          </Badge>
        </div>
      </div>
      
      <div className="flex-1 p-4 overflow-y-auto space-y-3">
        <AnimatePresence>
          {patients.map((patient) => (
            <DraggablePatientCard 
              key={patient.id} 
              patient={patient} 
              onEdit={onPatientEdit}
            />
          ))}
        </AnimatePresence>
        
        {patients.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Nenhum paciente</p>
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Componente DraggablePatientCard
interface DraggablePatientCardProps {
  patient: Patient;
  onEdit: (patient: Patient) => void;
}

const DraggablePatientCard: React.FC<DraggablePatientCardProps> = ({ patient, onEdit }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'patient',
    item: { id: patient.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'emergencia': 
        return { color: 'border-red-500 bg-red-50 dark:bg-red-950', textColor: 'text-red-700 dark:text-red-300', icon: AlertTriangle };
      case 'alta': 
        return { color: 'border-orange-500 bg-orange-50 dark:bg-orange-950', textColor: 'text-orange-700 dark:text-orange-300', icon: Heart };
      case 'media': 
        return { color: 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950', textColor: 'text-yellow-700 dark:text-yellow-300', icon: Clock };
      case 'baixa': 
        return { color: 'border-green-500 bg-green-50 dark:bg-green-950', textColor: 'text-green-700 dark:text-green-300', icon: CheckCircle };
      default: 
        return { color: 'border-gray-500 bg-gray-50 dark:bg-gray-950', textColor: 'text-gray-700 dark:text-gray-300', icon: Clock };
    }
  };

  const priorityConfig = getPriorityConfig(patient.priority);
  const PriorityIcon = priorityConfig.icon;

  return (
    <motion.div
      ref={drag}
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.02 }}
      className={`cursor-move ${isDragging ? 'opacity-50' : 'opacity-100'}`}
    >
      <Card className={`border-l-4 hover:shadow-md transition-all duration-200 ${priorityConfig.color}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <GripVertical className="h-4 w-4 text-muted-foreground" />
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-primary/10">
                  <UserRound className="h-4 w-4 text-primary" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-sm text-foreground">{patient.name}</p>
                <p className="text-xs text-muted-foreground">
                  {patient.age} anos • {patient.sex === 'M' ? 'Masculino' : 'Feminino'}
                </p>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onEdit(patient)}
            >
              <Edit className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <PriorityIcon className={`h-3 w-3 ${priorityConfig.textColor}`} />
              <span className={`text-xs font-medium ${priorityConfig.textColor}`}>
                {patient.priority.charAt(0).toUpperCase() + patient.priority.slice(1)}
              </span>
            </div>
            
            {patient.symptoms && (
              <div className="text-xs text-muted-foreground">
                <strong>Sintomas:</strong> {patient.symptoms}
              </div>
            )}
            
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Chegada: {new Date(patient.arrivalTime).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}</span>
              {patient.anamnese && (
                <div className="flex items-center gap-1">
                  <Eye className="h-3 w-3 text-green-600" />
                  <span className="text-green-600">Anamnese</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Modal de Edição de Paciente
interface EditPatientModalProps {
  patient: Patient | null;
  onClose: () => void;
  onSave: (updatedData: Partial<Patient>) => void;
}

const EditPatientModal: React.FC<EditPatientModalProps> = ({ patient, onClose, onSave }) => {
  const [formData, setFormData] = useState<Partial<Patient>>({});

  React.useEffect(() => {
    if (patient) {
      setFormData({
        name: patient.name,
        age: patient.age,
        sex: patient.sex,
        priority: patient.priority,
        symptoms: patient.symptoms,
        status: patient.status
      });
    }
  }, [patient]);

  const handleSave = () => {
    onSave(formData);
    // Toast será mostrado pelo componente pai
  };

  if (!patient) return null;

  return (
    <Dialog open={!!patient} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Editar Paciente</DialogTitle>
          <DialogDescription>
            Atualize as informações do paciente
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Nome</Label>
            <Input
              id="name"
              value={formData.name || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="age">Idade</Label>
              <Input
                id="age"
                type="number"
                value={formData.age || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, age: parseInt(e.target.value) }))}
              />
            </div>
            <div>
              <Label htmlFor="sex">Sexo</Label>
              <Select value={formData.sex} onValueChange={(value) => setFormData(prev => ({ ...prev, sex: value as 'M' | 'F' }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="M">Masculino</SelectItem>
                  <SelectItem value="F">Feminino</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="priority">Prioridade</Label>
            <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="baixa">Baixa</SelectItem>
                <SelectItem value="media">Média</SelectItem>
                <SelectItem value="alta">Alta</SelectItem>
                <SelectItem value="emergencia">Emergência</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="status">Status</Label>
            <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="triagem">Triagem</SelectItem>
                <SelectItem value="atendimento">Em Atendimento</SelectItem>
                <SelectItem value="exames">Aguardando Exames</SelectItem>
                <SelectItem value="alta">Alta Médica</SelectItem>
                <SelectItem value="internacao">Internação</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="symptoms">Sintomas</Label>
            <Textarea
              id="symptoms"
              value={formData.symptoms || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, symptoms: e.target.value }))}
              placeholder="Descreva os sintomas..."
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Modal de Configurações Profissionais
interface ProfessionalSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProfessionalSettingsModal: React.FC<ProfessionalSettingsModalProps> = ({ isOpen, onClose }) => {
  const [settings, setSettings] = useState({
    doctorName: 'Dr. João Silva',
    crm: 'CRM 123456-SP',
    specialty: 'Clínica Médica',
    hospital: 'Hospital São Lucas',
    workShift: 'Plantão Noturno',
    notifications: true,
    autoRefresh: true,
    priorityAlerts: true
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Configurações Profissionais</DialogTitle>
          <DialogDescription>
            Configure suas informações e preferências profissionais
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-foreground">Informações Profissionais</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="doctorName">Nome Completo</Label>
                <Input
                  id="doctorName"
                  value={settings.doctorName}
                  onChange={(e) => setSettings(prev => ({ ...prev, doctorName: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="crm">CRM</Label>
                <Input
                  id="crm"
                  value={settings.crm}
                  onChange={(e) => setSettings(prev => ({ ...prev, crm: e.target.value }))}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="specialty">Especialidade</Label>
              <Select value={settings.specialty} onValueChange={(value) => setSettings(prev => ({ ...prev, specialty: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Clínica Médica">Clínica Médica</SelectItem>
                  <SelectItem value="Cardiologia">Cardiologia</SelectItem>
                  <SelectItem value="Neurologia">Neurologia</SelectItem>
                  <SelectItem value="Pediatria">Pediatria</SelectItem>
                  <SelectItem value="Ortopedia">Ortopedia</SelectItem>
                  <SelectItem value="Ginecologia">Ginecologia</SelectItem>
                  <SelectItem value="Medicina de Emergência">Medicina de Emergência</SelectItem>
                  <SelectItem value="Medicina Interna">Medicina Interna</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="hospital">Hospital/Clínica</Label>
                <Input
                  id="hospital"
                  value={settings.hospital}
                  onChange={(e) => setSettings(prev => ({ ...prev, hospital: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="workShift">Turno de Trabalho</Label>
                <Select value={settings.workShift} onValueChange={(value) => setSettings(prev => ({ ...prev, workShift: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Plantão Diurno">Plantão Diurno</SelectItem>
                    <SelectItem value="Plantão Noturno">Plantão Noturno</SelectItem>
                    <SelectItem value="Consultório">Consultório</SelectItem>
                    <SelectItem value="Emergência">Emergência</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-foreground">Preferências do Sistema</h4>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="notifications" className="text-sm">Notificações de Sistema</Label>
                <input
                  type="checkbox"
                  id="notifications"
                  checked={settings.notifications}
                  onChange={(e) => setSettings(prev => ({ ...prev, notifications: e.target.checked }))}
                  className="rounded border-gray-300"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="autoRefresh" className="text-sm">Atualização Automática</Label>
                <input
                  type="checkbox"
                  id="autoRefresh"
                  checked={settings.autoRefresh}
                  onChange={(e) => setSettings(prev => ({ ...prev, autoRefresh: e.target.checked }))}
                  className="rounded border-gray-300"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="priorityAlerts" className="text-sm">Alertas de Prioridade</Label>
                <input
                  type="checkbox"
                  id="priorityAlerts"
                  checked={settings.priorityAlerts}
                  onChange={(e) => setSettings(prev => ({ ...prev, priorityAlerts: e.target.checked }))}
                  className="rounded border-gray-300"
                />
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={onClose}>
            <Save className="h-4 w-4 mr-2" />
            Salvar Configurações
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};