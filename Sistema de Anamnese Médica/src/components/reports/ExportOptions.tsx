import { useState } from 'react'
import { 
  Download, 
  FileText, 
  File, 
  Image,
  Code,
  Settings,
  CheckCircle,
  Loader2,
  X
} from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '../ui/dialog'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { Switch } from '../ui/switch'
import { Label } from '../ui/label'
import { Separator } from '../ui/separator'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { MedicalReport } from '../../services/reports/ReportGenerator'
import { ExportService, ExportFormat } from '../../services/export/ExportService'
import { toast } from 'sonner'

interface ExportOptionsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  report: MedicalReport
  includeAIAnalysis?: boolean
}

export function ExportOptions({ 
  open, 
  onOpenChange, 
  report, 
  includeAIAnalysis = true 
}: ExportOptionsProps) {
  const [selectedFormats, setSelectedFormats] = useState<Set<ExportFormat>>(new Set(['pdf']))
  const [includeMetadata, setIncludeMetadata] = useState(true)
  const [customFilename, setCustomFilename] = useState('')
  const [isExporting, setIsExporting] = useState(false)
  const [exportedFormats, setExportedFormats] = useState<Set<ExportFormat>>(new Set())

  const exportService = ExportService.getInstance()

  const formatOptions = [
    {
      format: 'txt' as ExportFormat,
      name: 'Texto Simples',
      description: 'Arquivo de texto para cópia e colagem',
      icon: FileText,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      extension: 'txt'
    },
    {
      format: 'html' as ExportFormat,
      name: 'HTML Formatado',
      description: 'Página web com formatação e estilos',
      icon: Code,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      extension: 'html'
    },
    {
      format: 'pdf' as ExportFormat,
      name: 'PDF Profissional',
      description: 'Documento PDF para impressão e arquivo',
      icon: File,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      extension: 'pdf'
    },
    {
      format: 'docx' as ExportFormat,
      name: 'Microsoft Word',
      description: 'Documento editável do Microsoft Word',
      icon: Image,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      extension: 'docx'
    }
  ]

  const handleFormatToggle = (format: ExportFormat) => {
    const newFormats = new Set(selectedFormats)
    if (newFormats.has(format)) {
      newFormats.delete(format)
    } else {
      newFormats.add(format)
    }
    setSelectedFormats(newFormats)
  }

  const handleExport = async () => {
    if (selectedFormats.size === 0) {
      toast.error('Selecione pelo menos um formato para exportação')
      return
    }

    setIsExporting(true)
    setExportedFormats(new Set())

    try {
      const exportPromises = Array.from(selectedFormats).map(async (format) => {
        const filename = customFilename || 
          `relatorio_${report.anamnesis.paciente.id}_${new Date().toISOString().split('T')[0]}`

        await exportService.exportReport(report, {
          format,
          filename: `${filename}.${formatOptions.find(f => f.format === format)?.extension}`,
          includeAIAnalysis,
          includeMetadata
        })

        setExportedFormats(prev => new Set([...prev, format]))
        return format
      })

      const results = await Promise.allSettled(exportPromises)
      
      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length

      if (successful > 0) {
        toast.success(`${successful} arquivo(s) exportado(s) com sucesso!`)
      }
      
      if (failed > 0) {
        toast.error(`Erro ao exportar ${failed} arquivo(s)`)
        console.error('Export errors:', results.filter(r => r.status === 'rejected'))
      }

      // Close dialog after successful export
      if (failed === 0) {
        setTimeout(() => onOpenChange(false), 1000)
      }
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Erro durante a exportação')
    } finally {
      setIsExporting(false)
    }
  }

  const generateFilename = () => {
    const date = new Date().toISOString().split('T')[0]
    const patientId = report.anamnesis.paciente.id
    return `relatorio_${patientId}_${date}`
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Opções de Exportação
          </DialogTitle>
          <DialogDescription>
            Escolha os formatos e configurações para exportar o relatório médico
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Formatos Disponíveis</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {formatOptions.map((option) => {
                const isSelected = selectedFormats.has(option.format)
                const isExported = exportedFormats.has(option.format)
                const Icon = option.icon
                
                return (
                  <Card 
                    key={option.format}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    } ${isExported ? 'bg-green-50 border-green-200' : ''}`}
                    onClick={() => !isExporting && handleFormatToggle(option.format)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg ${option.bgColor}`}>
                            <Icon className={`h-4 w-4 ${option.color}`} />
                          </div>
                          <div className="space-y-1">
                            <h4 className="text-sm font-medium">{option.name}</h4>
                            <p className="text-xs text-muted-foreground">
                              {option.description}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {isExported && (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          )}
                          {isSelected && !isExported && (
                            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                              <CheckCircle className="h-3 w-3 text-white" />
                            </div>
                          )}
                          {!isSelected && !isExported && (
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-full" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          <Separator />

          {/* Export Options */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Opções de Exportação
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="include-ai" className="text-sm font-medium">
                    Incluir Análise de IA
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Adicionar diagnósticos diferenciais e recomendações
                  </p>
                </div>
                <Switch
                  id="include-ai"
                  checked={includeAIAnalysis && !!report.aiAnalysis}
                  disabled={!report.aiAnalysis}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="include-metadata" className="text-sm font-medium">
                    Incluir Metadados
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Data de geração, versão do sistema, etc.
                  </p>
                </div>
                <Switch
                  id="include-metadata"
                  checked={includeMetadata}
                  onCheckedChange={setIncludeMetadata}
                />
              </div>
            </div>
          </div>

          {/* Filename */}
          <div className="space-y-2">
            <Label htmlFor="filename" className="text-sm font-medium">
              Nome do Arquivo (opcional)
            </Label>
            <input
              id="filename"
              type="text"
              placeholder={generateFilename()}
              value={customFilename}
              onChange={(e) => setCustomFilename(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isExporting}
            />
            <p className="text-xs text-muted-foreground">
              Se não especificado, será usado: {generateFilename()}
            </p>
          </div>

          <Separator />

          {/* Summary */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Resumo da Exportação</h3>
            
            <div className="bg-muted/50 p-3 rounded-lg space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Formatos selecionados:</span>
                <Badge variant="secondary">
                  {selectedFormats.size} de {formatOptions.length}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span>Incluir análise de IA:</span>
                <Badge variant={includeAIAnalysis && report.aiAnalysis ? "default" : "secondary"}>
                  {includeAIAnalysis && report.aiAnalysis ? 'Sim' : 'Não'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span>ID do Paciente:</span>
                <code className="text-xs bg-white px-2 py-1 rounded">
                  {report.anamnesis.paciente.id}
                </code>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isExporting}
              className="gap-2"
            >
              <X className="h-4 w-4" />
              Cancelar
            </Button>
            
            <Button
              onClick={handleExport}
              disabled={selectedFormats.size === 0 || isExporting}
              className="gap-2"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              {isExporting ? 'Exportando...' : `Exportar ${selectedFormats.size} arquivo(s)`}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}