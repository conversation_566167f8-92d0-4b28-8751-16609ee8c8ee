import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { 
  FileText, 
  Download, 
  Eye, 
  CheckCircle, 
  AlertCircle,
  Brain,
  Activity,
  TrendingUp,
  Shield,
  Lightbulb,
  AlertTriangle,
  RefreshCw,
  Settings
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { Progress } from '../ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Switch } from '../ui/switch'
import { Label } from '../ui/label'
import { useMedical } from '../MedicalContext'
import { DiagnosticSuggestions } from './DiagnosticSuggestions'
import { TreatmentRecommendations } from './TreatmentRecommendations'
import { ExportOptions } from './ExportOptions'
import { ReportGenerator, MedicalReport } from '../../services/reports/ReportGenerator'
import { DiagnosticService } from '../../services/ai/DiagnosticService'
import { AIAnalysisResult } from '../../types/ai-suggestions.types'
import { toast } from 'sonner'

export function ReportViewer() {
  const { anamnesisData, medicalColors } = useMedical()
  const [report, setReport] = useState<MedicalReport | null>(null)
  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysisResult | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [includeAI, setIncludeAI] = useState(true)
  const [autoAnalyze, setAutoAnalyze] = useState(true)
  const [showExportOptions, setShowExportOptions] = useState(false)

  const reportGenerator = ReportGenerator.getInstance()
  const diagnosticService = DiagnosticService.getInstance()

  useEffect(() => {
    generateReport()
  }, [anamnesisData])

  useEffect(() => {
    if (autoAnalyze && includeAI && report && !aiAnalysis) {
      performAIAnalysis()
    }
  }, [report, includeAI, autoAnalyze])

  const generateReport = () => {
    const newReport = reportGenerator.generateReport(anamnesisData, aiAnalysis || undefined)
    setReport(newReport)
  }

  const performAIAnalysis = async () => {
    if (isAnalyzing) return

    setIsAnalyzing(true)
    try {
      toast.info('Iniciando análise com IA...')
      const analysis = await diagnosticService.analyzeAnamnesis(anamnesisData)
      setAiAnalysis(analysis)
      
      // Regenerate report with AI analysis
      const updatedReport = reportGenerator.generateReport(anamnesisData, analysis)
      setReport(updatedReport)
      
      toast.success(`Análise concluída com ${Math.round(analysis.confidence * 100)}% de confiança`)
    } catch (error) {
      console.error('Erro na análise de IA:', error)
      toast.error('Erro ao realizar análise com IA')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleToggleAI = (enabled: boolean) => {
    setIncludeAI(enabled)
    if (enabled && !aiAnalysis && autoAnalyze) {
      performAIAnalysis()
    } else if (!enabled) {
      setAiAnalysis(null)
      generateReport() // Regenerate without AI
    }
  }

  const getAnalysisStatusColor = () => {
    if (isAnalyzing) return medicalColors.warning
    if (aiAnalysis) return medicalColors.success
    return medicalColors.secondary
  }

  const getAnalysisStatusIcon = () => {
    if (isAnalyzing) return RefreshCw
    if (aiAnalysis) return CheckCircle
    return AlertCircle
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return medicalColors.success
    if (confidence >= 0.6) return medicalColors.warning
    return medicalColors.error
  }

  if (!report) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Gerando relatório...</p>
        </div>
      </div>
    )
  }

  const StatusIcon = getAnalysisStatusIcon()

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-l-4 openai-card" style={{ borderLeftColor: medicalColors.neurology }}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div 
                className="p-2 rounded-lg"
                style={{ backgroundColor: `${medicalColors.neurology}20` }}
              >
                <FileText className="h-5 w-5" style={{ color: medicalColors.neurology }} />
              </div>
              <div>
                <CardTitle className="text-lg">Relatório Médico Completo</CardTitle>
                <CardDescription>Análise abrangente com suporte de IA</CardDescription>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="flex items-center space-x-2">
                <Switch
                  id="ai-analysis"
                  checked={includeAI}
                  onCheckedChange={handleToggleAI}
                  disabled={isAnalyzing}
                />
                <Label htmlFor="ai-analysis" className="text-sm">Análise com IA</Label>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowExportOptions(true)}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Exportar
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* AI Analysis Status */}
      {includeAI && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <StatusIcon 
                  className={`h-5 w-5 ${isAnalyzing ? 'animate-spin' : ''}`}
                  style={{ color: getAnalysisStatusColor() }}
                />
                <div>
                  <p className="font-medium">
                    {isAnalyzing ? 'Analisando...' : aiAnalysis ? 'Análise Concluída' : 'Análise Pendente'}
                  </p>
                  {aiAnalysis && (
                    <p className="text-sm text-muted-foreground">
                      Confiança: {Math.round(aiAnalysis.confidence * 100)}%
                    </p>
                  )}
                </div>
              </div>

              {aiAnalysis && (
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    {aiAnalysis.differentialDiagnosis.length} diagnósticos
                  </Badge>
                  <Badge variant="secondary">
                    {aiAnalysis.treatmentRecommendations.length} tratamentos
                  </Badge>
                </div>
              )}

              {!isAnalyzing && !aiAnalysis && (
                <Button
                  onClick={performAIAnalysis}
                  size="sm"
                  className="gap-2"
                >
                  <Brain className="h-4 w-4" />
                  Analisar
                </Button>
              )}
            </div>

            {aiAnalysis && (
              <div className="mt-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-muted-foreground">Confiança da Análise</span>
                  <span 
                    className="text-sm font-medium"
                    style={{ color: getConfidenceColor(aiAnalysis.confidence) }}
                  >
                    {Math.round(aiAnalysis.confidence * 100)}%
                  </span>
                </div>
                <Progress 
                  value={aiAnalysis.confidence * 100} 
                  className="h-2"
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="gap-2">
            <Eye className="h-4 w-4" />
            Visão Geral
          </TabsTrigger>
          <TabsTrigger value="diagnosis" className="gap-2" disabled={!aiAnalysis}>
            <Brain className="h-4 w-4" />
            Diagnósticos
          </TabsTrigger>
          <TabsTrigger value="treatment" className="gap-2" disabled={!aiAnalysis}>
            <Activity className="h-4 w-4" />
            Tratamentos
          </TabsTrigger>
          <TabsTrigger value="insights" className="gap-2" disabled={!aiAnalysis}>
            <Lightbulb className="h-4 w-4" />
            Insights
          </TabsTrigger>
          <TabsTrigger value="raw" className="gap-2">
            <FileText className="h-4 w-4" />
            Relatório
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Patient Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Resumo do Paciente</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">ID:</span>
                  <div className="font-mono">{report.anamnesis.paciente.id}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Faixa Etária:</span>
                  <div>{report.anamnesis.paciente.faixaEtaria || 'Não informado'}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Sexo:</span>
                  <div>
                    {report.anamnesis.paciente.sexoBiologico === 'F' ? 'Feminino' : 
                     report.anamnesis.paciente.sexoBiologico === 'M' ? 'Masculino' : 'Não informado'}
                  </div>
                </div>
                <div>
                  <span className="text-muted-foreground">Gerado em:</span>
                  <div>{report.generatedAt.toLocaleString('pt-BR')}</div>
                </div>
              </div>

              {report.anamnesis.paciente.gestante && (
                <Badge className="bg-pink-100 text-pink-800">🤰 Gestante</Badge>
              )}
            </CardContent>
          </Card>

          {/* Chief Complaint */}
          {report.anamnesis.queixaPrincipal.queixaPrincipal && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Queixa Principal</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-3">{report.anamnesis.queixaPrincipal.queixaPrincipal}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  {report.anamnesis.queixaPrincipal.duracaoSintomas && (
                    <div>
                      <span className="text-muted-foreground">Duração:</span>
                      <div>{report.anamnesis.queixaPrincipal.duracaoSintomas}</div>
                    </div>
                  )}
                  
                  {report.anamnesis.queixaPrincipal.intensidade && (
                    <div>
                      <span className="text-muted-foreground">Intensidade:</span>
                      <div className="flex items-center gap-2">
                        {report.anamnesis.queixaPrincipal.intensidade}/10
                        {report.anamnesis.queixaPrincipal.intensidade >= 8 && 
                          <Badge variant="destructive" className="text-xs">Alta</Badge>
                        }
                      </div>
                    </div>
                  )}
                </div>

                {report.anamnesis.queixaPrincipal.caracteristicas.length > 0 && (
                  <div className="mt-3">
                    <span className="text-sm text-muted-foreground">Características:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {report.anamnesis.queixaPrincipal.caracteristicas.map((char, index) => (
                        <Badge key={index} variant="outline" className="text-xs">{char}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* AI Highlights */}
          {aiAnalysis && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Top Diagnosis */}
              {aiAnalysis.differentialDiagnosis.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-blue-600" />
                      Hipótese Principal
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="font-medium text-sm">{aiAnalysis.differentialDiagnosis[0].name}</p>
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          {aiAnalysis.differentialDiagnosis[0].icd10}
                        </Badge>
                        <span 
                          className="text-sm font-medium"
                          style={{ color: medicalColors.primary }}
                        >
                          {Math.round(aiAnalysis.differentialDiagnosis[0].probability * 100)}%
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Risk Level */}
              {aiAnalysis.riskFactors.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Shield className="h-4 w-4 text-orange-600" />
                      Nível de Risco
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="font-medium text-sm">{aiAnalysis.riskFactors[0].factor}</p>
                      <Badge 
                        variant={aiAnalysis.riskFactors[0].level === 'alto' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {aiAnalysis.riskFactors[0].level.charAt(0).toUpperCase() + aiAnalysis.riskFactors[0].level.slice(1)}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Red Flags */}
              {aiAnalysis.redFlags.length > 0 && (
                <Card className="border-orange-200">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      Sinais de Alerta
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1">
                      {aiAnalysis.redFlags.slice(0, 2).map((flag, index) => (
                        <p key={index} className="text-sm text-red-700">{flag}</p>
                      ))}
                      {aiAnalysis.redFlags.length > 2 && (
                        <p className="text-xs text-muted-foreground">
                          +{aiAnalysis.redFlags.length - 2} mais
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="diagnosis">
          {aiAnalysis && (
            <DiagnosticSuggestions 
              diagnoses={aiAnalysis.differentialDiagnosis}
              confidence={aiAnalysis.confidence}
            />
          )}
        </TabsContent>

        <TabsContent value="treatment">
          {aiAnalysis && (
            <TreatmentRecommendations 
              treatments={aiAnalysis.treatmentRecommendations}
              exams={aiAnalysis.additionalExams}
            />
          )}
        </TabsContent>

        <TabsContent value="insights">
          {aiAnalysis && (
            <div className="space-y-6">
              {/* Clinical Pearls */}
              {aiAnalysis.clinicalPearls.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-600" />
                      Pérolas Clínicas
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {aiAnalysis.clinicalPearls.map((pearl, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                          <p className="text-sm text-yellow-800">{pearl}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Reasoning */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Raciocínio Clínico</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm leading-relaxed">{aiAnalysis.reasoning}</p>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="raw">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Relatório Completo</CardTitle>
              <CardDescription>Formato texto para cópia ou impressão</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="whitespace-pre-wrap text-xs bg-muted p-4 rounded-lg overflow-auto max-h-96 font-mono">
                {report.exportFormats.txt}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Export Options Dialog */}
      <ExportOptions
        open={showExportOptions}
        onOpenChange={setShowExportOptions}
        report={report}
        includeAIAnalysis={includeAI && !!aiAnalysis}
      />
    </div>
  )
}