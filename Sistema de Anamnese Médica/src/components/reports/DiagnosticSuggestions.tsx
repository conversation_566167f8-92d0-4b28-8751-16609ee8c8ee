import { motion } from 'motion/react'
import { 
  Brain, 
  <PERSON><PERSON>dingUp, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  CheckCircle,
  Clock,
  Info,
  ExternalLink
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { Separator } from '../ui/separator'
import { DiagnosisSuggestion } from '../../types/ai-suggestions.types'

interface DiagnosticSuggestionsProps {
  diagnoses: DiagnosisSuggestion[]
  confidence: number
}

export function DiagnosticSuggestions({ diagnoses, confidence }: DiagnosticSuggestionsProps) {
  const getProbabilityColor = (probability: number) => {
    if (probability >= 0.8) return 'text-green-600 bg-green-100'
    if (probability >= 0.6) return 'text-yellow-600 bg-yellow-100'
    if (probability >= 0.4) return 'text-orange-600 bg-orange-100'
    return 'text-red-600 bg-red-100'
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergencia': return 'bg-red-100 text-red-800 border-red-200'
      case 'alta': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'media': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'baixa': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'emergencia': return <AlertTriangle className="h-3 w-3" />
      case 'alta': return <TrendingUp className="h-3 w-3" />
      case 'media': return <Clock className="h-3 w-3" />
      case 'baixa': return <CheckCircle className="h-3 w-3" />
      default: return <Info className="h-3 w-3" />
    }
  }

  const sortedDiagnoses = [...diagnoses].sort((a, b) => b.probability - a.probability)

  return (
    <div className="space-y-6">
      {/* Header with overall confidence */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Brain className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Diagnósticos Diferenciais</CardTitle>
                <CardDescription>
                  Sugestões baseadas em análise de padrões clínicos
                </CardDescription>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm text-muted-foreground">Confiança Geral</div>
              <div className="text-2xl font-bold text-blue-600">
                {Math.round(confidence * 100)}%
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Diagnostic suggestions */}
      <div className="space-y-4">
        {sortedDiagnoses.map((diagnosis, index) => (
          <motion.div
            key={diagnosis.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className={`${index === 0 ? 'ring-2 ring-blue-200 shadow-lg' : ''} hover:shadow-md transition-shadow`}>
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">{diagnosis.name}</CardTitle>
                      {index === 0 && (
                        <Badge variant="default" className="text-xs">
                          Principal
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono text-xs">
                        CID-10: {diagnosis.icd10}
                      </Badge>
                      <Badge 
                        className={`${getUrgencyColor(diagnosis.urgency)} text-xs gap-1`}
                      >
                        {getUrgencyIcon(diagnosis.urgency)}
                        {diagnosis.urgency.charAt(0).toUpperCase() + diagnosis.urgency.slice(1)}
                      </Badge>
                    </div>
                  </div>

                  <div className="text-right">
                    <div 
                      className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${getProbabilityColor(diagnosis.probability)}`}
                    >
                      {Math.round(diagnosis.probability * 100)}%
                    </div>
                  </div>
                </div>

                {/* Probability bar */}
                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Compatibilidade com sintomas</span>
                  </div>
                  <Progress 
                    value={diagnosis.probability * 100} 
                    className="h-2"
                  />
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Evidence */}
                {diagnosis.evidence.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Evidências que Suportam o Diagnóstico
                    </h4>
                    <div className="space-y-1">
                      {diagnosis.evidence.map((evidence, evidenceIndex) => (
                        <div 
                          key={evidenceIndex}
                          className="text-sm bg-green-50 text-green-800 px-3 py-2 rounded-md flex items-center gap-2"
                        >
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full flex-shrink-0" />
                          {evidence}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Related symptoms */}
                {diagnosis.relatedSymptoms.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Sintomas Relacionados</h4>
                    <div className="flex flex-wrap gap-1">
                      {diagnosis.relatedSymptoms.map((symptom, symptomIndex) => (
                        <Badge 
                          key={symptomIndex}
                          variant="secondary" 
                          className="text-xs"
                        >
                          {symptom}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Additional tests */}
                {diagnosis.additionalTests && diagnosis.additionalTests.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <Info className="h-4 w-4 text-blue-600" />
                      Exames para Confirmação
                    </h4>
                    <div className="space-y-1">
                      {diagnosis.additionalTests.map((test, testIndex) => (
                        <div 
                          key={testIndex}
                          className="text-sm bg-blue-50 text-blue-800 px-3 py-2 rounded-md"
                        >
                          {test}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2 border-t">
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="gap-2 text-xs"
                  >
                    <ExternalLink className="h-3 w-3" />
                    Guidelines
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="gap-2 text-xs"
                  >
                    <Info className="h-3 w-3" />
                    Mais Info
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Disclaimer */}
      <Card className="border-amber-200 bg-amber-50">
        <CardContent className="pt-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
            <div className="space-y-2">
              <h4 className="font-medium text-amber-800">Importante</h4>
              <p className="text-sm text-amber-700">
                Estas sugestões são baseadas em análise computacional e não substituem 
                o julgamento clínico profissional. Sempre considere o contexto clínico 
                completo e realize exames confirmatórios quando necessário.
              </p>
              <p className="text-xs text-amber-600">
                Análise realizada com {Math.round(confidence * 100)}% de confiança • 
                Baseada em padrões médicos conhecidos • 
                Última atualização: {new Date().toLocaleString('pt-BR')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}