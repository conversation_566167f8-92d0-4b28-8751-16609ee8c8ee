import { motion } from 'motion/react'
import { 
  Activity, 
  Pill, 
  Heart,
  Stethoscope,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
  FileText,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Separator } from '../ui/separator'
import { TreatmentOption, ExamSuggestion } from '../../types/ai-suggestions.types'

interface TreatmentRecommendationsProps {
  treatments: TreatmentOption[]
  exams: ExamSuggestion[]
}

export function TreatmentRecommendations({ treatments, exams }: TreatmentRecommendationsProps) {
  const getTreatmentIcon = (type: string) => {
    switch (type) {
      case 'medicamentoso': return <Pill className="h-5 w-5 text-blue-600" />
      case 'nao-medicamentoso': return <Heart className="h-5 w-5 text-green-600" />
      case 'procedimento': return <Activity className="h-5 w-5 text-purple-600" />
      default: return <Activity className="h-5 w-5 text-gray-600" />
    }
  }

  const getTreatmentTypeColor = (type: string) => {
    switch (type) {
      case 'medicamentoso': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'nao-medicamentoso': return 'bg-green-100 text-green-800 border-green-200'
      case 'procedimento': return 'bg-purple-100 text-purple-800 border-purple-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getExamIcon = (type: string) => {
    switch (type) {
      case 'laboratorial': return <Zap className="h-4 w-4 text-yellow-600" />
      case 'imagem': return <FileText className="h-4 w-4 text-blue-600" />
      case 'funcional': return <Activity className="h-4 w-4 text-green-600" />
      default: return <Stethoscope className="h-4 w-4 text-gray-600" />
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergencial': return 'bg-red-100 text-red-800 border-red-200'
      case 'urgente': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'rotina': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'alto': return 'text-red-600'
      case 'medio': return 'text-yellow-600'
      case 'baixo': return 'text-green-600'
      default: return 'text-gray-600'
    }
  }

  const sortedTreatments = [...treatments].sort((a, b) => a.priority - b.priority)
  const sortedExams = [...exams].sort((a, b) => {
    const urgencyOrder = { 'emergencial': 0, 'urgente': 1, 'rotina': 2 }
    return urgencyOrder[a.urgency] - urgencyOrder[b.urgency]
  })

  return (
    <div className="space-y-6">
      {/* Treatment Recommendations */}
      <Card className="border-l-4 border-l-green-500">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Activity className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Recomendações de Tratamento</CardTitle>
              <CardDescription>
                Opções terapêuticas baseadas em guidelines médicos
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Treatment Options */}
      {sortedTreatments.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Opções Terapêuticas</h3>
          
          <div className="space-y-3">
            {sortedTreatments.map((treatment, index) => (
              <motion.div
                key={treatment.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className={`${treatment.priority === 1 ? 'ring-2 ring-green-200 shadow-md' : ''} hover:shadow-sm transition-shadow`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        {getTreatmentIcon(treatment.type)}
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-base">{treatment.name}</CardTitle>
                            {treatment.priority === 1 && (
                              <Badge variant="default" className="text-xs">
                                Prioritário
                              </Badge>
                            )}
                          </div>
                          
                          <Badge 
                            className={`${getTreatmentTypeColor(treatment.type)} text-xs`}
                          >
                            {treatment.type.replace('-', ' ').charAt(0).toUpperCase() + treatment.type.slice(1)}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <TrendingUp className="h-4 w-4" />
                        Prioridade {treatment.priority}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Description */}
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {treatment.description}
                    </p>

                    {/* Medications */}
                    {treatment.medications && treatment.medications.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                          <Pill className="h-4 w-4 text-blue-600" />
                          Medicamentos
                        </h4>
                        <div className="space-y-2">
                          {treatment.medications.map((med, medIndex) => (
                            <div 
                              key={medIndex}
                              className="bg-blue-50 p-3 rounded-lg border border-blue-200"
                            >
                              <div className="flex items-start justify-between mb-1">
                                <h5 className="font-medium text-sm text-blue-900">{med.name}</h5>
                                <Badge variant="outline" className="text-xs">
                                  {med.activeIngredient}
                                </Badge>
                              </div>
                              
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-800 mt-2">
                                <div>
                                  <span className="font-medium">Dose:</span> {med.dosage}
                                </div>
                                <div>
                                  <span className="font-medium">Via:</span> {med.route}
                                </div>
                                <div>
                                  <span className="font-medium">Frequência:</span> {med.frequency}
                                </div>
                                <div>
                                  <span className="font-medium">Duração:</span> {med.duration}
                                </div>
                              </div>
                              
                              {med.warnings && med.warnings.length > 0 && (
                                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                                  <div className="flex items-center gap-1 text-xs text-yellow-800">
                                    <AlertCircle className="h-3 w-3" />
                                    <span className="font-medium">Precauções:</span>
                                  </div>
                                  <p className="text-xs text-yellow-700 mt-1">
                                    {med.warnings.join(', ')}
                                  </p>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Guidelines */}
                    {treatment.guidelines.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Baseado em:</h4>
                        <div className="flex flex-wrap gap-1">
                          {treatment.guidelines.map((guideline, guidelineIndex) => (
                            <Badge 
                              key={guidelineIndex}
                              variant="outline" 
                              className="text-xs"
                            >
                              {guideline}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Contraindications */}
                    {treatment.contraindicatedIf && treatment.contraindicatedIf.length > 0 && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <h4 className="text-sm font-medium mb-2 text-red-800 flex items-center gap-2">
                          <AlertCircle className="h-4 w-4" />
                          Contraindicações
                        </h4>
                        <div className="space-y-1">
                          {treatment.contraindicatedIf.map((contraindication, contrIndex) => (
                            <p key={contrIndex} className="text-sm text-red-700">
                              • {contraindication}
                            </p>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      <Separator />

      {/* Additional Exams */}
      {sortedExams.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-purple-600" />
            Exames Complementares Sugeridos
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {sortedExams.map((exam, index) => (
              <motion.div
                key={exam.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getExamIcon(exam.type)}
                        <CardTitle className="text-sm">{exam.name}</CardTitle>
                      </div>
                      
                      <div className="flex flex-col items-end gap-1">
                        <Badge 
                          className={`${getUrgencyColor(exam.urgency)} text-xs`}
                        >
                          {exam.urgency.charAt(0).toUpperCase() + exam.urgency.slice(1)}
                        </Badge>
                        <span className={`text-xs font-medium ${getCostColor(exam.cost)}`}>
                          Custo: {exam.cost}
                        </span>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-3">
                    <Badge variant="outline" className="text-xs">
                      {exam.type.charAt(0).toUpperCase() + exam.type.slice(1)}
                    </Badge>
                    
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {exam.rationale}
                    </p>

                    {exam.expectedFindings && exam.expectedFindings.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium text-muted-foreground mb-1">
                          Achados Esperados:
                        </h4>
                        <div className="space-y-1">
                          {exam.expectedFindings.map((finding, findingIndex) => (
                            <p key={findingIndex} className="text-xs text-gray-600">
                              • {finding}
                            </p>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Disclaimer */}
      <Card className="border-amber-200 bg-amber-50">
        <CardContent className="pt-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
            <div className="space-y-2">
              <h4 className="font-medium text-amber-800">Aviso Importante</h4>
              <p className="text-sm text-amber-700">
                Estas recomendações são sugestões baseadas em guidelines médicos e 
                devem ser adaptadas ao contexto clínico individual. Sempre considere 
                contraindicações, interações medicamentosas e condições específicas 
                do paciente antes de implementar qualquer tratamento.
              </p>
              <p className="text-xs text-amber-600">
                Consulte sempre as diretrizes institucionais e a literatura médica atualizada • 
                Verifique dosagens e contraindicações específicas
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}