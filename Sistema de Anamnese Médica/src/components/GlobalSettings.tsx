import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Switch } from './ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Separator } from './ui/separator';
import { Badge } from './ui/badge';
import { 
  Settings, 
  User, 
  Bell, 
  Palette, 
  Shield, 
  Download, 
  Upload,
  Save,
  RefreshCw,
  Monitor,
  Moon,
  Sun,
  Smartphone
} from 'lucide-react';

interface GlobalSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  darkMode: boolean;
  onToggleTheme: () => void;
}

export const GlobalSettings: React.FC<GlobalSettingsProps> = ({ 
  isOpen, 
  onClose, 
  darkMode, 
  onToggleTheme 
}) => {
  const [settings, setSettings] = useState({
    // Configurações do Profissional
    profile: {
      name: 'Dr. João Silva',
      crm: 'CRM 123456-SP',
      specialty: 'Clínica Médica',
      hospital: 'Hospital São Lucas',
      department: 'Emergência',
      phone: '(11) 99999-9999',
      email: '<EMAIL>'
    },
    
    // Configurações do Sistema
    system: {
      theme: darkMode ? 'dark' : 'light',
      language: 'pt-BR',
      timezone: 'America/Sao_Paulo',
      autoRefresh: true,
      refreshInterval: 30, // seconds
      compactMode: false,
      showAnimations: true,
      soundNotifications: true
    },
    
    // Configurações de Notificações
    notifications: {
      emergencyAlerts: true,
      patientUpdates: true,
      systemMessages: true,
      emailNotifications: false,
      pushNotifications: true,
      smsAlerts: false
    },
    
    // Configurações de Privacidade
    privacy: {
      dataRetention: '30', // days
      anonymizeData: true,
      shareAnalytics: false,
      logActivity: true,
      twoFactorAuth: false
    },
    
    // Configurações do Dashboard
    dashboard: {
      defaultView: 'minimal',
      showQuickStats: true,
      showRecentActivity: true,
      autoHideInactive: true,
      refreshDashboard: true,
      compactCards: false
    }
  });

  const handleSave = () => {
    // Salvar configurações no localStorage ou enviar para o backend
    localStorage.setItem('wellwave-settings', JSON.stringify(settings));
    onClose();
  };

  const handleReset = () => {
    // Reset para configurações padrão
    const confirmed = confirm('Tem certeza que deseja resetar todas as configurações?');
    if (confirmed) {
      // Resetar para valores padrão
      console.log('Configurações resetadas');
    }
  };

  const handleExport = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'wellwave-settings.json';
    link.click();
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string);
        setSettings(importedSettings);
      } catch (error) {
        alert('Erro ao importar configurações. Arquivo inválido.');
      }
    };
    reader.readAsText(file);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configurações Globais
          </DialogTitle>
          <DialogDescription>
            Configure suas preferências e informações profissionais
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="profile" className="flex items-center gap-1">
              <User className="h-4 w-4" />
              Perfil
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center gap-1">
              <Monitor className="h-4 w-4" />
              Sistema
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-1">
              <Bell className="h-4 w-4" />
              Notificações
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-1">
              <Shield className="h-4 w-4" />
              Privacidade
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-1">
              <Palette className="h-4 w-4" />
              Dashboard
            </TabsTrigger>
          </TabsList>

          {/* Perfil Profissional */}
          <TabsContent value="profile" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Informações Profissionais</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Nome Completo</Label>
                    <Input
                      id="name"
                      value={settings.profile.name}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        profile: { ...prev.profile, name: e.target.value }
                      }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="crm">CRM</Label>
                    <Input
                      id="crm"
                      value={settings.profile.crm}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        profile: { ...prev.profile, crm: e.target.value }
                      }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="specialty">Especialidade</Label>
                    <Select 
                      value={settings.profile.specialty}
                      onValueChange={(value) => setSettings(prev => ({
                        ...prev,
                        profile: { ...prev.profile, specialty: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Clínica Médica">Clínica Médica</SelectItem>
                        <SelectItem value="Cardiologia">Cardiologia</SelectItem>
                        <SelectItem value="Neurologia">Neurologia</SelectItem>
                        <SelectItem value="Pediatria">Pediatria</SelectItem>
                        <SelectItem value="Ortopedia">Ortopedia</SelectItem>
                        <SelectItem value="Ginecologia">Ginecologia</SelectItem>
                        <SelectItem value="Medicina de Emergência">Medicina de Emergência</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="department">Departamento</Label>
                    <Select 
                      value={settings.profile.department}
                      onValueChange={(value) => setSettings(prev => ({
                        ...prev,
                        profile: { ...prev.profile, department: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Emergência">Emergência</SelectItem>
                        <SelectItem value="UTI">UTI</SelectItem>
                        <SelectItem value="Enfermaria">Enfermaria</SelectItem>
                        <SelectItem value="Ambulatório">Ambulatório</SelectItem>
                        <SelectItem value="Centro Cirúrgico">Centro Cirúrgico</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="hospital">Hospital/Clínica</Label>
                  <Input
                    id="hospital"
                    value={settings.profile.hospital}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      profile: { ...prev.profile, hospital: e.target.value }
                    }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      value={settings.profile.phone}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        profile: { ...prev.profile, phone: e.target.value }
                      }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={settings.profile.email}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        profile: { ...prev.profile, email: e.target.value }
                      }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Configurações do Sistema */}
          <TabsContent value="system" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Aparência e Comportamento</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Tema</Label>
                    <p className="text-sm text-muted-foreground">Escolha entre modo claro ou escuro</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Sun className="h-4 w-4" />
                    <Switch
                      checked={darkMode}
                      onCheckedChange={onToggleTheme}
                    />
                    <Moon className="h-4 w-4" />
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Atualização Automática</Label>
                    <p className="text-sm text-muted-foreground">Atualizar dados automaticamente</p>
                  </div>
                  <Switch
                    checked={settings.system.autoRefresh}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      system: { ...prev.system, autoRefresh: checked }
                    }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Animações</Label>
                    <p className="text-sm text-muted-foreground">Habilitar animações na interface</p>
                  </div>
                  <Switch
                    checked={settings.system.showAnimations}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      system: { ...prev.system, showAnimations: checked }
                    }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Modo Compacto</Label>
                    <p className="text-sm text-muted-foreground">Interface mais densa</p>
                  </div>
                  <Switch
                    checked={settings.system.compactMode}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      system: { ...prev.system, compactMode: checked }
                    }))}
                  />
                </div>

                <div>
                  <Label htmlFor="refreshInterval">Intervalo de Atualização (segundos)</Label>
                  <Select 
                    value={settings.system.refreshInterval.toString()}
                    onValueChange={(value) => setSettings(prev => ({
                      ...prev,
                      system: { ...prev.system, refreshInterval: parseInt(value) }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10 segundos</SelectItem>
                      <SelectItem value="30">30 segundos</SelectItem>
                      <SelectItem value="60">1 minuto</SelectItem>
                      <SelectItem value="300">5 minutos</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notificações */}
          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Configurações de Notificações</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Alertas de Emergência</Label>
                    <p className="text-sm text-muted-foreground">Notificações para pacientes críticos</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={settings.notifications.emergencyAlerts}
                      onCheckedChange={(checked) => setSettings(prev => ({
                        ...prev,
                        notifications: { ...prev.notifications, emergencyAlerts: checked }
                      }))}
                    />
                    <Badge variant="destructive" className="text-xs">Crítico</Badge>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Atualizações de Pacientes</Label>
                    <p className="text-sm text-muted-foreground">Mudanças de status e novos registros</p>
                  </div>
                  <Switch
                    checked={settings.notifications.patientUpdates}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      notifications: { ...prev.notifications, patientUpdates: checked }
                    }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Mensagens do Sistema</Label>
                    <p className="text-sm text-muted-foreground">Atualizações e manutenções</p>
                  </div>
                  <Switch
                    checked={settings.notifications.systemMessages}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      notifications: { ...prev.notifications, systemMessages: checked }
                    }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Notificações Sonoras</Label>
                    <p className="text-sm text-muted-foreground">Sons para alertas importantes</p>
                  </div>
                  <Switch
                    checked={settings.system.soundNotifications}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      system: { ...prev.system, soundNotifications: checked }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Privacidade */}
          <TabsContent value="privacy" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Configurações de Privacidade</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="dataRetention">Retenção de Dados (dias)</Label>
                  <Select 
                    value={settings.privacy.dataRetention}
                    onValueChange={(value) => setSettings(prev => ({
                      ...prev,
                      privacy: { ...prev.privacy, dataRetention: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">7 dias</SelectItem>
                      <SelectItem value="30">30 dias</SelectItem>
                      <SelectItem value="90">90 dias</SelectItem>
                      <SelectItem value="365">1 ano</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Anonimizar Dados</Label>
                    <p className="text-sm text-muted-foreground">Remover informações pessoais dos logs</p>
                  </div>
                  <Switch
                    checked={settings.privacy.anonymizeData}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      privacy: { ...prev.privacy, anonymizeData: checked }
                    }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Registrar Atividades</Label>
                    <p className="text-sm text-muted-foreground">Manter log de ações do usuário</p>
                  </div>
                  <Switch
                    checked={settings.privacy.logActivity}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      privacy: { ...prev.privacy, logActivity: checked }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Dashboard */}
          <TabsContent value="dashboard" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Configurações do Dashboard</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="defaultView">Visualização Padrão</Label>
                  <Select 
                    value={settings.dashboard.defaultView}
                    onValueChange={(value) => setSettings(prev => ({
                      ...prev,
                      dashboard: { ...prev.dashboard, defaultView: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="minimal">Minimalista</SelectItem>
                      <SelectItem value="detailed">Detalhado</SelectItem>
                      <SelectItem value="compact">Compacto</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Estatísticas Rápidas</Label>
                    <p className="text-sm text-muted-foreground">Mostrar cards de estatísticas</p>
                  </div>
                  <Switch
                    checked={settings.dashboard.showQuickStats}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      dashboard: { ...prev.dashboard, showQuickStats: checked }
                    }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Atividade Recente</Label>
                    <p className="text-sm text-muted-foreground">Exibir feed de atividades</p>
                  </div>
                  <Switch
                    checked={settings.dashboard.showRecentActivity}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      dashboard: { ...prev.dashboard, showRecentActivity: checked }
                    }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Cards Compactos</Label>
                    <p className="text-sm text-muted-foreground">Usar layout mais denso nos cards</p>
                  </div>
                  <Switch
                    checked={settings.dashboard.compactCards}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      dashboard: { ...prev.dashboard, compactCards: checked }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <div>
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
                id="import-settings"
              />
              <Button variant="outline" size="sm" asChild>
                <label htmlFor="import-settings" className="cursor-pointer flex items-center">
                  <Upload className="h-4 w-4 mr-2" />
                  Importar
                </label>
              </Button>
            </div>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Resetar
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Salvar Configurações
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};