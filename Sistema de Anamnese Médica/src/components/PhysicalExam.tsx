import { motion } from 'motion/react'
import { Stethoscope, Activity, Heart, Thermometer, Eye, Check } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'
import { Checkbox } from './ui/checkbox'
import { useMedical } from './MedicalContext'

export function PhysicalExam() {
  const { anamnesisData, updateNestedAnamnesisData, medicalColors } = useMedical()

  const vitalSignsFields = [
    { key: 'fc', label: 'Frequência Cardíaca', placeholder: '88', unit: 'bpm', icon: Heart },
    { key: 'fr', label: 'Frequência Respiratória', placeholder: '18', unit: 'irpm', icon: Stethoscope },
    { key: 'pa', label: 'Pressão Arterial', placeholder: '120/80', unit: 'mmHg', icon: Activity },
    { key: 'temp', label: 'Temperatura', placeholder: '36.8', unit: '°C', icon: Thermometer },
    { key: 'sat', label: 'Saturação O2', placeholder: '96', unit: '%', icon: Activity },
    { key: 'glicemia', label: 'Glicemia Capilar', placeholder: '110', unit: 'mg/dL', icon: Eye }
  ]

  // Achados cardiovasculares estruturados
  const cardiovascularFindings = [
    { id: 'ritmo-regular', label: 'Ritmo cardíaco regular, bulhas normofonéticas, sem sopros' },
    { id: 'ritmo-irregular', label: 'Ritmo cardíaco irregular' },
    { id: 'sopro', label: 'Presença de sopro' },
    { id: 'b3', label: 'Presença de B3' },
    { id: 'b4', label: 'Presença de B4' },
    { id: 'atrito', label: 'Atrito pericárdico' }
  ]

  const physicalExamFields = [
    { key: 'aspectoGeral', label: 'Aspecto Geral', placeholder: 'BEG, corado, hidratado, anictérico...' },
    { key: 'cabecaPescoco', label: 'Cabeça e Pescoço', placeholder: 'Normocéfalo, ausculta pulmonar...' },
    { key: 'torax', label: 'Tórax', placeholder: 'Simétrico, expansibilidade preservada...' },
    { key: 'cardiovascular', label: 'Cardiovascular', placeholder: 'BRNF 2T, sem sopros...' },
    { key: 'respiratorio', label: 'Respiratório', placeholder: 'MV+ bilateralmente, sem RA...' },
    { key: 'abdome', label: 'Abdome', placeholder: 'Plano, flácido, indolor...' },
    { key: 'extremidades', label: 'Extremidades', placeholder: 'Sem edemas, pulsos palpáveis...' },
    { key: 'neurologico', label: 'Neurológico', placeholder: 'Consciente, orientado, força preservada...' }
  ]

  // Achados comuns do exame físico
  const commonFindings = [
    'BEG', 'REG', 'MEG', 'Hidratado', 'Desidratado', 'Corado', 'Descorado',
    'Anictérico', 'Ictérico', 'Afebril', 'Febril', 'Normocárdico', 'Taquicárdico',
    'Bradicárdico', 'Normotenso', 'Hipertenso', 'Hipotensor', 'Eupneico', 'Dispneico',
    'Taquipneico', 'Bradipneico', 'Cianose +', 'Cianose -', 'Edema +', 'Edema -'
  ]

  return (
    <div className="space-y-4 max-w-4xl mx-auto">
      {/* Header Minimalista */}
      <motion.div 
        className="mb-6"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-xl bg-gradient-to-br from-red-500 to-pink-600 shadow-lg">
            <Stethoscope className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-slate-900 dark:text-white">Exame Físico</h2>
            <p className="text-sm text-slate-600 dark:text-slate-400">Sinais vitais e achados principais</p>
          </div>
        </div>
      </motion.div>

      {/* Sinais Vitais - Layout Compacto */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Activity className="h-4 w-4 text-red-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Sinais Vitais
              </Label>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {/* Pressão Arterial */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-slate-600 dark:text-slate-400 flex items-center gap-2">
                  <Activity className="h-3 w-3" />
                  Pressão Arterial
                </Label>
                <Input
                  placeholder="120/80"
                  value={anamnesisData.exameFisico.sinaisVitais.pa || ''}
                  onChange={(e) => updateNestedAnamnesisData('exameFisico', 'sinaisVitais', {
                    ...anamnesisData.exameFisico.sinaisVitais,
                    pa: e.target.value
                  })}
                  className="rounded-xl border-slate-300 dark:border-slate-600 focus:border-red-500 focus:ring-red-500/20 bg-white dark:bg-slate-800 text-sm"
                />
              </div>
              
              {/* Outros sinais vitais */}
              {vitalSignsFields.map((field) => {
                const Icon = field.icon
                return (
                  <div key={field.key} className="space-y-2">
                    <Label className="text-sm font-medium text-slate-600 dark:text-slate-400 flex items-center gap-2">
                      <Icon className="h-3 w-3" />
                      {field.label}
                    </Label>
                    <div className="relative">
                      <Input
                        placeholder={field.placeholder}
                        value={anamnesisData.exameFisico.sinaisVitais[field.key as keyof typeof anamnesisData.exameFisico.sinaisVitais] || ''}
                        onChange={(e) => updateNestedAnamnesisData('exameFisico', 'sinaisVitais', {
                          ...anamnesisData.exameFisico.sinaisVitais,
                          [field.key]: e.target.value
                        })}
                        className="rounded-xl border-slate-300 dark:border-slate-600 focus:border-red-500 focus:ring-red-500/20 bg-white dark:bg-slate-800 pr-12 text-sm"
                      />
                      <span className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-slate-500">
                        {field.unit}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Achados Cardiovasculares - Radio Options */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Heart className="h-4 w-4 text-red-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Achados Cardiovasculares
              </Label>
            </div>
            
            <div className="space-y-2">
              {cardiovascularFindings.map((finding) => (
                <div key={finding.id} className="flex items-center">
                  <input
                    type="radio"
                    id={finding.id}
                    name="achados-cardiovasculares"
                    checked={anamnesisData.exameFisico.exameFisico.cardiovascular === finding.label}
                    onChange={() => updateNestedAnamnesisData('exameFisico', 'exameFisico', {
                      ...anamnesisData.exameFisico.exameFisico,
                      cardiovascular: finding.label
                    })}
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <label htmlFor={finding.id} className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    {finding.label}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Achados Gerais do Exame Físico - Checkboxes */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Check className="h-4 w-4 text-green-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Achados Gerais
              </Label>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {commonFindings.map((finding) => {
                // Criar um valor único para cada achado
                const currentFindings = anamnesisData.exameFisico.exameFisico.aspectoGeral || ''
                const isSelected = currentFindings.includes(finding)
                
                return (
                  <motion.div
                    key={finding}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <label className={`
                      flex items-center gap-3 p-3 rounded-xl border-2 cursor-pointer transition-all
                      ${isSelected 
                        ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' 
                        : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800'
                      }
                    `}>
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => {
                          const currentFindings = anamnesisData.exameFisico.exameFisico.aspectoGeral || ''
                          let newFindings = ''
                          
                          if (checked) {
                            // Adicionar achado
                            const findingsArray = currentFindings ? currentFindings.split(', ') : []
                            if (!findingsArray.includes(finding)) {
                              findingsArray.push(finding)
                            }
                            newFindings = findingsArray.join(', ')
                          } else {
                            // Remover achado
                            const findingsArray = currentFindings.split(', ')
                            newFindings = findingsArray.filter(f => f !== finding).join(', ')
                          }
                          
                          updateNestedAnamnesisData('exameFisico', 'exameFisico', {
                            ...anamnesisData.exameFisico.exameFisico,
                            aspectoGeral: newFindings
                          })
                        }}
                        className="w-4 h-4"
                      />
                      <span className="text-sm font-medium flex-1">
                        {finding}
                      </span>
                    </label>
                  </motion.div>
                )
              })}
            </div>

            {/* Achados selecionados */}
            {anamnesisData.exameFisico.exameFisico.aspectoGeral && (
              <div className="mt-4 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-xl">
                <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">Achados selecionados:</p>
                <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {anamnesisData.exameFisico.exameFisico.aspectoGeral}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Observações Específicas - Opcional */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-3">
            <Label className="font-semibold text-slate-700 dark:text-slate-300">
              Achados Específicos (Opcional)
            </Label>
            <Textarea
              placeholder="Descreva achados específicos por sistema: cardiovascular, respiratório, abdominal, neurológico..."
              value={anamnesisData.exameFisico.observacoes}
              onChange={(e) => updateNestedAnamnesisData('exameFisico', 'observacoes', e.target.value)}
              rows={3}
              className="resize-none rounded-xl border-slate-300 dark:border-slate-600 focus:border-green-500 focus:ring-green-500/20 bg-white dark:bg-slate-800"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PhysicalExam