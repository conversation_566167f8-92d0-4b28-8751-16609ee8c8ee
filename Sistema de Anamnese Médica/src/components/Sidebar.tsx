import { But<PERSON> } from "./ui/button";
import { Separator } from "./ui/separator";
import { Plus, <PERSON>, <PERSON>, Lung<PERSON>, Zap, CircleAlert } from "lucide-react";

interface SidebarProps {
  onNewAnamnese: () => void;
  onLoadComplaint: (complaint: string) => void;
}

const frequentComplaints = [
  { value: 'dor-toracica', label: '<PERSON><PERSON>', icon: Heart },
  { value: 'cefaleia', label: '<PERSON><PERSON><PERSON><PERSON>', icon: Brain },
  { value: 'dispneia', label: 'Dispneia', icon: Lungs },
  { value: 'dor-abdominal', label: 'Dor Abdominal', icon: CircleAlert },
  { value: 'febre', label: 'Febre', icon: Zap }
];

export function Sidebar({ onNewAnamnese, onLoadComplaint }: SidebarProps) {
  return (
    <div className="w-64 h-full bg-sidebar border-r border-sidebar-border flex flex-col">
      <div className="p-4">
        <Button 
          onClick={onNewAnamnese}
          className="w-full justify-start gap-2"
          variant="default"
        >
          <Plus className="h-4 w-4" />
          Nova Anamnese
        </Button>
      </div>
      
      <Separator />
      
      <div className="flex-1 p-4">
        <div className="space-y-4">
          <div>
            <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-3">
              Queixas Frequentes
            </h3>
            <div className="space-y-1">
              {frequentComplaints.map((complaint) => {
                const IconComponent = complaint.icon;
                return (
                  <Button
                    key={complaint.value}
                    variant="ghost"
                    className="w-full justify-start gap-3 text-sm font-normal h-auto py-3 text-left"
                    onClick={() => onLoadComplaint(complaint.value)}
                  >
                    <IconComponent className="h-4 w-4 flex-shrink-0" />
                    <span>{complaint.label}</span>
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}