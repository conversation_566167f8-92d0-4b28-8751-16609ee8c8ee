import { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { 
  MessageSquare, Clock, Activity, AlertTriangle, Plus, X, Zap, Check,
  Heart, Wind, Brain, Circle, Thermometer, Zap as ShockIcon,
  User, Shield, Droplet, Stethoscope
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Slider } from './ui/slider'
import { Checkbox } from './ui/checkbox'
import { useMedical } from './MedicalContext'

export function ChiefComplaint() {
  const { anamnesisData, updateNestedAnamnesisData, medicalColors } = useMedical()
  const [newCharacteristic, setNewCharacteristic] = useState('')
  const [newAssociatedFactor, setNewAssociatedFactor] = useState('')
  
  // Estados para o sistema de sintomas por síndrome
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSyndrome, setSelectedSyndrome] = useState<string | null>(null)
  const [selectedSymptoms, setSelectedSymptoms] = useState<string[]>([])
  const [customSymptom, setCustomSymptom] = useState('')

  // Base de dados de síndromes médicas do pronto-socorro
  const medicalSyndromes = useMemo(() => [
    {
      id: 'coronary',
      name: 'Sínd. Coronariana',
      icon: Heart,
      color: '#DC2626',
      symptoms: [
        { name: 'Dor torácica opressiva', description: 'Dor em aperto, opressão no peito', urgent: true },
        { name: 'Dor irradiada para braço esquerdo', description: 'Irradiação para membro superior esquerdo', urgent: true },
        { name: 'Dor em mandíbula', description: 'Dor irradiada para região mandibular', urgent: true },
        { name: 'Dispneia aos esforços', description: 'Falta de ar durante atividades', urgent: false },
        { name: 'Sudorese profusa', description: 'Suor excessivo', urgent: true },
        { name: 'Náuseas', description: 'Sensação de enjoo', urgent: false },
        { name: 'Vômitos', description: 'Episódios de vômito', urgent: false },
        { name: 'Palpitações', description: 'Batimentos cardíacos acelerados ou irregulares', urgent: false },
        { name: 'Sensação de morte iminente', description: 'Angústia, sensação de que vai morrer', urgent: true },
        { name: 'Fadiga excessiva', description: 'Cansaço desproporcional ao esforço', urgent: false }
      ]
    },
    {
      id: 'respiratory',
      name: 'Sínd. Respiratória',
      icon: Wind,
      color: '#2563EB',
      symptoms: [
        { name: 'Dispneia aguda', description: 'Falta de ar súbita', urgent: true },
        { name: 'Tosse seca', description: 'Tosse sem expectoração', urgent: false },
        { name: 'Tosse produtiva', description: 'Tosse com catarro', urgent: false },
        { name: 'Hemoptise', description: 'Tosse com sangue', urgent: true },
        { name: 'Dor pleurítica', description: 'Dor que piora com respiração', urgent: true },
        { name: 'Chiado no peito', description: 'Som sibilante na respiração', urgent: false },
        { name: 'Estridor', description: 'Som na inspiração, obstrução alta', urgent: true },
        { name: 'Cianose', description: 'Coloração azulada', urgent: true },
        { name: 'Ortopneia', description: 'Falta de ar ao deitar', urgent: false },
        { name: 'Taquipneia', description: 'Respiração acelerada', urgent: true }
      ]
    },
    {
      id: 'neurological',
      name: 'Sínd. Neurológica',
      icon: Brain,
      color: '#7C3AED',
      symptoms: [
        { name: 'Cefaleia súbita intensa', description: 'Dor de cabeça explosiva', urgent: true },
        { name: 'Déficit motor', description: 'Fraqueza em membros', urgent: true },
        { name: 'Afasia', description: 'Dificuldade para falar', urgent: true },
        { name: 'Disartria', description: 'Fala pastosa', urgent: true },
        { name: 'Diplopia', description: 'Visão dupla', urgent: true },
        { name: 'Vertigem', description: 'Sensação de rotação', urgent: false },
        { name: 'Ataxia', description: 'Descoordenação motora', urgent: true },
        { name: 'Convulsões', description: 'Crises epilépticas', urgent: true },
        { name: 'Alteração do nível de consciência', description: 'Sonolência, confusão', urgent: true },
        { name: 'Rigidez de nuca', description: 'Pescoço rígido', urgent: true },
        { name: 'Fotofobia', description: 'Intolerância à luz', urgent: false },
        { name: 'Parestesias', description: 'Formigamento, dormência', urgent: false }
      ]
    },
    {
      id: 'abdominal',
      name: 'Sínd. Abdominal',
      icon: Circle,
      color: '#EA580C',
      symptoms: [
        { name: 'Dor abdominal intensa', description: 'Dor forte na barriga', urgent: true },
        { name: 'Dor em fossa ilíaca direita', description: 'Dor na região da apendicite', urgent: true },
        { name: 'Dor epigástrica', description: 'Dor na boca do estômago', urgent: false },
        { name: 'Dor em hipocôndrio direito', description: 'Dor na região do fígado/vesícula', urgent: false },
        { name: 'Náuseas e vômitos', description: 'Enjoo e vômito', urgent: false },
        { name: 'Distensão abdominal', description: 'Barriga inchada', urgent: false },
        { name: 'Parada de eliminação de gases', description: 'Não consegue soltar gases', urgent: true },
        { name: 'Melena', description: 'Fezes escuras com sangue', urgent: true },
        { name: 'Hematêmese', description: 'Vômito com sangue', urgent: true },
        { name: 'Diarreia', description: 'Evacuações líquidas frequentes', urgent: false },
        { name: 'Constipação', description: 'Prisão de ventre', urgent: false },
        { name: 'Icterícia', description: 'Amarelão', urgent: false }
      ]
    },
    {
      id: 'febrile',
      name: 'Sínd. Febril',
      icon: Thermometer,
      color: '#DC2626',
      symptoms: [
        { name: 'Febre alta (>39°C)', description: 'Temperatura corporal elevada', urgent: true },
        { name: 'Calafrios', description: 'Tremores e sensação de frio', urgent: false },
        { name: 'Sudorese', description: 'Suor excessivo', urgent: false },
        { name: 'Mal-estar geral', description: 'Sensação de estar doente', urgent: false },
        { name: 'Mialgia', description: 'Dor no corpo, dor muscular', urgent: false },
        { name: 'Artralgia', description: 'Dor nas articulações', urgent: false },
        { name: 'Cefaleia', description: 'Dor de cabeça', urgent: false },
        { name: 'Prostração', description: 'Fraqueza extrema', urgent: true },
        { name: 'Anorexia', description: 'Falta de apetite', urgent: false },
        { name: 'Irritabilidade', description: 'Estado de agitação', urgent: false }
      ]
    },
    {
      id: 'shock',
      name: 'Sínd. de Choque',
      icon: ShockIcon,
      color: '#991B1B',
      symptoms: [
        { name: 'Hipotensão arterial', description: 'Pressão baixa', urgent: true },
        { name: 'Taquicardia', description: 'Batimentos acelerados', urgent: true },
        { name: 'Pele fria e pegajosa', description: 'Pele sudorética e fria', urgent: true },
        { name: 'Cianose periférica', description: 'Extremidades azuladas', urgent: true },
        { name: 'Enchimento capilar lentificado', description: 'Perfusão periférica prejudicada', urgent: true },
        { name: 'Oligúria', description: 'Pouca urina', urgent: true },
        { name: 'Alteração do estado mental', description: 'Confusão, agitação', urgent: true },
        { name: 'Sede intensa', description: 'Muita sede', urgent: false },
        { name: 'Fraqueza extrema', description: 'Prostração severa', urgent: true },
        { name: 'Pulsos periféricos fracos', description: 'Pulsos difíceis de palpar', urgent: true }
      ]
    },
    {
      id: 'psychiatric',
      name: 'Sínd. Psiquiátrica',
      icon: User,
      color: '#9333EA',
      symptoms: [
        { name: 'Agitação psicomotora', description: 'Inquietação, não consegue ficar parado', urgent: true },
        { name: 'Ideação suicida', description: 'Pensamentos de se matar', urgent: true },
        { name: 'Alucinações', description: 'Ver ou ouvir coisas que não existem', urgent: true },
        { name: 'Delírios', description: 'Ideias falsas fixas', urgent: true },
        { name: 'Ansiedade extrema', description: 'Ansiedade intensa e incapacitante', urgent: false },
        { name: 'Pânico', description: 'Crise de pânico', urgent: false },
        { name: 'Comportamento bizarro', description: 'Atitudes estranhas e inadequadas', urgent: true },
        { name: 'Agressividade', description: 'Comportamento violento', urgent: true },
        { name: 'Confusão mental', description: 'Desorientação, confusão', urgent: true },
        { name: 'Mutismo', description: 'Recusa ou incapacidade de falar', urgent: false }
      ]
    },
    {
      id: 'trauma',
      name: 'Sínd. Traumática',
      icon: Shield,
      color: '#B45309',
      symptoms: [
        { name: 'Dor no local do trauma', description: 'Dor na região traumatizada', urgent: true },
        { name: 'Deformidade óssea', description: 'Alteração da forma normal do osso', urgent: true },
        { name: 'Hematoma', description: 'Mancha roxa, sangramento interno', urgent: false },
        { name: 'Edema local', description: 'Inchaço no local', urgent: false },
        { name: 'Limitação funcional', description: 'Dificuldade para mover', urgent: false },
        { name: 'Sangramento ativo', description: 'Sangramento visível', urgent: true },
        { name: 'Instabilidade hemodinâmica', description: 'Pressão instável', urgent: true },
        { name: 'Perda de sensibilidade', description: 'Dormência na região', urgent: true },
        { name: 'Crepitação óssea', description: 'Som de ossos se esfregando', urgent: false },
        { name: 'Impotência funcional', description: 'Impossibilidade de movimentar', urgent: true }
      ]
    },
    {
      id: 'dermatological',
      name: 'Sínd. Dermatológica',
      icon: Activity,
      color: '#059669',
      symptoms: [
        { name: 'Erupção cutânea generalizada', description: 'Lesões na pele espalhadas', urgent: true },
        { name: 'Prurido intenso', description: 'Coceira intensa', urgent: false },
        { name: 'Urticária', description: 'Placas avermelhadas que coçam', urgent: false },
        { name: 'Angioedema', description: 'Inchaço de face, lábios, língua', urgent: true },
        { name: 'Bolhas na pele', description: 'Lesões com líquido', urgent: false },
        { name: 'Descamação', description: 'Pele saindo em escamas', urgent: false },
        { name: 'Eritema', description: 'Vermelhidão da pele', urgent: false },
        { name: 'Dor na pele', description: 'Pele dolorosa ao toque', urgent: false },
        { name: 'Púrpura', description: 'Manchas roxas na pele', urgent: true },
        { name: 'Necrose cutânea', description: 'Morte de tecido da pele', urgent: true }
      ]
    },
    {
      id: 'urological',
      name: 'Sínd. Urológica',
      icon: Droplet,
      color: '#0891B2',
      symptoms: [
        { name: 'Disúria', description: 'Dor para urinar', urgent: false },
        { name: 'Urgência miccional', description: 'Vontade urgente de urinar', urgent: false },
        { name: 'Polaciúria', description: 'Urinar muitas vezes', urgent: false },
        { name: 'Hematúria', description: 'Sangue na urina', urgent: true },
        { name: 'Dor lombar', description: 'Dor nas costas, região dos rins', urgent: false },
        { name: 'Cólica renal', description: 'Dor intensa tipo cólica nas costas', urgent: true },
        { name: 'Anúria', description: 'Parou de urinar', urgent: true },
        { name: 'Oligúria', description: 'Pouca urina', urgent: true },
        { name: 'Retenção urinária', description: 'Não consegue urinar', urgent: true },
        { name: 'Edema', description: 'Inchaço no corpo', urgent: false },
        { name: 'Urina turva', description: 'Urina com aspecto alterado', urgent: false }
      ]
    }
  ], [])

  // Presets de duração médica estruturada
  const durationPresets = [
    { value: '< 1 hora', label: '< 1 hora', category: 'agudo' },
    { value: '1-6 horas', label: '1-6 horas', category: 'agudo' },
    { value: '6-24 horas', label: '6-24 horas', category: 'agudo' },
    { value: '1-3 dias', label: '1-3 dias', category: 'subagudo' },
    { value: '3-7 dias', label: '3-7 dias', category: 'subagudo' },
    { value: '1-4 semanas', label: '1-4 semanas', category: 'cronico' },
    { value: '> 1 mês', label: '> 1 mês', category: 'cronico' }
  ]

  // Características da dor estruturadas
  const painCharacteristics = [
    { id: 'aperto', label: 'Em aperto/pressão', icon: Heart },
    { id: 'queimacao', label: 'Em queimação', icon: Thermometer },
    { id: 'pontada', label: 'Em pontada', icon: Zap },
    { id: 'pleuritica', label: 'Pleurítica', icon: Wind },
    { id: 'colica', label: 'Em cólica', icon: Activity },
    { id: 'pulsatil', label: 'Pulsátil', icon: Heart },
    { id: 'continua', label: 'Contínua', icon: Circle },
    { id: 'intermitente', label: 'Intermitente', icon: Activity }
  ]

  // Fatores associados estruturados  
  const associatedFactors = [
    { id: 'nausea', label: 'Náusea/vômito', category: 'gastrointestinal' },
    { id: 'sudorese', label: 'Sudorese', category: 'sistemico' },
    { id: 'dispneia', label: 'Dispneia associada', category: 'respiratorio' },
    { id: 'sincope', label: 'Síncope', category: 'neurologico' },
    { id: 'palpitacoes', label: 'Palpitações', category: 'cardiovascular' },
    { id: 'tontura', label: 'Tontura', category: 'neurologico' },
    { id: 'febre', label: 'Febre', category: 'sistemico' },
    { id: 'calafrios', label: 'Calafrios', category: 'sistemico' },
    { id: 'fraqueza', label: 'Fraqueza', category: 'sistemico' },
    { id: 'cefaleia', label: 'Cefaleia', category: 'neurologico' }
  ]

  // Filtro de sintomas baseado na busca e síndrome selecionada
  const filteredSymptoms = useMemo(() => {
    let symptoms = []
    
    if (selectedSyndrome) {
      const syndrome = medicalSyndromes.find(s => s.id === selectedSyndrome)
      symptoms = syndrome?.symptoms || []
    } else {
      symptoms = medicalSyndromes.flatMap(s => s.symptoms)
    }
    
    if (searchTerm.trim()) {
      symptoms = symptoms.filter(symptom => 
        symptom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        symptom.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    
    return symptoms
  }, [searchTerm, selectedSyndrome, medicalSyndromes])

  // Função para adicionar sintoma personalizado
  const handleAddCustomSymptom = () => {
    const trimmedSymptom = customSymptom.trim()
    if (trimmedSymptom && !selectedSymptoms.includes(trimmedSymptom)) {
      setSelectedSymptoms([...selectedSymptoms, trimmedSymptom])
      setCustomSymptom('')
    }
  }

  // Atualizar queixa principal baseada nos sintomas selecionados
  useMemo(() => {
    if (selectedSymptoms.length > 0) {
      const complaintsText = selectedSymptoms.join(', ')
      updateNestedAnamnesisData('queixaPrincipal', 'queixaPrincipal', complaintsText)
    }
  }, [selectedSymptoms, updateNestedAnamnesisData])


  const addCharacteristic = (characteristic: string) => {
    if (characteristic && !anamnesisData.queixaPrincipal.caracteristicas.includes(characteristic)) {
      const newCaracteristicas = [...anamnesisData.queixaPrincipal.caracteristicas, characteristic]
      updateNestedAnamnesisData('queixaPrincipal', 'caracteristicas', newCaracteristicas)
      setNewCharacteristic('')
    }
  }

  const removeCharacteristic = (index: number) => {
    const newCaracteristicas = anamnesisData.queixaPrincipal.caracteristicas.filter((_, i) => i !== index)
    updateNestedAnamnesisData('queixaPrincipal', 'caracteristicas', newCaracteristicas)
  }

  const addAssociatedFactor = (factor: string) => {
    if (factor && !anamnesisData.queixaPrincipal.fatoresAssociados.includes(factor)) {
      const newFactors = [...anamnesisData.queixaPrincipal.fatoresAssociados, factor]
      updateNestedAnamnesisData('queixaPrincipal', 'fatoresAssociados', newFactors)
      setNewAssociatedFactor('')
    }
  }

  const removeAssociatedFactor = (index: number) => {
    const newFactors = anamnesisData.queixaPrincipal.fatoresAssociados.filter((_, i) => i !== index)
    updateNestedAnamnesisData('queixaPrincipal', 'fatoresAssociados', newFactors)
  }

  const intensityColors = {
    1: '#10B981', 2: '#10B981', 3: '#F59E0B', 4: '#F59E0B', 
    5: '#F59E0B', 6: '#F97316', 7: '#F97316', 8: '#EF4444', 
    9: '#EF4444', 10: '#DC2626'
  }

  const getIntensityLabel = (intensity: number) => {
    if (intensity <= 3) return 'Leve'
    if (intensity <= 6) return 'Moderada'
    if (intensity <= 8) return 'Intensa'
    return 'Muito Intensa'
  }

  return (
    <div className="space-y-4 max-w-4xl mx-auto">
      {/* Header Minimalista */}
      <motion.div 
        className="mb-6"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-lg">
            <MessageSquare className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-slate-900 dark:text-white">Queixa Principal</h2>
            <p className="text-sm text-slate-600 dark:text-slate-400">Descreva o motivo da consulta</p>
          </div>
        </div>
      </motion.div>

      {/* Queixa Principal - Layout Simplificado */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6 space-y-6">
          {/* Sistema de Busca de Sintomas */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-amber-500" />
                <Label className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                  Seleção de Sintomas por Síndrome
                </Label>
              </div>
              <Badge variant="outline" className="text-xs bg-amber-50 text-amber-700 border-amber-200">
                Baseado na Literatura Médica
              </Badge>
            </div>
            
            {/* Busca Universal de Sintomas */}
            <div className="relative">
              <Input
                placeholder="Busque sintomas... Ex: dor torácica, dispneia, cefaleia"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="rounded-xl pl-10 border-slate-300 dark:border-slate-600 focus:border-amber-500 focus:ring-amber-500/20 bg-white dark:bg-slate-800"
              />
              <MessageSquare className="h-4 w-4 text-slate-400 absolute left-3 top-3" />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchTerm('')}
                  className="absolute right-2 top-1.5 h-7 w-7 p-0 hover:bg-slate-100"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          {/* Sistema de Síndromes Médicas */}
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {medicalSyndromes.map((syndrome) => (
                <Button
                  key={syndrome.id}
                  variant={selectedSyndrome === syndrome.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedSyndrome(selectedSyndrome === syndrome.id ? null : syndrome.id)}
                  className={`
                    rounded-full text-xs transition-all duration-200
                    ${selectedSyndrome === syndrome.id 
                      ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg' 
                      : 'hover:bg-amber-50 hover:border-amber-300'
                    }
                  `}
                >
                  <syndrome.icon className="h-3 w-3 mr-1" />
                  {syndrome.name}
                  <Badge variant="secondary" className="ml-2 text-[10px] bg-white/20">
                    {syndrome.symptoms.length}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Lista de Sintomas Filtrados */}
          <div className="space-y-4">
            {filteredSymptoms.length > 0 && (
              <div className="bg-gradient-to-br from-slate-50 to-amber-50 dark:from-slate-800 dark:to-slate-700 rounded-xl p-4 border border-slate-200 dark:border-slate-600">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-amber-500" />
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      {selectedSyndrome 
                        ? medicalSyndromes.find(s => s.id === selectedSyndrome)?.name + ' - Sintomas'
                        : 'Resultados da Busca'
                      }
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {filteredSymptoms.length} encontrados
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const allFiltered = filteredSymptoms.map(s => s.name);
                      const currentSymptoms = selectedSymptoms.filter(s => !allFiltered.includes(s));
                      setSelectedSymptoms([...currentSymptoms, ...allFiltered]);
                    }}
                    className="text-xs text-amber-600 hover:text-amber-700"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    Selecionar Todos
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {filteredSymptoms.map((symptom) => (
                    <motion.div
                      key={symptom.name}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/60 dark:hover:bg-slate-600/60 transition-colors"
                    >
                      <Checkbox
                        id={`symptom-${symptom.name}`}
                        checked={selectedSymptoms.includes(symptom.name)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedSymptoms([...selectedSymptoms, symptom.name]);
                          } else {
                            setSelectedSymptoms(selectedSymptoms.filter(s => s !== symptom.name));
                          }
                        }}
                        className="data-[state=checked]:bg-amber-500 data-[state=checked]:border-amber-500"
                      />
                      <label
                        htmlFor={`symptom-${symptom.name}`}
                        className="text-xs font-medium text-slate-600 dark:text-slate-300 cursor-pointer leading-tight"
                        title={symptom.description}
                      >
                        {symptom.name}
                      </label>
                      {symptom.urgent && (
                        <AlertTriangle className="h-3 w-3 text-red-500 flex-shrink-0" title="Sintoma de urgência" />
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sintomas Selecionados */}
          {selectedSymptoms.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Sintomas Selecionados ({selectedSymptoms.length})
                </Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedSymptoms([])}
                  className="text-xs text-red-600 hover:text-red-700"
                >
                  <X className="h-3 w-3 mr-1" />
                  Limpar Tudo
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <AnimatePresence>
                  {selectedSymptoms.map((symptom) => (
                    <motion.div
                      key={symptom}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="group"
                    >
                      <Badge
                        variant="secondary"
                        className="bg-amber-100 text-amber-800 border-amber-200 pr-1 hover:bg-amber-200 transition-colors"
                      >
                        {symptom}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedSymptoms(selectedSymptoms.filter(s => s !== symptom))}
                          className="ml-1 h-4 w-4 p-0 hover:bg-amber-300/50 group-hover:visible"
                        >
                          <X className="h-2 w-2" />
                        </Button>
                      </Badge>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </div>
          )}

          {/* Adicionar Sintoma Personalizado */}
          <div className="space-y-3 pt-4 border-t border-slate-200 dark:border-slate-600">
            <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Adicionar Sintoma Personalizado
            </Label>
            <div className="flex gap-2">
              <Input
                placeholder="Digite um sintoma não listado..."
                value={customSymptom}
                onChange={(e) => setCustomSymptom(e.target.value)}
                className="rounded-xl border-slate-300 dark:border-slate-600 focus:border-amber-500 focus:ring-amber-500/20"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && customSymptom.trim()) {
                    handleAddCustomSymptom();
                  }
                }}
              />
              <Button
                onClick={handleAddCustomSymptom}
                disabled={!customSymptom.trim() || selectedSymptoms.includes(customSymptom.trim())}
                className="rounded-xl bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Duração dos Sintomas */}
          <div className="space-y-2 pt-4 border-t border-slate-200 dark:border-slate-600">
            <Label htmlFor="duracao" className="text-sm font-semibold text-slate-700 dark:text-slate-300">
              Duração dos Sintomas *
            </Label>
            <div className="flex flex-wrap gap-2 mb-3">
              {durationPresets.map((duration) => {
                const isSelected = anamnesisData.queixaPrincipal.duracaoSintomas === duration.value
                
                return (
                  <Button
                    key={duration.value}
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateNestedAnamnesisData('queixaPrincipal', 'duracaoSintomas', duration.value)}
                    className={`
                      px-4 py-2 text-sm rounded-md transition-all
                      ${isSelected 
                        ? 'bg-blue-600 text-white border-blue-600 shadow-lg' 
                        : 'border-gray-300 text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800'
                      }
                    `}
                  >
                    {duration.label}
                  </Button>
                )
              })}
            </div>
            <Input
              id="duracao"
              placeholder="Ex: 2 horas, 3 dias, 1 semana..."
              value={anamnesisData.queixaPrincipal.duracaoSintomas}
              onChange={(e) => updateNestedAnamnesisData('queixaPrincipal', 'duracaoSintomas', e.target.value)}
              className="rounded-xl border-slate-300 dark:border-slate-600 focus:border-amber-500 focus:ring-amber-500/20 bg-white dark:bg-slate-800"
            />
          </div>
        </CardContent>
      </Card>

      {/* Intensidade - Design Minimalista */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-red-500" />
                <Label className="font-semibold text-slate-700 dark:text-slate-300">
                  Intensidade da Dor
                </Label>
              </div>
              <div className="flex items-center gap-3">
                <Badge 
                  className="text-white font-bold px-3 py-1"
                  style={{ 
                    backgroundColor: intensityColors[anamnesisData.queixaPrincipal.intensidade as keyof typeof intensityColors] 
                  }}
                >
                  {anamnesisData.queixaPrincipal.intensidade}/10
                </Badge>
                <span className="text-sm font-medium text-slate-600 dark:text-slate-400">
                  {getIntensityLabel(anamnesisData.queixaPrincipal.intensidade)}
                </span>
              </div>
            </div>
            
            <div className="space-y-3">
              <Slider
                value={[anamnesisData.queixaPrincipal.intensidade]}
                onValueChange={(value) => updateNestedAnamnesisData('queixaPrincipal', 'intensidade', value[0])}
                max={10}
                min={1}
                step={1}
                className="w-full"
              />
              
              <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400">
                <span>1 • Leve</span>
                <span>5 • Moderada</span>
                <span>10 • Insuportável</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Características - Checkboxes Minimalistas */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Check className="h-4 w-4 text-blue-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Características da Dor
              </Label>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              {painCharacteristics.map((characteristic) => {
                const isSelected = anamnesisData.queixaPrincipal.caracteristicas.includes(characteristic.label)
                const Icon = characteristic.icon
                
                return (
                  <div key={characteristic.id} className="flex items-center">
                    <Checkbox
                      id={characteristic.id}
                      checked={isSelected}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          addCharacteristic(characteristic.label)
                        } else {
                          const index = anamnesisData.queixaPrincipal.caracteristicas.indexOf(characteristic.label)
                          if (index > -1) removeCharacteristic(index)
                        }
                      }}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label 
                      htmlFor={characteristic.id}
                      className="ml-2 flex items-center gap-2 text-sm text-gray-900 dark:text-gray-100 cursor-pointer"
                    >
                      <Icon className="h-3 w-3 text-gray-500" />
                      {characteristic.label}
                    </label>
                  </div>
                )
              })}
            </div>

            {/* Características selecionadas */}
            {anamnesisData.queixaPrincipal.caracteristicas.length > 0 && (
              <div className="mt-4 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-xl">
                <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">Selecionadas:</p>
                <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {anamnesisData.queixaPrincipal.caracteristicas.join(' • ')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Sintomas Associados - Checkboxes Minimalistas */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <AlertTriangle className="h-4 w-4 text-amber-500" />
              <Label className="font-semibold text-slate-700 dark:text-slate-300">
                Sintomas Associados
              </Label>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              {associatedFactors.map((factor) => {
                const isSelected = anamnesisData.queixaPrincipal.fatoresAssociados.includes(factor.label)
                
                return (
                  <div key={factor.id} className="flex items-center">
                    <Checkbox
                      id={factor.id}
                      checked={isSelected}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          addAssociatedFactor(factor.label)
                        } else {
                          const index = anamnesisData.queixaPrincipal.fatoresAssociados.indexOf(factor.label)
                          if (index > -1) removeAssociatedFactor(index)
                        }
                      }}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label 
                      htmlFor={factor.id}
                      className="ml-2 block text-sm text-gray-900 dark:text-gray-100 cursor-pointer"
                    >
                      {factor.label}
                      {factor.category === 'sistemico' && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          Sistêmico
                        </Badge>
                      )}
                    </label>
                  </div>
                )
              })}
            </div>

            {/* Sintomas selecionados */}
            {anamnesisData.queixaPrincipal.fatoresAssociados.length > 0 && (
              <div className="mt-4 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-xl">
                <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">Sintomas presentes:</p>
                <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {anamnesisData.queixaPrincipal.fatoresAssociados.join(' • ')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Observações - Opcional */}
      <Card className="bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/50 shadow-lg rounded-xl">
        <CardContent className="p-6">
          <div className="space-y-3">
            <Label className="font-semibold text-slate-700 dark:text-slate-300">
              Observações Adicionais (Opcional)
            </Label>
            <Textarea
              placeholder="Informações complementares: fatores de melhora/piora, cronologia, contexto..."
              value={anamnesisData.queixaPrincipal.observacoes}
              onChange={(e) => updateNestedAnamnesisData('queixaPrincipal', 'observacoes', e.target.value)}
              rows={3}
              className="resize-none rounded-xl border-slate-300 dark:border-slate-600 focus:border-blue-500 focus:ring-blue-500/20 bg-white dark:bg-slate-800"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ChiefComplaint