import React, { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { 
  Users, 
  Clock, 
  Activity, 
  FileText,
  Heart,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { useMedical } from './MedicalContext';
import { designSystem } from '../utils/design-system';

export const RealTimeStats: React.FC = () => {
  const { patients } = useMedical();
  const [stats, setStats] = useState({
    totalPatients: 0,
    activeConsultations: 0,
    waitingTime: 0,
    prescriptions: 0,
    emergencyCases: 0,
    bedOccupancy: 0,
    patientSatisfaction: 0,
    systemLoad: 0
  });

  const [trends, setTrends] = useState({
    totalPatients: 'up',
    activeConsultations: 'stable',
    waitingTime: 'down',
    prescriptions: 'up'
  });

  // Simular atualizações em tempo real
  useEffect(() => {
    const updateStats = () => {
      const totalPatients = patients.length;
      const activeConsultations = patients.filter(p => p.status === 'atendimento').length;
      const emergencyCases = patients.filter(p => p.priority === 'emergencia').length;
      
      setStats({
        totalPatients,
        activeConsultations,
        waitingTime: Math.floor(Math.random() * 45) + 15, // 15-60 min
        prescriptions: Math.floor(Math.random() * 20) + 80, // 80-100
        emergencyCases,
        bedOccupancy: Math.floor(Math.random() * 30) + 70, // 70-100%
        patientSatisfaction: Math.floor(Math.random() * 10) + 90, // 90-100%
        systemLoad: Math.floor(Math.random() * 40) + 20 // 20-60%
      });

      // Simular tendências
      const trendOptions = ['up', 'down', 'stable'];
      setTrends({
        totalPatients: trendOptions[Math.floor(Math.random() * 3)],
        activeConsultations: trendOptions[Math.floor(Math.random() * 3)],
        waitingTime: 'down', // Sempre bom quando diminui
        prescriptions: trendOptions[Math.floor(Math.random() * 3)]
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 30000); // Atualiza a cada 30s
    return () => clearInterval(interval);
  }, [patients]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-600" />;
      default:
        return <Minus className="h-3 w-3 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string, isPositive: boolean = true) => {
    if (trend === 'stable') return 'text-gray-600';
    
    if (isPositive) {
      return trend === 'up' ? 'text-green-600' : 'text-red-600';
    } else {
      return trend === 'up' ? 'text-red-600' : 'text-green-600';
    }
  };

  const statsData = [
    {
      id: 'patients',
      label: 'Pacientes Hoje',
      value: stats.totalPatients,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950',
      trend: trends.totalPatients,
      change: '+12%',
      isPositiveTrend: true
    },
    {
      id: 'consultations',
      label: 'Consultas Ativas',
      value: stats.activeConsultations,
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-950',
      trend: trends.activeConsultations,
      change: '+5%',
      isPositiveTrend: true
    },
    {
      id: 'waiting',
      label: 'Tempo Médio',
      value: `${stats.waitingTime}min`,
      icon: Clock,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50 dark:bg-amber-950',
      trend: trends.waitingTime,
      change: '-8%',
      isPositiveTrend: false
    },
    {
      id: 'prescriptions',
      label: 'Receituários',
      value: stats.prescriptions,
      icon: FileText,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-950',
      trend: trends.prescriptions,
      change: '+3%',
      isPositiveTrend: true
    }
  ];

  const detailedStats = [
    {
      label: 'Casos de Emergência',
      value: stats.emergencyCases,
      icon: AlertTriangle,
      color: stats.emergencyCases > 3 ? 'text-red-600' : 'text-orange-600',
      bgColor: stats.emergencyCases > 3 ? 'bg-red-50 dark:bg-red-950' : 'bg-orange-50 dark:bg-orange-950'
    },
    {
      label: 'Ocupação de Leitos',
      value: `${stats.bedOccupancy}%`,
      progress: stats.bedOccupancy,
      icon: Heart,
      color: stats.bedOccupancy > 90 ? 'text-red-600' : 'text-green-600',
      bgColor: stats.bedOccupancy > 90 ? 'bg-red-50 dark:bg-red-950' : 'bg-green-50 dark:bg-green-950'
    },
    {
      label: 'Satisfação dos Pacientes',
      value: `${stats.patientSatisfaction}%`,
      progress: stats.patientSatisfaction,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950'
    },
    {
      label: 'Carga do Sistema',
      value: `${stats.systemLoad}%`,
      progress: stats.systemLoad,
      icon: Activity,
      color: stats.systemLoad > 80 ? 'text-red-600' : 'text-green-600',
      bgColor: stats.systemLoad > 80 ? 'bg-red-50 dark:bg-red-950' : 'bg-green-50 dark:bg-green-950'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Quick Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsData.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="border hover:shadow-md transition-all duration-200 group cursor-pointer">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className={`p-2 rounded-lg ${stat.bgColor} transition-transform group-hover:scale-110`}>
                      <IconComponent className={`h-4 w-4 ${stat.color}`} />
                    </div>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(stat.trend)}
                      <span className={`text-xs font-medium ${getTrendColor(stat.trend, stat.isPositiveTrend)}`}>
                        {stat.change}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-2xl font-bold text-foreground mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stat.label}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Detailed Stats */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="font-semibold text-foreground">Estatísticas Detalhadas</h3>
            <Badge variant="outline" className="text-xs">
              Tempo real
            </Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {detailedStats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                        <IconComponent className={`h-4 w-4 ${stat.color}`} />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-foreground">
                          {stat.label}
                        </div>
                        <div className={`text-lg font-bold ${stat.color}`}>
                          {stat.value}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {stat.progress !== undefined && (
                    <div className="space-y-2">
                      <Progress 
                        value={stat.progress} 
                        className="h-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>0%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card className={`border-l-4 ${
        stats.systemLoad > 80 
          ? 'border-l-red-500 bg-red-50 dark:bg-red-950' 
          : stats.systemLoad > 60 
            ? 'border-l-amber-500 bg-amber-50 dark:bg-amber-950'
            : 'border-l-green-500 bg-green-50 dark:bg-green-950'
      }`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                stats.systemLoad > 80 
                  ? 'bg-red-500 animate-pulse' 
                  : stats.systemLoad > 60 
                    ? 'bg-amber-500' 
                    : 'bg-green-500'
              }`}></div>
              <div>
                <div className="text-sm font-medium text-foreground">
                  Status do Sistema
                </div>
                <div className="text-xs text-muted-foreground">
                  {stats.systemLoad > 80 
                    ? 'Carga alta - monitorando performance' 
                    : stats.systemLoad > 60 
                      ? 'Funcionamento normal' 
                      : 'Sistema operando perfeitamente'
                  }
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm font-medium text-foreground">
                {stats.systemLoad}% de uso
              </div>
              <div className="text-xs text-muted-foreground">
                Última atualização: agora
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};