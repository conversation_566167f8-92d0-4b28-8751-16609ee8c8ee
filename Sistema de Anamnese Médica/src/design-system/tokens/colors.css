/* Medical Color Tokens - Enhanced Design System v2.0 */

:root {
  /* Medical Primary Colors - Professional Blue Palette Enhanced */
  --medical-color-primary: #0A6EBD;
  --medical-color-primary-50: #EBF7FF;
  --medical-color-primary-100: #D6EEFF;
  --medical-color-primary-200: #B5DFFF;
  --medical-color-primary-300: #83C9FF;
  --medical-color-primary-400: #48ABFF;
  --medical-color-primary-500: #1E8FFF;
  --medical-color-primary-600: #0A6EBD;
  --medical-color-primary-700: #0B5394;
  --medical-color-primary-800: #10467A;
  --medical-color-primary-900: #133D64;
  
  /* Medical Secondary Colors - Trust Building Palette */
  --medical-color-secondary: #6366F1;
  --medical-color-secondary-50: #EEF2FF;
  --medical-color-secondary-100: #E0E7FF;
  --medical-color-secondary-200: #C7D2FE;
  --medical-color-secondary-300: #A5B4FC;
  --medical-color-secondary-400: #818CF8;
  --medical-color-secondary-500: #6366F1;
  --medical-color-secondary-600: #4F46E5;
  --medical-color-secondary-700: #4338CA;
  --medical-color-secondary-800: #3730A3;
  --medical-color-secondary-900: #312E81;
  
  /* Medical Status Colors - Following Clinical Standards */
  --medical-color-critical: #D92D20;
  --medical-color-critical-50: #FEF3F2;
  --medical-color-critical-100: #FEE4E2;
  --medical-color-critical-200: #FECDCA;
  --medical-color-critical-300: #FDA29B;
  --medical-color-critical-400: #F97066;
  --medical-color-critical-500: #F04438;
  --medical-color-critical-600: #D92D20;
  --medical-color-critical-700: #B42318;
  --medical-color-critical-800: #912018;
  --medical-color-critical-900: #7A271A;
  
  --medical-color-urgent: #F79009;
  --medical-color-urgent-50: #FFFCF5;
  --medical-color-urgent-100: #FFFAEB;
  --medical-color-urgent-200: #FEF0C7;
  --medical-color-urgent-300: #FEDF89;
  --medical-color-urgent-400: #FEC84B;
  --medical-color-urgent-500: #FDB022;
  --medical-color-urgent-600: #F79009;
  --medical-color-urgent-700: #DC6803;
  --medical-color-urgent-800: #B54708;
  --medical-color-urgent-900: #93370D;
  
  --medical-color-routine: var(--medical-color-primary);
  --medical-color-routine-50: var(--medical-color-primary-50);
  --medical-color-routine-100: var(--medical-color-primary-100);
  --medical-color-routine-200: var(--medical-color-primary-200);
  --medical-color-routine-300: var(--medical-color-primary-300);
  --medical-color-routine-400: var(--medical-color-primary-400);
  --medical-color-routine-500: var(--medical-color-primary-500);
  --medical-color-routine-600: var(--medical-color-primary-600);
  --medical-color-routine-700: var(--medical-color-primary-700);
  --medical-color-routine-800: var(--medical-color-primary-800);
  --medical-color-routine-900: var(--medical-color-primary-900);
  
  --medical-color-success: #22C55E;
  --medical-color-success-50: #F0FDF4;
  --medical-color-success-100: #DCFCE7;
  --medical-color-success-200: #BBF7D0;
  --medical-color-success-300: #86EFAC;
  --medical-color-success-400: #4ADE80;
  --medical-color-success-500: #22C55E;
  --medical-color-success-600: #16A34A;
  --medical-color-success-700: #15803D;
  --medical-color-success-800: #166534;
  --medical-color-success-900: #14532D;
  
  --medical-color-info: #2E90FA;
  --medical-color-info-50: #EFF6FF;
  --medical-color-info-100: #DBEAFE;
  --medical-color-info-200: #BFDBFE;
  --medical-color-info-300: #93C5FD;
  --medical-color-info-400: #60A5FA;
  --medical-color-info-500: #3B82F6;
  --medical-color-info-600: #2E90FA;
  --medical-color-info-700: #1D4ED8;
  --medical-color-info-800: #1E40AF;
  --medical-color-info-900: #1E3A8A;

  /* Medical Background Colors */
  --medical-bg-primary: white;
  --medical-bg-secondary: #F8FAFC;
  --medical-bg-tertiary: #F1F5F9;
  --medical-bg-elevated: white;
  --medical-bg-overlay: rgba(15, 23, 42, 0.1);
  
  /* Medical Glass Morphism Colors */
  --medical-glass-bg: rgba(255, 255, 255, 0.9);
  --medical-glass-border: rgba(255, 255, 255, 0.2);
  --medical-glass-hero: rgba(255, 255, 255, 0.95);
  --medical-glass-premium: rgba(255, 255, 255, 0.92);
  
  /* Medical Text Colors */
  --medical-text-primary: #0F172A;
  --medical-text-secondary: #475569;
  --medical-text-tertiary: #64748B;
  --medical-text-inverse: white;
  --medical-text-muted: #94A3B8;
  
  /* Medical Border Colors */
  --medical-border-base: #E2E8F0;
  --medical-border-strong: #CBD5E1;
  --medical-border-critical: var(--medical-color-critical-400);
  --medical-border-urgent: var(--medical-color-urgent-400);
  --medical-border-success: var(--medical-color-success-400);
  --medical-border-info: var(--medical-color-info-400);
  
  /* Medical Focus and Interactive States */
  --medical-focus-ring: var(--medical-color-primary-500);
  --medical-focus-ring-critical: var(--medical-color-critical-500);
  --medical-focus-ring-urgent: var(--medical-color-urgent-500);
  
  /* Medical Card and Component Backgrounds */
  --medical-card-bg: white;
  --medical-card-patient: #FEFEFE;
  --medical-card-data: white;
  --medical-card-dashboard: rgba(255, 255, 255, 0.95);
  --medical-card-emergency: var(--medical-color-critical-50);
  
  /* Medical Input Colors */
  --medical-input-bg: white;
  --medical-input-border: var(--medical-border-base);
  --medical-input-border-focus: var(--medical-color-primary);
  --medical-input-border-error: var(--medical-color-critical);
  --medical-input-border-success: var(--medical-color-success);
  
  /* Medical Button Colors */
  --medical-button-primary-bg: var(--medical-color-primary);
  --medical-button-primary-hover: var(--medical-color-primary-700);
  --medical-button-primary-text: white;
  --medical-button-critical-bg: var(--medical-color-critical);
  --medical-button-critical-hover: var(--medical-color-critical-700);
  --medical-button-urgent-bg: var(--medical-color-urgent);
  --medical-button-urgent-hover: var(--medical-color-urgent-700);
  --medical-button-success-bg: var(--medical-color-success);
  --medical-button-success-hover: var(--medical-color-success-700);
  
  /* Medical Gradient Tokens - Professional Gradients */
  --medical-gradient-primary: linear-gradient(135deg, var(--medical-color-primary-500) 0%, var(--medical-color-primary-700) 100%);
  --medical-gradient-secondary: linear-gradient(135deg, var(--medical-color-secondary-500) 0%, var(--medical-color-secondary-700) 100%);
  --medical-gradient-critical: linear-gradient(135deg, var(--medical-color-critical-500) 0%, var(--medical-color-critical-700) 100%);
  --medical-gradient-urgent: linear-gradient(135deg, var(--medical-color-urgent-500) 0%, var(--medical-color-urgent-700) 100%);
  --medical-gradient-success: linear-gradient(135deg, var(--medical-color-success-500) 0%, var(--medical-color-success-700) 100%);
  --medical-gradient-soft: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  --medical-gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  
  /* Medical Shadow Tokens - Professional Depth */
  --medical-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --medical-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --medical-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --medical-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --medical-shadow-glow: 0 0 20px rgba(10, 110, 189, 0.3);
  --medical-shadow-critical: 0 0 20px rgba(217, 45, 32, 0.3);
  --medical-shadow-urgent: 0 0 20px rgba(247, 144, 9, 0.3);
  --medical-shadow-success: 0 0 20px rgba(34, 197, 94, 0.3);
  
  /* Medical Animation Tokens - Professional Timing */
  --medical-duration-fast: 150ms;
  --medical-duration-normal: 200ms;
  --medical-duration-slow: 300ms;
  --medical-ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --medical-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --medical-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Medical Colors */
[data-theme="dark"], .dark {
  /* Medical Primary Colors - Dark Mode Variants */
  --medical-color-primary-dark: #1E40AF;
  --medical-color-primary: #3B82F6;
  --medical-color-primary-hover: #2563EB;
  
  /* Medical Status Colors - Dark Mode */
  --medical-color-critical: #EF4444;
  --medical-color-critical-hover: #DC2626;
  --medical-color-urgent: #F59E0B;
  --medical-color-urgent-hover: #D97706;
  --medical-color-success: #10B981;
  --medical-color-success-hover: #059669;
  --medical-color-info: #3B82F6;
  --medical-color-info-hover: #2563EB;
  
  /* Medical Backgrounds - Dark Mode */
  --medical-bg-primary: #0F172A;
  --medical-bg-secondary: #1E293B;
  --medical-bg-tertiary: #334155;
  --medical-bg-elevated: #1E293B;
  --medical-bg-overlay: rgba(0, 0, 0, 0.8);
  
  /* Medical Glass Morphism - Dark Mode */
  --medical-glass-bg: rgba(30, 41, 59, 0.9);
  --medical-glass-border: rgba(148, 163, 184, 0.2);
  --medical-glass-hero: rgba(30, 41, 59, 0.95);
  --medical-glass-premium: rgba(30, 41, 59, 0.92);
  --medical-glass-dark: rgba(15, 23, 42, 0.9);
  --medical-glass-dark-border: rgba(148, 163, 184, 0.1);
  
  /* Medical Text Colors - Dark Mode */
  --medical-text-primary: #F8FAFC;
  --medical-text-secondary: #CBD5E1;
  --medical-text-tertiary: #94A3B8;
  --medical-text-inverse: #0F172A;
  --medical-text-muted: #64748B;
  
  /* Medical Border Colors - Dark Mode */
  --medical-border-base: #374151;
  --medical-border-strong: #4B5563;
  
  /* Medical Card Backgrounds - Dark Mode */
  --medical-card-bg: #1E293B;
  --medical-card-patient: #1E293B;
  --medical-card-data: #1E293B;
  --medical-card-dashboard: rgba(30, 41, 59, 0.95);
  
  /* Medical Input Colors - Dark Mode */
  --medical-input-bg: #1E293B;
  --medical-input-border: #374151;
  
  /* Medical Gradient Tokens - Dark Mode */
  --medical-gradient-primary: linear-gradient(135deg, var(--medical-color-primary) 0%, var(--medical-color-primary-hover) 100%);
  --medical-gradient-secondary: linear-gradient(135deg, var(--medical-color-secondary) 0%, var(--medical-color-secondary-700) 100%);
  --medical-gradient-soft: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(30, 41, 59, 0.7) 100%);
  --medical-gradient-glass: linear-gradient(135deg, rgba(30, 41, 59, 0.1) 0%, rgba(30, 41, 59, 0.05) 100%);
  
  /* Medical Shadow Tokens - Dark Mode */
  --medical-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --medical-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --medical-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --medical-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  --medical-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.4);
  --medical-shadow-critical: 0 0 20px rgba(239, 68, 68, 0.4);
  --medical-shadow-urgent: 0 0 20px rgba(245, 158, 11, 0.4);
  --medical-shadow-success: 0 0 20px rgba(16, 185, 129, 0.4);
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --medical-color-primary: #0000FF;
    --medical-color-critical: #FF0000;
    --medical-color-success: #00FF00;
    --medical-border-base: #000000;
    --medical-text-primary: #000000;
    --medical-bg-primary: #FFFFFF;
  }
  
  [data-theme="dark"], .dark {
    --medical-color-primary: #00FFFF;
    --medical-color-critical: #FF4444;
    --medical-color-success: #00FF00;
    --medical-border-base: #FFFFFF;
    --medical-text-primary: #FFFFFF;
    --medical-bg-primary: #000000;
  }
}

/* Utility Classes for Medical Colors */
.bg-medical-primary { background-color: var(--medical-color-primary); }
.bg-medical-critical { background-color: var(--medical-color-critical); }
.bg-medical-urgent { background-color: var(--medical-color-urgent); }
.bg-medical-routine { background-color: var(--medical-color-routine); }
.bg-medical-success { background-color: var(--medical-color-success); }
.bg-medical-info { background-color: var(--medical-color-info); }

.text-medical-primary { color: var(--medical-color-primary); }
.text-medical-critical { color: var(--medical-color-critical); }
.text-medical-urgent { color: var(--medical-color-urgent); }
.text-medical-success { color: var(--medical-color-success); }
.text-medical-info { color: var(--medical-color-info); }
.text-white { color: white; }

.border-medical-primary { border-color: var(--medical-color-primary); }
.border-medical-critical { border-color: var(--medical-border-critical); }
.border-medical-critical-border { border-color: var(--medical-color-critical); }
.border-medical-urgent { border-color: var(--medical-border-urgent); }
.border-medical-success { border-color: var(--medical-border-success); }

/* Medical Glass Morphism Classes */
.bg-medical-glass { background-color: var(--medical-glass-bg); }
.bg-medical-glass-premium { background-color: var(--medical-glass-premium); }
.bg-medical-glass-hero { background-color: var(--medical-glass-hero); }
.bg-medical-glass-dark { background-color: var(--medical-glass-dark); }

.border-medical-glass-border { border-color: var(--medical-glass-border); }
.border-medical-glass-dark-border { border-color: var(--medical-glass-dark-border); }

.backdrop-blur-medical { backdrop-filter: blur(12px); }
.backdrop-blur-medical-subtle { backdrop-filter: blur(8px); }
.backdrop-blur-medical-strong { backdrop-filter: blur(16px); }

/* Medical Gradient Classes */
.bg-medical-gradient-primary { background: var(--medical-gradient-primary); }
.bg-medical-gradient-secondary { background: var(--medical-gradient-secondary); }
.bg-medical-gradient-critical { background: var(--medical-gradient-critical); }
.bg-medical-gradient-urgent { background: var(--medical-gradient-urgent); }
.bg-medical-gradient-success { background: var(--medical-gradient-success); }
.bg-medical-gradient-soft { background: var(--medical-gradient-soft); }
.bg-medical-gradient-glass { background: var(--medical-gradient-glass); }

/* Medical Shadow Classes */
.shadow-medical-sm { box-shadow: var(--medical-shadow-sm); }
.shadow-medical-md { box-shadow: var(--medical-shadow-md); }
.shadow-medical-lg { box-shadow: var(--medical-shadow-lg); }
.shadow-medical-xl { box-shadow: var(--medical-shadow-xl); }
.shadow-medical-glow { box-shadow: var(--medical-shadow-glow); }
.shadow-medical-critical { box-shadow: var(--medical-shadow-critical); }
.shadow-medical-urgent { box-shadow: var(--medical-shadow-urgent); }
.shadow-medical-success { box-shadow: var(--medical-shadow-success); }

/* Medical Animation Classes */
.transition-medical-fast { transition-duration: var(--medical-duration-fast); }
.transition-medical-normal { transition-duration: var(--medical-duration-normal); }
.transition-medical-slow { transition-duration: var(--medical-duration-slow); }
.ease-medical-out { transition-timing-function: var(--medical-ease-out); }
.ease-medical-in { transition-timing-function: var(--medical-ease-in); }
.ease-medical-in-out { transition-timing-function: var(--medical-ease-in-out); }