/* Medical Design System Tokens - Main Import File */

/* Import all medical design tokens */
@import './colors.css';
@import './typography.css';
@import './spacing.css';
@import './shadows.css';
@import './animations.css';
@import './layout.css';

/* Medical Design System Global Styles */
:root {
  /* Color scheme support */
  color-scheme: light dark;
  
  /* CSS containment for performance */
  contain: layout style paint;
}

/* Medical Base Styles */
* {
  /* Ensure consistent box-sizing */
  box-sizing: border-box;
  
  /* Improve font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Medical Body Defaults */
body {
  /* Apply medical typography */
  font-family: var(--medical-font-family);
  font-size: var(--medical-text-base);
  line-height: var(--medical-leading-relaxed);
  color: var(--medical-text-primary);
  background-color: var(--medical-bg-primary);
  
  /* Disable text selection on UI elements */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* Medical Focus Management */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--medical-focus-ring);
  outline-offset: 2px;
}

/* Medical Button Reset */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
}

/* Medical Input Reset */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: transparent;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
}

/* Medical Link Styles */
a {
  color: var(--medical-color-primary);
  text-decoration: none;
  transition: color var(--medical-duration-fast) var(--medical-ease-out);
}

a:hover {
  color: var(--medical-color-primary-hover);
  text-decoration: underline;
}

a:focus-visible {
  outline: 2px solid var(--medical-focus-ring);
  outline-offset: 2px;
  border-radius: var(--medical-radius-xs);
}

/* Medical List Reset */
ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Medical Heading Defaults */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--medical-font-semibold);
  line-height: var(--medical-leading-tight);
  color: var(--medical-text-primary);
  margin: 0;
}

h1 { font-size: var(--medical-text-4xl); }
h2 { font-size: var(--medical-text-3xl); }
h3 { font-size: var(--medical-text-2xl); }
h4 { font-size: var(--medical-text-xl); }
h5 { font-size: var(--medical-text-lg); }
h6 { font-size: var(--medical-text-base); }

/* Medical Paragraph Defaults */
p {
  margin: 0;
  line-height: var(--medical-leading-relaxed);
  color: var(--medical-text-primary);
}

/* Medical Code Elements */
code {
  font-family: var(--medical-font-family-mono);
  font-size: 0.875em;
  background-color: var(--medical-bg-secondary);
  padding: 0.125rem 0.25rem;
  border-radius: var(--medical-radius-xs);
  color: var(--medical-color-primary);
}

pre {
  font-family: var(--medical-font-family-mono);
  background-color: var(--medical-bg-secondary);
  padding: var(--medical-spacing-base);
  border-radius: var(--medical-radius-base);
  overflow-x: auto;
  color: var(--medical-text-primary);
}

/* Medical Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  font-variant-numeric: tabular-nums;
}

th,
td {
  padding: var(--medical-spacing-sm) var(--medical-spacing-base);
  text-align: left;
  border-bottom: 1px solid var(--medical-border-base);
}

th {
  font-weight: var(--medical-font-semibold);
  color: var(--medical-text-secondary);
  font-size: var(--medical-text-sm);
  text-transform: uppercase;
  letter-spacing: var(--medical-tracking-wide);
}

/* Medical Form Elements */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

legend {
  font-weight: var(--medical-font-semibold);
  color: var(--medical-text-primary);
  margin-bottom: var(--medical-spacing-sm);
}

/* Medical Utility Classes */
.medical-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.medical-not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Medical High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --medical-shadow-base: 0 0 0 1px var(--medical-border-base);
    --medical-shadow-elevated: 0 0 0 2px var(--medical-border-strong);
  }
}

/* Medical Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Medical Print Styles */
@media print {
  :root {
    --medical-bg-primary: white;
    --medical-text-primary: black;
    --medical-border-base: #000;
  }
  
  * {
    color: black !important;
    background: transparent !important;
    box-shadow: none !important;
  }
  
  a,
  a:visited {
    color: black !important;
    text-decoration: underline;
  }
  
  .medical-print-hidden {
    display: none !important;
  }
  
  .medical-print-break-before {
    page-break-before: always;
  }
  
  .medical-print-break-after {
    page-break-after: always;
  }
  
  .medical-print-avoid-break {
    page-break-inside: avoid;
  }
}

/* Medical Device-Specific Optimizations */
@media (pointer: coarse) {
  /* Touch devices - larger touch targets */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"] {
    min-height: var(--medical-touch-target-min);
    min-width: var(--medical-touch-target-min);
  }
}

@media (hover: none) {
  /* No hover capability - disable hover effects */
  .hover\:bg-medical-primary:hover {
    background-color: initial;
  }
  
  .hover\:shadow-medical-hover:hover {
    box-shadow: initial;
  }
}

/* Medical Performance Hints */
.medical-gpu-acceleration {
  transform: translateZ(0);
  will-change: transform;
}

.medical-contain-layout {
  contain: layout;
}

.medical-contain-paint {
  contain: paint;
}

.medical-contain-size {
  contain: size;
}

/* Medical Animation Performance */
@media (prefers-reduced-motion: no-preference) {
  .medical-smooth-scroll {
    scroll-behavior: smooth;
  }
}

/* Medical Focus-Within Support */
.medical-focus-within:focus-within {
  outline: 2px solid var(--medical-focus-ring);
  outline-offset: 2px;
}

/* Medical Color Scheme Utilities */
.medical-light-only {
  display: block;
}

.medical-dark-only {
  display: none;
}

@media (prefers-color-scheme: dark) {
  .medical-light-only {
    display: none;
  }
  
  .medical-dark-only {
    display: block;
  }
}

[data-theme="dark"] .medical-light-only,
.dark .medical-light-only {
  display: none;
}

[data-theme="dark"] .medical-dark-only,
.dark .medical-dark-only {
  display: block;
}

/* Medical RTL Support */
[dir="rtl"] {
  .medical-rtl\:text-right {
    text-align: right;
  }
  
  .medical-rtl\:pl-medical-base {
    padding-left: var(--medical-spacing-base);
  }
  
  .medical-rtl\:pr-medical-0 {
    padding-right: 0;
  }
}