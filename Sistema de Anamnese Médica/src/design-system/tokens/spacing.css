/* Medical Spacing Tokens - 8px Grid System */

:root {
  /* Medical Base Grid Unit */
  --medical-grid-base: 8px;
  
  /* Medical Spacing Scale - Based on 8px Grid */
  --medical-spacing-0: 0px;
  --medical-spacing-px: 1px;
  --medical-spacing-xs: calc(var(--medical-grid-base) * 0.5);  /* 4px */
  --medical-spacing-sm: calc(var(--medical-grid-base) * 1);    /* 8px */
  --medical-spacing-base: calc(var(--medical-grid-base) * 2);  /* 16px */
  --medical-spacing-lg: calc(var(--medical-grid-base) * 3);    /* 24px */
  --medical-spacing-xl: calc(var(--medical-grid-base) * 4);    /* 32px */
  --medical-spacing-2xl: calc(var(--medical-grid-base) * 5);   /* 40px */
  --medical-spacing-3xl: calc(var(--medical-grid-base) * 6);   /* 48px */
  --medical-spacing-4xl: calc(var(--medical-grid-base) * 8);   /* 64px */
  --medical-spacing-5xl: calc(var(--medical-grid-base) * 10);  /* 80px */
  --medical-spacing-6xl: calc(var(--medical-grid-base) * 12);  /* 96px */
  
  /* Medical Grid Gaps */
  --medical-gap-grid: var(--medical-grid-base);                /* 8px */
  --medical-gap-component: var(--medical-spacing-base);        /* 16px */
  --medical-gap-section: var(--medical-spacing-lg);            /* 24px */
  --medical-gap-layout: var(--medical-spacing-xl);             /* 32px */
  
  /* Medical Touch Spacing - For Device Compatibility */
  --medical-touch-xs: calc(var(--medical-grid-base) * 1.5);    /* 12px */
  --medical-touch-sm: calc(var(--medical-grid-base) * 2);      /* 16px */
  --medical-touch-base: calc(var(--medical-grid-base) * 2.5);  /* 20px */
  --medical-touch-lg: calc(var(--medical-grid-base) * 3);      /* 24px */
  --medical-touch-xl: calc(var(--medical-grid-base) * 4);      /* 32px */
  
  /* Medical Component Specific Spacing */
  --medical-button-padding-x: var(--medical-spacing-lg);       /* 24px */
  --medical-button-padding-y: var(--medical-spacing-base);     /* 16px */
  --medical-card-padding: var(--medical-spacing-lg);           /* 24px */
  --medical-input-padding: var(--medical-spacing-base);        /* 16px */
  --medical-form-gap: var(--medical-spacing-lg);               /* 24px */
  --medical-section-gap: var(--medical-spacing-xl);            /* 32px */
  
  /* Medical Layout Spacing */
  --medical-layout-padding-xs: var(--medical-spacing-base);    /* 16px */
  --medical-layout-padding-sm: var(--medical-spacing-lg);      /* 24px */
  --medical-layout-padding-base: var(--medical-spacing-xl);    /* 32px */
  --medical-layout-padding-lg: var(--medical-spacing-2xl);     /* 40px */
  --medical-layout-padding-xl: var(--medical-spacing-3xl);     /* 48px */
  
  /* Medical Dashboard Specific Spacing */
  --medical-dashboard-padding: var(--medical-spacing-xl);      /* 32px */
  --medical-dashboard-gap: var(--medical-spacing-lg);          /* 24px */
  --medical-widget-padding: var(--medical-spacing-base);       /* 16px */
  --medical-widget-gap: var(--medical-spacing-sm);             /* 8px */
  
  /* Medical Form Specific Spacing */
  --medical-form-padding: var(--medical-spacing-xl);           /* 32px */
  --medical-field-gap: var(--medical-spacing-base);            /* 16px */
  --medical-fieldset-gap: var(--medical-spacing-lg);           /* 24px */
  --medical-form-section-gap: var(--medical-spacing-2xl);      /* 40px */
}

/* Medical Padding Classes */
.p-medical-0 { padding: var(--medical-spacing-0); }
.p-medical-px { padding: var(--medical-spacing-px); }
.p-medical-xs { padding: var(--medical-spacing-xs); }
.p-medical-sm { padding: var(--medical-spacing-sm); }
.p-medical-base { padding: var(--medical-spacing-base); }
.p-medical-lg { padding: var(--medical-spacing-lg); }
.p-medical-xl { padding: var(--medical-spacing-xl); }
.p-medical-2xl { padding: var(--medical-spacing-2xl); }
.p-medical-3xl { padding: var(--medical-spacing-3xl); }
.p-medical-4xl { padding: var(--medical-spacing-4xl); }

/* Medical Padding X (Horizontal) Classes */
.px-medical-0 { padding-left: var(--medical-spacing-0); padding-right: var(--medical-spacing-0); }
.px-medical-xs { padding-left: var(--medical-spacing-xs); padding-right: var(--medical-spacing-xs); }
.px-medical-sm { padding-left: var(--medical-spacing-sm); padding-right: var(--medical-spacing-sm); }
.px-medical-base { padding-left: var(--medical-spacing-base); padding-right: var(--medical-spacing-base); }
.px-medical-lg { padding-left: var(--medical-spacing-lg); padding-right: var(--medical-spacing-lg); }
.px-medical-xl { padding-left: var(--medical-spacing-xl); padding-right: var(--medical-spacing-xl); }

/* Medical Padding Y (Vertical) Classes */
.py-medical-0 { padding-top: var(--medical-spacing-0); padding-bottom: var(--medical-spacing-0); }
.py-medical-xs { padding-top: var(--medical-spacing-xs); padding-bottom: var(--medical-spacing-xs); }
.py-medical-sm { padding-top: var(--medical-spacing-sm); padding-bottom: var(--medical-spacing-sm); }
.py-medical-base { padding-top: var(--medical-spacing-base); padding-bottom: var(--medical-spacing-base); }
.py-medical-lg { padding-top: var(--medical-spacing-lg); padding-bottom: var(--medical-spacing-lg); }
.py-medical-xl { padding-top: var(--medical-spacing-xl); padding-bottom: var(--medical-spacing-xl); }

/* Medical Margin Classes */
.m-medical-0 { margin: var(--medical-spacing-0); }
.m-medical-xs { margin: var(--medical-spacing-xs); }
.m-medical-sm { margin: var(--medical-spacing-sm); }
.m-medical-base { margin: var(--medical-spacing-base); }
.m-medical-lg { margin: var(--medical-spacing-lg); }
.m-medical-xl { margin: var(--medical-spacing-xl); }
.m-medical-2xl { margin: var(--medical-spacing-2xl); }
.m-medical-auto { margin: auto; }

/* Medical Margin X (Horizontal) Classes */
.mx-medical-0 { margin-left: var(--medical-spacing-0); margin-right: var(--medical-spacing-0); }
.mx-medical-xs { margin-left: var(--medical-spacing-xs); margin-right: var(--medical-spacing-xs); }
.mx-medical-sm { margin-left: var(--medical-spacing-sm); margin-right: var(--medical-spacing-sm); }
.mx-medical-base { margin-left: var(--medical-spacing-base); margin-right: var(--medical-spacing-base); }
.mx-medical-lg { margin-left: var(--medical-spacing-lg); margin-right: var(--medical-spacing-lg); }
.mx-medical-auto { margin-left: auto; margin-right: auto; }

/* Medical Margin Y (Vertical) Classes */
.my-medical-0 { margin-top: var(--medical-spacing-0); margin-bottom: var(--medical-spacing-0); }
.my-medical-xs { margin-top: var(--medical-spacing-xs); margin-bottom: var(--medical-spacing-xs); }
.my-medical-sm { margin-top: var(--medical-spacing-sm); margin-bottom: var(--medical-spacing-sm); }
.my-medical-base { margin-top: var(--medical-spacing-base); margin-bottom: var(--medical-spacing-base); }
.my-medical-lg { margin-top: var(--medical-spacing-lg); margin-bottom: var(--medical-spacing-lg); }
.my-medical-xl { margin-top: var(--medical-spacing-xl); margin-bottom: var(--medical-spacing-xl); }

/* Medical Gap Classes */
.gap-medical-0 { gap: var(--medical-spacing-0); }
.gap-medical-xs { gap: var(--medical-spacing-xs); }
.gap-medical-sm { gap: var(--medical-spacing-sm); }
.gap-medical-base { gap: var(--medical-spacing-base); }
.gap-medical-lg { gap: var(--medical-spacing-lg); }
.gap-medical-xl { gap: var(--medical-spacing-xl); }
.gap-medical-2xl { gap: var(--medical-spacing-2xl); }

.gap-medical-grid { gap: var(--medical-gap-grid); }
.gap-medical-component { gap: var(--medical-gap-component); }
.gap-medical-section { gap: var(--medical-gap-section); }
.gap-medical-layout { gap: var(--medical-gap-layout); }

/* Medical Touch-Friendly Padding */
.p-medical-touch { padding: var(--medical-touch-base); }
.p-medical-touch-lg { padding: var(--medical-touch-lg); }
.p-medical-touch-xl { padding: var(--medical-touch-xl); }

.px-medical-touch { padding-left: var(--medical-touch-base); padding-right: var(--medical-touch-base); }
.py-medical-touch { padding-top: var(--medical-touch-base); padding-bottom: var(--medical-touch-base); }

/* Medical Component Specific Spacing Classes */
.spacing-medical-button {
  padding: var(--medical-button-padding-y) var(--medical-button-padding-x);
}

.spacing-medical-card {
  padding: var(--medical-card-padding);
}

.spacing-medical-input {
  padding: var(--medical-input-padding);
}

.spacing-medical-form {
  gap: var(--medical-form-gap);
}

.spacing-medical-dashboard {
  padding: var(--medical-dashboard-padding);
  gap: var(--medical-dashboard-gap);
}

/* Medical Device Responsive Spacing */
@media (min-width: 1920px) {
  /* Medical Workstation Large (1920px+) */
  .medical-workstation\:p-medical-lg { padding: var(--medical-spacing-lg); }
  .medical-workstation\:p-medical-xl { padding: var(--medical-spacing-xl); }
  .medical-workstation\:gap-medical-lg { gap: var(--medical-spacing-lg); }
}

@media (min-width: 1440px) and (max-width: 1919px) {
  /* Medical Workstation Standard (1440px - 1919px) */
  .medical-workstation\:p-medical-base { padding: var(--medical-spacing-base); }
  .medical-workstation\:p-medical-lg { padding: var(--medical-spacing-lg); }
  .medical-workstation\:gap-medical-base { gap: var(--medical-spacing-base); }
}

@media (min-width: 768px) and (max-width: 1439px) {
  /* Medical Tablet (768px - 1439px) */
  .medical-tablet\:p-medical-sm { padding: var(--medical-spacing-sm); }
  .medical-tablet\:p-medical-base { padding: var(--medical-spacing-base); }
  .medical-tablet\:p-medical-md { padding: var(--medical-spacing-lg); }
  .medical-tablet\:gap-medical-sm { gap: var(--medical-spacing-sm); }
  .medical-tablet\:touch-target-large { padding: var(--medical-touch-lg); }
}

@media (max-width: 767px) {
  /* Medical Mobile (< 768px) */
  .medical-mobile\:p-medical-xs { padding: var(--medical-spacing-xs); }
  .medical-mobile\:p-medical-sm { padding: var(--medical-spacing-sm); }
  .medical-mobile\:gap-medical-xs { gap: var(--medical-spacing-xs); }
  .medical-mobile\:gap-medical-tight { gap: var(--medical-spacing-xs); }
}

/* Medical Layout Patterns */
.layout-medical-dashboard {
  padding: var(--medical-dashboard-padding);
  gap: var(--medical-dashboard-gap);
}

.layout-medical-form {
  padding: var(--medical-form-padding);
  gap: var(--medical-form-gap);
}

.layout-medical-card {
  padding: var(--medical-card-padding);
}

/* Medical Grid Layouts */
.grid-medical-dashboard {
  display: grid;
  gap: var(--medical-gap-grid);
}

.grid-medical-form {
  display: grid;
  gap: var(--medical-form-gap);
}

.grid-medical-form-2col {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--medical-form-gap);
}

.grid-cols-medical-dashboard-desktop {
  grid-template-columns: repeat(12, 1fr);
}

/* Medical Safe Areas (for devices with notches/borders) */
.safe-area-medical {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* Medical Consistent Spacing for Complex Layouts */
.medical-consistent-vertical > * + * {
  margin-top: var(--medical-spacing-base);
}

.medical-consistent-form > * + * {
  margin-top: var(--medical-form-gap);
}

.medical-consistent-section > * + * {
  margin-top: var(--medical-section-gap);
}