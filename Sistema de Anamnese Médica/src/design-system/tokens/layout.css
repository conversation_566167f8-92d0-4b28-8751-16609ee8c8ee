/* Medical Layout Tokens - Border Radius, Sizing, and Layout Utilities */

:root {
  /* Medical Border Radius Scale */
  --medical-radius-none: 0px;
  --medical-radius-xs: 2px;
  --medical-radius-sm: 4px;
  --medical-radius-base: 8px;      /* Medical standard */
  --medical-radius-md: 8px;
  --medical-radius-lg: 12px;
  --medical-radius-xl: 16px;
  --medical-radius-2xl: 20px;
  --medical-radius-3xl: 24px;
  --medical-radius-full: 9999px;
  
  /* Medical Component Specific Radius */
  --medical-radius-button: var(--medical-radius-base);     /* 8px */
  --medical-radius-card: var(--medical-radius-lg);         /* 12px */
  --medical-radius-input: var(--medical-radius-sm);        /* 4px */
  --medical-radius-modal: var(--medical-radius-xl);        /* 16px */
  --medical-radius-tooltip: var(--medical-radius-sm);      /* 4px */
  --medical-radius-badge: var(--medical-radius-full);      /* Full rounded */
  --medical-radius-avatar: var(--medical-radius-full);     /* Full rounded */
  
  /* Medical Z-Index Scale */
  --medical-z-auto: auto;
  --medical-z-0: 0;
  --medical-z-10: 10;
  --medical-z-20: 20;
  --medical-z-30: 30;
  --medical-z-40: 40;
  --medical-z-50: 50;
  --medical-z-dropdown: 100;       /* Dropdowns and popovers */
  --medical-z-sticky: 200;         /* Sticky headers */
  --medical-z-fixed: 300;          /* Fixed elements */
  --medical-z-modal-backdrop: 400; /* Modal backdrops */
  --medical-z-modal: 500;          /* Modal content */
  --medical-z-popover: 600;        /* Popovers and tooltips */
  --medical-z-toast: 700;          /* Toast notifications */
  --medical-z-tooltip: 800;        /* Tooltips */
  --medical-z-emergency: 999;      /* Emergency/critical elements */
  
  /* Medical Container Sizes */
  --medical-container-xs: 480px;
  --medical-container-sm: 640px;
  --medical-container-md: 768px;
  --medical-container-lg: 1024px;
  --medical-container-xl: 1280px;
  --medical-container-2xl: 1536px;
  --medical-container-medical: 1440px;  /* Optimal for medical workstations */
  
  /* Medical Breakpoints */
  --medical-breakpoint-mobile: 480px;
  --medical-breakpoint-tablet: 768px;
  --medical-breakpoint-desktop: 1024px;
  --medical-breakpoint-workstation: 1440px;
  --medical-breakpoint-large-display: 1920px;
  
  /* Medical Grid System */
  --medical-grid-cols-1: repeat(1, 1fr);
  --medical-grid-cols-2: repeat(2, 1fr);
  --medical-grid-cols-3: repeat(3, 1fr);
  --medical-grid-cols-4: repeat(4, 1fr);
  --medical-grid-cols-6: repeat(6, 1fr);
  --medical-grid-cols-8: repeat(8, 1fr);
  --medical-grid-cols-12: repeat(12, 1fr);
  
  /* Medical Component Sizes */
  --medical-size-xs: 1rem;      /* 16px */
  --medical-size-sm: 1.5rem;    /* 24px */
  --medical-size-base: 2rem;    /* 32px */
  --medical-size-lg: 2.5rem;    /* 40px */
  --medical-size-xl: 3rem;      /* 48px */
  --medical-size-2xl: 4rem;     /* 64px */
  
  /* Medical Touch Targets */
  --medical-touch-target-min: 44px;    /* WCAG minimum */
  --medical-touch-target-sm: 48px;     /* Comfortable */
  --medical-touch-target-base: 52px;   /* Medical optimal */
  --medical-touch-target-lg: 56px;     /* Large touch areas */
  
  /* Medical Aspect Ratios */
  --medical-aspect-square: 1 / 1;
  --medical-aspect-video: 16 / 9;
  --medical-aspect-photo: 4 / 3;
  --medical-aspect-medical-chart: 3 / 2;
  --medical-aspect-badge: 2 / 1;
}

/* Medical Border Radius Utility Classes */
.rounded-medical-none { border-radius: var(--medical-radius-none); }
.rounded-medical-xs { border-radius: var(--medical-radius-xs); }
.rounded-medical-sm { border-radius: var(--medical-radius-sm); }
.rounded-medical-base { border-radius: var(--medical-radius-base); }
.rounded-medical-md { border-radius: var(--medical-radius-md); }
.rounded-medical-lg { border-radius: var(--medical-radius-lg); }
.rounded-medical-xl { border-radius: var(--medical-radius-xl); }
.rounded-medical-2xl { border-radius: var(--medical-radius-2xl); }
.rounded-medical-3xl { border-radius: var(--medical-radius-3xl); }
.rounded-medical-full { border-radius: var(--medical-radius-full); }

/* Medical Component Specific Radius */
.rounded-medical-button { border-radius: var(--medical-radius-button); }
.rounded-medical-card { border-radius: var(--medical-radius-card); }
.rounded-medical-input { border-radius: var(--medical-radius-input); }
.rounded-medical-modal { border-radius: var(--medical-radius-modal); }
.rounded-medical-tooltip { border-radius: var(--medical-radius-tooltip); }
.rounded-medical-badge { border-radius: var(--medical-radius-badge); }
.rounded-medical-avatar { border-radius: var(--medical-radius-avatar); }

/* Medical Z-Index Utility Classes */
.z-medical-auto { z-index: var(--medical-z-auto); }
.z-medical-0 { z-index: var(--medical-z-0); }
.z-medical-10 { z-index: var(--medical-z-10); }
.z-medical-20 { z-index: var(--medical-z-20); }
.z-medical-30 { z-index: var(--medical-z-30); }
.z-medical-40 { z-index: var(--medical-z-40); }
.z-medical-50 { z-index: var(--medical-z-50); }
.z-medical-dropdown { z-index: var(--medical-z-dropdown); }
.z-medical-sticky { z-index: var(--medical-z-sticky); }
.z-medical-fixed { z-index: var(--medical-z-fixed); }
.z-medical-modal-backdrop { z-index: var(--medical-z-modal-backdrop); }
.z-medical-modal { z-index: var(--medical-z-modal); }
.z-medical-popover { z-index: var(--medical-z-popover); }
.z-medical-toast { z-index: var(--medical-z-toast); }
.z-medical-tooltip { z-index: var(--medical-z-tooltip); }
.z-medical-emergency { z-index: var(--medical-z-emergency); }

/* Medical Grid System Classes */
.grid-cols-medical-1 { grid-template-columns: var(--medical-grid-cols-1); }
.grid-cols-medical-2 { grid-template-columns: var(--medical-grid-cols-2); }
.grid-cols-medical-3 { grid-template-columns: var(--medical-grid-cols-3); }
.grid-cols-medical-4 { grid-template-columns: var(--medical-grid-cols-4); }
.grid-cols-medical-6 { grid-template-columns: var(--medical-grid-cols-6); }
.grid-cols-medical-8 { grid-template-columns: var(--medical-grid-cols-8); }
.grid-cols-medical-12 { grid-template-columns: var(--medical-grid-cols-12); }

/* Medical Size Utility Classes */
.size-medical-xs { width: var(--medical-size-xs); height: var(--medical-size-xs); }
.size-medical-sm { width: var(--medical-size-sm); height: var(--medical-size-sm); }
.size-medical-base { width: var(--medical-size-base); height: var(--medical-size-base); }
.size-medical-lg { width: var(--medical-size-lg); height: var(--medical-size-lg); }
.size-medical-xl { width: var(--medical-size-xl); height: var(--medical-size-xl); }
.size-medical-2xl { width: var(--medical-size-2xl); height: var(--medical-size-2xl); }

/* Medical Width Classes */
.w-medical-xs { width: var(--medical-size-xs); }
.w-medical-sm { width: var(--medical-size-sm); }
.w-medical-base { width: var(--medical-size-base); }
.w-medical-lg { width: var(--medical-size-lg); }
.w-medical-xl { width: var(--medical-size-xl); }
.w-medical-2xl { width: var(--medical-size-2xl); }

/* Medical Height Classes */
.h-medical-xs { height: var(--medical-size-xs); }
.h-medical-sm { height: var(--medical-size-sm); }
.h-medical-base { height: var(--medical-size-base); }
.h-medical-lg { height: var(--medical-size-lg); }
.h-medical-xl { height: var(--medical-size-xl); }
.h-medical-2xl { height: var(--medical-size-2xl); }

/* Medical Touch Target Classes */
.min-h-medical-touch { min-height: var(--medical-touch-target-min); }
.min-w-medical-touch { min-width: var(--medical-touch-target-min); }
.min-size-medical-touch { 
  min-width: var(--medical-touch-target-min); 
  min-height: var(--medical-touch-target-min); 
}

.min-h-medical-touch-base { min-height: var(--medical-touch-target-base); }
.min-w-medical-touch-base { min-width: var(--medical-touch-target-base); }
.min-size-medical-touch-base { 
  min-width: var(--medical-touch-target-base); 
  min-height: var(--medical-touch-target-base); 
}

/* Medical Aspect Ratio Classes */
.aspect-medical-square { aspect-ratio: var(--medical-aspect-square); }
.aspect-medical-video { aspect-ratio: var(--medical-aspect-video); }
.aspect-medical-photo { aspect-ratio: var(--medical-aspect-photo); }
.aspect-medical-chart { aspect-ratio: var(--medical-aspect-medical-chart); }
.aspect-medical-badge { aspect-ratio: var(--medical-aspect-badge); }

/* Medical Container Classes */
.container-medical {
  width: 100%;
  max-width: var(--medical-container-medical);
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--medical-spacing-base);
  padding-right: var(--medical-spacing-base);
}

.container-medical-xs { max-width: var(--medical-container-xs); }
.container-medical-sm { max-width: var(--medical-container-sm); }
.container-medical-md { max-width: var(--medical-container-md); }
.container-medical-lg { max-width: var(--medical-container-lg); }
.container-medical-xl { max-width: var(--medical-container-xl); }
.container-medical-2xl { max-width: var(--medical-container-2xl); }

/* Medical Layout Patterns */
.layout-medical-stack {
  display: flex;
  flex-direction: column;
  gap: var(--medical-spacing-base);
}

.layout-medical-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout-medical-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.layout-medical-grid-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--medical-gap-grid);
}

.layout-medical-sidebar {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--medical-spacing-xl);
}

.layout-medical-split {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--medical-spacing-lg);
}

/* Medical Responsive Layout Classes */
@media (min-width: 1920px) {
  /* Large Medical Display (1920px+) */
  .medical-large-display\:container-full { max-width: 100%; }
  .medical-large-display\:grid-cols-12 { grid-template-columns: var(--medical-grid-cols-12); }
  .medical-large-display\:px-medical-xl { 
    padding-left: var(--medical-spacing-xl); 
    padding-right: var(--medical-spacing-xl); 
  }
}

@media (min-width: 1440px) and (max-width: 1919px) {
  /* Medical Workstation (1440px - 1919px) */
  .medical-workstation\:container-medical { max-width: var(--medical-container-medical); }
  .medical-workstation\:grid-cols-12 { grid-template-columns: var(--medical-grid-cols-12); }
  .medical-workstation\:gap-medical-lg { gap: var(--medical-spacing-lg); }
}

@media (min-width: 768px) and (max-width: 1439px) {
  /* Medical Tablet (768px - 1439px) */
  .medical-tablet\:grid-cols-6 { grid-template-columns: var(--medical-grid-cols-6); }
  .medical-tablet\:grid-cols-2 { grid-template-columns: var(--medical-grid-cols-2); }
  .medical-tablet\:px-medical-base { 
    padding-left: var(--medical-spacing-base); 
    padding-right: var(--medical-spacing-base); 
  }
  .medical-tablet\:layout-stack { 
    display: flex; 
    flex-direction: column; 
    gap: var(--medical-spacing-base); 
  }
}

@media (max-width: 767px) {
  /* Medical Mobile (< 768px) */
  .medical-mobile\:grid-cols-1 { grid-template-columns: var(--medical-grid-cols-1); }
  .medical-mobile\:px-medical-sm { 
    padding-left: var(--medical-spacing-sm); 
    padding-right: var(--medical-spacing-sm); 
  }
  .medical-mobile\:layout-stack { 
    display: flex; 
    flex-direction: column; 
    gap: var(--medical-spacing-sm); 
  }
  .medical-mobile\:full-width { width: 100%; }
}

/* Medical Safe Area Support */
.safe-area-inset-medical {
  padding-left: max(var(--medical-spacing-base), env(safe-area-inset-left));
  padding-right: max(var(--medical-spacing-base), env(safe-area-inset-right));
  padding-top: max(var(--medical-spacing-base), env(safe-area-inset-top));
  padding-bottom: max(var(--medical-spacing-base), env(safe-area-inset-bottom));
}

/* Medical Overflow Management */
.overflow-medical-hidden { overflow: hidden; }
.overflow-medical-scroll { overflow: auto; }
.overflow-medical-x-scroll { overflow-x: auto; overflow-y: hidden; }
.overflow-medical-y-scroll { overflow-x: hidden; overflow-y: auto; }

/* Medical Scrollbar Styling */
.scrollbar-medical-thin::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-medical-thin::-webkit-scrollbar-track {
  background: var(--medical-bg-secondary);
  border-radius: var(--medical-radius-base);
}

.scrollbar-medical-thin::-webkit-scrollbar-thumb {
  background: var(--medical-border-strong);
  border-radius: var(--medical-radius-base);
}

.scrollbar-medical-thin::-webkit-scrollbar-thumb:hover {
  background: var(--medical-color-primary);
}

/* Medical Position Utilities */
.position-medical-sticky {
  position: sticky;
  top: var(--medical-spacing-base);
  z-index: var(--medical-z-sticky);
}

.position-medical-fixed {
  position: fixed;
  z-index: var(--medical-z-fixed);
}

.position-medical-modal {
  position: fixed;
  inset: 0;
  z-index: var(--medical-z-modal);
}

/* Medical Display Utilities */
.display-medical-flex { display: flex; }
.display-medical-grid { display: grid; }
.display-medical-block { display: block; }
.display-medical-inline-block { display: inline-block; }
.display-medical-none { display: none; }

/* Medical Visibility Utilities */
.visible-medical { visibility: visible; }
.invisible-medical { visibility: hidden; }
.sr-only-medical {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}