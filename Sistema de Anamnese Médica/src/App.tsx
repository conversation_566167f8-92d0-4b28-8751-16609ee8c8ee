import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "./components/ui/button";
import { Badge } from "./components/ui/badge";
import {
  Moon,
  Sun,
  UserRound,
  Bot,
  Calendar,
  FileText,
  Pill,
  Activity,
  Stethoscope,
  Menu,
  X,
  BarChart3,
} from "lucide-react";
import { motion, AnimatePresence } from "motion/react";
import { ChatMedico } from "./components/ChatMedico";
import { KanbanPlantao } from "./components/KanbanPlantao";
import { PrescricaoMedica } from "./components/PrescricaoMedica";
import { MedicamentosSUS } from "./components/MedicamentosSUS";
import { ExamesSADT } from "./components/ExamesSADT";
import { AnamneseRapida } from "./components/AnamneseRapida";
import { PremiumDashboard } from "./components/premium/PremiumDashboard";
import { MinimalDashboard } from "./components/premium/MinimalDashboard";
import {
  MedicalProvider,
  useMedical,
} from "./components/MedicalContext";
import { OpenAISidebar } from "./components/smart-sidebar/OpenAISidebar";
import { MinimalSidebar } from "./components/smart-sidebar/MinimalSidebar";
import { GlobalSettings } from "./components/GlobalSettings";
import { ToastProvider } from "./components/Toast";
import wellwaveLogo from "figma:asset/0861b8c218cc0038134a72897c76dab4ae1a5c19.png";

// Safe imports with error handling
let Toast: any = null;
let initWellWaveOpenAI: any = null;

try {
  const toastModule = require("./utils/openai-animations");
  Toast = toastModule.Toast;
} catch (error) {
  console.warn("WellWave: Toast system not available:", error);
  // Fallback toast system
  Toast = {
    show: (
      message: string,
      type: string = "info",
      duration: number = 3000,
    ) => {
      console.log(`Toast [${type}]: ${message}`);
      return { close: () => {} };
    },
    success: (message: string) =>
      Toast.show(message, "success"),
    error: (message: string) => Toast.show(message, "error"),
    warning: (message: string) =>
      Toast.show(message, "warning"),
    info: (message: string) => Toast.show(message, "info"),
  };
}

try {
  initWellWaveOpenAI =
    require("./utils/init-openai-effects").default;
} catch (error) {
  console.warn(
    "WellWave: OpenAI effects not available:",
    error,
  );
  initWellWaveOpenAI = () => ({ cleanup: () => {} });
}

type ActiveModule =
  | "dashboard"
  | "anamnese"
  | "chat"
  | "kanban"
  | "prescricao"
  | "medicamentos"
  | "exames";

function AppContent() {
  const [darkMode, setDarkMode] = useState(false);
  const [activeModule, setActiveModule] =
    useState<ActiveModule>("dashboard");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [showGlobalSettings, setShowGlobalSettings] =
    useState(false);
  const { medicalColors, resetAnamnese } = useMedical();

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
      // Auto-collapse sidebar on mobile
      if (window.innerWidth < 1024) {
        setSidebarExpanded(false);
      } else {
        // Restore sidebar state on desktop
        const savedSidebarState = localStorage.getItem(
          "wellwave-sidebar-expanded",
        );
        setSidebarExpanded(savedSidebarState !== "false");
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () =>
      window.removeEventListener("resize", checkMobile);
  }, []);

  // Initialize OpenAI effects on mount with error handling
  useEffect(() => {
    let cleanup: (() => void) | null = null;

    try {
      if (initWellWaveOpenAI) {
        const effects = initWellWaveOpenAI();
        cleanup = effects?.cleanup;
      }
    } catch (error) {
      console.warn(
        "WellWave: Error initializing effects:",
        error,
      );
    }

    return () => {
      try {
        if (cleanup && typeof cleanup === "function") {
          cleanup();
        }
      } catch (error) {
        console.warn("WellWave: Error during cleanup:", error);
      }
    };
  }, []);

  // Initialize theme on mount
  useEffect(() => {
    try {
      const savedTheme = localStorage.getItem("wellwave-theme");
      if (savedTheme === "dark") {
        setDarkMode(true);
        document.documentElement.classList.add("dark");
        document.documentElement.setAttribute(
          "data-theme",
          "dark",
        );
      } else {
        setDarkMode(false);
        document.documentElement.classList.remove("dark");
        document.documentElement.setAttribute(
          "data-theme",
          "light",
        );
      }
    } catch (error) {
      console.warn(
        "WellWave: Error initializing theme:",
        error,
      );
    }
  }, []);

  // Generate sample timestamps for demonstration
  const generateSampleTimestamp = (
    daysAgo: number = 0,
    hoursAgo: number = 0,
  ) => {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    date.setHours(date.getHours() - hoursAgo);
    return date;
  };

  const sidebarModules = [
    {
      id: "dashboard",
      label: "Dashboard Premium",
      icon: BarChart3,
      description: "Visão geral médica",
      color: medicalColors.primary,
      timestamp: generateSampleTimestamp(0, 0), // Now
    },
    {
      id: "anamnese",
      label: "Anamnese Rápida",
      icon: Stethoscope,
      description: "Sistema estruturado",
      color: medicalColors.secondary,
      timestamp: generateSampleTimestamp(0, 1), // 1 hour ago
    },
    {
      id: "chat",
      label: "Assistente IA",
      icon: Bot,
      description: "Chat médico inteligente",
      color: medicalColors.cardiology,
      timestamp: generateSampleTimestamp(0, 3), // 3 hours ago
    },
    {
      id: "kanban",
      label: "Plantão Médico",
      icon: Calendar,
      description: "Gestão de pacientes",
      color: medicalColors.warning,
      timestamp: generateSampleTimestamp(1, 2), // Yesterday
    },
    {
      id: "prescricao",
      label: "Prescrições",
      icon: FileText,
      description: "Receituários digitais",
      color: medicalColors.success,
      timestamp: generateSampleTimestamp(1, 5), // Yesterday
    },
    {
      id: "medicamentos",
      label: "Base Medicamentos",
      icon: Pill,
      description: "Farmacologia SUS",
      color: medicalColors.gastro,
      timestamp: generateSampleTimestamp(3, 0), // 3 days ago
    },
    {
      id: "exames",
      label: "Solicitações",
      icon: Activity,
      description: "Exames e SADT",
      color: medicalColors.cardiology,
      timestamp: generateSampleTimestamp(5, 0), // 5 days ago
    },
  ];

  const toggleTheme = () => {
    try {
      const newDarkMode = !darkMode;
      setDarkMode(newDarkMode);

      // Apply theme to document
      if (newDarkMode) {
        document.documentElement.classList.add("dark");
        document.documentElement.setAttribute(
          "data-theme",
          "dark",
        );
        localStorage.setItem("wellwave-theme", "dark");
      } else {
        document.documentElement.classList.remove("dark");
        document.documentElement.setAttribute(
          "data-theme",
          "light",
        );
        localStorage.setItem("wellwave-theme", "light");
      }

      // Show toast notification
      if (Toast) {
        Toast.show(
          newDarkMode
            ? "🌙 Modo escuro ativado"
            : "☀️ Modo claro ativado",
          "info",
          2000,
        );
      }
    } catch (error) {
      console.error("Error in toggleTheme:", error);
      if (Toast) {
        Toast.show("❌ Erro ao alterar tema", "error");
      }
    }
  };

  const handleSidebarToggle = (expanded: boolean) => {
    setSidebarExpanded(expanded);
    // Save sidebar state to localStorage
    try {
      localStorage.setItem(
        "wellwave-sidebar-expanded",
        expanded.toString(),
      );
      if (Toast) {
        Toast.show(
          expanded
            ? "📖 Sidebar expandida"
            : "📝 Sidebar retraída",
          "info",
          1500,
        );
      }
    } catch (error) {
      console.warn("Error saving sidebar state:", error);
    }
  };

  const handleNewAnamnese = () => {
    try {
      resetAnamnese();
      setActiveModule("anamnese");
      setMobileMenuOpen(false);
      if (Toast) {
        Toast.show("📋 Nova anamnese iniciada", "success");
      }
    } catch (error) {
      console.error("Error in handleNewAnamnese:", error);
      if (Toast) {
        Toast.show("❌ Erro ao iniciar nova anamnese", "error");
      }
    }
  };

  const handleModuleChange = (moduleId: string) => {
    try {
      setActiveModule(moduleId as ActiveModule);
      setMobileMenuOpen(false);

      // Find module info for toast
      const module = sidebarModules.find(
        (m) => m.id === moduleId,
      );
      if (module && Toast) {
        Toast.show(
          `🌊 Navegando para ${module.label}`,
          "info",
          1500,
        );
      }
    } catch (error) {
      console.error("Error in handleModuleChange:", error);
    }
  };

  const renderContent = () => {
    const contentVariants = {
      hidden: {
        opacity: 0,
        y: 10,
        scale: 0.98,
      },
      visible: {
        opacity: 1,
        y: 0,
        scale: 1,
      },
      exit: {
        opacity: 0,
        y: -10,
        scale: 0.98,
      },
    };

    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={activeModule}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{
            duration: 0.2,
            ease: [0.4, 0, 0.2, 1], // OpenAI easing
          }}
          className="h-full openai-fade-in"
          data-reveal
        >
          {(() => {
            try {
              switch (activeModule) {
                case "dashboard":
                  return (
                    <MinimalDashboard
                      darkMode={darkMode}
                      onToggleTheme={toggleTheme}
                      onModuleChange={handleModuleChange}
                      onOpenSettings={() =>
                        setShowGlobalSettings(true)
                      }
                    />
                  );
                case "chat":
                  return <ChatMedico />;
                case "kanban":
                  return <KanbanPlantao />;
                case "prescricao":
                  return <PrescricaoMedica />;
                case "medicamentos":
                  return <MedicamentosSUS />;
                case "exames":
                  return <ExamesSADT />;
                case "anamnese":
                default:
                  return <AnamneseRapida />;
              }
            } catch (error) {
              console.error("Error rendering content:", error);
              return (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-6xl mb-4">🏥</div>
                    <h2 className="text-xl font-semibold mb-2">
                      Módulo Indisponível
                    </h2>
                    <p className="text-muted-foreground mb-4">
                      Ocorreu um erro ao carregar este módulo.
                    </p>
                    <Button
                      onClick={() =>
                        setActiveModule("dashboard")
                      }
                      className="bg-gradient-wave text-white"
                    >
                      Voltar ao Dashboard
                    </Button>
                  </div>
                </div>
              );
            }
          })()}
        </motion.div>
      </AnimatePresence>
    );
  };

  // Calculate dynamic styles based on sidebar state
  const sidebarWidth = sidebarExpanded ? 280 : 80;
  const contentMargin = isMobile ? 0 : sidebarWidth;

  return (
    <div className={`app-container ${darkMode ? "dark" : ""}`}>
      <div className="flex min-h-svh-fix relative">
        {/* Mobile Menu Button */}
        {isMobile && (
          <div className="fixed top-4 left-4 z-50">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setMobileMenuOpen(!mobileMenuOpen)
                }
                className="openai-button bg-background/80 backdrop-blur-md border shadow-wave-md"
                data-no-effects
                style={{
                  borderColor: medicalColors.primary + "40",
                  backgroundColor: darkMode
                    ? medicalColors.primary + "20"
                    : "#FFFFFF",
                }}
              >
                {mobileMenuOpen ? (
                  <X
                    className="h-4 w-4"
                    style={{ color: medicalColors.primary }}
                  />
                ) : (
                  <Menu
                    className="h-4 w-4"
                    style={{ color: medicalColors.primary }}
                  />
                )}
              </Button>
            </motion.div>
          </div>
        )}

        {/* Mobile Overlay */}
        <AnimatePresence>
          {mobileMenuOpen && isMobile && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40 openai-sidebar-overlay"
              onClick={() => setMobileMenuOpen(false)}
            />
          )}
        </AnimatePresence>

        {/* WellWave OpenAI Sidebar */}
        <motion.div
          className={`
            ${isMobile ? "fixed" : "relative"} inset-y-0 left-0 z-50 
            ${isMobile ? (mobileMenuOpen ? "translate-x-0" : "-translate-x-full") : "translate-x-0"}
            openai-sidebar
          `}
          animate={{
            width: isMobile
              ? mobileMenuOpen
                ? 280
                : 0
              : sidebarWidth,
          }}
          transition={{
            duration: 0.3,
            ease: [0.4, 0, 0.2, 1],
          }}
          style={{
            transform: isMobile ? undefined : "none",
          }}
        >
          <MinimalSidebar
            modules={sidebarModules}
            activeModule={activeModule}
            onModuleChange={handleModuleChange}
            onNewAction={handleNewAnamnese}
            newActionLabel="Nova Anamnese"
            darkMode={darkMode}
            onToggleTheme={toggleTheme}
            userName="Dr. João Silva"
            userEmail="<EMAIL>"
            expanded={sidebarExpanded}
            onToggle={handleSidebarToggle}
            isMobile={isMobile}
            isOpen={mobileMenuOpen}
            onClose={() => setMobileMenuOpen(false)}
          />
        </motion.div>

        {/* Main Content Area with Dynamic Margin */}
        <motion.div
          className="flex-1 flex flex-col overflow-hidden"
          animate={{
            marginLeft: isMobile ? 0 : 0, // No margin needed as sidebar is relative
            width: isMobile
              ? "100%"
              : `calc(100% - ${sidebarWidth}px)`,
          }}
          transition={{
            duration: 0.3,
            ease: [0.4, 0, 0.2, 1],
          }}
          style={{
            marginLeft: 0, // Override since sidebar is relative positioned
          }}
        >
          {/* Global App Header */}
          {
            (
            <motion.header
              className="h-16 border-b backdrop-blur-md px-4 md:px-6 flex items-center justify-between shadow-wave-sm relative z-10 safe-top"
              style={{
                background: darkMode
                  ? "rgba(26, 35, 50, 0.95)"
                  : "rgba(255, 255, 255, 0.95)",
                borderColor: medicalColors.primary + "20",
              }}
              initial={{ y: -10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{
                duration: 0.3,
                ease: [0.4, 0, 0.2, 1],
              }}
            >
              <motion.div
                className="flex items-center gap-4"
                initial={{ x: -10, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{
                  delay: 0.1,
                  ease: [0.4, 0, 0.2, 1],
                }}
              >
                {/* WellWave Logo Section com Logo Oficial */}
                <div className="hidden lg:flex items-center gap-3">
                  <motion.div
                    className="wave-motion p-2 rounded-xl"
                    style={{
                      background: medicalColors.gradients.soft,
                      boxShadow: medicalColors.shadows.glow,
                    }}
                    animate={{
                      rotate: [0, 2, -2, 0],
                      scale: [1, 1.02, 1],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <motion.img
                      src={wellwaveLogo}
                      alt="WellWave Logo"
                      className="h-5 w-5 object-contain"
                      style={{
                        filter:
                          "drop-shadow(0 0 4px rgba(255,255,255,0.5))",
                        borderRadius: "2px",
                      }}
                      animate={{
                        scale: [1, 1.05, 1],
                        rotate: [0, 1, -1, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                  </motion.div>
                  <div>
                    <div
                      className="font-bold text-lg"
                      style={{
                        background:
                          medicalColors.gradients.primary,
                        WebkitBackgroundClip: "text",
                        WebkitTextFillColor: "transparent",
                        backgroundClip: "text",
                      }}
                    >
                      WellWave
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date().toLocaleDateString("pt-BR", {
                        weekday: "short",
                        month: "short",
                        day: "numeric",
                      })}
                    </div>
                  </div>
                </div>

                {/* Mobile: Show active module */}
                <div className="lg:hidden">
                  <div className="flex items-center gap-2">
                    {(() => {
                      const currentModule = sidebarModules.find(
                        (m) => m.id === activeModule,
                      );
                      if (!currentModule) return null;
                      const Icon = currentModule.icon;
                      return (
                        <>
                          <motion.div
                            className="p-1.5 rounded-lg"
                            style={{
                              backgroundColor:
                                currentModule.color + "20",
                            }}
                            whileHover={{ scale: 1.05 }}
                          >
                            <Icon
                              className="h-4 w-4"
                              style={{
                                color: currentModule.color,
                              }}
                            />
                          </motion.div>
                          <div>
                            <div className="font-medium text-sm">
                              {currentModule.label}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {currentModule.description}
                            </div>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="flex items-center gap-3"
                initial={{ x: 10, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{
                  delay: 0.2,
                  ease: [0.4, 0, 0.2, 1],
                }}
              >
                {/* Sidebar Toggle for Desktop */}
                {!isMobile && (
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() =>
                        handleSidebarToggle(!sidebarExpanded)
                      }
                      className="h-9 w-9 openai-button transition-wave hover:shadow-wave-md"
                      title={
                        sidebarExpanded
                          ? "Retrair sidebar"
                          : "Expandir sidebar"
                      }
                      style={{
                        backgroundColor: darkMode
                          ? medicalColors.primary + "20"
                          : medicalColors.accent,
                        color: medicalColors.primary,
                      }}
                    >
                      <motion.div
                        animate={{
                          rotate: sidebarExpanded ? 0 : 180,
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <Menu className="h-4 w-4" />
                      </motion.div>
                    </Button>
                  </motion.div>
                )}

                {/* Theme Toggle */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleTheme}
                    className="h-9 w-9 openai-button transition-wave hover:shadow-wave-md"
                    data-no-effects
                    style={{
                      backgroundColor: darkMode
                        ? medicalColors.primary + "20"
                        : medicalColors.accent,
                      color: medicalColors.primary,
                    }}
                  >
                    <motion.div
                      animate={{ rotate: darkMode ? 180 : 0 }}
                      transition={{
                        duration: 0.5,
                        ease: [0.4, 0, 0.2, 1],
                      }}
                    >
                      {darkMode ? (
                        <Sun className="h-4 w-4" />
                      ) : (
                        <Moon className="h-4 w-4" />
                      )}
                    </motion.div>
                  </Button>
                </motion.div>

                {/* User Profile Badge WellWave com Logo */}
                <motion.div
                  className="hidden lg:flex items-center gap-3 px-4 py-2 rounded-xl shadow-wave-sm border openai-card"
                  style={{
                    background: medicalColors.gradients.soft,
                    borderColor: medicalColors.primary + "30",
                    backdropFilter: "blur(20px)",
                  }}
                  whileHover={{
                    scale: 1.02,
                    boxShadow: medicalColors.shadows.md,
                  }}
                  transition={{ duration: 0.15 }}
                  data-card
                >
                  <motion.div
                    className="p-2 rounded-full wave-pulse overflow-hidden"
                    style={{
                      background:
                        medicalColors.gradients.primary,
                      boxShadow: medicalColors.shadows.glow,
                    }}
                    whileHover={{ scale: 1.1 }}
                  >
                    <motion.img
                      src={wellwaveLogo}
                      alt="WellWave Profile Logo"
                      className="h-4 w-4 object-contain"
                      style={{
                        filter: "brightness(0) invert(1)",
                      }}
                    />
                  </motion.div>
                  <div className="flex flex-col">
                    <span
                      className="text-sm font-semibold"
                      style={{
                        color: medicalColors.primaryDark,
                      }}
                    >
                      Dr. João Silva
                    </span>
                    <span className="text-xs text-muted-foreground">
                      CRM 123456-SP
                    </span>
                  </div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    animate={{
                      boxShadow: [
                        medicalColors.shadows.md,
                        medicalColors.shadows.glow,
                        medicalColors.shadows.md,
                      ],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <Badge
                      className="text-xs text-white font-semibold"
                      style={{
                        background:
                          medicalColors.gradients.primary,
                        border: "none",
                        boxShadow: medicalColors.shadows.sm,
                      }}
                    >
                      🌊 PREMIUM
                    </Badge>
                  </motion.div>
                </motion.div>
              </motion.div>
            </motion.header>
          )}

          {/* Content Area with WellWave Styling - Medical Layout Enhanced */}
          <motion.div
            className="flex-1 relative bg-white dark:bg-gray-900 medical-container-wide content-gutters"
            layout
            transition={{
              duration: 0.3,
              ease: [0.4, 0, 0.2, 1],
            }}
            style={{
              minHeight: 'calc(var(--app-min-h) - 80px)',
              height: '100%',
            }}
          >
            <div className="h-full w-full">
              {/* Constrain text-heavy modules for readability */}
              {(() => {
                const constrain = [
                  'anamnese',
                  'prescricao',
                  'medicamentos',
                  'exames',
                  'chat',
                ].includes(activeModule);
                return (
                  <div className={constrain ? 'content-max' : ''}>
                    {renderContent()}
                  </div>
                );
              })()}
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Global Settings Modal */}
      <GlobalSettings
        isOpen={showGlobalSettings}
        onClose={() => setShowGlobalSettings(false)}
        darkMode={darkMode}
        onToggleTheme={toggleTheme}
      />
    </div>
  );
}

export default function App() {
  return (
    <MedicalProvider>
      <ToastProvider>
        <AppContent />
      </ToastProvider>
    </MedicalProvider>
  );
}
