export interface StatCard {
  title: string;
  value: string | number;
  change: string;
  icon: string;
  color: string;
  gradient: string;
  trend: 'up' | 'down' | 'neutral';
  category: string;
  sparklineData?: number[];
}

export interface Patient {
  id: string;
  name: string;
  age: number;
  priority: 'baixa' | 'media' | 'alta' | 'emergencia';
  complaint: string;
  arrivalTime: string;
  estimatedTime: string;
  status: 'aguardando' | 'chamado' | 'atendimento';
  avatar?: string;
}

export interface Activity {
  id: string;
  type: 'consulta' | 'exame' | 'prescricao' | 'emergencia';
  patient: string;
  action: string;
  time: string;
  priority: 'baixa' | 'media' | 'alta' | 'emergencia';
}

// Stats cards data
export const DASHBOARD_STATS: StatCard[] = [
  {
    title: 'Pacientes Hoje',
    value: 47,
    change: '+12%',
    icon: 'patients',
    color: 'var(--medical-primary)',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    category: 'navigation',
    sparklineData: [32, 35, 41, 43, 47, 45, 47]
  },
  {
    title: 'Tempo Médio',
    value: '22min',
    change: '-5min',
    icon: 'measure',
    color: 'var(--medical-secondary)',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'down',
    category: 'actions',
    sparklineData: [28, 26, 24, 25, 22, 23, 22]
  },
  {
    title: 'Taxa Resolutividade',
    value: '94%',
    change: '+2%',
    icon: 'approve',
    color: 'var(--medical-success)',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    category: 'actions',
    sparklineData: [88, 90, 92, 91, 93, 94, 94]
  },
  {
    title: 'Emergências',
    value: 3,
    change: '0',
    icon: 'emergency',
    color: 'var(--medical-danger)',
    gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    trend: 'neutral',
    category: 'specialties',
    sparklineData: [2, 4, 3, 1, 3, 2, 3]
  }
];

// Sample patient queue data
export const PATIENT_QUEUE: Patient[] = [
  {
    id: '1',
    name: 'Maria Silva',
    age: 45,
    priority: 'emergencia',
    complaint: 'Dor torácica intensa',
    arrivalTime: '14:30',
    estimatedTime: '5min',
    status: 'chamado'
  },
  {
    id: '2',
    name: 'João Santos',
    age: 67,
    priority: 'alta',
    complaint: 'Dispneia aos esforços',
    arrivalTime: '15:15',
    estimatedTime: '15min',
    status: 'aguardando'
  },
  {
    id: '3',
    name: 'Ana Costa',
    age: 32,
    priority: 'media',
    complaint: 'Cefaleia persistente',
    arrivalTime: '15:45',
    estimatedTime: '25min',
    status: 'aguardando'
  },
  {
    id: '4',
    name: 'Pedro Oliveira',
    age: 8,
    priority: 'alta',
    complaint: 'Febre alta e convulsão',
    arrivalTime: '16:00',
    estimatedTime: '10min',
    status: 'atendimento'
  },
  {
    id: '5',
    name: 'Carla Mendes',
    age: 28,
    priority: 'baixa',
    complaint: 'Consulta de rotina',
    arrivalTime: '16:30',
    estimatedTime: '45min',
    status: 'aguardando'
  }
];

// Recent activities
export const RECENT_ACTIVITIES: Activity[] = [
  {
    id: '1',
    type: 'emergencia',
    patient: 'Maria Silva',
    action: 'Atendimento iniciado - Dor torácica',
    time: '2min atrás',
    priority: 'emergencia'
  },
  {
    id: '2',
    type: 'prescricao',
    patient: 'João Santos',
    action: 'Prescrição emitida - Broncodilatador',
    time: '15min atrás',
    priority: 'alta'
  },
  {
    id: '3',
    type: 'exame',
    patient: 'Ana Costa',
    action: 'Exame solicitado - TC Crânio',
    time: '32min atrás',
    priority: 'media'
  },
  {
    id: '4',
    type: 'consulta',
    patient: 'Pedro Oliveira',
    action: 'Consulta pediátrica finalizada',
    time: '1h atrás',
    priority: 'alta'
  }
];

// Priority colors mapping
export const PRIORITY_COLORS = {
  'baixa': {
    bg: 'rgba(0, 200, 150, 0.1)',
    border: 'var(--medical-success)',
    text: 'var(--medical-success)'
  },
  'media': {
    bg: 'rgba(255, 184, 0, 0.1)',
    border: 'var(--medical-warning)',
    text: 'var(--medical-warning)'
  },
  'alta': {
    bg: 'rgba(255, 90, 95, 0.1)',
    border: 'var(--medical-danger)',
    text: 'var(--medical-danger)'
  },
  'emergencia': {
    bg: 'rgba(255, 0, 0, 0.15)',
    border: '#FF0000',
    text: '#FF0000'
  }
};

// Status colors mapping
export const STATUS_COLORS = {
  'aguardando': {
    bg: 'rgba(139, 127, 255, 0.1)',
    border: 'var(--medical-info)',
    text: 'var(--medical-info)'
  },
  'chamado': {
    bg: 'rgba(255, 184, 0, 0.1)',
    border: 'var(--medical-warning)',
    text: 'var(--medical-warning)'
  },
  'atendimento': {
    bg: 'rgba(0, 200, 150, 0.1)',
    border: 'var(--medical-success)',
    text: 'var(--medical-success)'
  }
};

// Filter options for dashboard
export const DASHBOARD_FILTERS = [
  { value: 'todos', label: 'Todos' },
  { value: 'emergencia', label: 'Emergência' },
  { value: 'alta', label: 'Alta Prioridade' },
  { value: 'ambulatorio', label: 'Ambulatório' },
  { value: 'pediatria', label: 'Pediatria' },
  { value: 'adulto', label: 'Adulto' },
  { value: 'idoso', label: 'Idoso' }
];

// Time periods for stats
export const TIME_PERIODS = [
  { value: 'hoje', label: 'Hoje' },
  { value: 'semana', label: 'Esta Semana' },
  { value: 'mes', label: 'Este Mês' },
  { value: 'trimestre', label: '3 Meses' }
];

// Chart configurations
export const CHART_CONFIG = {
  colors: {
    primary: 'var(--medical-primary)',
    secondary: 'var(--medical-secondary)', 
    success: 'var(--medical-success)',
    warning: 'var(--medical-warning)',
    danger: 'var(--medical-danger)',
    info: 'var(--medical-info)'
  },
  gradients: {
    primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    success: 'linear-gradient(135deg, #56CCF2 0%, #2F80ED 100%)',
    warning: 'linear-gradient(135deg, #F2C94C 0%, #F2994A 100%)',
    danger: 'linear-gradient(135deg, #EB5757 0%, #9B51E0 100%)'
  }
};

// Activity type icons
export const ACTIVITY_TYPE_ICONS = {
  'consulta': 'examine',
  'exame': 'tests',
  'prescricao': 'prescribe',
  'emergencia': 'emergency'
};

export default {
  DASHBOARD_STATS,
  PATIENT_QUEUE,
  RECENT_ACTIVITIES,
  PRIORITY_COLORS,
  STATUS_COLORS,
  DASHBOARD_FILTERS,
  TIME_PERIODS,
  CHART_CONFIG,
  ACTIVITY_TYPE_ICONS
};