import { 
  // Ícones médicos específicos do Lucide
  Stethoscope,     // Estetoscópio
  Heart,           // Coração/Cardiologia
  Brain,           // Neurologia
  Pill,            // Medicamentos
  Syringe,         // Vacinas/Injeções
  Activity,        // Sinais vitais
  Thermometer,     // Temperatura
  FileText,        // Prontu<PERSON><PERSON>
  User<PERSON>heck,       // Paciente examinado
  Calendar,        // Agendamento
  AlertCircle,     // Alerta médico
  Shield,          // Proteção/Segurança
  Microscope,      // Laboratório
  TestTube,        // Exames
  Clipboard,       // Anamnese
  Users,           // Equipe médica
  Building,        // Hospital/Clínica
  Clock,           // Tempo/Urgência
  CheckCircle,     // Diagnóstico confirmado
  Info,            // Informações médicas
  Zap,             // Emergência
  Eye,             // Oftalmologia
  Ear,             // Otorrinolaringologia
  Baby,            // Pediatria
  UserX,           // Paciente crítico
  Ambulance,       // Ambulância/Emergência
  Bandage,         // Ferimentos
  Bone,            // Ortopedia
  Lungs,           // Pneumologia
  Droplet,         // Fluidos/Sangue
  Monitor,         // Monitoramento
  Tablet,          // Medicação tablet
  FlaskConical,    // Laboratório/Química
  Scan,            // Exames de imagem
  Phone,           // Contato/Telemedicina
  MessageSquare,   // Chat/Comunicação
  Target,          // Diagnóstico preciso
  TrendingUp,      // Melhora/Progresso
  TrendingDown,    // Piora/Declínio
  Minus,           // Estável
  Plus,            // Adicionar
  X,               // Remover/Negativo
  Check,           // Positivo/Confirmado
  AlertTriangle,   // Atenção/Cuidado
  Star,            // Favorito/Importante
  Bookmark,        // Marcador
  Search,          // Buscar/Investigar
  Download,        // Baixar/Exportar
  Upload,          // Enviar/Importar
  Copy,            // Copiar
  Edit,            // Editar
  Save,            // Salvar
  Printer,         // Imprimir
  Mail,            // Email
  Globe,           // Online/Conectado
  Wifi,            // Conectividade
  Database,        // Banco de dados
  Server,          // Servidor
  Lock,            // Segurança/Privacidade
  Unlock,          // Acesso liberado
  Key,             // Chave/Autenticação
  Settings,        // Configurações
  HelpCircle,      // Ajuda/Dúvidas
  Home,            // Home/Principal
  ArrowRight,      // Próximo/Avançar
  ArrowLeft,       // Anterior/Voltar
  ArrowUp,         // Subir/Melhorar
  ArrowDown,       // Descer/Piorar
  ChevronRight,    // Expandir/Próximo
  ChevronLeft,     // Recolher/Anterior
  ChevronUp,       // Expandir para cima
  ChevronDown,     // Expandir para baixo
  MoreHorizontal,  // Mais opções
  MoreVertical,    // Menu vertical
  Filter,          // Filtrar
  Sort,            // Ordenar
  BarChart,        // Gráficos/Estatísticas
  PieChart,        // Gráfico pizza
  LineChart,       // Gráfico linha
  Map,             // Localização
  MapPin,          // Pin de localização
  Navigation,      // Navegação
  Compass,         // Orientação
  Flag,            // Marcador/Objetivo
  Bookmark as BookmarkIcon, // Alias para evitar conflito
  Tag,             // Etiqueta/Tag
  Hash,            // ID/Código
  Type,            // Texto/Digitação
  Image,           // Imagem
  Video,           // Vídeo
  Mic,             // Microfone/Áudio
  MicOff,          // Microfone desligado
  Volume2,         // Som ligado
  VolumeX,         // Som desligado
  Camera,          // Câmera/Foto
  CameraOff,       // Câmera desligada
  Monitor as MonitorIcon, // Monitor/Tela
  Smartphone,      // Celular/Mobile
  Laptop,          // Laptop/Computador
  Tablet as TabletIcon, // Tablet/iPad
  Watch,           // Relógio/Tempo
  Timer,           // Cronômetro
  Stopwatch,       // Cronômetro preciso
  PlayCircle,      // Play/Iniciar
  PauseCircle,     // Pausar
  StopCircle,      // Parar
  RefreshCw,       // Atualizar/Sincronizar
  RotateCcw,       // Desfazer
  RotateCw,        // Refazer
  Repeat,          // Repetir
  Shuffle,         // Embaralhar
  SkipBack,        // Anterior
  SkipForward,     // Próximo
  FastForward,     // Avançar rápido
  Rewind,          // Retroceder
  Power,           // Ligar/Desligar
  LogIn,           // Login/Entrar
  LogOut,          // Logout/Sair
  UserPlus,        // Adicionar usuário
  UserMinus,       // Remover usuário
  UserCog,         // Configurar usuário
  Crown,           // Premium/VIP
  Award,           // Prêmio/Conquista
  Medal,           // Medalha
  Trophy,          // Troféu
  Gift,            // Presente/Benefício
  Package,         // Pacote/Entrega
  Truck,           // Transporte/Entrega
  Plane,           // Viagem/Transporte aéreo
  Car,             // Carro/Transporte
  Bus,             // Ônibus/Transporte público
  Train,           // Trem
  Ship,            // Navio
  Anchor,          // Âncora/Estabilidade
  Wind,            // Vento/Respiração
  Sun,             // Sol/Dia/Claro
  Moon,            // Lua/Noite/Escuro
  Cloud,           // Nuvem/Online
  CloudRain,       // Chuva/Depressão
  CloudSnow,       // Neve/Frio
  Snowflake,       // Floco de neve
  Flame,           // Fogo/Febre
  Zap as Lightning, // Raio/Energia
  Battery,         // Bateria/Energia
  BatteryLow,      // Bateria baixa
  Plug,            // Tomada/Conectar
  Bluetooth,       // Bluetooth
  WifiOff,         // Sem internet
} from 'lucide-react';

// Mapeamento de ícones por contexto médico WellWave
export const MedicalIcons = {
  // 🏥 Navegação principal
  navigation: {
    dashboard: Home,
    patients: Users,
    anamnesis: Clipboard,
    prescription: Pill,
    exams: TestTube,
    emergency: Zap,
    schedule: Calendar,
    reports: BarChart,
    chat: MessageSquare,
    kanban: Target,
    medications: Pill,
  },
  
  // 🩺 Especialidades médicas
  specialties: {
    cardiology: Heart,
    neurology: Brain,
    pediatrics: Baby,
    geriatrics: Shield,
    gynecology: UserCheck,
    orthopedics: Bone,
    psychiatry: Brain,
    dermatology: Eye,
    pneumology: Lungs,
    gastroenterology: Activity,
    endocrinology: TestTube,
    ophthalmology: Eye,
    otorhinolaryngology: Ear,
    urology: Droplet,
    oncology: Target,
    radiology: Scan,
    anesthesiology: Syringe,
    pathology: Microscope,
    surgery: Bandage,
    emergency: Ambulance,
  },
  
  // 🚨 Status e alertas médicos
  status: {
    critical: AlertTriangle,
    urgent: AlertCircle,
    moderate: Info,
    stable: CheckCircle,
    resolved: Check,
    pending: Clock,
    cancelled: X,
    scheduled: Calendar,
    inProgress: RefreshCw,
    completed: CheckCircle,
    review: Eye,
    approved: Check,
    rejected: X,
  },
  
  // ⚕️ Ações médicas
  actions: {
    examine: Stethoscope,
    prescribe: Pill,
    inject: Syringe,
    monitor: MonitorIcon,
    measure: Thermometer,
    document: FileText,
    approve: CheckCircle,
    alert: AlertCircle,
    diagnose: Target,
    treat: Heart,
    operate: Bandage,
    discharge: ArrowRight,
    admit: ArrowLeft,
    transfer: ArrowRight,
    refer: Send,
    consult: MessageSquare,
    follow: Eye,
    schedule: Calendar,
    cancel: X,
    reschedule: RotateCcw,
  },
  
  // 📊 Dados e métricas médicas
  metrics: {
    heartRate: Activity,
    bloodPressure: TrendingUp,
    temperature: Thermometer,
    weight: Scale,
    height: ArrowUp,
    bmi: BarChart,
    glucose: Droplet,
    oxygen: Wind,
    pain: AlertTriangle,
    mobility: Users,
    cognition: Brain,
    mood: Sun,
    sleep: Moon,
    appetite: Utensils,
    hydration: Droplet,
  },
  
  // 🧪 Exames e testes
  tests: {
    bloodTest: Droplet,
    urine: TestTube,
    xray: Scan,
    mri: Monitor,
    ct: Scan,
    ultrasound: Activity,
    ecg: Activity,
    eeg: Brain,
    biopsy: Microscope,
    culture: FlaskConical,
    genetic: TestTube,
    pathology: Microscope,
    lab: FlaskConical,
    imaging: Camera,
    endoscopy: Eye,
  },
  
  // 💊 Medicamentos e tratamentos
  medications: {
    pill: Pill,
    injection: Syringe,
    iv: Droplet,
    topical: Bandage,
    inhaler: Wind,
    drops: Droplet,
    patch: Bandage,
    suppository: Pill,
    spray: Wind,
    tablet: TabletIcon,
    capsule: Pill,
    liquid: Droplet,
    powder: Package,
    cream: Package,
    ointment: Package,
  },
  
  // 🏥 Departamentos hospitalares
  departments: {
    emergency: Ambulance,
    icu: MonitorIcon,
    surgery: Bandage,
    maternity: Baby,
    pediatrics: Baby,
    geriatrics: Shield,
    cardiology: Heart,
    neurology: Brain,
    oncology: Target,
    radiology: Scan,
    laboratory: FlaskConical,
    pharmacy: Pill,
    rehabilitation: TrendingUp,
    psychiatry: Brain,
    outpatient: Users,
  },
  
  // 👥 Pessoas e funções
  people: {
    doctor: UserCheck,
    nurse: Users,
    patient: UserCheck,
    family: Users,
    specialist: Award,
    resident: UserPlus,
    student: Book,
    admin: Settings,
    tech: Laptop,
    pharmacist: Pill,
    therapist: Heart,
    social: Users,
    chaplain: Heart,
    volunteer: Gift,
    visitor: Eye,
  },
  
  // 📱 Tecnologia e sistemas
  technology: {
    app: Smartphone,
    web: Globe,
    mobile: Smartphone,
    tablet: TabletIcon,
    desktop: MonitorIcon,
    laptop: Laptop,
    server: Server,
    database: Database,
    cloud: Cloud,
    api: Code,
    integration: Wifi,
    sync: RefreshCw,
    backup: Download,
    security: Lock,
    privacy: Shield,
  },
  
  // 🎯 Interface e navegação
  interface: {
    home: Home,
    back: ArrowLeft,
    forward: ArrowRight,
    up: ArrowUp,
    down: ArrowDown,
    menu: MoreHorizontal,
    close: X,
    add: Plus,
    remove: Minus,
    edit: Edit,
    save: Save,
    copy: Copy,
    delete: Trash,
    search: Search,
    filter: Filter,
    sort: Sort,
    refresh: RefreshCw,
    settings: Settings,
    help: HelpCircle,
    info: Info,
    warning: AlertTriangle,
    error: AlertCircle,
    success: CheckCircle,
  },
  
  // 🌊 WellWave específicos
  wellwave: {
    logo: Activity, // Usar como base para animação wave
    wave: Activity,
    flow: TrendingUp,
    pulse: Activity,
    gradient: Palette,
    premium: Crown,
    medical: Stethoscope,
    digital: MonitorIcon,
    smart: Brain,
    ai: Cpu,
    analytics: BarChart,
    insights: Eye,
    prediction: Target,
    optimization: TrendingUp,
    automation: RefreshCw,
  }
};

// 🎨 Componente de ícone médico com styling WellWave
export interface MedicalIconProps {
  name: string;
  category?: keyof typeof MedicalIcons;
  size?: number | string;
  color?: string;
  className?: string;
  animated?: boolean;
  style?: React.CSSProperties;
  title?: string;
}

export function MedicalIcon({ 
  name, 
  category = 'interface', 
  size = 20, 
  color, 
  className = '', 
  animated = false,
  style = {},
  title
}: MedicalIconProps) {
  // Encontrar o ícone na categoria especificada
  const IconComponent = MedicalIcons[category]?.[name as keyof typeof MedicalIcons[typeof category]];
  
  // Fallback para ícone padrão se não encontrar
  const FallbackIcon = Info;
  const Icon = IconComponent || FallbackIcon;
  
  const baseClasses = 'medical-icon';
  const animationClasses = animated ? 'transition-wave hover:scale-110' : '';
  const finalClassName = `${baseClasses} ${animationClasses} ${className}`.trim();
  
  const finalStyle = {
    width: typeof size === 'number' ? `${size}px` : size,
    height: typeof size === 'number' ? `${size}px` : size,
    color: color || 'currentColor',
    ...style
  };

  return (
    <Icon 
      className={finalClassName}
      style={finalStyle}
      title={title || name}
      aria-label={title || name}
    />
  );
}

// 🎯 Hook para usar ícones médicos facilmente
export function useMedicalIcon(name: string, category: keyof typeof MedicalIcons = 'interface') {
  const IconComponent = MedicalIcons[category]?.[name as keyof typeof MedicalIcons[typeof category]];
  return IconComponent || Info;
}

// 📋 Utilitários para ícones por contexto
export const IconUtils = {
  // Obter ícone por prioridade médica
  getByPriority: (priority: 'baixa' | 'media' | 'alta' | 'emergencia') => {
    const mapping = {
      'baixa': MedicalIcons.status.stable,
      'media': MedicalIcons.status.moderate,
      'alta': MedicalIcons.status.urgent,
      'emergencia': MedicalIcons.status.critical
    };
    return mapping[priority];
  },
  
  // Obter ícone por status do paciente
  getByStatus: (status: 'triagem' | 'atendimento' | 'exames' | 'alta' | 'internacao') => {
    const mapping = {
      'triagem': MedicalIcons.status.pending,
      'atendimento': MedicalIcons.status.inProgress,
      'exames': MedicalIcons.actions.examine,
      'alta': MedicalIcons.actions.discharge,
      'internacao': MedicalIcons.actions.admit
    };
    return mapping[status];
  },
  
  // Obter ícone por faixa etária
  getByAge: (ageGroup: 'pediatrico' | 'adulto' | 'idoso') => {
    const mapping = {
      'pediatrico': MedicalIcons.specialties.pediatrics,
      'adulto': MedicalIcons.people.patient,
      'idoso': MedicalIcons.specialties.geriatrics
    };
    return mapping[ageGroup];
  },
  
  // Obter ícone por sexo
  getBySex: (sex: 'M' | 'F') => {
    const mapping = {
      'M': MedicalIcons.people.patient,
      'F': MedicalIcons.people.patient // Pode ser diferenciado se necessário
    };
    return mapping[sex];
  },
  
  // Obter ícone por especialidade
  getBySpecialty: (specialty: string) => {
    const normalizedSpecialty = specialty.toLowerCase().replace(/\s+/g, '');
    const specialtyMap: Record<string, any> = {
      'cardiologia': MedicalIcons.specialties.cardiology,
      'neurologia': MedicalIcons.specialties.neurology,
      'pediatria': MedicalIcons.specialties.pediatrics,
      'geriatria': MedicalIcons.specialties.geriatrics,
      'ginecologia': MedicalIcons.specialties.gynecology,
      'ortopedia': MedicalIcons.specialties.orthopedics,
      'psiquiatria': MedicalIcons.specialties.psychiatry,
      'dermatologia': MedicalIcons.specialties.dermatology,
      'pneumologia': MedicalIcons.specialties.pneumology,
      'gastroenterologia': MedicalIcons.specialties.gastroenterology,
      'endocrinologia': MedicalIcons.specialties.endocrinology,
      'oftalmologia': MedicalIcons.specialties.ophthalmology,
      'otorrinolaringologia': MedicalIcons.specialties.otorhinolaryngology,
      'urologia': MedicalIcons.specialties.urology,
      'oncologia': MedicalIcons.specialties.oncology,
      'radiologia': MedicalIcons.specialties.radiology,
      'anestesiologia': MedicalIcons.specialties.anesthesiology,
      'patologia': MedicalIcons.specialties.pathology,
      'cirurgia': MedicalIcons.specialties.surgery,
      'emergencia': MedicalIcons.specialties.emergency,
    };
    
    return specialtyMap[normalizedSpecialty] || MedicalIcons.people.doctor;
  }
};

// 🌊 Exportar tudo para uso fácil
export default MedicalIcons;
export { MedicalIcons };