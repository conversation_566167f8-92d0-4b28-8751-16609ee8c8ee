/**
 * WellWave Design System
 * Sistema de design padronizado para toda a aplicação
 */

export const designSystem = {
  // Cores principais padronizadas
  colors: {
    // Primárias (Azul WellWave)
    primary: {
      50: '#eff6ff',
      100: '#dbeafe', 
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6', // Cor principal
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    },

    // Status médicos
    status: {
      // Emergência (Vermelho)
      emergency: {
        50: '#fef2f2',
        500: '#ef4444',
        600: '#dc2626',
        900: '#7f1d1d'
      },
      // Alta prioridade (Laranja)
      high: {
        50: '#fff7ed',
        500: '#f97316',
        600: '#ea580c',
        900: '#9a3412'
      },
      // Média prioridade (Amarelo)
      medium: {
        50: '#fefce8',
        500: '#eab308',
        600: '#ca8a04',
        900: '#713f12'
      },
      // Baixa prioridade (Verde)
      low: {
        50: '#f0fdf4',
        500: '#22c55e',
        600: '#16a34a',
        900: '#14532d'
      },
      // Estável (Azul)
      stable: {
        50: '#eff6ff',
        500: '#3b82f6',
        600: '#2563eb',
        900: '#1e3a8a'
      }
    },

    // Status do sistema
    system: {
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },

    // Neutros
    neutral: {
      0: '#ffffff',
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712'
    }
  },

  // Especialidades médicas com cores específicas
  specialties: {
    cardiologia: '#dc2626', // Vermelho
    neurologia: '#7c3aed', // Roxo  
    pediatria: '#06b6d4', // Ciano
    ortopedia: '#059669', // Verde
    ginecologia: '#db2777', // Rosa
    dermatologia: '#d97706', // Laranja
    oftalmologia: '#2563eb', // Azul
    psiquiatria: '#7c2d12', // Marrom
    clinicaMedica: '#374151', // Cinza
    emergencia: '#dc2626' // Vermelho
  },

  // Gradientes
  gradients: {
    primary: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
    success: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
    warning: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
    error: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
    wave: 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 25%, #3b82f6 50%, #1d4ed8 75%, #1e3a8a 100%)'
  },

  // Sombras padronizadas
  shadows: {
    xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    glow: '0 0 0 1px rgb(59 130 246 / 0.1), 0 0 20px rgb(59 130 246 / 0.2)'
  },

  // Espaçamentos
  spacing: {
    xs: '0.25rem', // 4px
    sm: '0.5rem',  // 8px
    md: '1rem',    // 16px
    lg: '1.5rem',  // 24px
    xl: '2rem',    // 32px
    '2xl': '3rem', // 48px
    '3xl': '4rem'  // 64px
  },

  // Bordas arredondadas
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    full: '9999px'
  },

  // Tipografia
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['ui-monospace', 'SFMono-Regular', 'monospace']
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }]
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700'
    }
  },

  // Transições e animações
  transitions: {
    fast: '150ms cubic-bezier(0.4, 0, 0.2, 1)',
    normal: '200ms cubic-bezier(0.4, 0, 0.2, 1)',
    slow: '300ms cubic-bezier(0.4, 0, 0.2, 1)',
    spring: '300ms cubic-bezier(0.34, 1.56, 0.64, 1)'
  },

  // Breakpoints responsivos
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  }
};

// Utilitários para uso das cores
export const getStatusColor = (status: string, variant: 50 | 500 | 600 | 900 = 500) => {
  switch (status) {
    case 'emergencia':
    case 'emergency':
      return designSystem.colors.status.emergency[variant];
    case 'alta':
    case 'high':
      return designSystem.colors.status.high[variant];
    case 'media':
    case 'medium':
      return designSystem.colors.status.medium[variant];
    case 'baixa':
    case 'low':
      return designSystem.colors.status.low[variant];
    case 'estavel':
    case 'stable':
      return designSystem.colors.status.stable[variant];
    default:
      return designSystem.colors.primary[variant];
  }
};

export const getSpecialtyColor = (specialty: string) => {
  const key = specialty.toLowerCase().replace(/\s+/g, '') as keyof typeof designSystem.specialties;
  return designSystem.specialties[key] || designSystem.colors.primary[500];
};

export const getPriorityConfig = (priority: string) => {
  switch (priority) {
    case 'emergencia':
      return {
        color: getStatusColor('emergency'),
        bgColor: getStatusColor('emergency', 50),
        textColor: 'text-red-700 dark:text-red-300',
        borderColor: 'border-red-500',
        badgeClass: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      };
    case 'alta':
      return {
        color: getStatusColor('high'),
        bgColor: getStatusColor('high', 50),
        textColor: 'text-orange-700 dark:text-orange-300',
        borderColor: 'border-orange-500',
        badgeClass: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      };
    case 'media':
      return {
        color: getStatusColor('medium'),
        bgColor: getStatusColor('medium', 50),
        textColor: 'text-yellow-700 dark:text-yellow-300',
        borderColor: 'border-yellow-500',
        badgeClass: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      };
    case 'baixa':
      return {
        color: getStatusColor('low'),
        bgColor: getStatusColor('low', 50),
        textColor: 'text-green-700 dark:text-green-300',
        borderColor: 'border-green-500',
        badgeClass: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      };
    default:
      return {
        color: designSystem.colors.primary[500],
        bgColor: designSystem.colors.primary[50],
        textColor: 'text-blue-700 dark:text-blue-300',
        borderColor: 'border-blue-500',
        badgeClass: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      };
  }
};

// CSS Custom Properties para uso no CSS
export const getCSSVars = () => ({
  '--color-primary': designSystem.colors.primary[500],
  '--color-primary-50': designSystem.colors.primary[50],
  '--color-primary-100': designSystem.colors.primary[100],
  '--color-primary-600': designSystem.colors.primary[600],
  '--color-primary-700': designSystem.colors.primary[700],
  '--color-success': designSystem.colors.system.success,
  '--color-warning': designSystem.colors.system.warning,
  '--color-error': designSystem.colors.system.error,
  '--color-info': designSystem.colors.system.info,
  '--gradient-primary': designSystem.gradients.primary,
  '--gradient-wave': designSystem.gradients.wave,
  '--shadow-sm': designSystem.shadows.sm,
  '--shadow-md': designSystem.shadows.md,
  '--shadow-lg': designSystem.shadows.lg,
  '--transition-fast': designSystem.transitions.fast,
  '--transition-normal': designSystem.transitions.normal,
  '--radius': designSystem.borderRadius.lg
});

export default designSystem;