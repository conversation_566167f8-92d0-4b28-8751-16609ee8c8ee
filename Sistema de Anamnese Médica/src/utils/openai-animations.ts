/**
 * 🌊 WellWave OpenAI Animations System - Secure Version
 * 
 * Sistema de animações premium sem acesso direto a cssRules
 * Compatível com políticas de segurança rigorosas
 */

// Safe animation utilities without cssRules access
export class WellWaveAnimations {
  private static initialized = false;
  private static observers = new Map<string, IntersectionObserver>();
  private static toastContainer: HTMLElement | null = null;

  /**
   * Initialize animation system safely
   */
  static init() {
    if (this.initialized) return;

    try {
      this.setupRevealAnimations();
      this.setupToastSystem();
      this.setupKeyboardDetection();
      this.initialized = true;
    } catch (error) {
      console.warn('WellWave Animations: Safe initialization fallback', error);
    }
  }

  /**
   * Setup reveal animations using Intersection Observer
   */
  private static setupRevealAnimations() {
    if (typeof window === 'undefined') return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            element.classList.add('openai-fade-in');
            observer.unobserve(element);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '20px 0px'
      }
    );

    this.observers.set('reveal', observer);

    // Observe elements with data-reveal
    const observeRevealElements = () => {
      const elements = document.querySelectorAll('[data-reveal]');
      elements.forEach((el) => observer.observe(el));
    };

    // Initial observation
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', observeRevealElements);
    } else {
      observeRevealElements();
    }

    // Watch for new elements
    const mutationObserver = new MutationObserver(() => {
      observeRevealElements();
    });

    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Setup keyboard detection for focus states
   */
  private static setupKeyboardDetection() {
    if (typeof document === 'undefined') return;

    let isUsingKeyboard = false;

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        isUsingKeyboard = true;
        document.body.classList.add('using-keyboard');
        document.body.classList.remove('using-mouse');
      }
    });

    document.addEventListener('mousedown', () => {
      isUsingKeyboard = false;
      document.body.classList.remove('using-keyboard');
      document.body.classList.add('using-mouse');
    });
  }

  /**
   * Setup toast notification system
   */
  private static setupToastSystem() {
    if (typeof document === 'undefined') return;

    // Create toast container if it doesn't exist
    if (!this.toastContainer) {
      this.toastContainer = document.createElement('div');
      this.toastContainer.id = 'wellwave-toast-container';
      this.toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2 pointer-events-none';
      document.body.appendChild(this.toastContainer);
    }
  }

  /**
   * Create a safe toast notification
   */
  static createToast(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info', duration: number = 3000) {
    if (!this.toastContainer) {
      this.setupToastSystem();
    }

    const toast = document.createElement('div');
    const toastId = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Toast styling based on type
    const typeStyles = {
      info: 'bg-blue-50 border-blue-200 text-blue-800',
      success: 'bg-green-50 border-green-200 text-green-800',
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      error: 'bg-red-50 border-red-200 text-red-800'
    };

    const typeIcons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };

    toast.id = toastId;
    toast.className = `
      ${typeStyles[type]}
      border rounded-lg p-3 shadow-lg backdrop-blur-md
      animate-scale pointer-events-auto max-w-sm
      transition-all duration-300 ease-out
    `;

    toast.innerHTML = `
      <div class="flex items-center gap-2">
        <span class="text-lg">${typeIcons[type]}</span>
        <span class="text-sm font-medium">${message}</span>
        <button class="ml-auto text-current opacity-70 hover:opacity-100" onclick="this.parentElement.parentElement.remove()">
          ✕
        </button>
      </div>
    `;

    // Add to container with animation
    this.toastContainer?.appendChild(toast);

    // Auto-remove after duration
    setTimeout(() => {
      if (toast.parentNode) {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => toast.remove(), 300);
      }
    }, duration);

    return {
      close: () => toast.remove()
    };
  }

  /**
   * Add hover effect to element safely
   */
  static addHoverEffect(element: HTMLElement, effect: 'lift' | 'glow' | 'scale' = 'lift') {
    if (!element) return;

    const effects = {
      lift: 'hover-lift',
      glow: 'hover-glow', 
      scale: 'hover-grow'
    };

    element.classList.add(effects[effect]);
  }

  /**
   * Add wave animation to element
   */
  static addWaveAnimation(element: HTMLElement, animation: 'motion' | 'pulse' | 'flow' = 'motion') {
    if (!element) return;

    const animations = {
      motion: 'wave-motion',
      pulse: 'wave-pulse',
      flow: 'wave-flow'
    };

    element.classList.add(animations[animation]);
  }

  /**
   * Create ripple effect on click
   */
  static createRipple(event: MouseEvent, element: HTMLElement) {
    try {
      const rect = element.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = event.clientX - rect.left - size / 2;
      const y = event.clientY - rect.top - size / 2;

      const ripple = document.createElement('div');
      ripple.className = 'ripple';
      ripple.style.cssText = `
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
      `;

      element.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    } catch (error) {
      console.warn('WellWave: Ripple effect failed safely', error);
    }
  }

  /**
   * Performance monitoring without cssRules access
   */
  static monitorPerformance() {
    if (typeof window === 'undefined') return;

    try {
      // Web Vitals monitoring
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            // Log performance metrics safely
            console.debug('WellWave Performance:', {
              name: entry.name,
              type: entry.entryType,
              duration: entry.duration,
              startTime: entry.startTime
            });
          });
        });

        observer.observe({ 
          entryTypes: ['measure', 'navigation', 'paint'] 
        });
      }
    } catch (error) {
      console.warn('WellWave: Performance monitoring failed safely', error);
    }
  }

  /**
   * Cleanup observers and resources
   */
  static cleanup() {
    this.observers.forEach((observer) => {
      observer.disconnect();
    });
    this.observers.clear();

    if (this.toastContainer) {
      this.toastContainer.remove();
      this.toastContainer = null;
    }

    this.initialized = false;
  }
}

/**
 * Safe Toast API
 */
export const Toast = {
  show: (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info', duration: number = 3000) => {
    return WellWaveAnimations.createToast(message, type, duration);
  },
  
  success: (message: string, duration: number = 3000) => {
    return WellWaveAnimations.createToast(message, 'success', duration);
  },
  
  error: (message: string, duration: number = 5000) => {
    return WellWaveAnimations.createToast(message, 'error', duration);
  },
  
  warning: (message: string, duration: number = 4000) => {
    return WellWaveAnimations.createToast(message, 'warning', duration);
  },
  
  info: (message: string, duration: number = 3000) => {
    return WellWaveAnimations.createToast(message, 'info', duration);
  }
};

/**
 * Animation utilities
 */
export const AnimationUtils = {
  // Fade in element
  fadeIn: (element: HTMLElement, duration: number = 300) => {
    if (!element) return;
    
    element.style.opacity = '0';
    element.style.transition = `opacity ${duration}ms ease-out`;
    
    requestAnimationFrame(() => {
      element.style.opacity = '1';
    });
  },

  // Slide in element
  slideIn: (element: HTMLElement, direction: 'up' | 'down' | 'left' | 'right' = 'up', duration: number = 300) => {
    if (!element) return;

    const transforms = {
      up: 'translateY(20px)',
      down: 'translateY(-20px)',
      left: 'translateX(20px)',
      right: 'translateX(-20px)'
    };

    element.style.transform = transforms[direction];
    element.style.opacity = '0';
    element.style.transition = `transform ${duration}ms ease-out, opacity ${duration}ms ease-out`;

    requestAnimationFrame(() => {
      element.style.transform = 'translate(0, 0)';
      element.style.opacity = '1';
    });
  },

  // Scale in element
  scaleIn: (element: HTMLElement, duration: number = 300) => {
    if (!element) return;

    element.style.transform = 'scale(0.9)';
    element.style.opacity = '0';
    element.style.transition = `transform ${duration}ms ease-out, opacity ${duration}ms ease-out`;

    requestAnimationFrame(() => {
      element.style.transform = 'scale(1)';
      element.style.opacity = '1';
    });
  }
};

/**
 * Auto-initialize when imported
 */
if (typeof window !== 'undefined') {
  // Safe initialization
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      WellWaveAnimations.init();
    });
  } else {
    WellWaveAnimations.init();
  }
}

export default WellWaveAnimations;