/**
 * 🌊 WellWave OpenAI Effects Initialization - Secure Version
 * 
 * Inicialização segura dos efeitos sem acesso a cssRules
 * Sistema de efeitos premium para WellWave Medical Dashboard
 */

import WellWaveAnimations, { Toast, AnimationUtils } from './openai-animations';

/**
 * Safe OpenAI Effects System
 */
class SafeOpenAIEffects {
  private initialized = false;
  private observers: IntersectionObserver[] = [];
  private eventListeners: Array<{ element: Element | Document | Window, event: string, handler: EventListener }> = [];

  /**
   * Initialize all WellWave effects safely
   */
  init() {
    if (this.initialized || typeof window === 'undefined') {
      return { cleanup: () => {} };
    }

    try {
      console.log('🌊 Initializing WellWave Premium Effects...');
      
      this.setupScrollEffects();
      this.setupHoverEffects();
      this.setupClickEffects();
      this.setupThemeEffects();
      this.setupResponsiveEffects();
      this.setupMedicalAnimations();
      
      this.initialized = true;
      console.log('✅ WellWave Effects initialized successfully');
      
      // Show welcome toast
      setTimeout(() => {
        Toast.success('🌊 WellWave Premium carregado!', 2000);
      }, 1000);

    } catch (error) {
      console.warn('⚠️ WellWave Effects: Safe fallback mode', error);
    }

    return {
      cleanup: () => this.cleanup()
    };
  }

  /**
   * Setup scroll-based animations
   */
  private setupScrollEffects() {
    // Parallax effect for wave backgrounds
    const parallaxObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            this.addScrollListener(element);
          }
        });
      },
      { threshold: 0 }
    );

    this.observers.push(parallaxObserver);

    // Observe wave elements
    const observeWaveElements = () => {
      const waveElements = document.querySelectorAll('.wave-flow, .wave-motion');
      waveElements.forEach(el => parallaxObserver.observe(el));
    };

    this.scheduleObservation(observeWaveElements);
  }

  /**
   * Add safe scroll listener
   */
  private addScrollListener(element: HTMLElement) {
    let ticking = false;
    
    const handleScroll = () => {
      if (!ticking && element.classList.contains('wave-flow')) {
        requestAnimationFrame(() => {
          this.updateWaveAnimation(element);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    this.eventListeners.push({ element: window, event: 'scroll', handler: handleScroll });
  }

  /**
   * Update wave animation safely
   */
  private updateWaveAnimation(element: HTMLElement) {
    try {
      const rect = element.getBoundingClientRect();
      const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
      
      if (isVisible) {
        const intensity = Math.min(1, (window.innerHeight - rect.top) / window.innerHeight);
        element.style.setProperty('--wave-intensity', intensity.toString());
      }
    } catch (error) {
      console.debug('Wave animation update skipped safely');
    }
  }

  /**
   * Setup hover effects for interactive elements
   */
  private setupHoverEffects() {
    const addHoverEffects = () => {
      // Cards with hover lift
      document.querySelectorAll('.openai-card, .stat-card').forEach((card) => {
        if (card instanceof HTMLElement && !card.dataset.hoverSetup) {
          this.setupCardHover(card);
          card.dataset.hoverSetup = 'true';
        }
      });

      // Buttons with ripple effect
      document.querySelectorAll('.openai-button, .ripple').forEach((button) => {
        if (button instanceof HTMLElement && !button.dataset.rippleSetup) {
          this.setupRippleEffect(button);
          button.dataset.rippleSetup = 'true';
        }
      });

      // Sidebar items with smooth transitions
      document.querySelectorAll('.nav-link, .openai-sidebar-item').forEach((item) => {
        if (item instanceof HTMLElement && !item.dataset.navSetup) {
          this.setupNavItemHover(item);
          item.dataset.navSetup = 'true';
        }
      });
    };

    this.scheduleObservation(addHoverEffects);
  }

  /**
   * Setup card hover effects
   */
  private setupCardHover(card: HTMLElement) {
    const mouseEnter = () => {
      card.style.transform = 'translateY(-4px) scale(1.02)';
      card.style.boxShadow = 'var(--shadow-wave-lg)';
    };

    const mouseLeave = () => {
      card.style.transform = '';
      card.style.boxShadow = '';
    };

    card.addEventListener('mouseenter', mouseEnter);
    card.addEventListener('mouseleave', mouseLeave);
    
    this.eventListeners.push(
      { element: card, event: 'mouseenter', handler: mouseEnter },
      { element: card, event: 'mouseleave', handler: mouseLeave }
    );
  }

  /**
   * Setup ripple effect safely
   */
  private setupRippleEffect(button: HTMLElement) {
    const handleClick = (event: Event) => {
      if (event instanceof MouseEvent) {
        this.createSafeRipple(event, button);
      }
    };

    button.addEventListener('click', handleClick);
    this.eventListeners.push({ element: button, event: 'click', handler: handleClick });
  }

  /**
   * Create safe ripple effect
   */
  private createSafeRipple(event: MouseEvent, element: HTMLElement) {
    try {
      const rect = element.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = event.clientX - rect.left - size / 2;
      const y = event.clientY - rect.top - size / 2;

      const ripple = document.createElement('div');
      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: radial-gradient(circle, rgba(74, 144, 226, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        transform: scale(0);
        animation: rippleWave 0.6s ease-out;
        pointer-events: none;
        z-index: 1;
      `;

      element.style.position = 'relative';
      element.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    } catch (error) {
      console.debug('Ripple effect failed safely');
    }
  }

  /**
   * Setup navigation item hover
   */
  private setupNavItemHover(item: HTMLElement) {
    const mouseEnter = () => {
      item.style.transform = 'translateX(4px)';
      item.style.backgroundColor = 'var(--sidebar-accent)';
    };

    const mouseLeave = () => {
      if (!item.classList.contains('active')) {
        item.style.transform = '';
        item.style.backgroundColor = '';
      }
    };

    item.addEventListener('mouseenter', mouseEnter);
    item.addEventListener('mouseleave', mouseLeave);
    
    this.eventListeners.push(
      { element: item, event: 'mouseenter', handler: mouseEnter },
      { element: item, event: 'mouseleave', handler: mouseLeave }
    );
  }

  /**
   * Setup click effects and feedback
   */
  private setupClickEffects() {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      
      // Add click feedback to interactive elements
      if (target.matches('button, .nav-link, .openai-button, [role="button"]')) {
        this.addClickFeedback(target);
      }

      // Handle special WellWave actions
      if (target.matches('[data-wellwave-action]')) {
        this.handleWellWaveAction(target);
      }
    });
  }

  /**
   * Add visual click feedback
   */
  private addClickFeedback(element: HTMLElement) {
    const originalTransform = element.style.transform;
    
    element.style.transform = `${originalTransform} scale(0.95)`;
    element.style.transition = 'transform 0.1s ease-out';
    
    setTimeout(() => {
      element.style.transform = originalTransform;
    }, 100);
  }

  /**
   * Handle WellWave specific actions
   */
  private handleWellWaveAction(element: HTMLElement) {
    const action = element.dataset.wellwaveAction;
    
    switch (action) {
      case 'pulse':
        element.classList.add('wave-pulse');
        setTimeout(() => element.classList.remove('wave-pulse'), 2000);
        break;
      case 'glow':
        element.classList.add('animate-glow');
        setTimeout(() => element.classList.remove('animate-glow'), 3000);
        break;
      case 'heartbeat':
        element.classList.add('animate-heartbeat');
        setTimeout(() => element.classList.remove('animate-heartbeat'), 2000);
        break;
    }
  }

  /**
   * Setup theme transition effects
   */
  private setupThemeEffects() {
    const themeObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target as HTMLElement;
          if (target === document.documentElement) {
            this.handleThemeChange();
          }
        }
      });
    });

    themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    this.observers.push(themeObserver as any);
  }

  /**
   * Handle theme change animations
   */
  private handleThemeChange() {
    // Add smooth transition to all elements during theme change
    const elements = document.querySelectorAll('*');
    elements.forEach((el) => {
      if (el instanceof HTMLElement) {
        el.style.transition = 'background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease';
      }
    });

    // Remove transition after animation
    setTimeout(() => {
      elements.forEach((el) => {
        if (el instanceof HTMLElement) {
          el.style.transition = '';
        }
      });
    }, 300);
  }

  /**
   * Setup responsive effects
   */
  private setupResponsiveEffects() {
    let resizeTimeout: number;
    
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = window.setTimeout(() => {
        this.updateResponsiveElements();
      }, 150);
    };

    window.addEventListener('resize', handleResize);
    this.eventListeners.push({ element: window, event: 'resize', handler: handleResize });
  }

  /**
   * Update elements on resize
   */
  private updateResponsiveElements() {
    // Update sidebar behavior on resize
    const sidebar = document.querySelector('.openai-sidebar');
    if (sidebar instanceof HTMLElement) {
      const isMobile = window.innerWidth < 1024;
      sidebar.classList.toggle('mobile-mode', isMobile);
    }

    // Update dashboard grid
    const dashboard = document.querySelector('.dashboard-container');
    if (dashboard instanceof HTMLElement) {
      dashboard.style.setProperty('--viewport-width', `${window.innerWidth}px`);
    }
  }

  /**
   * Setup medical-specific animations
   */
  private setupMedicalAnimations() {
    // Heartbeat animation for medical icons
    const startHeartbeat = () => {
      document.querySelectorAll('[data-medical-heartbeat]').forEach((el) => {
        if (el instanceof HTMLElement) {
          el.classList.add('animate-heartbeat');
        }
      });
    };

    // ECG line animation
    const startECG = () => {
      document.querySelectorAll('[data-medical-ecg]').forEach((el) => {
        if (el instanceof HTMLElement) {
          el.classList.add('pulse-ecg');
        }
      });
    };

    this.scheduleObservation(() => {
      startHeartbeat();
      startECG();
    });
  }

  /**
   * Schedule observation with safe timing
   */
  private scheduleObservation(callback: () => void) {
    // Immediate execution if DOM is ready
    if (document.readyState === 'complete') {
      callback();
    } else {
      document.addEventListener('DOMContentLoaded', callback);
    }

    // Also run on mutations for dynamic content
    const mutationObserver = new MutationObserver(() => {
      callback();
    });

    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.observers.push(mutationObserver);
  }

  /**
   * Cleanup all effects and observers
   */
  private cleanup() {
    console.log('🧹 Cleaning up WellWave Effects...');

    // Disconnect all observers
    this.observers.forEach(observer => {
      observer.disconnect();
    });
    this.observers = [];

    // Remove all event listeners
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];

    // Cleanup animation system
    WellWaveAnimations.cleanup();

    this.initialized = false;
  }
}

/**
 * Create singleton instance
 */
const safeOpenAIEffects = new SafeOpenAIEffects();

/**
 * Default export function
 */
export default function initWellWaveOpenAI() {
  return safeOpenAIEffects.init();
}

/**
 * Named exports for flexibility
 */
export {
  safeOpenAIEffects as WellWaveEffects,
  Toast,
  AnimationUtils,
  WellWaveAnimations
};