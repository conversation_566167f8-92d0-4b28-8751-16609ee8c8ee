import React from 'react';
import {
  // CORE LUCIDE ICONS - VERIFIED VALID IMPORTS
  Activity, AlertCircle, AlertTriangle, ArrowDown, ArrowLeft, ArrowRight, ArrowUp,
  ArrowUpDown, Award, Baby, Bandage, BarChart, Bell, Bone, Bot, Brain, 
  Briefcase, Building, Calendar, CalendarCheck, CalendarDays, Camera, Car,
  Check, CheckCircle, ChevronDown, ChevronLeft, ChevronRight, ChevronUp,
  Circle, CircleDot, Clipboard, Clock, Cloud, CloudRain, Copy, Crown, 
  Database, Download, Droplet, Ear, Edit, Eye, FileCheck, FilePlus, FileText, 
  FileX, Filter, Flag, FlaskConical, Gift, Globe, Hash, Heart, HeartHandshake,
  HelpCircle, Hexagon, Home, Image, Info, Key, Laptop, LineChart, Lock, 
  LogIn, LogOut, Mail, Map, MapPin, Medal, MessageSquare, Microscope, 
  Minus, Monitor, Moon, MoreHorizontal, MoreVertical, Navigation, Package, 
  Phone, PieChart, Pill, Plus, Power, Printer, RefreshCw, Repeat, 
  RotateCcw, RotateCw, Save, Scan, Search, Send, Server, Settings, 
  Shield, Smartphone, Square, Star, Stethoscope, Sun, Syringe, Target, 
  TestTube, Thermometer, Timer, Triangle, TrendingDown, TrendingFlat, 
  TrendingUp, Trophy, Truck, Type, Unlock, Upload, User, UserCheck, 
  UserMinus, UserPlus, UserRound, Users, Video, Watch, Wifi, WifiOff, 
  Wind, X, Zap, Anchor, Bandage as MedicalBandage, Cross, Dna, 
  Bookmark, Compass, Flame, Snowflake, BrainCircuit, UserCog
} from 'lucide-react';

// Enum para tipos de ícones
export enum IconVariant {
  OUTLINE = 'outline',
  FILLED = 'filled',
  DUOTONE = 'duotone'
}

export enum IconSize {
  XS = 12,
  SM = 16,
  MD = 20,
  LG = 24,
  XL = 32,
  XXL = 48
}

// Interface para propriedades do ícone médico
export interface MedicalIconProps {
  name: string;
  variant?: IconVariant;
  size?: IconSize | number;
  color?: string;
  className?: string;
  animated?: boolean;
  tooltip?: string;
  onClick?: () => void;
}

/**
 * 🎨 Sistema de Ícones Médicos Premium WellWave
 * 
 * Sistema simplificado com apenas ícones válidos do Lucide React
 */
export class MedicalIconSystem {
  // Mapeamento de ícones médicos por categoria - APENAS ÍCONES VÁLIDOS
  static icons = {
    // 🏥 NAVEGAÇÃO PRINCIPAL
    navigation: {
      dashboard: { outline: Home, filled: Home, duotone: Home },
      patients: { outline: Users, filled: Users, duotone: Users },
      anamnesis: { outline: Clipboard, filled: Clipboard, duotone: Clipboard },
      prescription: { outline: Pill, filled: Pill, duotone: Pill },
      exams: { outline: TestTube, filled: TestTube, duotone: TestTube },
      emergency: { outline: Zap, filled: Zap, duotone: Zap },
      schedule: { outline: Calendar, filled: CalendarCheck, duotone: CalendarDays },
      reports: { outline: BarChart, filled: BarChart, duotone: BarChart },
      chat: { outline: MessageSquare, filled: MessageSquare, duotone: MessageSquare },
      kanban: { outline: Target, filled: Target, duotone: Target },
      medications: { outline: Pill, filled: Package, duotone: Gift },
    },

    // 🩺 ESPECIALIDADES MÉDICAS
    specialties: {
      cardiology: { outline: Heart, filled: Heart, duotone: HeartHandshake },
      neurology: { outline: Brain, filled: BrainCircuit, duotone: Brain },
      pediatrics: { outline: Baby, filled: Baby, duotone: Heart },
      geriatrics: { outline: Shield, filled: Shield, duotone: Award },
      gynecology: { outline: UserCheck, filled: User, duotone: UserCheck },
      orthopedics: { outline: Bone, filled: Bone, duotone: Shield },
      psychiatry: { outline: Brain, filled: BrainCircuit, duotone: Heart },
      dermatology: { outline: Eye, filled: Eye, duotone: Scan },
      pneumology: { outline: Wind, filled: Activity, duotone: Wind },
      gastroenterology: { outline: Activity, filled: TrendingUp, duotone: Activity },
      endocrinology: { outline: TestTube, filled: FlaskConical, duotone: TestTube },
      ophthalmology: { outline: Eye, filled: Eye, duotone: Scan },
      otorhinolaryngology: { outline: Ear, filled: Ear, duotone: Activity },
      urology: { outline: Droplet, filled: Droplet, duotone: TestTube },
      oncology: { outline: Target, filled: CircleDot, duotone: Shield },
      radiology: { outline: Scan, filled: Monitor, duotone: Image },
      anesthesiology: { outline: Syringe, filled: Syringe, duotone: Activity },
      pathology: { outline: Microscope, filled: Microscope, duotone: Search },
      surgery: { outline: MedicalBandage, filled: Cross, duotone: Zap },
      emergency: { outline: Zap, filled: Truck, duotone: AlertTriangle },
    },

    // 🚨 STATUS E ALERTAS MÉDICOS
    status: {
      critical: { outline: AlertTriangle, filled: AlertTriangle, duotone: Zap },
      urgent: { outline: AlertCircle, filled: AlertCircle, duotone: Circle },
      moderate: { outline: Info, filled: CircleDot, duotone: Info },
      stable: { outline: CheckCircle, filled: CheckCircle, duotone: Check },
      resolved: { outline: Check, filled: CheckCircle, duotone: FileCheck },
      pending: { outline: Clock, filled: Timer, duotone: Clock },
      cancelled: { outline: X, filled: X, duotone: FileX },
      scheduled: { outline: Calendar, filled: CalendarCheck, duotone: CalendarDays },
      inProgress: { outline: RefreshCw, filled: Activity, duotone: TrendingUp },
      completed: { outline: CheckCircle, filled: Trophy, duotone: Medal },
      review: { outline: Eye, filled: Search, duotone: Eye },
      approved: { outline: Check, filled: Trophy, duotone: Award },
      rejected: { outline: X, filled: AlertTriangle, duotone: X },
    },

    // ⚕️ AÇÕES MÉDICAS
    actions: {
      examine: { outline: Stethoscope, filled: Stethoscope, duotone: Eye },
      prescribe: { outline: Pill, filled: Package, duotone: Gift },
      inject: { outline: Syringe, filled: Syringe, duotone: Zap },
      monitor: { outline: Monitor, filled: Activity, duotone: TrendingUp },
      measure: { outline: Thermometer, filled: Thermometer, duotone: BarChart },
      document: { outline: FileText, filled: Clipboard, duotone: FilePlus },
      approve: { outline: CheckCircle, filled: Trophy, duotone: Award },
      alert: { outline: AlertCircle, filled: Bell, duotone: Zap },
      diagnose: { outline: Target, filled: Search, duotone: Eye },
      treat: { outline: Heart, filled: HeartHandshake, duotone: Shield },
      operate: { outline: MedicalBandage, filled: Cross, duotone: Target },
      discharge: { outline: ArrowRight, filled: LogOut, duotone: ArrowRight },
      admit: { outline: ArrowLeft, filled: LogIn, duotone: ArrowLeft },
      transfer: { outline: ArrowRight, filled: Truck, duotone: Navigation },
      refer: { outline: Send, filled: Mail, duotone: ArrowRight },
      consult: { outline: MessageSquare, filled: Phone, duotone: Video },
      follow: { outline: Eye, filled: Target, duotone: Search },
      schedule: { outline: Calendar, filled: CalendarCheck, duotone: Clock },
      cancel: { outline: X, filled: AlertTriangle, duotone: X },
      reschedule: { outline: RotateCcw, filled: RefreshCw, duotone: Calendar },
    },

    // 📊 DADOS E MÉTRICAS MÉDICAS
    metrics: {
      heartRate: { outline: Activity, filled: Heart, duotone: TrendingUp },
      bloodPressure: { outline: TrendingUp, filled: BarChart, duotone: Activity },
      temperature: { outline: Thermometer, filled: Flame, duotone: TrendingUp },
      weight: { outline: BarChart, filled: TrendingUp, duotone: LineChart },
      height: { outline: ArrowUp, filled: TrendingUp, duotone: BarChart },
      bmi: { outline: BarChart, filled: PieChart, duotone: Target },
      glucose: { outline: Droplet, filled: TestTube, duotone: FlaskConical },
      oxygen: { outline: Wind, filled: Activity, duotone: TrendingUp },
      pain: { outline: AlertTriangle, filled: Zap, duotone: AlertCircle },
      mobility: { outline: Users, filled: User, duotone: ArrowUp },
      cognition: { outline: Brain, filled: BrainCircuit, duotone: Target },
      mood: { outline: Sun, filled: Star, duotone: Heart },
      sleep: { outline: Moon, filled: Clock, duotone: Timer },
      appetite: { outline: Plus, filled: CircleDot, duotone: Target },
      hydration: { outline: Droplet, filled: TestTube, duotone: FlaskConical },
    },

    // 🧪 EXAMES E TESTES
    tests: {
      bloodTest: { outline: Droplet, filled: TestTube, duotone: FlaskConical },
      urine: { outline: TestTube, filled: FlaskConical, duotone: Droplet },
      xray: { outline: Scan, filled: Monitor, duotone: Image },
      mri: { outline: Monitor, filled: Scan, duotone: Brain },
      ct: { outline: Scan, filled: Target, duotone: Monitor },
      ultrasound: { outline: Activity, filled: Monitor, duotone: Eye },
      ecg: { outline: Activity, filled: Heart, duotone: TrendingUp },
      eeg: { outline: Brain, filled: BrainCircuit, duotone: Activity },
      biopsy: { outline: Microscope, filled: Target, duotone: Search },
      culture: { outline: FlaskConical, filled: Microscope, duotone: TestTube },
      genetic: { outline: Dna, filled: TestTube, duotone: Target },
      pathology: { outline: Microscope, filled: Search, duotone: Eye },
      lab: { outline: FlaskConical, filled: TestTube, duotone: Monitor },
      imaging: { outline: Camera, filled: Scan, duotone: Image },
      endoscopy: { outline: Eye, filled: Search, duotone: Target },
    },

    // 💊 MEDICAMENTOS E TRATAMENTOS
    medications: {
      pill: { outline: Pill, filled: Circle, duotone: Package },
      injection: { outline: Syringe, filled: Zap, duotone: Activity },
      iv: { outline: Droplet, filled: TestTube, duotone: TrendingUp },
      topical: { outline: MedicalBandage, filled: Package, duotone: Gift },
      inhaler: { outline: Wind, filled: Activity, duotone: Cloud },
      drops: { outline: Droplet, filled: Eye, duotone: Ear },
      patch: { outline: MedicalBandage, filled: Square, duotone: Package },
      suppository: { outline: Pill, filled: Triangle, duotone: Package },
      spray: { outline: Wind, filled: Cloud, duotone: Droplet },
      tablet: { outline: Pill, filled: Square, duotone: Package },
      capsule: { outline: Pill, filled: Hexagon, duotone: Circle },
      liquid: { outline: Droplet, filled: TestTube, duotone: FlaskConical },
      powder: { outline: Package, filled: Snowflake, duotone: Gift },
      cream: { outline: Package, filled: Droplet, duotone: Circle },
      ointment: { outline: MedicalBandage, filled: Package, duotone: Gift },
    },

    // 👥 PESSOAS E FUNÇÕES
    people: {
      doctor: { outline: UserCheck, filled: Stethoscope, duotone: Award },
      nurse: { outline: Users, filled: Heart, duotone: Shield },
      patient: { outline: User, filled: UserCheck, duotone: Heart },
      family: { outline: Users, filled: Heart, duotone: Shield },
      specialist: { outline: Award, filled: Crown, duotone: Medal },
      resident: { outline: UserPlus, filled: User, duotone: Award },
      student: { outline: UserPlus, filled: User, duotone: Bookmark },
      admin: { outline: Settings, filled: Briefcase, duotone: Key },
      tech: { outline: Laptop, filled: Monitor, duotone: Database },
      pharmacist: { outline: Pill, filled: Package, duotone: FlaskConical },
      therapist: { outline: Heart, filled: Shield, duotone: Activity },
      social: { outline: Users, filled: Heart, duotone: Shield },
      chaplain: { outline: Heart, filled: Star, duotone: Sun },
      volunteer: { outline: Gift, filled: Heart, duotone: Award },
      visitor: { outline: Eye, filled: User, duotone: Users },
    },

    // 🎯 INTERFACE E NAVEGAÇÃO
    interface: {
      home: { outline: Home, filled: Building, duotone: Home },
      back: { outline: ArrowLeft, filled: ChevronLeft, duotone: ArrowLeft },
      forward: { outline: ArrowRight, filled: ChevronRight, duotone: ArrowRight },
      up: { outline: ArrowUp, filled: ChevronUp, duotone: TrendingUp },
      down: { outline: ArrowDown, filled: ChevronDown, duotone: TrendingDown },
      menu: { outline: MoreHorizontal, filled: MoreVertical, duotone: MoreHorizontal },
      close: { outline: X, filled: X, duotone: Circle },
      add: { outline: Plus, filled: Plus, duotone: CircleDot },
      remove: { outline: Minus, filled: Minus, duotone: X },
      edit: { outline: Edit, filled: Edit, duotone: FileText },
      save: { outline: Save, filled: Download, duotone: Check },
      copy: { outline: Copy, filled: Clipboard, duotone: FileText },
      delete: { outline: X, filled: X, duotone: AlertTriangle },
      search: { outline: Search, filled: Eye, duotone: Target },
      filter: { outline: Filter, filled: Filter, duotone: ArrowUpDown },
      sort: { outline: ArrowUpDown, filled: TrendingUp, duotone: BarChart },
      refresh: { outline: RefreshCw, filled: RotateCcw, duotone: Repeat },
      settings: { outline: Settings, filled: Settings, duotone: Key },
      help: { outline: HelpCircle, filled: Info, duotone: Circle },
      info: { outline: Info, filled: CircleDot, duotone: Eye },
      warning: { outline: AlertTriangle, filled: AlertCircle, duotone: Zap },
      error: { outline: AlertCircle, filled: X, duotone: AlertTriangle },
      success: { outline: CheckCircle, filled: Check, duotone: Trophy },
    },

    // 🌊 WELLWAVE ESPECÍFICOS
    wellwave: {
      logo: { outline: Activity, filled: Heart, duotone: TrendingUp },
      wave: { outline: Activity, filled: TrendingUp, duotone: Activity },
      flow: { outline: TrendingUp, filled: Activity, duotone: ArrowUp },
      pulse: { outline: Activity, filled: Heart, duotone: TrendingUp },
      gradient: { outline: Star, filled: Crown, duotone: Award },
      premium: { outline: Crown, filled: Award, duotone: Medal },
      medical: { outline: Stethoscope, filled: Cross, duotone: Heart },
      digital: { outline: Monitor, filled: Smartphone, duotone: Globe },
      smart: { outline: Brain, filled: BrainCircuit, duotone: Target },
      ai: { outline: Bot, filled: BrainCircuit, duotone: Zap },
      analytics: { outline: BarChart, filled: TrendingUp, duotone: LineChart },
      insights: { outline: Eye, filled: Target, duotone: Search },
      prediction: { outline: Target, filled: TrendingUp, duotone: Eye },
      optimization: { outline: TrendingUp, filled: Zap, duotone: Target },
      automation: { outline: RefreshCw, filled: Bot, duotone: Settings },
    }
  };

  /**
   * Renderiza um ícone médico com variante específica
   */
  static renderIcon(
    name: string, 
    variant: IconVariant = IconVariant.OUTLINE, 
    size: IconSize | number = IconSize.MD, 
    color: string = 'currentColor',
    category: string = 'interface'
  ): JSX.Element {
    const iconData = this.icons[category]?.[name];
    
    if (!iconData) {
      console.warn(`WellWave Medical Icon não encontrado: ${category}.${name}`);
      return React.createElement(Info, { size: typeof size === 'number' ? size : size, color });
    }

    const IconComponent = iconData[variant] || iconData[IconVariant.OUTLINE];
    
    return React.createElement(IconComponent, {
      size: typeof size === 'number' ? size : size,
      color,
      className: `medical-icon medical-icon-${variant} medical-icon-${name}`
    });
  }

  /**
   * Ícones animados para estados específicos
   */
  static animatedIcons = {
    heartbeat: (size: number = 24, color: string = 'var(--medical-primary)') => (
      <div className="animate-heartbeat" style={{ color }}>
        <Activity size={size} className="pulse-ecg" />
      </div>
    ),

    loading: (size: number = 60) => (
      <div className="dna-helix" style={{ width: size, height: size }}>
        <RefreshCw size={size * 0.6} className="animate-spin" />
      </div>
    ),

    pulse: (size: number = 24, color: string = 'var(--medical-primary)') => (
      <div className="wave-pulse" style={{ color }}>
        <Activity size={size} />
      </div>
    ),

    wave: (size: number = 32, color: string = 'var(--wave-primary)') => (
      <div className="wave-motion" style={{ color }}>
        <Activity size={size} />
      </div>
    ),

    spinner: (size: number = 24, color: string = 'var(--wave-primary)') => (
      <div className="animate-spin" style={{ color }}>
        <RefreshCw size={size} />
      </div>
    )
  };

  /**
   * Utilitários para obter ícones por contexto
   */
  static getByPriority = (priority: 'baixa' | 'media' | 'alta' | 'emergencia') => {
    const mapping = {
      'baixa': 'stable',
      'media': 'moderate', 
      'alta': 'urgent',
      'emergencia': 'critical'
    };
    return this.renderIcon(mapping[priority], IconVariant.FILLED, IconSize.MD, '', 'status');
  };

  static getByStatus = (status: 'triagem' | 'atendimento' | 'exames' | 'alta' | 'internacao') => {
    const mapping = {
      'triagem': 'pending',
      'atendimento': 'inProgress',
      'exames': 'examine',
      'alta': 'discharge', 
      'internacao': 'admit'
    };
    return this.renderIcon(mapping[status], IconVariant.DUOTONE, IconSize.MD, '', status === 'exames' ? 'actions' : 'status');
  };

  static getByAge = (ageGroup: 'pediatrico' | 'adulto' | 'idoso') => {
    const mapping = {
      'pediatrico': 'pediatrics',
      'adulto': 'patient',
      'idoso': 'geriatrics'
    };
    return this.renderIcon(mapping[ageGroup], IconVariant.FILLED, IconSize.MD, '', ageGroup === 'adulto' ? 'people' : 'specialties');
  };

  static getBySpecialty = (specialty: string) => {
    const normalizedSpecialty = specialty.toLowerCase().replace(/\s+/g, '');
    const found = Object.keys(this.icons.specialties).find(key => 
      key.toLowerCase().includes(normalizedSpecialty) || 
      normalizedSpecialty.includes(key.toLowerCase())
    );
    
    return found 
      ? this.renderIcon(found, IconVariant.DUOTONE, IconSize.MD, '', 'specialties')
      : this.renderIcon('doctor', IconVariant.OUTLINE, IconSize.MD, '', 'people');
  };
}

/**
 * 🎨 Componente React para Ícone Médico Premium
 */
export const MedicalIcon: React.FC<MedicalIconProps> = ({
  name,
  variant = IconVariant.OUTLINE,
  size = IconSize.MD,
  color = 'currentColor',
  className = '',
  animated = false,
  tooltip,
  onClick
}) => {
  // Encontrar categoria automaticamente
  const findIconCategory = (iconName: string): string => {
    for (const [category, icons] of Object.entries(MedicalIconSystem.icons)) {
      if (icons[iconName]) {
        return category;
      }
    }
    return 'interface'; // fallback
  };

  const category = findIconCategory(name);
  const iconData = MedicalIconSystem.icons[category]?.[name];
  
  if (!iconData) {
    console.warn(`WellWave Medical Icon não encontrado: ${name}`);
    return <Info size={size} color={color} className={className} />;
  }

  const IconComponent = iconData[variant] || iconData[IconVariant.OUTLINE];
  
  const iconElement = (
    <div 
      className={`medical-icon-wrapper ${animated ? 'transition-wave hover:scale-110' : ''} ${className}`}
      onClick={onClick}
      onKeyDown={onClick ? (e) => e.key === 'Enter' && onClick(e as any) : undefined}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      style={{ 
        cursor: onClick ? 'pointer' : 'default',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      title={tooltip || name}
    >
      <IconComponent 
        size={typeof size === 'number' ? size : size}
        color={color}
        className={`medical-icon medical-icon-${variant} medical-icon-${name}`}
      />
      {animated && <div className="icon-pulse absolute inset-0 rounded-full opacity-0 hover:opacity-20 bg-current transition-opacity" />}
    </div>
  );

  return tooltip ? (
    <div className="relative group">
      {iconElement}
      <div className="icon-tooltip absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-black rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
        {tooltip}
      </div>
    </div>
  ) : iconElement;
};

/**
 * 🎯 Hook para usar ícones médicos facilmente
 */
export function useMedicalIcon(name: string, category?: string) {
  const findCategory = () => {
    if (category && MedicalIconSystem.icons[category]?.[name]) {
      return category;
    }
    
    for (const [cat, icons] of Object.entries(MedicalIconSystem.icons)) {
      if (icons[name]) {
        return cat;
      }
    }
    return 'interface';
  };

  const iconCategory = findCategory();
  const iconData = MedicalIconSystem.icons[iconCategory]?.[name];
  
  return {
    category: iconCategory,
    exists: !!iconData,
    render: (variant: IconVariant = IconVariant.OUTLINE, size: IconSize | number = IconSize.MD, color?: string) => 
      MedicalIconSystem.renderIcon(name, variant, size, color, iconCategory),
    component: iconData?.[IconVariant.OUTLINE] || Info
  };
}

/**
 * 📋 Utilitários adicionais para ícones
 */
export const IconUtils = {
  getByPriority: MedicalIconSystem.getByPriority,
  getByStatus: MedicalIconSystem.getByStatus,
  getByAge: MedicalIconSystem.getByAge,
  getBySpecialty: MedicalIconSystem.getBySpecialty,
  
  getAllCategories: () => Object.keys(MedicalIconSystem.icons),
  getIconsInCategory: (category: string) => Object.keys(MedicalIconSystem.icons[category] || {}),
  
  searchIcons: (query: string) => {
    const results: Array<{category: string, name: string}> = [];
    const searchTerm = query.toLowerCase();
    
    Object.entries(MedicalIconSystem.icons).forEach(([category, icons]) => {
      Object.keys(icons).forEach(name => {
        if (name.toLowerCase().includes(searchTerm) || 
            category.toLowerCase().includes(searchTerm)) {
          results.push({ category, name });
        }
      });
    });
    
    return results;
  },
  
  getEmergencyIcons: () => ['critical', 'urgent', 'emergency', 'alert'],
  getStatusIcons: () => ['stable', 'pending', 'inProgress', 'completed', 'cancelled'],
  getSpecialtyIcons: () => Object.keys(MedicalIconSystem.icons.specialties),
  getActionIcons: () => Object.keys(MedicalIconSystem.icons.actions)
};

export default MedicalIconSystem;