export type PatientSex = 'M' | 'F' | '';
export type AgeGroup = 'adulto' | 'pediatrico' | 'idoso' | '';

export interface GenderAgreement {
  article: string;           // o/a
  articleDef: string;        // do/da  
  articleIndef: string;      // um/uma
  articlePrep: string;       // ao/à
  articleLoc: string;        // no/na
  pronoun: string;           // ele/ela
  patientTerm: string;       // paciente (neutro) mas com concordância
  participleEnding: string;  // -o/-a para particípios
}

export interface PatientDescription {
  sex: PatientSex;
  ageGroup: AgeGroup;
  isPregnant?: boolean;
}

/**
 * Retorna os artigos e concordâncias corretas baseadas no sexo
 */
export const getGenderAgreement = (sex: PatientSex): GenderAgreement => {
  if (sex === 'F') {
    return {
      article: 'a',
      articleDef: 'da',
      articleIndef: 'uma',
      articlePrep: 'à',
      articleLoc: 'na',
      pronoun: 'ela',
      patientTerm: 'paciente',
      participleEnding: 'a'
    };
  } else {
    return {
      article: 'o',
      articleDef: 'do',
      articleIndef: 'um',
      articlePrep: 'ao',
      articleLoc: 'no',
      pronoun: 'ele',
      patientTerm: 'paciente',
      participleEnding: 'o'
    };
  }
};

/**
 * Gera descrição do paciente de forma anônima
 */
export const getPatientDescription = ({ sex, ageGroup, isPregnant }: PatientDescription): string => {
  const agreement = getGenderAgreement(sex);
  
  let description = `${agreement.article} paciente`;
  
  // Adiciona faixa etária
  switch (ageGroup) {
    case 'pediatrico':
      description += sex === 'F' ? ' pediátrica' : ' pediátrico';
      break;
    case 'idoso':
      description += sex === 'F' ? ' idosa' : ' idoso';
      break;
    case 'adulto':
    default:
      description += sex === 'F' ? ' adulta' : ' adulto';
      break;
  }
  
  // Adiciona informação de gravidez se aplicável
  if (sex === 'F' && isPregnant) {
    description += ' gestante';
  }
  
  return description;
};

/**
 * Aplica concordância de gênero em um texto usando placeholders
 * 
 * Placeholders disponíveis:
 * - {article} -> o/a
 * - {articleDef} -> do/da
 * - {articleIndef} -> um/uma
 * - {articlePrep} -> ao/à
 * - {articleLoc} -> no/na
 * - {pronoun} -> ele/ela
 * - {patientTerm} -> paciente
 * - {participleEnding} -> o/a
 * - {patientDescription} -> descrição completa
 */
export const applyGenderAgreement = (
  text: string, 
  patientData: PatientDescription
): string => {
  const agreement = getGenderAgreement(patientData.sex);
  const description = getPatientDescription(patientData);
  
  return text
    .replace(/\{article\}/g, agreement.article)
    .replace(/\{articleDef\}/g, agreement.articleDef)
    .replace(/\{articleIndef\}/g, agreement.articleIndef)
    .replace(/\{articlePrep\}/g, agreement.articlePrep)
    .replace(/\{articleLoc\}/g, agreement.articleLoc)
    .replace(/\{pronoun\}/g, agreement.pronoun)
    .replace(/\{patientTerm\}/g, agreement.patientTerm)
    .replace(/\{participleEnding\}/g, agreement.participleEnding)
    .replace(/\{patientDescription\}/g, description);
};

/**
 * Exemplos de uso:
 * 
 * const text = "{article} paciente foi encaminh{participleEnding} {articlePrep} especialista"
 * 
 * Para mulher adulta:
 * "A paciente foi encaminhada à especialista"
 * 
 * Para homem adulto:
 * "O paciente foi encaminhado ao especialista"
 */