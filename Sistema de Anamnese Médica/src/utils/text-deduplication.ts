/**
 * Utilitário para deduplicação de texto preservando contexto linguístico
 */
// React import removed - no longer using hooks

// Remove acentos e normaliza para comparação
const stripAccents = (s: string) => s.normalize('NFD').replace(/\p{M}/gu, '')
const norm = (s: string) => stripAccents(s).toLowerCase().trim()

/**
 * Remove acentos de uma string para comparação
 */
function removeAccents(str: string): string {
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

/**
 * Normaliza uma string para comparação (remove acentos, converte para lowercase)
 */
function normalizeForComparison(str: string): string {
  return removeAccents(str.trim().toLowerCase());
}

/** Deduplica um array de strings preservando ordem (case/acento-insensitive) */
export function dedupeStrings(items: string[]): string[] {
  const seen = new Set<string>()
  const out: string[] = []
  for (const raw of items) {
    const key = norm(raw)
    if (!key) continue
    if (!seen.has(key)) {
      seen.add(key)
      out.push(raw.trim())
    }
  }
  return out
}

/** Checagem inclusiva (case/acento-insensitive) */
export function hasStringCI(list: string[], candidate: string): boolean {
  const key = norm(candidate)
  return list.some(item => norm(item) === key)
}

/**
 * Deduplica tokens separados por vírgula, ponto e vírgula ou espaço
 * Mantém ordem original e preserva pontuação
 */
export function deduplicateTokens(text: string, separators: string[] = [',', ';', ' ']): string {
  if (!text || typeof text !== 'string') return text;
  
  // Detecta o separador principal usado no texto
  const mainSeparator = separators.find(sep => text.includes(sep)) || ',';
  
  // Separa por vírgula ou ponto e vírgula (mais comum em listas médicas)
  const tokens = text.split(new RegExp(`[${separators.join('')}]`)).map(token => token.trim());
  
  const seen = new Set<string>();
  const deduplicatedTokens: string[] = [];
  
  for (const token of tokens) {
    if (!token) continue;
    
    const normalizedToken = normalizeForComparison(token);
    
    if (!seen.has(normalizedToken)) {
      seen.add(normalizedToken);
      deduplicatedTokens.push(token);
    }
  }
  
  return deduplicatedTokens.join(mainSeparator === ' ' ? ' ' : `${mainSeparator} `);
}

/**
 * Deduplica palavras consecutivas idênticas
 * Ex: "dor dor abdominal" → "dor abdominal"
 */
export function deduplicateConsecutive(text: string): string {
  if (!text || typeof text !== 'string') return text;
  
  const words = text.split(/\s+/);
  const result: string[] = [];
  
  for (let i = 0; i < words.length; i++) {
    const currentWord = words[i];
    const previousWord = words[i - 1];
    
    // Só adiciona se não for igual à palavra anterior (case e accent insensitive)
    if (!previousWord || normalizeForComparison(currentWord) !== normalizeForComparison(previousWord)) {
      result.push(currentWord);
    }
  }
  
  return result.join(' ');
}

/**
 * API principal: segura para usar em textos médicos
 */
export function deduplicateMedicalList(input: string): string {
  if (!input) return input
  const lines = input.split(/\r?\n/)
  const processed = lines.map(l => {
    const withLists = dedupeCommaLists(l)
    return dedupeConsecutive(withLists)
  })
  return processed.join('\n')
}

/**
 * Deduplica arrays de strings (para uso em componentes)
 */
export function deduplicateArray<T extends string>(
  items: T[], 
  keyExtractor?: (item: T) => string
): T[] {
  const seen = new Set<string>();
  const result: T[] = [];
  
  for (const item of items) {
    const key = keyExtractor ? keyExtractor(item) : item;
    const normalizedKey = normalizeForComparison(key);
    
    if (!seen.has(normalizedKey)) {
      seen.add(normalizedKey);
      result.push(item);
    }
  }
  
  return result;
}

/** Deduplica listas separadas por vírgula/; dentro de uma linha */
function dedupeCommaLists(line: string): string {
  if (!/,|;/.test(line)) return line
  // Se houver dois-pontos, dedupe só a parte "da lista" após o último ':'
  const idx = line.lastIndexOf(':')
  const head = idx !== -1 ? line.slice(0, idx + 1) : ''
  const tail = idx !== -1 ? line.slice(idx + 1) : line
  const parts = tail.split(/\s*[,;]\s*/)
  const deduped = dedupeStrings(parts)
  return head ? `${head} ${deduped.join(', ')}` : deduped.join(', ')
}

/** Colapsa duplicações consecutivas por espaço dentro da linha */
function dedupeConsecutive(line: string): string {
  const words = line.split(/\s+/)
  const out: string[] = []
  let prev = ''
  for (const w of words) {
    const k = norm(w)
    if (!k) continue
    if (k !== prev) out.push(w)
    prev = k
  }
  return out.join(' ')
}

/**
 * Função para deduplicação (não é mais um hook)
 * Use deduplicateMedicalList() diretamente para evitar problemas com Rules of Hooks
 */
export function getDeduplicatedText(text?: string | null): string {
  return text ? deduplicateMedicalList(text) : '';
}

/**
 * Exemplos de uso:
 * 
 * deduplicateTokens("Hipertensão, Diabetes, hipertensão, diabetes") 
 * → "Hipertensão, Diabetes"
 * 
 * deduplicateConsecutive("paciente paciente refere dor dor torácica")
 * → "paciente refere dor torácica"
 * 
 * deduplicateMedicalList("Dor torácica, cefaleia, dor torácica")
 * → "Dor torácica, cefaleia"
 */