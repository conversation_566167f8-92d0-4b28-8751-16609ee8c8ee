export interface SidebarConfig {
  // Timing configurations - ajustados para premium experience
  hoverIntentMs: number;
  hoverLeaveMs: number;
  idleMs: number;
  collapsedWidth: number;
  defaultWidth: number;
  minWidth: number;
  maxWidth: number;
  
  // Resize configurations
  resizeStep: number;
  
  // Animation configurations - mais sofisticadas
  springConfig: {
    type: "spring";
    damping: number;
    stiffness: number;
    restDelta: number;
  };
  
  // Transition timings refinados
  transitions: {
    width: string;
    opacity: string;
    transform: string;
    slow: string;
    medium: string;
    fast: string;
  };
  
  // Storage keys
  storageKeys: {
    width: string;
    collapsed: string;
  };
}

export const sidebarConfig: SidebarConfig = {
  // Hover and auto-collapse timings - mais suaves
  hoverIntentMs: 200,      // Mais tempo para intent
  hoverLeaveMs: 300,       // Delay maior ao sair
  idleMs: 2000,           // Mais tempo antes de auto-hide
  
  // Width configurations - melhor proporção
  collapsedWidth: 72,      // Ligeiramente maior quando collapsed
  defaultWidth: 320,       // Mai<PERSON> espa<PERSON>oso por padrão
  minWidth: 280,          // Mínimo mais generoso
  maxWidth: 480,          // Máximo mais amplo
  
  // Resize step for keyboard navigation
  resizeStep: 12,
  
  // Spring animation mais suave e elegante
  springConfig: {
    type: "spring" as const,
    damping: 25,            // Mais damping para suavidade
    stiffness: 200,         // Menos stiffness para movimento mais fluido
    restDelta: 0.01         // Precisão maior no rest
  },
  
  // Timing transitions refinados
  transitions: {
    width: "400ms cubic-bezier(0.23, 1, 0.32, 1)",      // Easing mais suave
    opacity: "250ms cubic-bezier(0.4, 0, 0.2, 1)",      // Fade elegante
    transform: "300ms cubic-bezier(0.34, 1.56, 0.64, 1)", // Bounce sutil
    slow: "600ms cubic-bezier(0.23, 1, 0.32, 1)",       // Animações lentas
    medium: "400ms cubic-bezier(0.4, 0, 0.2, 1)",       // Médias
    fast: "200ms cubic-bezier(0.4, 0, 0.2, 1)"          // Rápidas
  },
  
  // LocalStorage keys
  storageKeys: {
    width: "wellwave-sidebar-width",
    collapsed: "wellwave-sidebar-collapsed"
  }
};

export type SidebarState = {
  collapsed: boolean;
  width: number;
  isHovering: boolean;
  isResizing: boolean;
  isMobile: boolean;
  isDrawerOpen: boolean;
  isTransitioning: boolean;
};

export type SidebarActions = {
  toggle: () => void;
  collapse: () => void;
  expand: () => void;
  setWidth: (width: number) => void;
  setHovering: (hovering: boolean) => void;
  setResizing: (resizing: boolean) => void;
  setTransitioning: (transitioning: boolean) => void;
  openDrawer: () => void;
  closeDrawer: () => void;
};