import { ExtendedAnamneseData } from '../../components/MedicalContext'
import { AIAnalysisResult } from '../../types/ai-suggestions.types'

export interface MedicalReport {
  id: string
  anamnesis: ExtendedAnamneseData
  generatedAt: Date
  aiAnalysis?: AIAnalysisResult
  exportFormats: {
    txt: string
    html: string
    pdf?: Blob
    docx?: Blob
  }
  metadata: {
    version: string
    generatedBy: string
    reviewedBy?: string
    tags?: string[]
  }
}

export class ReportGenerator {
  private static instance: ReportGenerator
  
  private constructor() {}
  
  static getInstance(): ReportGenerator {
    if (!ReportGenerator.instance) {
      ReportGenerator.instance = new ReportGenerator()
    }
    return ReportGenerator.instance
  }

  generateReport(
    anamnesis: ExtendedAnamneseData,
    aiAnalysis?: AIAnalysisResult
  ): MedicalReport {
    const reportId = this.generateReportId()
    
    return {
      id: reportId,
      anamnesis,
      generatedAt: new Date(),
      aiAnalysis,
      exportFormats: {
        txt: this.generateTxtFormat(anamnesis, aiAnalysis),
        html: this.generateHtmlFormat(anamnesis, aiAnalysis)
      },
      metadata: {
        version: '1.0.0',
        generatedBy: 'WellWave Medical System',
        tags: this.generateTags(anamnesis)
      }
    }
  }

  private generateReportId(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 9)
    return `REP-${timestamp}-${random}`.toUpperCase()
  }

  private generateTags(anamnesis: ExtendedAnamneseData): string[] {
    const tags: string[] = []
    
    if (anamnesis.paciente.faixaEtaria) {
      tags.push(anamnesis.paciente.faixaEtaria)
    }
    
    if (anamnesis.queixaPrincipal.intensidade >= 7) {
      tags.push('alta-intensidade')
    }
    
    if (anamnesis.historicoMedico.comorbidades.length > 3) {
      tags.push('multiplas-comorbidades')
    }
    
    return tags
  }

  private generateTxtFormat(
    anamnesis: ExtendedAnamneseData,
    aiAnalysis?: AIAnalysisResult
  ): string {
    let report = ''
    
    // Header
    report += '='.repeat(80) + '\n'
    report += '🌊 WELLWAVE - RELATÓRIO MÉDICO COMPLETO\n'
    report += '='.repeat(80) + '\n\n'
    
    // Patient Info
    report += this.formatPatientSection(anamnesis)
    
    // Chief Complaint
    report += this.formatChiefComplaintSection(anamnesis)
    
    // Medical History
    report += this.formatMedicalHistorySection(anamnesis)
    
    // Physical Exam
    report += this.formatPhysicalExamSection(anamnesis)
    
    // Current Medications
    report += this.formatMedicationsSection(anamnesis)
    
    // Assessment and Plan
    report += this.formatAssessmentSection(anamnesis)
    
    // AI Analysis (if available)
    if (aiAnalysis) {
      report += this.formatAIAnalysisSection(aiAnalysis)
    }
    
    // Footer
    report += '\n' + '='.repeat(80) + '\n'
    report += `Relatório gerado em: ${new Date().toLocaleString('pt-BR')}\n`
    report += 'WellWave Medical System - v1.0.0\n'
    
    return report
  }

  private formatPatientSection(anamnesis: ExtendedAnamneseData): string {
    const { paciente } = anamnesis
    let section = '📋 IDENTIFICAÇÃO DO PACIENTE\n'
    section += '-'.repeat(40) + '\n'
    section += `ID: ${paciente.id}\n`
    section += `Faixa Etária: ${paciente.faixaEtaria || 'Não informado'}\n`
    section += `Sexo Biológico: ${this.formatSex(paciente.sexoBiologico)}\n`
    
    if (paciente.gestante) {
      section += '🤰 Gestante: Sim\n'
    }
    
    if (paciente.telefone && paciente.consentimentoWhatsApp) {
      section += `Contato: ${paciente.telefone} (WhatsApp autorizado)\n`
    }
    
    section += `Data/Hora: ${paciente.timestamp.toLocaleString('pt-BR')}\n`
    section += '\n'
    
    return section
  }

  private formatChiefComplaintSection(anamnesis: ExtendedAnamneseData): string {
    const { queixaPrincipal } = anamnesis
    
    if (!queixaPrincipal.queixaPrincipal) return ''
    
    let section = '🔍 QUEIXA PRINCIPAL E HDA\n'
    section += '-'.repeat(40) + '\n'
    section += `Queixa: ${queixaPrincipal.queixaPrincipal}\n`
    
    if (queixaPrincipal.duracaoSintomas) {
      section += `Duração: ${queixaPrincipal.duracaoSintomas}\n`
    }
    
    if (queixaPrincipal.intensidade) {
      section += `Intensidade: ${queixaPrincipal.intensidade}/10`
      section += this.getIntensityEmoji(queixaPrincipal.intensidade) + '\n'
    }
    
    if (queixaPrincipal.caracteristicas.length > 0) {
      section += `Características: ${queixaPrincipal.caracteristicas.join(', ')}\n`
    }
    
    if (queixaPrincipal.fatoresAssociados.length > 0) {
      section += `Fatores Associados: ${queixaPrincipal.fatoresAssociados.join(', ')}\n`
    }
    
    if (queixaPrincipal.observacoes) {
      section += `Observações: ${queixaPrincipal.observacoes}\n`
    }
    
    section += '\n'
    return section
  }

  private formatMedicalHistorySection(anamnesis: ExtendedAnamneseData): string {
    const { historicoMedico } = anamnesis
    let section = '📚 HISTÓRIA MÉDICA PREGRESSA\n'
    section += '-'.repeat(40) + '\n'
    
    // Comorbidities
    section += 'Comorbidades: '
    if (historicoMedico.comorbidades.length > 0) {
      section += '\n'
      historicoMedico.comorbidades.forEach(c => {
        section += `  • ${c}\n`
      })
    } else {
      section += 'Nega antecedentes patológicos relevantes\n'
    }
    
    // Current Medications
    section += '\nMedicamentos em Uso: '
    if (historicoMedico.medicamentosUso.length > 0) {
      section += '\n'
      historicoMedico.medicamentosUso.forEach(m => {
        section += `  • ${m}\n`
      })
    } else {
      section += 'Não faz uso de medicações contínuas\n'
    }
    
    // Allergies
    section += '\nAlergias: '
    if (historicoMedico.alergias.length > 0) {
      section += historicoMedico.alergias.join(', ') + '\n'
    } else {
      section += 'Nega alergias conhecidas\n'
    }
    
    // Previous Surgeries
    if (historicoMedico.cirurgiasAnteriores.length > 0) {
      section += '\nCirurgias Anteriores:\n'
      historicoMedico.cirurgiasAnteriores.forEach(c => {
        section += `  • ${c}\n`
      })
    }
    
    // Lifestyle
    if (historicoMedico.habitosVida.length > 0) {
      section += '\nHábitos de Vida:\n'
      historicoMedico.habitosVida.forEach(h => {
        section += `  • ${h}\n`
      })
    }
    
    // Family History
    if (historicoMedico.historicoFamiliar.length > 0) {
      section += '\nHistória Familiar:\n'
      historicoMedico.historicoFamiliar.forEach(h => {
        section += `  • ${h}\n`
      })
    }
    
    section += '\n'
    return section
  }

  private formatPhysicalExamSection(anamnesis: ExtendedAnamneseData): string {
    const { exameFisico } = anamnesis
    let section = '🩺 EXAME FÍSICO\n'
    section += '-'.repeat(40) + '\n'
    
    // Vital Signs
    const sv = exameFisico.sinaisVitais
    if (Object.values(sv).some(v => v)) {
      section += 'Sinais Vitais:\n'
      if (sv.pa) section += `  • PA: ${sv.pa} mmHg\n`
      if (sv.fc) section += `  • FC: ${sv.fc} bpm\n`
      if (sv.fr) section += `  • FR: ${sv.fr} irpm\n`
      if (sv.temp) section += `  • Temperatura: ${sv.temp}°C\n`
      if (sv.sat) section += `  • SatO2: ${sv.sat}%\n`
      if (sv.glicemia) section += `  • Glicemia: ${sv.glicemia} mg/dL\n`
      section += '\n'
    }
    
    // Physical Examination
    const ef = exameFisico.exameFisico
    if (ef.aspectoGeral) {
      section += `Aspecto Geral: ${ef.aspectoGeral}\n`
    }
    if (ef.cabecaPescoco) {
      section += `Cabeça e Pescoço: ${ef.cabecaPescoco}\n`
    }
    if (ef.torax) {
      section += `Tórax: ${ef.torax}\n`
    }
    if (ef.cardiovascular) {
      section += `Cardiovascular: ${ef.cardiovascular}\n`
    }
    if (ef.respiratorio) {
      section += `Respiratório: ${ef.respiratorio}\n`
    }
    if (ef.abdome) {
      section += `Abdome: ${ef.abdome}\n`
    }
    if (ef.extremidades) {
      section += `Extremidades: ${ef.extremidades}\n`
    }
    if (ef.neurologico) {
      section += `Neurológico: ${ef.neurologico}\n`
    }
    
    if (exameFisico.observacoes) {
      section += `\nObservações: ${exameFisico.observacoes}\n`
    }
    
    section += '\n'
    return section
  }

  private formatMedicationsSection(anamnesis: ExtendedAnamneseData): string {
    const { medicamentos } = anamnesis
    
    if (medicamentos.prescricaoAtual.length === 0) return ''
    
    let section = '💊 PRESCRIÇÃO MÉDICA\n'
    section += '-'.repeat(40) + '\n'
    
    medicamentos.prescricaoAtual.forEach((med, index) => {
      if (med.medicamento) {
        section += `${index + 1}. ${med.medicamento}`
        if (med.dose) section += ` - ${med.dose}`
        if (med.via) section += ` - Via ${med.via}`
        if (med.frequencia) section += ` - ${med.frequencia}`
        if (med.duracao) section += ` por ${med.duracao}`
        section += '\n'
        if (med.orientacoes) {
          section += `   Orientações: ${med.orientacoes}\n`
        }
      }
    })
    
    section += '\n'
    return section
  }

  private formatAssessmentSection(anamnesis: ExtendedAnamneseData): string {
    const { avaliacaoConduta } = anamnesis
    let section = '📊 AVALIAÇÃO E CONDUTA\n'
    section += '-'.repeat(40) + '\n'
    
    if (avaliacaoConduta.hipoteseDiagnostica.length > 0) {
      section += 'Hipóteses Diagnósticas:\n'
      avaliacaoConduta.hipoteseDiagnostica.forEach((h, i) => {
        section += `  ${i + 1}. ${h}\n`
      })
      section += '\n'
    }
    
    if (avaliacaoConduta.condutaImediata.length > 0) {
      section += 'Conduta Imediata:\n'
      avaliacaoConduta.condutaImediata.forEach(c => {
        section += `  • ${c}\n`
      })
      section += '\n'
    }
    
    if (avaliacaoConduta.examesSolicitados.length > 0) {
      section += 'Exames Solicitados:\n'
      avaliacaoConduta.examesSolicitados.forEach(e => {
        section += `  • ${e}\n`
      })
      section += '\n'
    }
    
    if (avaliacaoConduta.retorno) {
      section += `Retorno: ${avaliacaoConduta.retorno}\n`
    }
    
    if (avaliacaoConduta.orientacoes.length > 0) {
      section += '\nOrientações:\n'
      avaliacaoConduta.orientacoes.forEach(o => {
        section += `  • ${o}\n`
      })
    }
    
    section += '\n'
    return section
  }

  private formatAIAnalysisSection(aiAnalysis: AIAnalysisResult): string {
    let section = '🤖 ANÁLISE ASSISTIDA POR IA\n'
    section += '-'.repeat(40) + '\n'
    section += `Confiança da Análise: ${Math.round(aiAnalysis.confidence * 100)}%\n\n`
    
    // Differential Diagnosis
    if (aiAnalysis.differentialDiagnosis.length > 0) {
      section += '📍 Diagnósticos Diferenciais:\n'
      aiAnalysis.differentialDiagnosis.forEach(d => {
        section += `  • ${d.name} (CID-10: ${d.icd10})\n`
        section += `    Probabilidade: ${Math.round(d.probability * 100)}%\n`
        if (d.evidence.length > 0) {
          section += `    Evidências: ${d.evidence.join(', ')}\n`
        }
      })
      section += '\n'
    }
    
    // Treatment Recommendations
    if (aiAnalysis.treatmentRecommendations.length > 0) {
      section += '💊 Recomendações de Tratamento:\n'
      aiAnalysis.treatmentRecommendations.forEach(t => {
        section += `  • ${t.name} (${t.type})\n`
        section += `    ${t.description}\n`
      })
      section += '\n'
    }
    
    // Additional Exams
    if (aiAnalysis.additionalExams.length > 0) {
      section += '🔬 Exames Complementares Sugeridos:\n'
      aiAnalysis.additionalExams.forEach(e => {
        section += `  • ${e.name} (${e.type})\n`
        section += `    Justificativa: ${e.rationale}\n`
        section += `    Urgência: ${e.urgency}\n`
      })
      section += '\n'
    }
    
    // Risk Factors
    if (aiAnalysis.riskFactors.length > 0) {
      section += '⚠️ Fatores de Risco:\n'
      aiAnalysis.riskFactors.forEach(r => {
        const emoji = this.getRiskEmoji(r.level)
        section += `  ${emoji} ${r.factor} - Nível: ${r.level}\n`
        section += `    ${r.description}\n`
      })
      section += '\n'
    }
    
    // Red Flags
    if (aiAnalysis.redFlags.length > 0) {
      section += '🚨 Sinais de Alerta:\n'
      aiAnalysis.redFlags.forEach(flag => {
        section += `  • ${flag}\n`
      })
      section += '\n'
    }
    
    // Clinical Pearls
    if (aiAnalysis.clinicalPearls.length > 0) {
      section += '💡 Pérolas Clínicas:\n'
      aiAnalysis.clinicalPearls.forEach(pearl => {
        section += `  • ${pearl}\n`
      })
      section += '\n'
    }
    
    return section
  }

  private generateHtmlFormat(
    anamnesis: ExtendedAnamneseData,
    aiAnalysis?: AIAnalysisResult
  ): string {
    const txtContent = this.generateTxtFormat(anamnesis, aiAnalysis)
    
    // Convert text to HTML with proper formatting
    const htmlContent = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório Médico - ${anamnesis.paciente.id}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #2563eb;
      border-bottom: 3px solid #2563eb;
      padding-bottom: 10px;
    }
    h2 {
      color: #1e40af;
      margin-top: 25px;
      border-bottom: 1px solid #e5e7eb;
      padding-bottom: 5px;
    }
    .section {
      margin-bottom: 20px;
    }
    .vital-signs {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 10px;
      margin: 15px 0;
    }
    .vital-sign {
      background: #f0f9ff;
      padding: 10px;
      border-radius: 4px;
      border-left: 3px solid #3b82f6;
    }
    .diagnosis {
      background: #fef3c7;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      border-left: 3px solid #f59e0b;
    }
    .risk-high {
      background: #fee2e2;
      border-left-color: #ef4444;
    }
    .risk-medium {
      background: #fed7aa;
      border-left-color: #f97316;
    }
    .risk-low {
      background: #dcfce7;
      border-left-color: #22c55e;
    }
    .metadata {
      color: #6b7280;
      font-size: 0.9em;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e5e7eb;
    }
    @media print {
      body {
        background: white;
      }
      .container {
        box-shadow: none;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🌊 Relatório Médico - WellWave</h1>
    <pre style="white-space: pre-wrap; font-family: inherit;">${this.escapeHtml(txtContent)}</pre>
    <div class="metadata">
      <p>Documento gerado eletronicamente em ${new Date().toLocaleString('pt-BR')}</p>
      <p>WellWave Medical System v1.0.0</p>
    </div>
  </div>
</body>
</html>`
    
    return htmlContent
  }

  private formatSex(sex: string): string {
    switch (sex) {
      case 'M': return 'Masculino'
      case 'F': return 'Feminino'
      default: return 'Não informado'
    }
  }

  private getIntensityEmoji(intensity: number): string {
    if (intensity <= 3) return ' 🟢'
    if (intensity <= 6) return ' 🟡'
    if (intensity <= 8) return ' 🟠'
    return ' 🔴'
  }

  private getRiskEmoji(level: string): string {
    switch (level) {
      case 'critico': return '🔴'
      case 'alto': return '🟠'
      case 'moderado': return '🟡'
      case 'baixo': return '🟢'
      default: return '⚪'
    }
  }

  private escapeHtml(text: string): string {
    const map: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    }
    return text.replace(/[&<>"']/g, m => map[m])
  }
}