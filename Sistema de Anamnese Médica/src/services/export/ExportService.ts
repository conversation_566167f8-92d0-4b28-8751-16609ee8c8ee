import jsPDF from 'jspdf'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, BorderStyle } from 'docx'
import { saveAs } from 'file-saver'
import { MedicalReport } from '../reports/ReportGenerator'

export type ExportFormat = 'txt' | 'html' | 'pdf' | 'docx'

export interface ExportOptions {
  format: ExportFormat
  filename?: string
  includeAIAnalysis?: boolean
  includeMetadata?: boolean
  customStyles?: any
}

export class ExportService {
  private static instance: ExportService
  
  private constructor() {}
  
  static getInstance(): ExportService {
    if (!ExportService.instance) {
      ExportService.instance = new ExportService()
    }
    return ExportService.instance
  }

  async exportReport(
    report: MedicalReport,
    options: ExportOptions
  ): Promise<void> {
    const filename = options.filename || this.generateFilename(report, options.format)
    
    switch (options.format) {
      case 'txt':
        await this.exportTxt(report, filename, options)
        break
      case 'html':
        await this.exportHtml(report, filename, options)
        break
      case 'pdf':
        await this.exportPdf(report, filename, options)
        break
      case 'docx':
        await this.exportDocx(report, filename, options)
        break
      default:
        throw new Error(`Formato não suportado: ${options.format}`)
    }
  }

  private generateFilename(report: MedicalReport, format: string): string {
    const date = new Date().toISOString().split('T')[0]
    const patientId = report.anamnesis.paciente.id
    return `relatorio_${patientId}_${date}.${format}`
  }

  private async exportTxt(
    report: MedicalReport,
    filename: string,
    options: ExportOptions
  ): Promise<void> {
    const content = report.exportFormats.txt
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    saveAs(blob, filename)
  }

  private async exportHtml(
    report: MedicalReport,
    filename: string,
    options: ExportOptions
  ): Promise<void> {
    const content = report.exportFormats.html
    const blob = new Blob([content], { type: 'text/html;charset=utf-8' })
    saveAs(blob, filename)
  }

  private async exportPdf(
    report: MedicalReport,
    filename: string,
    options: ExportOptions
  ): Promise<void> {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })

    // Configure fonts and colors
    const primaryColor = [37, 99, 235] // Blue
    const textColor = [51, 51, 51]
    const lightGray = [156, 163, 175]
    
    let yPosition = 20

    // Header with WellWave branding
    pdf.setFontSize(24)
    pdf.setTextColor(...primaryColor)
    pdf.text('🌊 WellWave', 20, yPosition)
    
    pdf.setFontSize(12)
    pdf.setTextColor(...lightGray)
    pdf.text('Relatório Médico Digital', 20, yPosition + 8)
    
    // Draw header line
    pdf.setDrawColor(...primaryColor)
    pdf.setLineWidth(0.5)
    pdf.line(20, yPosition + 12, 190, yPosition + 12)
    
    yPosition += 25

    // Patient Information Section
    pdf.setFontSize(14)
    pdf.setTextColor(...primaryColor)
    pdf.text('IDENTIFICAÇÃO DO PACIENTE', 20, yPosition)
    yPosition += 8

    pdf.setFontSize(10)
    pdf.setTextColor(...textColor)
    
    const { paciente } = report.anamnesis
    pdf.text(`ID: ${paciente.id}`, 20, yPosition)
    yPosition += 5
    pdf.text(`Faixa Etária: ${paciente.faixaEtaria || 'Não informado'}`, 20, yPosition)
    yPosition += 5
    pdf.text(`Sexo: ${this.formatSex(paciente.sexoBiologico)}`, 20, yPosition)
    yPosition += 5
    
    if (paciente.gestante) {
      pdf.setTextColor(255, 20, 147) // Pink for pregnant
      pdf.text('🤰 Gestante', 20, yPosition)
      pdf.setTextColor(...textColor)
      yPosition += 5
    }
    
    pdf.text(`Data/Hora: ${paciente.timestamp.toLocaleString('pt-BR')}`, 20, yPosition)
    yPosition += 10

    // Chief Complaint Section
    const { queixaPrincipal } = report.anamnesis
    if (queixaPrincipal.queixaPrincipal) {
      pdf.setFontSize(14)
      pdf.setTextColor(...primaryColor)
      pdf.text('QUEIXA PRINCIPAL', 20, yPosition)
      yPosition += 8

      pdf.setFontSize(10)
      pdf.setTextColor(...textColor)
      
      // Word wrap for long text
      const lines = pdf.splitTextToSize(queixaPrincipal.queixaPrincipal, 170)
      lines.forEach((line: string) => {
        if (yPosition > 270) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(line, 20, yPosition)
        yPosition += 5
      })
      
      if (queixaPrincipal.duracaoSintomas) {
        pdf.text(`Duração: ${queixaPrincipal.duracaoSintomas}`, 20, yPosition)
        yPosition += 5
      }
      
      if (queixaPrincipal.intensidade) {
        const intensityColor = this.getIntensityColor(queixaPrincipal.intensidade)
        pdf.setTextColor(...intensityColor)
        pdf.text(`Intensidade: ${queixaPrincipal.intensidade}/10`, 20, yPosition)
        pdf.setTextColor(...textColor)
        yPosition += 5
      }
      
      yPosition += 5
    }

    // Medical History Section
    const { historicoMedico } = report.anamnesis
    if (yPosition > 240) {
      pdf.addPage()
      yPosition = 20
    }
    
    pdf.setFontSize(14)
    pdf.setTextColor(...primaryColor)
    pdf.text('HISTÓRIA MÉDICA', 20, yPosition)
    yPosition += 8

    pdf.setFontSize(10)
    pdf.setTextColor(...textColor)
    
    // Comorbidities
    if (historicoMedico.comorbidades.length > 0) {
      pdf.text('Comorbidades:', 20, yPosition)
      yPosition += 5
      historicoMedico.comorbidades.forEach(item => {
        if (yPosition > 270) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(`  • ${item}`, 25, yPosition)
        yPosition += 4
      })
    } else {
      pdf.text('Comorbidades: Nega', 20, yPosition)
      yPosition += 5
    }
    
    // Current Medications
    if (historicoMedico.medicamentosUso.length > 0) {
      yPosition += 3
      pdf.text('Medicamentos em Uso:', 20, yPosition)
      yPosition += 5
      historicoMedico.medicamentosUso.forEach(med => {
        if (yPosition > 270) {
          pdf.addPage()
          yPosition = 20
        }
        pdf.text(`  • ${med}`, 25, yPosition)
        yPosition += 4
      })
    }
    
    // Physical Exam Section
    const { exameFisico } = report.anamnesis
    if (yPosition > 220) {
      pdf.addPage()
      yPosition = 20
    }
    
    yPosition += 5
    pdf.setFontSize(14)
    pdf.setTextColor(...primaryColor)
    pdf.text('EXAME FÍSICO', 20, yPosition)
    yPosition += 8

    pdf.setFontSize(10)
    pdf.setTextColor(...textColor)
    
    // Vital Signs in a grid
    const sv = exameFisico.sinaisVitais
    if (Object.values(sv).some(v => v)) {
      pdf.text('Sinais Vitais:', 20, yPosition)
      yPosition += 5
      
      // Create a mini table for vital signs
      const vitalSigns = [
        { label: 'PA', value: sv.pa, unit: 'mmHg' },
        { label: 'FC', value: sv.fc, unit: 'bpm' },
        { label: 'FR', value: sv.fr, unit: 'irpm' },
        { label: 'Temp', value: sv.temp, unit: '°C' },
        { label: 'SatO2', value: sv.sat, unit: '%' },
        { label: 'Glicemia', value: sv.glicemia, unit: 'mg/dL' }
      ]
      
      let xPos = 25
      vitalSigns.forEach((vs, index) => {
        if (vs.value) {
          if (index > 0 && index % 3 === 0) {
            yPosition += 5
            xPos = 25
          }
          pdf.text(`${vs.label}: ${vs.value} ${vs.unit}`, xPos, yPosition)
          xPos += 60
        }
      })
      yPosition += 10
    }

    // AI Analysis Section (if available and requested)
    if (report.aiAnalysis && options.includeAIAnalysis !== false) {
      if (yPosition > 200) {
        pdf.addPage()
        yPosition = 20
      }
      
      yPosition += 5
      pdf.setFontSize(14)
      pdf.setTextColor(...primaryColor)
      pdf.text('ANÁLISE ASSISTIDA POR IA', 20, yPosition)
      yPosition += 8

      pdf.setFontSize(10)
      pdf.setTextColor(...textColor)
      
      // Confidence Score
      const confidence = Math.round(report.aiAnalysis.confidence * 100)
      pdf.text(`Confiança da Análise: ${confidence}%`, 20, yPosition)
      yPosition += 8
      
      // Differential Diagnosis
      if (report.aiAnalysis.differentialDiagnosis.length > 0) {
        pdf.setFontSize(11)
        pdf.text('Diagnósticos Diferenciais:', 20, yPosition)
        yPosition += 5
        
        pdf.setFontSize(10)
        report.aiAnalysis.differentialDiagnosis.slice(0, 5).forEach(diag => {
          if (yPosition > 270) {
            pdf.addPage()
            yPosition = 20
          }
          pdf.text(`  • ${diag.name} (${Math.round(diag.probability * 100)}%)`, 25, yPosition)
          yPosition += 4
          pdf.setFontSize(9)
          pdf.setTextColor(...lightGray)
          pdf.text(`    CID-10: ${diag.icd10}`, 25, yPosition)
          yPosition += 4
          pdf.setFontSize(10)
          pdf.setTextColor(...textColor)
        })
      }
    }

    // Footer
    pdf.setFontSize(8)
    pdf.setTextColor(...lightGray)
    const pageCount = pdf.getNumberOfPages()
    
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i)
      pdf.text(
        `Página ${i} de ${pageCount} | Gerado em ${new Date().toLocaleString('pt-BR')} | WellWave Medical System`,
        105,
        287,
        { align: 'center' }
      )
    }

    // Save the PDF
    pdf.save(filename)
  }

  private async exportDocx(
    report: MedicalReport,
    filename: string,
    options: ExportOptions
  ): Promise<void> {
    const { anamnesis, aiAnalysis } = report
    
    // Create document sections
    const sections = []

    // Title
    sections.push(
      new Paragraph({
        text: '🌊 WELLWAVE - RELATÓRIO MÉDICO',
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 }
      })
    )

    // Patient Information
    sections.push(
      new Paragraph({
        text: 'IDENTIFICAÇÃO DO PACIENTE',
        heading: HeadingLevel.HEADING_1,
        spacing: { before: 200, after: 200 }
      })
    )

    sections.push(
      new Paragraph({
        children: [
          new TextRun({ text: 'ID: ', bold: true }),
          new TextRun(anamnesis.paciente.id)
        ],
        spacing: { after: 100 }
      })
    )

    sections.push(
      new Paragraph({
        children: [
          new TextRun({ text: 'Faixa Etária: ', bold: true }),
          new TextRun(anamnesis.paciente.faixaEtaria || 'Não informado')
        ],
        spacing: { after: 100 }
      })
    )

    sections.push(
      new Paragraph({
        children: [
          new TextRun({ text: 'Sexo: ', bold: true }),
          new TextRun(this.formatSex(anamnesis.paciente.sexoBiologico))
        ],
        spacing: { after: 100 }
      })
    )

    if (anamnesis.paciente.gestante) {
      sections.push(
        new Paragraph({
          children: [
            new TextRun({ 
              text: '🤰 Gestante', 
              bold: true,
              color: 'FF1493'
            })
          ],
          spacing: { after: 100 }
        })
      )
    }

    sections.push(
      new Paragraph({
        children: [
          new TextRun({ text: 'Data/Hora: ', bold: true }),
          new TextRun(anamnesis.paciente.timestamp.toLocaleString('pt-BR'))
        ],
        spacing: { after: 300 }
      })
    )

    // Chief Complaint
    if (anamnesis.queixaPrincipal.queixaPrincipal) {
      sections.push(
        new Paragraph({
          text: 'QUEIXA PRINCIPAL',
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 200, after: 200 }
        })
      )

      sections.push(
        new Paragraph({
          text: anamnesis.queixaPrincipal.queixaPrincipal,
          spacing: { after: 100 }
        })
      )

      if (anamnesis.queixaPrincipal.duracaoSintomas) {
        sections.push(
          new Paragraph({
            children: [
              new TextRun({ text: 'Duração: ', bold: true }),
              new TextRun(anamnesis.queixaPrincipal.duracaoSintomas)
            ],
            spacing: { after: 100 }
          })
        )
      }

      if (anamnesis.queixaPrincipal.intensidade) {
        sections.push(
          new Paragraph({
            children: [
              new TextRun({ text: 'Intensidade: ', bold: true }),
              new TextRun(`${anamnesis.queixaPrincipal.intensidade}/10`)
            ],
            spacing: { after: 100 }
          })
        )
      }

      if (anamnesis.queixaPrincipal.caracteristicas.length > 0) {
        sections.push(
          new Paragraph({
            children: [
              new TextRun({ text: 'Características: ', bold: true }),
              new TextRun(anamnesis.queixaPrincipal.caracteristicas.join(', '))
            ],
            spacing: { after: 100 }
          })
        )
      }
    }

    // Medical History
    sections.push(
      new Paragraph({
        text: 'HISTÓRIA MÉDICA',
        heading: HeadingLevel.HEADING_1,
        spacing: { before: 200, after: 200 }
      })
    )

    if (anamnesis.historicoMedico.comorbidades.length > 0) {
      sections.push(
        new Paragraph({
          text: 'Comorbidades:',
          bold: true,
          spacing: { after: 100 }
        })
      )
      
      anamnesis.historicoMedico.comorbidades.forEach(item => {
        sections.push(
          new Paragraph({
            text: `• ${item}`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      })
    }

    if (anamnesis.historicoMedico.medicamentosUso.length > 0) {
      sections.push(
        new Paragraph({
          text: 'Medicamentos em Uso:',
          bold: true,
          spacing: { before: 150, after: 100 }
        })
      )
      
      anamnesis.historicoMedico.medicamentosUso.forEach(med => {
        sections.push(
          new Paragraph({
            text: `• ${med}`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      })
    }

    // Physical Exam
    sections.push(
      new Paragraph({
        text: 'EXAME FÍSICO',
        heading: HeadingLevel.HEADING_1,
        spacing: { before: 200, after: 200 }
      })
    )

    const sv = anamnesis.exameFisico.sinaisVitais
    if (Object.values(sv).some(v => v)) {
      sections.push(
        new Paragraph({
          text: 'Sinais Vitais:',
          bold: true,
          spacing: { after: 100 }
        })
      )

      if (sv.pa) {
        sections.push(
          new Paragraph({
            text: `• PA: ${sv.pa} mmHg`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      }
      if (sv.fc) {
        sections.push(
          new Paragraph({
            text: `• FC: ${sv.fc} bpm`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      }
      if (sv.fr) {
        sections.push(
          new Paragraph({
            text: `• FR: ${sv.fr} irpm`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      }
      if (sv.temp) {
        sections.push(
          new Paragraph({
            text: `• Temperatura: ${sv.temp}°C`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      }
      if (sv.sat) {
        sections.push(
          new Paragraph({
            text: `• SatO2: ${sv.sat}%`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      }
      if (sv.glicemia) {
        sections.push(
          new Paragraph({
            text: `• Glicemia: ${sv.glicemia} mg/dL`,
            indent: { left: 360 },
            spacing: { after: 50 }
          })
        )
      }
    }

    // AI Analysis (if available)
    if (aiAnalysis && options.includeAIAnalysis !== false) {
      sections.push(
        new Paragraph({
          text: 'ANÁLISE ASSISTIDA POR IA',
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 200, after: 200 }
        })
      )

      sections.push(
        new Paragraph({
          children: [
            new TextRun({ text: 'Confiança da Análise: ', bold: true }),
            new TextRun(`${Math.round(aiAnalysis.confidence * 100)}%`)
          ],
          spacing: { after: 200 }
        })
      )

      if (aiAnalysis.differentialDiagnosis.length > 0) {
        sections.push(
          new Paragraph({
            text: 'Diagnósticos Diferenciais:',
            bold: true,
            spacing: { after: 100 }
          })
        )

        aiAnalysis.differentialDiagnosis.forEach(diag => {
          sections.push(
            new Paragraph({
              text: `• ${diag.name} (${Math.round(diag.probability * 100)}%)`,
              indent: { left: 360 },
              spacing: { after: 50 }
            })
          )
          sections.push(
            new Paragraph({
              text: `  CID-10: ${diag.icd10}`,
              indent: { left: 720 },
              spacing: { after: 100 },
              color: '666666',
              size: 20
            })
          )
        })
      }
    }

    // Footer
    sections.push(
      new Paragraph({
        text: `Documento gerado em ${new Date().toLocaleString('pt-BR')}`,
        alignment: AlignmentType.CENTER,
        spacing: { before: 400 },
        color: '666666',
        size: 18
      })
    )

    sections.push(
      new Paragraph({
        text: 'WellWave Medical System v1.0.0',
        alignment: AlignmentType.CENTER,
        color: '666666',
        size: 18
      })
    )

    // Create and save document
    const doc = new Document({
      sections: [{
        properties: {},
        children: sections
      }]
    })

    const buffer = await Packer.toBlob(doc)
    saveAs(buffer, filename)
  }

  private formatSex(sex: string): string {
    switch (sex) {
      case 'M': return 'Masculino'
      case 'F': return 'Feminino'
      default: return 'Não informado'
    }
  }

  private getIntensityColor(intensity: number): [number, number, number] {
    if (intensity <= 3) return [34, 197, 94] // Green
    if (intensity <= 6) return [251, 191, 36] // Yellow
    if (intensity <= 8) return [251, 146, 60] // Orange
    return [239, 68, 68] // Red
  }
}