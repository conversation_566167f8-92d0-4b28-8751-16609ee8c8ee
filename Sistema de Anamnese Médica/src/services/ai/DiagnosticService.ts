import { 
  AIAnalysisResult, 
  DiagnosisSuggestion, 
  TreatmentOption, 
  ExamSuggestion, 
  RiskAssessment,
  AIServiceConfig 
} from '../../types/ai-suggestions.types'
import { ExtendedAnamneseData } from '../../components/MedicalContext'

// Medical knowledge base for offline diagnosis suggestions
const MEDICAL_CONDITIONS_DB = {
  // Cardiovascular conditions
  'chest_pain': [
    {
      name: 'Síndrome Coronariana Aguda',
      icd10: 'I20.0',
      keywords: ['dor no peito', 'aperto', 'pressão', 'sudorese', 'náusea'],
      riskFactors: ['hipertensão', 'diabetes', 'tabagismo', 'dislipidemia'],
      urgency: 'emergencia' as const,
      probability: 0.8
    },
    {
      name: 'Do<PERSON> Atípic<PERSON>',
      icd10: 'R06.02',
      keywords: ['dor torácica', 'desconforto'],
      riskFactors: [],
      urgency: 'media' as const,
      probability: 0.6
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      icd10: 'K21.9',
      keywords: ['queimação', 'azia', 'regurgitação'],
      riskFactors: ['obesidade', 'hérnia hiatal'],
      urgency: 'baixa' as const,
      probability: 0.4
    }
  ],
  'dyspnea': [
    {
      name: 'Insuficiência Cardíaca Congestiva',
      icd10: 'I50.9',
      keywords: ['dispneia', 'cansaço', 'edema', 'ortopneia'],
      riskFactors: ['hipertensão', 'diabetes', 'infarto prévio'],
      urgency: 'alta' as const,
      probability: 0.7
    },
    {
      name: 'Asma Brônquica',
      icd10: 'J45.9',
      keywords: ['falta de ar', 'chiado', 'tosse seca'],
      riskFactors: ['alergia', 'histórico familiar'],
      urgency: 'media' as const,
      probability: 0.5
    }
  ],
  'abdominal_pain': [
    {
      name: 'Apendicite Aguda',
      icd10: 'K35.9',
      keywords: ['dor abdominal', 'fossa ilíaca direita', 'náusea', 'vômito'],
      riskFactors: ['idade jovem'],
      urgency: 'emergencia' as const,
      probability: 0.6
    },
    {
      name: 'Gastrite Aguda',
      icd10: 'K29.1',
      keywords: ['dor epigástrica', 'azia', 'náusea'],
      riskFactors: ['uso de AINES', 'álcool', 'estresse'],
      urgency: 'media' as const,
      probability: 0.5
    }
  ]
}

const TREATMENT_RECOMMENDATIONS_DB = {
  'chest_pain': [
    {
      type: 'medicamentoso' as const,
      name: 'AAS + Clopidogrel (se SCA)',
      description: 'Terapia antiplaquetária dupla para síndrome coronariana aguda',
      guidelines: ['ESC Guidelines 2020', 'ACC/AHA Guidelines'],
      priority: 1
    },
    {
      type: 'nao-medicamentoso' as const,
      name: 'Modificação do Estilo de Vida',
      description: 'Cessação do tabagismo, dieta mediterrânea, atividade física regular',
      guidelines: ['Prevenção Primária Cardiovascular'],
      priority: 2
    }
  ],
  'dyspnea': [
    {
      type: 'medicamentoso' as const,
      name: 'IECA/BRA + Betabloqueador',
      description: 'Terapia padrão para insuficiência cardíaca',
      guidelines: ['ESC Heart Failure Guidelines'],
      priority: 1
    }
  ]
}

const EXAM_SUGGESTIONS_DB = {
  'chest_pain': [
    {
      name: 'ECG de 12 derivações',
      type: 'funcional' as const,
      rationale: 'Avaliar sinais de isquemia ou infarto agudo',
      urgency: 'emergencial' as const,
      cost: 'baixo' as const
    },
    {
      name: 'Troponina I',
      type: 'laboratorial' as const,
      rationale: 'Marcador de lesão miocárdica',
      urgency: 'emergencial' as const,
      cost: 'medio' as const
    },
    {
      name: 'Ecocardiograma',
      type: 'imagem' as const,
      rationale: 'Avaliação da função ventricular e estruturas cardíacas',
      urgency: 'urgente' as const,
      cost: 'medio' as const
    }
  ],
  'dyspnea': [
    {
      name: 'Raio-X de Tórax',
      type: 'imagem' as const,
      rationale: 'Avaliar congestão pulmonar e cardiomegalia',
      urgency: 'urgente' as const,
      cost: 'baixo' as const
    },
    {
      name: 'BNP ou NT-proBNP',
      type: 'laboratorial' as const,
      rationale: 'Marcador de insuficiência cardíaca',
      urgency: 'urgente' as const,
      cost: 'medio' as const
    }
  ]
}

export class DiagnosticService {
  private static instance: DiagnosticService
  private config: AIServiceConfig
  private cache: Map<string, AIAnalysisResult> = new Map()

  private constructor() {
    this.config = {
      provider: 'local', // Default to local/offline mode
      enableCache: true,
      cacheTimeout: 30 * 60 * 1000, // 30 minutes
      temperature: 0.3,
      maxTokens: 2000
    }
  }

  static getInstance(): DiagnosticService {
    if (!DiagnosticService.instance) {
      DiagnosticService.instance = new DiagnosticService()
    }
    return DiagnosticService.instance
  }

  configure(config: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...config }
  }

  async analyzeAnamnesis(anamnesis: ExtendedAnamneseData): Promise<AIAnalysisResult> {
    const cacheKey = this.generateCacheKey(anamnesis)
    
    // Check cache first
    if (this.config.enableCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!
      if (Date.now() - cached.timestamp.getTime() < this.config.cacheTimeout!) {
        return cached
      }
    }

    let result: AIAnalysisResult

    try {
      if (this.config.provider === 'local') {
        result = await this.analyzeLocalMode(anamnesis)
      } else {
        result = await this.analyzeWithAI(anamnesis)
      }
    } catch (error) {
      console.warn('AI analysis failed, falling back to local mode:', error)
      result = await this.analyzeLocalMode(anamnesis)
    }

    // Cache the result
    if (this.config.enableCache) {
      this.cache.set(cacheKey, result)
    }

    return result
  }

  private async analyzeLocalMode(anamnesis: ExtendedAnamneseData): Promise<AIAnalysisResult> {
    const symptoms = this.extractSymptoms(anamnesis)
    const riskFactors = this.extractRiskFactors(anamnesis)
    
    const differentialDiagnosis = this.generateDifferentialDiagnosis(symptoms, riskFactors)
    const treatmentRecommendations = this.generateTreatmentRecommendations(symptoms)
    const additionalExams = this.generateExamSuggestions(symptoms)
    const riskAssessment = this.assessRisks(anamnesis, riskFactors)
    const clinicalPearls = this.generateClinicalPearls(symptoms, differentialDiagnosis)
    const redFlags = this.identifyRedFlags(anamnesis, symptoms)

    const confidence = this.calculateConfidence(
      differentialDiagnosis,
      symptoms.length,
      riskFactors.length
    )

    return {
      differentialDiagnosis,
      treatmentRecommendations,
      additionalExams,
      riskFactors: riskAssessment,
      clinicalPearls,
      redFlags,
      confidence,
      reasoning: this.generateReasoning(symptoms, differentialDiagnosis),
      timestamp: new Date()
    }
  }

  private extractSymptoms(anamnesis: ExtendedAnamneseData): string[] {
    const symptoms: string[] = []
    
    // From chief complaint
    if (anamnesis.queixaPrincipal.queixaPrincipal) {
      symptoms.push(anamnesis.queixaPrincipal.queixaPrincipal.toLowerCase())
    }
    
    // From characteristics and associated factors
    symptoms.push(...anamnesis.queixaPrincipal.caracteristicas.map(c => c.toLowerCase()))
    symptoms.push(...anamnesis.queixaPrincipal.fatoresAssociados.map(f => f.toLowerCase()))
    
    // From observations
    if (anamnesis.queixaPrincipal.observacoes) {
      symptoms.push(anamnesis.queixaPrincipal.observacoes.toLowerCase())
    }
    
    return symptoms.filter((s, i, arr) => arr.indexOf(s) === i) // Remove duplicates
  }

  private extractRiskFactors(anamnesis: ExtendedAnamneseData): string[] {
    const riskFactors: string[] = []
    
    // From comorbidities
    riskFactors.push(...anamnesis.historicoMedico.comorbidades.map(c => c.toLowerCase()))
    
    // From lifestyle habits
    riskFactors.push(...anamnesis.historicoMedico.habitosVida.map(h => h.toLowerCase()))
    
    // Age group as risk factor
    if (anamnesis.paciente.faixaEtaria === 'idoso') {
      riskFactors.push('idade avançada')
    }
    
    // Pregnancy as risk factor
    if (anamnesis.paciente.gestante) {
      riskFactors.push('gestação')
    }
    
    return riskFactors
  }

  private generateDifferentialDiagnosis(
    symptoms: string[],
    riskFactors: string[]
  ): DiagnosisSuggestion[] {
    const diagnoses: DiagnosisSuggestion[] = []
    
    // Analyze symptoms against medical conditions database
    Object.entries(MEDICAL_CONDITIONS_DB).forEach(([category, conditions]) => {
      conditions.forEach(condition => {
        let score = 0
        let evidence: string[] = []
        
        // Check symptom matches
        condition.keywords.forEach(keyword => {
          if (symptoms.some(s => s.includes(keyword))) {
            score += 0.3
            evidence.push(`Sintoma: ${keyword}`)
          }
        })
        
        // Check risk factor matches
        condition.riskFactors.forEach(rf => {
          if (riskFactors.some(r => r.includes(rf))) {
            score += 0.2
            evidence.push(`Fator de risco: ${rf}`)
          }
        })
        
        if (score > 0.2) { // Minimum threshold
          diagnoses.push({
            id: `diag_${condition.icd10.replace('.', '_')}`,
            name: condition.name,
            icd10: condition.icd10,
            probability: Math.min(score * condition.probability, 0.95),
            evidence,
            relatedSymptoms: condition.keywords,
            urgency: condition.urgency
          })
        }
      })
    })
    
    // Sort by probability and return top 5
    return diagnoses
      .sort((a, b) => b.probability - a.probability)
      .slice(0, 5)
  }

  private generateTreatmentRecommendations(symptoms: string[]): TreatmentOption[] {
    const treatments: TreatmentOption[] = []
    
    // Match symptoms to treatment categories
    Object.entries(TREATMENT_RECOMMENDATIONS_DB).forEach(([category, treatmentList]) => {
      const hasRelevantSymptoms = symptoms.some(symptom => {
        switch (category) {
          case 'chest_pain':
            return symptom.includes('dor') && (symptom.includes('peito') || symptom.includes('torácica'))
          case 'dyspnea':
            return symptom.includes('dispneia') || symptom.includes('falta de ar')
          default:
            return false
        }
      })
      
      if (hasRelevantSymptoms) {
        treatmentList.forEach(treatment => {
          treatments.push({
            id: `treat_${category}_${treatment.priority}`,
            type: treatment.type,
            name: treatment.name,
            description: treatment.description,
            guidelines: treatment.guidelines,
            priority: treatment.priority
          })
        })
      }
    })
    
    // Add general recommendations
    treatments.push({
      id: 'general_monitoring',
      type: 'nao-medicamentoso',
      name: 'Monitorização Contínua',
      description: 'Acompanhamento regular dos sinais vitais e evolução clínica',
      guidelines: ['Protocolo Institucional'],
      priority: 3
    })
    
    return treatments.sort((a, b) => a.priority - b.priority)
  }

  private generateExamSuggestions(symptoms: string[]): ExamSuggestion[] {
    const exams: ExamSuggestion[] = []
    
    // Match symptoms to exam categories
    Object.entries(EXAM_SUGGESTIONS_DB).forEach(([category, examList]) => {
      const hasRelevantSymptoms = symptoms.some(symptom => {
        switch (category) {
          case 'chest_pain':
            return symptom.includes('dor') && (symptom.includes('peito') || symptom.includes('torácica'))
          case 'dyspnea':
            return symptom.includes('dispneia') || symptom.includes('falta de ar')
          default:
            return false
        }
      })
      
      if (hasRelevantSymptoms) {
        examList.forEach(exam => {
          exams.push({
            id: `exam_${category}_${exam.name.replace(/\s+/g, '_').toLowerCase()}`,
            name: exam.name,
            type: exam.type,
            rationale: exam.rationale,
            urgency: exam.urgency,
            cost: exam.cost
          })
        })
      }
    })
    
    // Add basic exams
    if (exams.length === 0) {
      exams.push({
        id: 'basic_labs',
        name: 'Hemograma Completo',
        type: 'laboratorial',
        rationale: 'Avaliação geral do estado de saúde',
        urgency: 'rotina',
        cost: 'baixo'
      })
    }
    
    return exams
  }

  private assessRisks(
    anamnesis: ExtendedAnamneseData,
    riskFactors: string[]
  ): RiskAssessment[] {
    const risks: RiskAssessment[] = []
    
    // Cardiovascular risk assessment
    const cvRiskFactors = riskFactors.filter(rf => 
      ['hipertensão', 'diabetes', 'tabagismo', 'dislipidemia'].some(cvrf => rf.includes(cvrf))
    )
    
    if (cvRiskFactors.length > 0) {
      let level: 'baixo' | 'moderado' | 'alto' | 'critico' = 'baixo'
      if (cvRiskFactors.length >= 3) level = 'alto'
      else if (cvRiskFactors.length >= 2) level = 'moderado'
      
      risks.push({
        factor: 'Risco Cardiovascular',
        level,
        description: `Presença de ${cvRiskFactors.length} fator(es) de risco cardiovascular`,
        recommendations: [
          'Controle rigoroso dos fatores de risco',
          'Avaliação cardiológica especializada',
          'Modificações no estilo de vida'
        ],
        monitoringRequired: level !== 'baixo'
      })
    }
    
    // Age-related risk
    if (anamnesis.paciente.faixaEtaria === 'idoso') {
      risks.push({
        factor: 'Risco Relacionado à Idade',
        level: 'moderado',
        description: 'Paciente idoso com maior risco de complicações',
        recommendations: [
          'Atenção à polifarmácia',
          'Avaliação geriátrica abrangente',
          'Prevenção de quedas'
        ],
        monitoringRequired: true
      })
    }
    
    // High intensity symptoms
    if (anamnesis.queixaPrincipal.intensidade >= 8) {
      risks.push({
        factor: 'Sintomas de Alta Intensidade',
        level: 'alto',
        description: 'Intensidade elevada dos sintomas pode indicar condição grave',
        recommendations: [
          'Avaliação médica urgente',
          'Monitorização contínua',
          'Investigação diagnóstica acelerada'
        ],
        monitoringRequired: true
      })
    }
    
    return risks
  }

  private generateClinicalPearls(
    symptoms: string[],
    diagnoses: DiagnosisSuggestion[]
  ): string[] {
    const pearls: string[] = []
    
    // General clinical pearls
    pearls.push('A história clínica é fundamental: 80% dos diagnósticos vêm da anamnese')
    
    // Symptom-specific pearls
    if (symptoms.some(s => s.includes('dor') && s.includes('peito'))) {
      pearls.push('Dor torácica: sempre considerar as 6 causas fatais (SCA, EP, Pneumotórax, Tamponamento, Dissecção aórtica, Ruptura esofágica)')
    }
    
    if (symptoms.some(s => s.includes('dispneia'))) {
      pearls.push('Dispneia aguda: BNP/NT-proBNP ajuda a distinguir causa cardíaca de pulmonar')
    }
    
    // Diagnosis-specific pearls
    if (diagnoses.some(d => d.name.includes('Coronariana'))) {
      pearls.push('SCA: ECG normal não exclui infarto - troponina é essencial')
    }
    
    return pearls.slice(0, 3) // Maximum 3 pearls
  }

  private identifyRedFlags(
    anamnesis: ExtendedAnamneseData,
    symptoms: string[]
  ): string[] {
    const redFlags: string[] = []
    
    // Vital signs red flags
    const vs = anamnesis.exameFisico.sinaisVitais
    if (vs.pa) {
      const systolic = parseInt(vs.pa.split('/')[0])
      if (systolic > 180 || systolic < 90) {
        redFlags.push('Pressão arterial crítica')
      }
    }
    
    if (vs.fc) {
      const hr = parseInt(vs.fc)
      if (hr > 120 || hr < 50) {
        redFlags.push('Frequência cardíaca anormal')
      }
    }
    
    if (vs.sat) {
      const saturation = parseInt(vs.sat)
      if (saturation < 95) {
        redFlags.push('Saturação de oxigênio baixa')
      }
    }
    
    // Symptom-based red flags
    if (anamnesis.queixaPrincipal.intensidade >= 9) {
      redFlags.push('Dor de intensidade muito alta (≥9/10)')
    }
    
    if (symptoms.some(s => s.includes('sudorese') && s.includes('dor'))) {
      redFlags.push('Dor associada à sudorese profusa')
    }
    
    if (symptoms.some(s => s.includes('sincope') || s.includes('desmaio'))) {
      redFlags.push('História de síncope/desmaio')
    }
    
    // Age-related red flags
    if (anamnesis.paciente.faixaEtaria === 'idoso' && symptoms.some(s => s.includes('confusão'))) {
      redFlags.push('Confusão mental em idoso')
    }
    
    return redFlags
  }

  private calculateConfidence(
    diagnoses: DiagnosisSuggestion[],
    symptomCount: number,
    riskFactorCount: number
  ): number {
    let baseConfidence = 0.3
    
    // Increase confidence based on available data
    if (symptomCount > 3) baseConfidence += 0.2
    if (riskFactorCount > 1) baseConfidence += 0.1
    if (diagnoses.length > 0) baseConfidence += 0.2
    
    // Highest diagnosis probability influences overall confidence
    if (diagnoses.length > 0) {
      const highestProbability = Math.max(...diagnoses.map(d => d.probability))
      baseConfidence += highestProbability * 0.2
    }
    
    return Math.min(baseConfidence, 0.85) // Cap at 85% for local mode
  }

  private generateReasoning(
    symptoms: string[],
    diagnoses: DiagnosisSuggestion[]
  ): string {
    let reasoning = 'Análise baseada em padrões clínicos reconhecidos. '
    
    if (symptoms.length > 0) {
      reasoning += `Identificados ${symptoms.length} sintomas principais. `
    }
    
    if (diagnoses.length > 0) {
      reasoning += `As hipóteses diagnósticas foram geradas com base na correlação entre sintomas apresentados e condições clínicas conhecidas. `
      reasoning += `A hipótese mais provável (${diagnoses[0].name}) apresenta ${Math.round(diagnoses[0].probability * 100)}% de compatibilidade com o quadro clínico.`
    }
    
    return reasoning
  }

  private generateCacheKey(anamnesis: ExtendedAnamneseData): string {
    // Create a hash-like key from relevant data
    const relevantData = {
      complaint: anamnesis.queixaPrincipal.queixaPrincipal,
      duration: anamnesis.queixaPrincipal.duracaoSintomas,
      intensity: anamnesis.queixaPrincipal.intensidade,
      characteristics: anamnesis.queixaPrincipal.caracteristicas,
      factors: anamnesis.queixaPrincipal.fatoresAssociados,
      comorbidities: anamnesis.historicoMedico.comorbidades,
      vitals: anamnesis.exameFisico.sinaisVitais
    }
    
    return btoa(JSON.stringify(relevantData)).substring(0, 32)
  }

  // Simulate API call for external AI services (not implemented)
  private async analyzeWithAI(anamnesis: ExtendedAnamneseData): Promise<AIAnalysisResult> {
    throw new Error('External AI services not implemented yet. Using local mode.')
  }

  // Clear cache (useful for testing)
  clearCache(): void {
    this.cache.clear()
  }
}