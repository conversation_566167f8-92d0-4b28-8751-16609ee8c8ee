import { useState, useEffect, useRef, useCallback } from 'react';
import { sidebarConfig, type SidebarState, type SidebarActions } from '../config/ui/sidebar';

// SSR-safe localStorage utilities
const getStoredValue = (key: string, defaultValue: any) => {
  if (typeof window === 'undefined') return defaultValue;
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch {
    return defaultValue;
  }
};

const setStoredValue = (key: string, value: any) => {
  if (typeof window === 'undefined') return;
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch {
    // Silently fail if localStorage is not available
  }
};

// Check if device is mobile
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  return isMobile;
};

export const useSmartSidebar = () => {
  const isMobile = useIsMobile();
  
  // Core state
  const [collapsed, setCollapsed] = useState(() => 
    getStoredValue(sidebarConfig.storageKeys.collapsed, false)
  );
  
  const [width, setWidth] = useState(() => 
    getStoredValue(sidebarConfig.storageKeys.width, sidebarConfig.defaultWidth)
  );
  
  const [isHovering, setIsHovering] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  
  // Refs for timers and state tracking
  const hoverEnterTimeoutRef = useRef<NodeJS.Timeout>();
  const hoverLeaveTimeoutRef = useRef<NodeJS.Timeout>();
  const idleTimeoutRef = useRef<NodeJS.Timeout>();
  const transitionTimeoutRef = useRef<NodeJS.Timeout>();
  const lastActivityRef = useRef(Date.now());
  const wasCollapsedBeforeHoverRef = useRef(collapsed);
  
  // Persist state to localStorage
  useEffect(() => {
    setStoredValue(sidebarConfig.storageKeys.collapsed, collapsed);
  }, [collapsed]);
  
  useEffect(() => {
    setStoredValue(sidebarConfig.storageKeys.width, width);
  }, [width]);
  
  // Auto-collapse on mobile
  useEffect(() => {
    if (isMobile && !collapsed) {
      setCollapsed(true);
    }
  }, [isMobile, collapsed]);
  
  // Activity tracking for auto-hide
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    
    // Clear existing idle timer
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }
    
    // Start new idle timer only if not hovering and not mobile
    if (!isHovering && !isMobile && !isResizing) {
      idleTimeoutRef.current = setTimeout(() => {
        const timeSinceActivity = Date.now() - lastActivityRef.current;
        if (timeSinceActivity >= sidebarConfig.idleMs && !collapsed) {
          setCollapsed(true);
        }
      }, sidebarConfig.idleMs);
    }
  }, [isHovering, isMobile, isResizing, collapsed]);
  
  // Track user activity
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });
    
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, [updateActivity]);
  
  // Transition management
  const startTransition = useCallback((duration: number) => {
    setIsTransitioning(true);
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }
    transitionTimeoutRef.current = setTimeout(() => {
      setIsTransitioning(false);
    }, duration);
  }, []);
  
  // Hover intent management - mais sofisticado
  const handleMouseEnter = useCallback(() => {
    if (isMobile) return;
    
    setIsHovering(true);
    
    // Clear any existing timeouts
    if (hoverEnterTimeoutRef.current) {
      clearTimeout(hoverEnterTimeoutRef.current);
    }
    if (hoverLeaveTimeoutRef.current) {
      clearTimeout(hoverLeaveTimeoutRef.current);
    }
    
    // Store collapsed state before hover
    if (collapsed) {
      wasCollapsedBeforeHoverRef.current = true;
      
      // Expand after hover intent delay
      hoverEnterTimeoutRef.current = setTimeout(() => {
        setCollapsed(false);
        startTransition(400); // Duration da animação de width
      }, sidebarConfig.hoverIntentMs);
    }
  }, [isMobile, collapsed, startTransition]);
  
  const handleMouseLeave = useCallback(() => {
    if (isMobile) return;
    
    setIsHovering(false);
    
    // Clear enter timeout
    if (hoverEnterTimeoutRef.current) {
      clearTimeout(hoverEnterTimeoutRef.current);
    }
    
    // Collapse after leaving if it was collapsed before hover
    if (wasCollapsedBeforeHoverRef.current) {
      hoverLeaveTimeoutRef.current = setTimeout(() => {
        setCollapsed(true);
        startTransition(400);
        wasCollapsedBeforeHoverRef.current = false;
      }, sidebarConfig.hoverLeaveMs);
    }
  }, [isMobile, startTransition]);
  
  // Focus management - pause auto-hide when focused
  const handleFocusIn = useCallback(() => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }
  }, []);
  
  const handleFocusOut = useCallback(() => {
    updateActivity();
  }, [updateActivity]);
  
  // Actions com melhor feedback
  const toggle = useCallback(() => {
    setCollapsed(prev => !prev);
    startTransition(400);
    updateActivity();
  }, [updateActivity, startTransition]);
  
  const collapse = useCallback(() => {
    setCollapsed(true);
    startTransition(400);
    updateActivity();
  }, [updateActivity, startTransition]);
  
  const expand = useCallback(() => {
    setCollapsed(false);
    startTransition(400);
    updateActivity();
  }, [updateActivity, startTransition]);
  
  const handleSetWidth = useCallback((newWidth: number) => {
    const clampedWidth = Math.max(
      sidebarConfig.minWidth,
      Math.min(sidebarConfig.maxWidth, newWidth)
    );
    setWidth(clampedWidth);
    updateActivity();
  }, [updateActivity]);
  
  const handleSetHovering = useCallback((hovering: boolean) => {
    if (hovering) {
      handleMouseEnter();
    } else {
      handleMouseLeave();
    }
  }, [handleMouseEnter, handleMouseLeave]);
  
  const handleSetResizing = useCallback((resizing: boolean) => {
    setIsResizing(resizing);
    if (resizing) {
      // Clear idle timeout while resizing
      if (idleTimeoutRef.current) {
        clearTimeout(idleTimeoutRef.current);
      }
    } else {
      updateActivity();
    }
  }, [updateActivity]);
  
  const handleSetTransitioning = useCallback((transitioning: boolean) => {
    setIsTransitioning(transitioning);
  }, []);
  
  const openDrawer = useCallback(() => {
    setIsDrawerOpen(true);
  }, []);
  
  const closeDrawer = useCallback(() => {
    setIsDrawerOpen(false);
  }, []);
  
  // Cleanup timers
  useEffect(() => {
    return () => {
      if (hoverEnterTimeoutRef.current) {
        clearTimeout(hoverEnterTimeoutRef.current);
      }
      if (hoverLeaveTimeoutRef.current) {
        clearTimeout(hoverLeaveTimeoutRef.current);
      }
      if (idleTimeoutRef.current) {
        clearTimeout(idleTimeoutRef.current);
      }
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
    };
  }, []);
  
  // Calculate effective width
  const effectiveWidth = collapsed ? sidebarConfig.collapsedWidth : width;
  
  const state: SidebarState = {
    collapsed,
    width: effectiveWidth,
    isHovering,
    isResizing,
    isMobile,
    isDrawerOpen,
    isTransitioning
  };
  
  const actions: SidebarActions = {
    toggle,
    collapse,
    expand,
    setWidth: handleSetWidth,
    setHovering: handleSetHovering,
    setResizing: handleSetResizing,
    setTransitioning: handleSetTransitioning,
    openDrawer,
    closeDrawer
  };
  
  return {
    ...state,
    ...actions,
    // Event handlers for sidebar container
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    onFocusIn: handleFocusIn,
    onFocusOut: handleFocusOut
  };
};