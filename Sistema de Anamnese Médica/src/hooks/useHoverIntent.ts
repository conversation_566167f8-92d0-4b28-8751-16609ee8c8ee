import { useRef, useCallback } from 'react';

interface UseHoverIntentOptions {
  onHoverStart?: () => void;
  onHoverEnd?: () => void;
  delay?: number;
  leaveDelay?: number;
}

export const useHoverIntent = ({
  onHoverStart,
  onHoverEnd,
  delay = 120,
  leaveDelay = 120
}: UseHoverIntentOptions = {}) => {
  const enterTimeoutRef = useRef<NodeJS.Timeout>();
  const leaveTimeoutRef = useRef<NodeJS.Timeout>();
  const isHoveringRef = useRef(false);
  
  const handleMouseEnter = useCallback(() => {
    // Clear any pending leave timeout
    if (leaveTimeoutRef.current) {
      clearTimeout(leaveTimeoutRef.current);
      leaveTimeoutRef.current = undefined;
    }
    
    // If already hovering, don't start again
    if (isHoveringRef.current) return;
    
    // Start hover intent timer
    enterTimeoutRef.current = setTimeout(() => {
      isHoveringRef.current = true;
      onHoverStart?.();
    }, delay);
  }, [onHoverStart, delay]);
  
  const handleMouseLeave = useCallback(() => {
    // Clear any pending enter timeout
    if (enterTimeoutRef.current) {
      clearTimeout(enterTimeoutRef.current);
      enterTimeoutRef.current = undefined;
    }
    
    // If not hovering, nothing to do
    if (!isHoveringRef.current) return;
    
    // Start leave timer
    leaveTimeoutRef.current = setTimeout(() => {
      isHoveringRef.current = false;
      onHoverEnd?.();
    }, leaveDelay);
  }, [onHoverEnd, leaveDelay]);
  
  // Cleanup function
  const cleanup = useCallback(() => {
    if (enterTimeoutRef.current) {
      clearTimeout(enterTimeoutRef.current);
    }
    if (leaveTimeoutRef.current) {
      clearTimeout(leaveTimeoutRef.current);
    }
    isHoveringRef.current = false;
  }, []);
  
  return {
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    cleanup
  };
};