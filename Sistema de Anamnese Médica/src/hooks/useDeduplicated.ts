import { useCallback, useRef } from 'react';

/**
 * Hook for text deduplication in medical contexts
 * Prevents duplicate entries in medical forms and data
 */
export function useDeduplicated() {
  const seenTexts = useRef(new Set<string>());
  const dedupMap = useRef(new Map<string, string>());

  const deduplicateText = useCallback((text: string): string => {
    if (!text || typeof text !== 'string') return text;

    // Normalize text for comparison (trim, lowercase, remove extra spaces)
    const normalizedText = text.trim().toLowerCase().replace(/\s+/g, ' ');
    
    if (normalizedText === '') return text;

    // Check if we've seen this normalized text before
    if (seenTexts.current.has(normalizedText)) {
      // Return the cached deduplicated version
      return dedupMap.current.get(normalizedText) || text;
    }

    // Mark as seen and cache the original
    seenTexts.current.add(normalizedText);
    dedupMap.current.set(normalizedText, text);

    return text;
  }, []);

  const isTextDuplicate = useCallback((text: string): boolean => {
    if (!text || typeof text !== 'string') return false;
    
    const normalizedText = text.trim().toLowerCase().replace(/\s+/g, ' ');
    return seenTexts.current.has(normalizedText);
  }, []);

  const clearDeduplicationCache = useCallback(() => {
    seenTexts.current.clear();
    dedupMap.current.clear();
  }, []);

  const getUniqueTexts = useCallback((): string[] => {
    return Array.from(dedupMap.current.values());
  }, []);

  const getDuplicationStats = useCallback(() => {
    return {
      totalSeen: seenTexts.current.size,
      uniqueTexts: dedupMap.current.size
    };
  }, []);

  return {
    deduplicateText,
    isTextDuplicate,
    clearDeduplicationCache,
    getUniqueTexts,
    getDuplicationStats
  };
}

export default useDeduplicated;