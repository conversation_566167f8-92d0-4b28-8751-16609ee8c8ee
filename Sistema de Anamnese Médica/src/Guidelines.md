# WellWave Medical Design System Guidelines
*Sistema Médico Moderno Inspirado na OpenAI*

## 🎯 Visão Geral

O WellWave é um sistema médico premium que combina a elegância e modernidade da interface da OpenAI com a funcionalidade especializada da área da saúde. Nossa abordagem prioriza clareza, eficiência e uma experiência visual sofisticada que inspira confiança médica.

---

## 🎨 Sistema de Cores

### Pa<PERSON>a Principal (Baseada no Logo)
```css
/* Cores principais extraídas do logo WellWave */
--medical-primary: #2E86DE          /* Azul principal médico */
--medical-primary-light: #74B9FF    /* Azul claro do logo */
--medical-primary-dark: #0B2F4B     /* Azul escuro do logo */
--medical-secondary: #5DADE2        /* Azul médio */

/* Gradientes premium */
--gradient-primary: linear-gradient(135deg, #74B9FF 0%, #2E86DE 50%, #0B2F4B 100%)
--gradient-wave: linear-gradient(135deg, #DDD6FE 0%, #74B9FF 25%, #2E86DE 50%, #0B2F4B 100%)
--gradient-accent: linear-gradient(135deg, #74B9FF 0%, #5DADE2 100%)
```

### Cores Funcionais Médicas
```css
--medical-cardiology: #E74C3C       /* Vermelho cardiologia */
--medical-respiratory: #27AE60      /* Verde respiratório */
--medical-neurology: #8E44AD        /* Roxo neurologia */
--medical-gastro: #F39C12           /* Laranja gastroenterologia */
--medical-endocrinology: #E91E63    /* Rosa endocrinologia */

/* Status médicos */
--medical-emergency: #E74C3C        /* Emergência */
--medical-success: #27AE60          /* Sucesso/alta */
--medical-warning: #F39C12          /* Atenção */
--medical-danger: #E74C3C           /* Perigo */
--medical-info: #3498DB             /* Informação */
```

### Aplicação de Cores
- **Nunca use cores aleatórias** - sempre referencie a paleta médica
- **Backgrounds**: Use gradientes sutis baseados na paleta (5-10% opacity)
- **Textos primários**: Sempre use gradientes para títulos importantes
- **Ícones**: Correspondam à especialidade médica (coração = cardiology, etc.)

---

## 🎭 Sistema de Animações

### Princípios de Animação Médica
1. **Suavidade Profissional**: Transições suaves que inspiram confiança
2. **Microinterações Médicas**: Feedbacks visuais sutis mas informativos
3. **Performance Clínica**: Animações rápidas que não atrapalham o workflow

### Durações Padrão
```css
--animation-fast: 0.15s       /* Hover states, small transitions */
--animation-normal: 0.3s      /* Standard transitions, modals */
--animation-slow: 0.5s        /* Page transitions, complex animations */
--animation-breathing: 3s     /* Breathing effects, ambient animations */
```

### Efeitos Obrigatórios
- **Hover Scale**: `scale(1.02)` para botões e cards
- **Tap Scale**: `scale(0.98)` para feedback tátil
- **Stagger**: Delay de 0.1s entre elementos em listas
- **Page Transitions**: Slide horizontal com fade
- **Logo Animation**: Rotação sutil + breathing effect

### Animações Específicas
```typescript
// Exemplo de animação médica premium
const medicalCardVariants = {
  hidden: { opacity: 0, y: 20, scale: 0.95 },
  visible: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: { duration: 0.3, ease: "easeOut" }
  },
  hover: { 
    scale: 1.02, 
    boxShadow: "0 10px 25px rgba(46, 134, 222, 0.15)",
    transition: { duration: 0.2 }
  }
}
```

---

## 🏗️ Layout Estilo OpenAI Médico

### Estrutura Principal
```
┌─ Sidebar (280px) ─┬─ Header (64px) ──────────────┐
│                   │                              │
│ Logo + Navigation │ Date + User + Theme Toggle  │
│                   ├──────────────────────────────┤
│ Quick Actions     │                              │
│                   │                              │
│ Medical Modules   │        Main Content          │
│                   │                              │
│ Specialty Quick   │                              │
│ Access (Context)  │                              │
│                   │                              │
└───────────────────┴──────────────────────────────┘
```

### Sidebar Médica
- **Width**: 280px (nunca menos, nunca mais)
- **Logo**: 48x48px com animação de breathing
- **Seções**: Logo → Quick Action → Modules → Context → Settings
- **Scroll**: Custom scrollbar médica
- **Background**: Gradient sutil baseado na paleta

### Content Area
- **Background**: Gradient diagonal sutil (5% opacity)
- **Padding**: 24px minimum
- **Max Width**: 1400px para dashboards, ilimitado para chat
- **Cards**: Shadow médica: `0 4px 20px rgba(46, 134, 222, 0.08)`

---

## 🧩 Componentes Médicos

### Medical Card
```typescript
interface MedicalCardProps {
  specialty: 'cardiology' | 'neurology' | 'respiratory' | 'gastro' | 'endocrinology'
  urgency: 'low' | 'medium' | 'high' | 'emergency'
  interactive?: boolean
}

// Sempre incluir:
// - Border-left colorido por especialidade
// - Hover animations
// - Status indicators
// - Icon matching specialty
```

### Medical Button
- **Primary**: Gradient médico principal
- **Secondary**: Outline com cor da especialidade
- **Danger**: Cor de emergência médica
- **Loading**: Pulse animation médica
- **Sizes**: sm (32px), md (40px), lg (48px)

### Medical Badge
```css
/* Status badges com cores médicas */
.badge-emergency { background: var(--medical-emergency); }
.badge-high { background: var(--medical-cardiology); }
.badge-medium { background: var(--medical-warning); }
.badge-low { background: var(--medical-success); }
```

### Medical Typography
- **Headers**: Sempre com gradient text para títulos principais
- **Body**: Inter/System font, 14px base
- **Medical Terms**: Highlight com background da especialidade (20% opacity)
- **Numbers**: Monospace para valores vitais

---

## 📱 Responsividade Médica

### Breakpoints
```css
--mobile: 768px       /* Tablets médicos */
--desktop: 1024px     /* Estações de trabalho */
--wide: 1400px        /* Monitores grandes */
```

### Adaptações Mobile
- **Sidebar**: Transforma em bottom navigation médica
- **Cards**: Stack verticalmente com spacing reduzido
- **Inputs**: Altura mínima 44px para touch médico
- **Tables**: Scroll horizontal com sticky headers

---

## 🎯 Interações Premium

### Hover States
```css
/* Padrão médico para hover */
.medical-hover {
  transition: all 0.2s ease-out;
}

.medical-hover:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(46, 134, 222, 0.15);
}
```

### Loading States
- **Skeleton**: Gradient shimmer médico
- **Spinners**: Pulse com cores da especialidade
- **Progress**: Gradient progress bars
- **Page loads**: Fade in com stagger

### Focus States
- **Ring**: Cor da especialidade médica
- **Keyboard**: Tab navigation sempre visível
- **Screen readers**: Labels adequados para termos médicos

---

## 🚀 Performance e Acessibilidade

### Performance Médica
- **Animações**: `prefers-reduced-motion` support
- **Images**: Lazy loading com skeleton médico
- **Fonts**: Preload da Inter font
- **Optimization**: Code splitting por módulo médico

### Acessibilidade Hospitalar
- **Contrast**: WCAG AA compliance mínimo
- **Screen readers**: ARIA labels médicos específicos
- **Keyboard**: Navegação completa sem mouse
- **Color blind**: Nunca use apenas cor para informação crítica

---

## 📋 Padrões de Chat Médico

### Interface Conversacional
```typescript
// Estilo OpenAI para chat médico
interface MedicalChatMessage {
  type: 'user' | 'assistant'
  category: 'diagnostic' | 'treatment' | 'general' | 'emergency'
  specialty?: MedicalSpecialty
  urgency?: UrgencyLevel
}
```

### Layout do Chat
- **Messages**: Max-width 700px, centralizados
- **Input**: Sticky bottom, shadow médica
- **Avatars**: User (médico) vs Assistant (IA)
- **Typing**: Dots animation médica
- **Quick Actions**: Buttons contextuais médicos

---

## 🎨 Efeitos Visuais Médicos

### Glass Morphism Médico
```css
.medical-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(46, 134, 222, 0.2);
}
```

### Shadows Médicas
```css
--shadow-medical-sm: 0 2px 8px rgba(46, 134, 222, 0.08);
--shadow-medical-md: 0 4px 20px rgba(46, 134, 222, 0.12);
--shadow-medical-lg: 0 8px 30px rgba(46, 134, 222, 0.15);
--shadow-medical-xl: 0 12px 40px rgba(46, 134, 222, 0.18);
```

### Gradients Contextuais
- **Emergency**: Red to orange gradient
- **Success**: Green to teal gradient  
- **Warning**: Orange to yellow gradient
- **Info**: Blue gradient do logo

---

## 🔧 Implementação Técnica

### CSS Custom Properties
```css
/* Sempre use as variáveis médicas */
:root {
  /* Spacing médico */
  --space-medical-xs: 4px;
  --space-medical-sm: 8px;
  --space-medical-md: 16px;
  --space-medical-lg: 24px;
  --space-medical-xl: 32px;
  
  /* Border radius médico */
  --radius-medical-sm: 6px;
  --radius-medical-md: 10px;
  --radius-medical-lg: 16px;
  --radius-medical-xl: 20px;
}
```

### Motion Presets
```typescript
// Presets de animação médica
export const medicalMotionPresets = {
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.2, ease: "easeOut" }
  },
  
  slideInRight: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  }
}
```

---

## 📊 Métricas e Monitoramento

### UX Metrics
- **Time to First Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Animation Frame Rate**: 60fps consistente
- **Interaction to Next Paint**: < 200ms

### Medical UX Goals
- **Task Completion**: > 95% success rate
- **Error Recovery**: < 3 steps to recover
- **Cognitive Load**: Minimal UI, maximum information
- **Trust Indicators**: Clear data sources, confidence levels

---

## 🎯 Exemplos de Implementação

### Medical Module Header
```tsx
const MedicalModuleHeader = ({ title, specialty, description, icon: Icon }) => (
  <motion.div 
    className="border-b bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm p-6 shadow-sm"
    initial={{ y: -10, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.3 }}
  >
    <div className="flex items-center gap-3">
      <div 
        className="p-3 rounded-xl"
        style={{ backgroundColor: `${getSpecialtyColor(specialty)}20` }}
      >
        <Icon className="h-6 w-6" style={{ color: getSpecialtyColor(specialty) }} />
      </div>
      <div>
        <h1 
          className="text-2xl font-semibold"
          style={{
            background: medicalColors.gradients.primary,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}
        >
          {title}
        </h1>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </div>
  </motion.div>
)
```

### Medical Card Pattern
```tsx
const MedicalCard = ({ children, specialty, interactive = true }) => (
  <motion.div
    className="shadow-lg border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
    variants={medicalCardVariants}
    initial="hidden"
    animate="visible"
    whileHover={interactive ? "hover" : undefined}
    style={{
      borderLeft: `4px solid ${getSpecialtyColor(specialty)}`
    }}
  >
    {children}
  </motion.div>
)
```

---

## ✅ Checklist de Implementação

### Para Cada Componente Médico:
- [ ] Usa cores da paleta médica oficial
- [ ] Inclui animações suaves e profissionais
- [ ] Tem estados de hover, focus e active
- [ ] É totalmente responsivo
- [ ] Inclui feedback visual adequado
- [ ] Segue padrões de acessibilidade
- [ ] Tem loading states apropriados
- [ ] Usa gradients quando apropriado
- [ ] Inclui microinterações relevantes
- [ ] É otimizado para performance

### Para Cada Página/Módulo:
- [ ] Header com gradient e ícone especializado
- [ ] Background com gradient sutil médico
- [ ] Animações de entrada staggered
- [ ] Navigation breadcrumb quando necessário
- [ ] Estados vazios elegantes
- [ ] Error boundaries médicos
- [ ] Loading skeletons médicos
- [ ] Transições entre estados
- [ ] Feedback de ações do usuário
- [ ] Otimização para workflow médico

---

## 🚨 Regras Críticas

### ❌ NUNCA:
- Use cores fora da paleta médica definida
- Implemente animações sem considerar performance
- Ignore estados de acessibilidade
- Use gradients excessivos ou chamativos
- Negligencie feedback visual de ações
- Implemente sem considerar mobile first
- Use textos sem hierarchy clara
- Ignore estados de erro ou loading

### ✅ SEMPRE:
- Referencie as cores médicas do contexto
- Implemente animações suaves e profissionais
- Considere o workflow médico específico
- Use gradients para elementos importantes
- Inclua feedback visual imediato
- Teste em dispositivos médicos reais
- Mantenha consistência entre módulos
- Priorize clareza sobre estética

---

*Este documento é um guia vivo e deve ser atualizado conforme o sistema evolui. Sempre consulte este guideline antes de implementar novos componentes ou funcionalidades.*