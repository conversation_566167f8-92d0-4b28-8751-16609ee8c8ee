import { ExtendedAnamneseData } from '../components/MedicalContext';

// ============================================================================
// TIPOS BASE PARA RELATÓRIOS MÉDICOS
// ============================================================================

/**
 * Relatório médico gerado automaticamente baseado na anamnese
 */
export interface RelatorioMedico {
  id: string;
  pacienteId: string;
  anamneseId: string;
  conteudo: string;
  sugestoesDiagnostico: SugestaoDiagnostico[];
  recomendacoesTratamento: RecomendacaoTratamento[];
  template: TemplateRelatorio;
  dataCriacao: Date;
  dataModificacao: Date;
  status: 'rascunho' | 'finalizado' | 'exportado';
  versao: number;
  observacoesMedico?: string;
  assinaturaDigital?: string;
}

/**
 * Template de relatório para diferentes especialidades médicas
 */
export interface TemplateRelatorio {
  id: string;
  nome: string;
  especialidade: EspecialidadeMedica;
  estrutura: EstruturaRelatorio;
  camposObrigatorios: string[];
  camposOpcionais: string[];
  formatoExportacao: FormatoExportacao[];
  ativo: boolean;
  dataCriacao: Date;
  dataModificacao: Date;
}

/**
 * Sugestão de diagnóstico diferencial baseada em sintomas e evidências
 */
export interface SugestaoDiagnostico {
  id: string;
  codigo: string; // CID-10 ou CID-11
  descricao: string;
  confianca: number; // 0-100
  evidencia: string[];
  sintomasRelacionados: string[];
  examesComplementares: string[];
  observacoes: string;
  fonte: 'algoritmo' | 'base_conhecimento' | 'ia' | 'manual';
}

/**
 * Recomendação de tratamento baseada em guidelines médicas
 */
export interface RecomendacaoTratamento {
  id: string;
  tipo: TipoTratamento;
  descricao: string;
  medicamentos: MedicamentoRecomendado[];
  exames: ExameRecomendado[];
  observacoes: string;
  nivelEvidencia: NivelEvidencia;
  fonte: string;
  contraindicacoes: string[];
  interacoes: string[];
}

/**
 * Histórico de relatórios gerados para um paciente
 */
export interface HistoricoRelatorios {
  pacienteId: string;
  relatorios: RelatorioMedico[];
  totalRelatorios: number;
  ultimoRelatorio?: RelatorioMedico;
  dataUltimaAtualizacao: Date;
}

// ============================================================================
// TIPOS AUXILIARES
// ============================================================================

export type EspecialidadeMedica = 
  | 'clinica_geral'
  | 'cardiologia'
  | 'pneumologia'
  | 'neurologia'
  | 'gastroenterologia'
  | 'endocrinologia'
  | 'ortopedia'
  | 'dermatologia'
  | 'psiquiatria'
  | 'pediatria'
  | 'geriatria'
  | 'emergencia'
  | 'uti';

export type FormatoExportacao = 'pdf' | 'docx' | 'html' | 'txt';

export type TipoTratamento = 
  | 'medicamentoso'
  | 'cirurgico'
  | 'fisioterapico'
  | 'psicologico'
  | 'dietetico'
  | 'observacao'
  | 'encaminhamento';

export type NivelEvidencia = 'A' | 'B' | 'C' | 'D';

export interface EstruturaRelatorio {
  secoes: SecaoRelatorio[];
  ordem: string[];
  estilos: EstilosRelatorio;
}

export interface SecaoRelatorio {
  id: string;
  titulo: string;
  conteudo: string;
  obrigatoria: boolean;
  ordem: number;
  campos: CampoRelatorio[];
}

export interface CampoRelatorio {
  id: string;
  nome: string;
  tipo: 'texto' | 'numero' | 'data' | 'lista' | 'booleano';
  obrigatorio: boolean;
  valorPadrao?: any;
  opcoes?: string[];
  validacao?: RegraValidacao;
}

export interface RegraValidacao {
  tipo: 'min' | 'max' | 'regex' | 'custom';
  valor: any;
  mensagem: string;
}

export interface EstilosRelatorio {
  fonte: string;
  tamanhoFonte: number;
  corPrimaria: string;
  corSecundaria: string;
  logo?: string;
  rodape?: string;
}

export interface MedicamentoRecomendado {
  nome: string;
  principioAtivo: string;
  dosagem: string;
  via: string;
  frequencia: string;
  duracao: string;
  observacoes: string;
  contraindicacoes: string[];
}

export interface ExameRecomendado {
  nome: string;
  tipo: string;
  urgencia: 'rotina' | 'urgente' | 'emergencia';
  justificativa: string;
  preparo?: string;
  observacoes: string;
}

// ============================================================================
// TIPOS PARA SERVIÇOS
// ============================================================================

export interface DadosRelatorio {
  anamnese: ExtendedAnamneseData;
  template: TemplateRelatorio;
  observacoesMedico?: string;
  configuracoes?: ConfiguracoesRelatorio;
}

export interface ConfiguracoesRelatorio {
  incluirSugestoesDiagnostico: boolean;
  incluirRecomendacoesTratamento: boolean;
  incluirExamesComplementares: boolean;
  incluirMedicamentos: boolean;
  incluirObservacoes: boolean;
  formatoExportacao: FormatoExportacao;
  idioma: 'pt-BR' | 'en-US' | 'es-ES';
}

export interface ResultadoGeracaoRelatorio {
  sucesso: boolean;
  relatorio?: RelatorioMedico;
  erro?: string;
  sugestoes?: SugestaoDiagnostico[];
  recomendacoes?: RecomendacaoTratamento[];
  tempoGeracao: number;
}

export interface ResultadoExportacaoRelatorio {
  sucesso: boolean;
  arquivo?: Blob;
  nomeArquivo?: string;
  erro?: string;
  tamanhoArquivo?: number;
}

// ============================================================================
// TIPOS PARA COMPONENTES REACT
// ============================================================================

export interface ReportGeneratorProps {
  anamnese: ExtendedAnamneseData;
  template?: TemplateRelatorio;
  onRelatorioGerado: (relatorio: RelatorioMedico) => void;
  onErro: (erro: string) => void;
  configuracao?: ConfiguracoesRelatorio;
}

export interface ReportViewerProps {
  relatorio: RelatorioMedico;
  editavel?: boolean;
  onRelatorioEditado?: (relatorio: RelatorioMedico) => void;
  onExportar?: (formato: FormatoExportacao) => void;
}

export interface ReportTemplatesProps {
  especialidade?: EspecialidadeMedica;
  onTemplateSelecionado: (template: TemplateRelatorio) => void;
  onNovoTemplate?: () => void;
}

export interface ReportExporterProps {
  relatorio: RelatorioMedico;
  formatosDisponiveis: FormatoExportacao[];
  onExportar: (formato: FormatoExportacao) => Promise<ResultadoExportacaoRelatorio>;
  onErro: (erro: string) => void;
}

export interface DiagnosisSuggestionsProps {
  anamnese: ExtendedAnamneseData;
  sugestoes: SugestaoDiagnostico[];
  onSugestaoSelecionada: (sugestao: SugestaoDiagnostico) => void;
  onSugestaoRejeitada: (sugestao: SugestaoDiagnostico) => void;
  carregando?: boolean;
}

export interface TreatmentRecommendationsProps {
  anamnese: ExtendedAnamneseData;
  sugestoesDiagnostico: SugestaoDiagnostico[];
  recomendacoes: RecomendacaoTratamento[];
  onRecomendacaoSelecionada: (recomendacao: RecomendacaoTratamento) => void;
  onRecomendacaoRejeitada: (recomendacao: RecomendacaoTratamento) => void;
  carregando?: boolean;
}

// ============================================================================
// TIPOS PARA HOOKS
// ============================================================================

export interface UseReportGeneratorReturn {
  gerarRelatorio: (dados: DadosRelatorio) => Promise<ResultadoGeracaoRelatorio>;
  carregando: boolean;
  erro: string | null;
  ultimoRelatorio: RelatorioMedico | null;
}

export interface UseReportExporterReturn {
  exportarRelatorio: (relatorio: RelatorioMedico, formato: FormatoExportacao) => Promise<ResultadoExportacaoRelatorio>;
  carregando: boolean;
  erro: string | null;
}

export interface UseReportTemplatesReturn {
  templates: TemplateRelatorio[];
  carregando: boolean;
  erro: string | null;
  buscarTemplates: (especialidade?: EspecialidadeMedica) => Promise<void>;
  criarTemplate: (template: Omit<TemplateRelatorio, 'id' | 'dataCriacao' | 'dataModificacao'>) => Promise<TemplateRelatorio>;
}

// ============================================================================
// TIPOS PARA VALIDAÇÃO
// ============================================================================

export interface ValidacaoRelatorio {
  valido: boolean;
  erros: ErroValidacao[];
  avisos: AvisoValidacao[];
}

export interface ErroValidacao {
  campo: string;
  mensagem: string;
  nivel: 'erro' | 'aviso' | 'info';
}

export interface AvisoValidacao {
  campo: string;
  mensagem: string;
  sugestao?: string;
}

// ============================================================================
// TIPOS PARA EVENTOS E CALLBACKS
// ============================================================================

export type RelatorioEvento = 
  | 'relatorio_gerado'
  | 'relatorio_editado'
  | 'relatorio_exportado'
  | 'template_selecionado'
  | 'sugestao_aceita'
  | 'sugestao_rejeitada'
  | 'erro_geracao'
  | 'erro_exportacao';

export interface RelatorioEventoData {
  tipo: RelatorioEvento;
  timestamp: Date;
  dados: any;
  usuario?: string;
}

// ============================================================================
// CONSTANTES E ENUMS
// ============================================================================

export const ESPECIALIDADES_MEDICAS: Record<EspecialidadeMedica, string> = {
  clinica_geral: 'Clínica Geral',
  cardiologia: 'Cardiologia',
  pneumologia: 'Pneumologia',
  neurologia: 'Neurologia',
  gastroenterologia: 'Gastroenterologia',
  endocrinologia: 'Endocrinologia',
  ortopedia: 'Ortopedia',
  dermatologia: 'Dermatologia',
  psiquiatria: 'Psiquiatria',
  pediatria: 'Pediatria',
  geriatria: 'Geriatria',
  emergencia: 'Emergência',
  uti: 'UTI'
};

export const FORMATOS_EXPORTACAO: Record<FormatoExportacao, string> = {
  pdf: 'PDF',
  docx: 'Word (DOCX)',
  html: 'HTML',
  txt: 'Texto (TXT)'
};

export const TIPOS_TRATAMENTO: Record<TipoTratamento, string> = {
  medicamentoso: 'Medicamentoso',
  cirurgico: 'Cirúrgico',
  fisioterapico: 'Fisioterápico',
  psicologico: 'Psicológico',
  dietetico: 'Dietético',
  observacao: 'Observação',
  encaminhamento: 'Encaminhamento'
};

export const NIVEIS_EVIDENCIA: Record<NivelEvidencia, string> = {
  A: 'Evidência A - Alta qualidade',
  B: 'Evidência B - Boa qualidade',
  C: 'Evidência C - Qualidade moderada',
  D: 'Evidência D - Baixa qualidade'
};
