export interface DiagnosisSuggestion {
  id: string
  name: string
  icd10: string
  probability: number
  evidence: string[]
  relatedSymptoms: string[]
  additionalTests?: string[]
  urgency: 'baixa' | 'media' | 'alta' | 'emergencia'
}

export interface TreatmentOption {
  id: string
  type: 'medicamentoso' | 'nao-medicamentoso' | 'procedimento'
  name: string
  description: string
  medications?: MedicationRecommendation[]
  guidelines: string[]
  contraindicatedIf?: string[]
  priority: number
}

export interface MedicationRecommendation {
  name: string
  activeIngredient: string
  dosage: string
  route: string
  frequency: string
  duration: string
  warnings?: string[]
  interactions?: string[]
}

export interface ExamSuggestion {
  id: string
  name: string
  type: 'laboratorial' | 'imagem' | 'funcional'
  rationale: string
  urgency: 'rotina' | 'urgente' | 'emergencial'
  expectedFindings?: string[]
  cost: 'baixo' | 'medio' | 'alto'
}

export interface RiskAssessment {
  factor: string
  level: 'baixo' | 'moderado' | 'alto' | 'critico'
  description: string
  recommendations: string[]
  monitoringRequired: boolean
}

export interface AIAnalysisResult {
  differentialDiagnosis: DiagnosisSuggestion[]
  treatmentRecommendations: TreatmentOption[]
  additionalExams: ExamSuggestion[]
  riskFactors: RiskAssessment[]
  clinicalPearls: string[]
  redFlags: string[]
  confidence: number
  reasoning: string
  timestamp: Date
}

export interface AIServiceConfig {
  provider: 'openai' | 'anthropic' | 'local'
  apiKey?: string
  model?: string
  temperature?: number
  maxTokens?: number
  enableCache?: boolean
  cacheTimeout?: number
}