/* eslint-env node */
module.exports = {
  root: true,
  env: { browser: true, es2023: true, node: true },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    ecmaFeatures: { jsx: true },
  },
  plugins: [
    "@typescript-eslint",
    "react",
    "react-hooks",
    "jsx-a11y",
  ],
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "prettier",
    "plugin:storybook/recommended"
  ],
  settings: {
    react: { version: "detect" },
  },
  rules: {
    // Keep JSX without React in scope (Vite/TSX)
    "react/react-in-jsx-scope": "off",
    // Relax strict rules to allow rapid iteration on this WIP codebase
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-require-imports": "off",
    "react/prop-types": "off",
    "react/jsx-no-undef": "off",
    "react/no-unescaped-entities": "off",
    "react-hooks/exhaustive-deps": "warn",
    "no-useless-escape": "off",
    // Accessibility rules as warnings for now
    "jsx-a11y/heading-has-content": "warn",
    "jsx-a11y/anchor-has-content": "warn",
    "jsx-a11y/img-redundant-alt": "warn",
    "jsx-a11y/click-events-have-key-events": "warn",
    "jsx-a11y/no-static-element-interactions": "warn",
    "react/jsx-no-duplicate-props": "warn",
  },
};
