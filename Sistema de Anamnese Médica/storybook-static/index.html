<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />

    <title>storybook - Storybook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    
    <link rel="icon" type="image/svg+xml" href="./favicon.svg" />
    
    <style>
      @font-face {
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-regular.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-italic.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-bold.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-bold-italic.woff2') format('woff2');
      }
    </style>

    <link href="./sb-manager/runtime.js" rel="modulepreload" />

    
    <link href="./sb-addons/storybook-core-server-presets-0/common-manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/chromatic-com-storybook-1/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/docs-2/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/onboarding-3/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/a11y-4/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/vitest-5/manager-bundle.js" rel="modulepreload" />
       

    <style>
      #storybook-root[hidden] {
        display: none !important;
      }
    </style>

    
  </head>
  <body>
    <div id="root"></div>

    
    <script>
      
        
          window['FEATURES'] = {
  "argTypeTargetsV7": true,
  "legacyDecoratorFileOrder": false,
  "disallowImplicitActionsInRenderV8": true,
  "viewport": true,
  "highlight": true,
  "controls": true,
  "interactions": true,
  "actions": true,
  "backgrounds": true,
  "outline": true,
  "measure": true
};
        
      
        
          window['REFS'] = {};
        
      
        
          window['LOGLEVEL'] = "info";
        
      
        
          window['DOCS_OPTIONS'] = {
  "defaultName": "Docs"
};
        
      
        
          window['CONFIG_TYPE'] = "PRODUCTION";
        
      
        
      
        
      
        
          window['TAGS_OPTIONS'] = {
  "dev-only": {
    "excludeFromDocsStories": true
  },
  "docs-only": {
    "excludeFromSidebar": true
  },
  "test-only": {
    "excludeFromSidebar": true,
    "excludeFromDocsStories": true
  }
};
        
      
        
          window['STORYBOOK_RENDERER'] = "react";
        
      
        
          window['STORYBOOK_BUILDER'] = "@storybook/builder-vite";
        
      
        
          window['STORYBOOK_FRAMEWORK'] = "@storybook/react-vite";
        
      
    </script>
    

    <script type="module">
      import './sb-manager/globals-runtime.js';

      
        import './sb-addons/storybook-core-server-presets-0/common-manager-bundle.js';
      
        import './sb-addons/chromatic-com-storybook-1/manager-bundle.js';
      
        import './sb-addons/docs-2/manager-bundle.js';
      
        import './sb-addons/onboarding-3/manager-bundle.js';
      
        import './sb-addons/a11y-4/manager-bundle.js';
      
        import './sb-addons/vitest-5/manager-bundle.js';
      

      import './sb-manager/runtime.js';
    </script>
  </body>
</html>
