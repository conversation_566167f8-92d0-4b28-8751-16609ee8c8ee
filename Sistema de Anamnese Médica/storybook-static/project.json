{"generatedAt": 1757497123992, "userSince": 1754626717998, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.1.1", "@storybook/addon-vitest": "9.1.5", "@testing-library/jest-dom": "6.8.0", "@testing-library/react": "16.3.0", "@testing-library/user-event": null, "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "chromatic": "13.1.4", "jest-axe": "10.0.0", "playwright": "1.55.0", "vitest": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.1.5", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.1.5"}, "eslint-plugin-storybook": {"version": "9.1.5"}, "storybook": {"version": "9.1.5"}}, "addons": {"@chromatic-com/storybook": {"version": "4.1.1"}, "@storybook/addon-docs": {"version": "9.1.5"}, "@storybook/addon-onboarding": {"version": "9.1.5"}, "@storybook/addon-a11y": {"version": "9.1.5"}, "@storybook/addon-vitest": {"version": "9.1.5"}, "chromatic": {"version": "13.1.4", "versionSpecifier": "^13.1.4"}}}