import{_ as me}from"./assets/preload-helper-D9Z9MdNV.js";class se{registryByUrl=new Map;registryById=new Map;clear(){this.registryByUrl.clear(),this.registryById.clear()}keys(){return this.registryByUrl.keys()}add(e){this.registryByUrl.set(e.url,e),this.registryById.set(e.id,e)}register(e,t,r,i,s){const a=typeof e=="object"?e.type:e;if(typeof e=="object"){const o=e;if(o instanceof q||o instanceof O||o instanceof F||o instanceof C)throw new TypeError(`[vitest] Cannot register a mock that is already defined. Expected a JSON representation from \`MockedModule.toJSON\`, instead got "${o.type}". Use "registry.add()" to update a mock instead.`);if(o.type==="automock"){const l=q.fromJSON(o);return this.add(l),l}else if(o.type==="autospy"){const l=O.fromJSON(o);return this.add(l),l}else if(o.type==="redirect"){const l=C.fromJSON(o);return this.add(l),l}else throw o.type==="manual"?new Error("Cannot set serialized manual mock. Define a factory function manually with `ManualMockedModule.fromJSON()`."):new Error(`Unknown mock type: ${o.type}`)}if(typeof t!="string")throw new TypeError("[vitest] Mocks require a raw string.");if(typeof i!="string")throw new TypeError("[vitest] Mocks require a url string.");if(typeof r!="string")throw new TypeError("[vitest] Mocks require an id string.");if(a==="manual"){if(typeof s!="function")throw new TypeError("[vitest] Manual mocks require a factory function.");const o=new F(t,r,i,s);return this.add(o),o}else if(a==="automock"||a==="autospy"){const o=a==="automock"?new q(t,r,i):new O(t,r,i);return this.add(o),o}else if(a==="redirect"){if(typeof s!="string")throw new TypeError("[vitest] Redirect mocks require a redirect string.");const o=new C(t,r,i,s);return this.add(o),o}else throw new Error(`[vitest] Unknown mock type: ${a}`)}delete(e){this.registryByUrl.delete(e)}get(e){return this.registryByUrl.get(e)}getById(e){return this.registryById.get(e)}has(e){return this.registryByUrl.has(e)}}class q{type="automock";constructor(e,t,r){this.raw=e,this.id=t,this.url=r}static fromJSON(e){return new O(e.raw,e.id,e.url)}toJSON(){return{type:this.type,url:this.url,raw:this.raw,id:this.id}}}class O{type="autospy";constructor(e,t,r){this.raw=e,this.id=t,this.url=r}static fromJSON(e){return new O(e.raw,e.id,e.url)}toJSON(){return{type:this.type,url:this.url,id:this.id,raw:this.raw}}}class C{type="redirect";constructor(e,t,r,i){this.raw=e,this.id=t,this.url=r,this.redirect=i}static fromJSON(e){return new C(e.raw,e.id,e.url,e.redirect)}toJSON(){return{type:this.type,url:this.url,raw:this.raw,id:this.id,redirect:this.redirect}}}class F{cache;type="manual";constructor(e,t,r,i){this.raw=e,this.id=t,this.url=r,this.factory=i}async resolve(){if(this.cache)return this.cache;let e;try{e=await this.factory()}catch(t){const r=new Error('[vitest] There was an error when mocking a module. If you are using "vi.mock" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock');throw r.cause=t,r}if(e===null||typeof e!="object"||Array.isArray(e))throw new TypeError(`[vitest] vi.mock("${this.raw}", factory?: () => unknown) is not returning an object. Did you mean to return an object with a "default" key?`);return this.cache=e}static fromJSON(e,t){return new F(e.raw,e.id,e.url,t)}toJSON(){return{type:this.type,url:this.url,id:this.id,raw:this.raw}}}function ye(n,e,t={}){const r=new Array,i=new ge,s=(l,d,h)=>{try{return l[d]=h,!0}catch{return!1}},a=(l,d)=>{const h=D(l),g=h==="Module"||!!l.__esModule;for(const{key:w,descriptor:b}of K(l,g,n.globalConstructors)){if(!g&&b.get){try{Object.defineProperty(d,w,b)}catch{}continue}if(ve(w,h))continue;const y=l[w],c=i.getId(y);if(c!==void 0){r.push(()=>s(d,w,i.getMockedValue(c)));continue}const u=D(y);if(Array.isArray(y)){s(d,w,[]);continue}const x=u.includes("Function")&&typeof y=="function";if((!x||y._isMockFunction)&&u!=="Object"&&u!=="Module"){s(d,w,y);continue}if(s(d,w,x?y:{})){if(x){let _=function(){if(this instanceof d[w])for(const{key:I,descriptor:R}of K(this,!1,n.globalConstructors)){if(R.get)continue;const N=this[I];if(D(N).includes("Function")&&typeof N=="function"){const L=this[I],f=M(this,I).mockImplementation(L),$=f.mockReset;f.mockRestore=f.mockReset=()=>($.call(f),f.mockImplementation(L),f)}}};if(!n.spyOn)throw new Error("[@vitest/mocker] `spyOn` is not defined. This is a Vitest error. Please open a new issue with reproduction.");const M=n.spyOn,P=M(d,w);if(n.type==="automock"){P.mockImplementation(_);const I=P.mockReset;P.mockRestore=P.mockReset=()=>(I.call(P),P.mockImplementation(_),P)}Object.defineProperty(d[w],"length",{value:0})}i.track(y,d[w]),a(y,d[w])}}},o=t;a(e,o);for(const l of r)l();return o}class ge{idMap=new Map;mockedValueMap=new Map;getId(e){return this.idMap.get(e)}getMockedValue(e){return this.mockedValueMap.get(e)}track(e,t){const r=this.idMap.size;return this.idMap.set(e,r),this.mockedValueMap.set(r,t),r}}function D(n){return Object.prototype.toString.apply(n).slice(8,-1)}function ve(n,e){return e.includes("Function")&&typeof n=="string"&&["arguments","callee","caller","length","name"].includes(n)}function K(n,e,t){const{Map:r,Object:i,Function:s,RegExp:a,Array:o}=t,l=new r;let d=n;do{if(d===i.prototype||d===s.prototype||d===a.prototype)break;we(d,h=>{const g=i.getOwnPropertyDescriptor(d,h);g&&l.set(h,{key:h,descriptor:g})})}while(d=i.getPrototypeOf(d));if(e&&!l.has("default")&&"default"in n){const h=i.getOwnPropertyDescriptor(n,"default");h&&l.set("default",{key:"default",descriptor:h})}return o.from(l.values())}function we(n,e){const t=typeof e=="function"?e:r=>e.add(r);Object.getOwnPropertyNames(n).forEach(t),Object.getOwnPropertySymbols(n).forEach(t)}const ke=/^[A-Za-z]:\//;function ce(n=""){return n&&n.replace(/\\/g,"/").replace(ke,e=>e.toUpperCase())}const Se=/^[/\\]{2}/,_e=/^[/\\](?![/\\])|^[/\\]{2}(?!\.)|^[A-Za-z]:[/\\]/,Ee=/^[A-Za-z]:$/,be=/.(\.[^./]+|\.)$/,Ie=function(n){if(n.length===0)return".";n=ce(n);const e=n.match(Se),t=H(n),r=n[n.length-1]==="/";return n=Te(n,!t),n.length===0?t?"/":r?"./":".":(r&&(n+="/"),Ee.test(n)&&(n+="/"),e?t?`//${n}`:`//./${n}`:t&&!H(n)?`/${n}`:n)},xe=function(...n){let e="";for(const t of n)if(t)if(e.length>0){const r=e[e.length-1]==="/",i=t[0]==="/";r&&i?e+=t.slice(1):e+=r||i?t:`/${t}`}else e+=t;return Ie(e)};function Te(n,e){let t="",r=0,i=-1,s=0,a=null;for(let o=0;o<=n.length;++o){if(o<n.length)a=n[o];else{if(a==="/")break;a="/"}if(a==="/"){if(!(i===o-1||s===1))if(s===2){if(t.length<2||r!==2||t[t.length-1]!=="."||t[t.length-2]!=="."){if(t.length>2){const l=t.lastIndexOf("/");l===-1?(t="",r=0):(t=t.slice(0,l),r=t.length-1-t.lastIndexOf("/")),i=o,s=0;continue}else if(t.length>0){t="",r=0,i=o,s=0;continue}}e&&(t+=t.length>0?"/..":"..",r=2)}else t.length>0?t+=`/${n.slice(i+1,o)}`:t=n.slice(i+1,o),r=o-i-1;i=o,s=0}else a==="."&&s!==-1?++s:s=-1}return t}const H=function(n){return _e.test(n)},$e=function(n){if(n==="..")return"";const e=be.exec(ce(n));return e&&e[1]||""};var Pe={reset:[0,0],bold:[1,22,"\x1B[22m\x1B[1m"],dim:[2,22,"\x1B[22m\x1B[2m"],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]},Me=Object.entries(Pe);function j(n){return String(n)}j.open="";j.close="";function Re(n=!1){let e=typeof process<"u"?process:void 0,t=e?.env||{},r=e?.argv||[];return!("NO_COLOR"in t||r.includes("--no-color"))&&("FORCE_COLOR"in t||r.includes("--color")||e?.platform==="win32"||n&&t.TERM!=="dumb"||"CI"in t)||typeof window<"u"&&!!window.chrome}function Ae(n=!1){let e=Re(n),t=(a,o,l,d)=>{let h="",g=0;do h+=a.substring(g,d)+l,g=d+o.length,d=a.indexOf(o,g);while(~d);return h+a.substring(g)},r=(a,o,l=a)=>{let d=h=>{let g=String(h),w=g.indexOf(o,a.length);return~w?a+t(g,o,l,w)+o:a+g+o};return d.open=a,d.close=o,d},i={isColorSupported:e},s=a=>`\x1B[${a}m`;for(let[a,o]of Me)i[a]=e?r(s(o[0]),s(o[1]),o[2]):j;return i}Ae();function ae(n,e){return e.forEach(function(t){t&&typeof t!="string"&&!Array.isArray(t)&&Object.keys(t).forEach(function(r){if(r!=="default"&&!(r in n)){var i=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return t[r]}})}})}),Object.freeze(n)}function le(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var U={exports:{}},k={};/**
* @license React
* react-is.production.js
*
* Copyright (c) Meta Platforms, Inc. and affiliates.
*
* This source code is licensed under the MIT license found in the
* LICENSE file in the root directory of this source tree.
*/var Z;function Oe(){if(Z)return k;Z=1;var n=Symbol.for("react.transitional.element"),e=Symbol.for("react.portal"),t=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),a=Symbol.for("react.context"),o=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),w=Symbol.for("react.view_transition"),b=Symbol.for("react.client.reference");function y(c){if(typeof c=="object"&&c!==null){var u=c.$$typeof;switch(u){case n:switch(c=c.type,c){case t:case i:case r:case l:case d:case w:return c;default:switch(c=c&&c.$$typeof,c){case a:case o:case g:case h:return c;case s:return c;default:return u}}case e:return u}}}return k.ContextConsumer=s,k.ContextProvider=a,k.Element=n,k.ForwardRef=o,k.Fragment=t,k.Lazy=g,k.Memo=h,k.Portal=e,k.Profiler=i,k.StrictMode=r,k.Suspense=l,k.SuspenseList=d,k.isContextConsumer=function(c){return y(c)===s},k.isContextProvider=function(c){return y(c)===a},k.isElement=function(c){return typeof c=="object"&&c!==null&&c.$$typeof===n},k.isForwardRef=function(c){return y(c)===o},k.isFragment=function(c){return y(c)===t},k.isLazy=function(c){return y(c)===g},k.isMemo=function(c){return y(c)===h},k.isPortal=function(c){return y(c)===e},k.isProfiler=function(c){return y(c)===i},k.isStrictMode=function(c){return y(c)===r},k.isSuspense=function(c){return y(c)===l},k.isSuspenseList=function(c){return y(c)===d},k.isValidElementType=function(c){return typeof c=="string"||typeof c=="function"||c===t||c===i||c===r||c===l||c===d||typeof c=="object"&&c!==null&&(c.$$typeof===g||c.$$typeof===h||c.$$typeof===a||c.$$typeof===s||c.$$typeof===o||c.$$typeof===b||c.getModuleId!==void 0)},k.typeOf=y,k}var G;function Ce(){return G||(G=1,U.exports=Oe()),U.exports}var ue=Ce(),Ne=le(ue),Le=ae({__proto__:null,default:Ne},[ue]),z={exports:{}},v={};/**
* @license React
* react-is.production.min.js
*
* Copyright (c) Facebook, Inc. and its affiliates.
*
* This source code is licensed under the MIT license found in the
* LICENSE file in the root directory of this source tree.
*/var Q;function Je(){if(Q)return v;Q=1;var n=Symbol.for("react.element"),e=Symbol.for("react.portal"),t=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),a=Symbol.for("react.context"),o=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),g=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),b=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function c(u){if(typeof u=="object"&&u!==null){var x=u.$$typeof;switch(x){case n:switch(u=u.type,u){case t:case i:case r:case d:case h:return u;default:switch(u=u&&u.$$typeof,u){case o:case a:case l:case w:case g:case s:return u;default:return x}}case e:return x}}}return v.ContextConsumer=a,v.ContextProvider=s,v.Element=n,v.ForwardRef=l,v.Fragment=t,v.Lazy=w,v.Memo=g,v.Portal=e,v.Profiler=i,v.StrictMode=r,v.Suspense=d,v.SuspenseList=h,v.isAsyncMode=function(){return!1},v.isConcurrentMode=function(){return!1},v.isContextConsumer=function(u){return c(u)===a},v.isContextProvider=function(u){return c(u)===s},v.isElement=function(u){return typeof u=="object"&&u!==null&&u.$$typeof===n},v.isForwardRef=function(u){return c(u)===l},v.isFragment=function(u){return c(u)===t},v.isLazy=function(u){return c(u)===w},v.isMemo=function(u){return c(u)===g},v.isPortal=function(u){return c(u)===e},v.isProfiler=function(u){return c(u)===i},v.isStrictMode=function(u){return c(u)===r},v.isSuspense=function(u){return c(u)===d},v.isSuspenseList=function(u){return c(u)===h},v.isValidElementType=function(u){return typeof u=="string"||typeof u=="function"||u===t||u===i||u===r||u===d||u===h||u===b||typeof u=="object"&&u!==null&&(u.$$typeof===w||u.$$typeof===g||u.$$typeof===s||u.$$typeof===a||u.$$typeof===l||u.$$typeof===y||u.getModuleId!==void 0)},v.typeOf=c,v}var ee;function Be(){return ee||(ee=1,z.exports=Je()),z.exports}var fe=Be(),Xe=le(fe),qe=ae({__proto__:null,default:Xe},[fe]);const Fe=["isAsyncMode","isConcurrentMode","isContextConsumer","isContextProvider","isElement","isForwardRef","isFragment","isLazy","isMemo","isPortal","isProfiler","isStrictMode","isSuspense","isSuspenseList","isValidElementType"];Object.fromEntries(Fe.map(n=>[n,e=>qe[n](e)||Le[n](e)]));let De=()=>"Promise{…}";try{const{getPromiseDetails:n,kPending:e,kRejected:t}=process.binding("util");Array.isArray(n(Promise.resolve()))&&(De=(r,i)=>{const[s,a]=n(r);return s===e?"Promise{<pending>}":`Promise${s===t?"!":""}{${i.inspect(a,i)}}`})}catch{}function Ue(n){const{message:e="$$stack trace error",stackTraceLimit:t=1}=n||{},r=Error.stackTraceLimit,i=Error.prepareStackTrace;Error.stackTraceLimit=t,Error.prepareStackTrace=o=>o.stack;const a=new Error(e).stack||"";return Error.prepareStackTrace=i,Error.stackTraceLimit=r,a}var V,te;function ze(){if(te)return V;te=1;var n,e,t,r,i,s,a,o,l,d,h,g,w,b,y,c,u,x,M;return w=/\/(?![*\/])(?:\[(?:(?![\]\\]).|\\.)*\]|(?![\/\\]).|\\.)*(\/[$_\u200C\u200D\p{ID_Continue}]*|\\)?/uy,g=/--|\+\+|=>|\.{3}|\??\.(?!\d)|(?:&&|\|\||\?\?|[+\-%&|^]|\*{1,2}|<{1,2}|>{1,3}|!=?|={1,2}|\/(?![\/*]))=?|[?~,:;[\](){}]/y,n=/(\x23?)(?=[$_\p{ID_Start}\\])(?:[$_\u200C\u200D\p{ID_Continue}]|\\u[\da-fA-F]{4}|\\u\{[\da-fA-F]+\})+/uy,y=/(['"])(?:(?!\1)[^\\\n\r]|\\(?:\r\n|[^]))*(\1)?/y,h=/(?:0[xX][\da-fA-F](?:_?[\da-fA-F])*|0[oO][0-7](?:_?[0-7])*|0[bB][01](?:_?[01])*)n?|0n|[1-9](?:_?\d)*n|(?:(?:0(?!\d)|0\d*[89]\d*|[1-9](?:_?\d)*)(?:\.(?:\d(?:_?\d)*)?)?|\.\d(?:_?\d)*)(?:[eE][+-]?\d(?:_?\d)*)?|0[0-7]+/y,c=/[`}](?:[^`\\$]|\\[^]|\$(?!\{))*(`|\$\{)?/y,M=/[\t\v\f\ufeff\p{Zs}]+/uy,o=/\r?\n|[\r\u2028\u2029]/y,l=/\/\*(?:[^*]|\*(?!\/))*(\*\/)?/y,b=/\/\/.*/y,t=/[<>.:={}]|\/(?![\/*])/y,e=/[$_\p{ID_Start}][$_\u200C\u200D\p{ID_Continue}-]*/uy,r=/(['"])(?:(?!\1)[^])*(\1)?/y,i=/[^<>{}]+/y,x=/^(?:[\/+-]|\.{3}|\?(?:InterpolationIn(?:JSX|Template)|NoLineTerminatorHere|NonExpressionParenEnd|UnaryIncDec))?$|[{}([,;<>=*%&|^!~?:]$/,u=/^(?:=>|[;\]){}]|else|\?(?:NoLineTerminatorHere|NonExpressionParenEnd))?$/,s=/^(?:await|case|default|delete|do|else|instanceof|new|return|throw|typeof|void|yield)$/,a=/^(?:return|throw|yield)$/,d=RegExp(o.source),V=function*(_,{jsx:P=!1}={}){var I,R,N,p,m,L,f,$,Y,T,J,S,B,E;for({length:L}=_,p=0,m="",E=[{tag:"JS"}],I=[],J=0,S=!1;p<L;){switch($=E[E.length-1],$.tag){case"JS":case"JSNonExpressionParen":case"InterpolationInTemplate":case"InterpolationInJSX":if(_[p]==="/"&&(x.test(m)||s.test(m))&&(w.lastIndex=p,f=w.exec(_))){p=w.lastIndex,m=f[0],S=!0,yield{type:"RegularExpressionLiteral",value:f[0],closed:f[1]!==void 0&&f[1]!=="\\"};continue}if(g.lastIndex=p,f=g.exec(_)){switch(B=f[0],Y=g.lastIndex,T=B,B){case"(":m==="?NonExpressionParenKeyword"&&E.push({tag:"JSNonExpressionParen",nesting:J}),J++,S=!1;break;case")":J--,S=!0,$.tag==="JSNonExpressionParen"&&J===$.nesting&&(E.pop(),T="?NonExpressionParenEnd",S=!1);break;case"{":g.lastIndex=0,N=!u.test(m)&&(x.test(m)||s.test(m)),I.push(N),S=!1;break;case"}":switch($.tag){case"InterpolationInTemplate":if(I.length===$.nesting){c.lastIndex=p,f=c.exec(_),p=c.lastIndex,m=f[0],f[1]==="${"?(m="?InterpolationInTemplate",S=!1,yield{type:"TemplateMiddle",value:f[0]}):(E.pop(),S=!0,yield{type:"TemplateTail",value:f[0],closed:f[1]==="`"});continue}break;case"InterpolationInJSX":if(I.length===$.nesting){E.pop(),p+=1,m="}",yield{type:"JSXPunctuator",value:"}"};continue}}S=I.pop(),T=S?"?ExpressionBraceEnd":"}";break;case"]":S=!0;break;case"++":case"--":T=S?"?PostfixIncDec":"?UnaryIncDec";break;case"<":if(P&&(x.test(m)||s.test(m))){E.push({tag:"JSXTag"}),p+=1,m="<",yield{type:"JSXPunctuator",value:B};continue}S=!1;break;default:S=!1}p=Y,m=T,yield{type:"Punctuator",value:B};continue}if(n.lastIndex=p,f=n.exec(_)){switch(p=n.lastIndex,T=f[0],f[0]){case"for":case"if":case"while":case"with":m!=="."&&m!=="?."&&(T="?NonExpressionParenKeyword")}m=T,S=!s.test(f[0]),yield{type:f[1]==="#"?"PrivateIdentifier":"IdentifierName",value:f[0]};continue}if(y.lastIndex=p,f=y.exec(_)){p=y.lastIndex,m=f[0],S=!0,yield{type:"StringLiteral",value:f[0],closed:f[2]!==void 0};continue}if(h.lastIndex=p,f=h.exec(_)){p=h.lastIndex,m=f[0],S=!0,yield{type:"NumericLiteral",value:f[0]};continue}if(c.lastIndex=p,f=c.exec(_)){p=c.lastIndex,m=f[0],f[1]==="${"?(m="?InterpolationInTemplate",E.push({tag:"InterpolationInTemplate",nesting:I.length}),S=!1,yield{type:"TemplateHead",value:f[0]}):(S=!0,yield{type:"NoSubstitutionTemplate",value:f[0],closed:f[1]==="`"});continue}break;case"JSXTag":case"JSXTagEnd":if(t.lastIndex=p,f=t.exec(_)){switch(p=t.lastIndex,T=f[0],f[0]){case"<":E.push({tag:"JSXTag"});break;case">":E.pop(),m==="/"||$.tag==="JSXTagEnd"?(T="?JSX",S=!0):E.push({tag:"JSXChildren"});break;case"{":E.push({tag:"InterpolationInJSX",nesting:I.length}),T="?InterpolationInJSX",S=!1;break;case"/":m==="<"&&(E.pop(),E[E.length-1].tag==="JSXChildren"&&E.pop(),E.push({tag:"JSXTagEnd"}))}m=T,yield{type:"JSXPunctuator",value:f[0]};continue}if(e.lastIndex=p,f=e.exec(_)){p=e.lastIndex,m=f[0],yield{type:"JSXIdentifier",value:f[0]};continue}if(r.lastIndex=p,f=r.exec(_)){p=r.lastIndex,m=f[0],yield{type:"JSXString",value:f[0],closed:f[2]!==void 0};continue}break;case"JSXChildren":if(i.lastIndex=p,f=i.exec(_)){p=i.lastIndex,m=f[0],yield{type:"JSXText",value:f[0]};continue}switch(_[p]){case"<":E.push({tag:"JSXTag"}),p++,m="<",yield{type:"JSXPunctuator",value:"<"};continue;case"{":E.push({tag:"InterpolationInJSX",nesting:I.length}),p++,m="?InterpolationInJSX",S=!1,yield{type:"JSXPunctuator",value:"{"};continue}}if(M.lastIndex=p,f=M.exec(_)){p=M.lastIndex,yield{type:"WhiteSpace",value:f[0]};continue}if(o.lastIndex=p,f=o.exec(_)){p=o.lastIndex,S=!1,a.test(m)&&(m="?NoLineTerminatorHere"),yield{type:"LineTerminatorSequence",value:f[0]};continue}if(l.lastIndex=p,f=l.exec(_)){p=l.lastIndex,d.test(f[0])&&(S=!1,a.test(m)&&(m="?NoLineTerminatorHere")),yield{type:"MultiLineComment",value:f[0],closed:f[1]!==void 0};continue}if(b.lastIndex=p,f=b.exec(_)){p=b.lastIndex,S=!1,yield{type:"SingleLineComment",value:f[0]};continue}R=String.fromCodePoint(_.codePointAt(p)),p+=R.length,m=R,S=!1,yield{type:$.tag.startsWith("JSX")?"JSXInvalid":"Invalid",value:R}}},V}ze();var de={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"]};new Set(de.keyword);new Set(de.strict);const re="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ve=new Uint8Array(64),We=new Uint8Array(128);for(let n=0;n<re.length;n++){const e=re.charCodeAt(n);Ve[n]=e,We[e]=n}var ne;(function(n){n[n.Empty=1]="Empty",n[n.Hash=2]="Hash",n[n.Query=3]="Query",n[n.RelativePath=4]="RelativePath",n[n.AbsolutePath=5]="AbsolutePath",n[n.SchemeRelative=6]="SchemeRelative",n[n.Absolute=7]="Absolute"})(ne||(ne={}));const je=/^[A-Za-z]:\//;function Ye(n=""){return n&&n.replace(/\\/g,"/").replace(je,e=>e.toUpperCase())}const Ke=/^[/\\](?![/\\])|^[/\\]{2}(?!\.)|^[A-Za-z]:[/\\]/;function He(){return typeof process<"u"&&typeof process.cwd=="function"?process.cwd().replace(/\\/g,"/"):"/"}const Ze=function(...n){n=n.map(r=>Ye(r));let e="",t=!1;for(let r=n.length-1;r>=-1&&!t;r--){const i=r>=0?n[r]:He();!i||i.length===0||(e=`${i}/${e}`,t=ie(i))}return e=Ge(e,!t),t&&!ie(e)?`/${e}`:e.length>0?e:"."};function Ge(n,e){let t="",r=0,i=-1,s=0,a=null;for(let o=0;o<=n.length;++o){if(o<n.length)a=n[o];else{if(a==="/")break;a="/"}if(a==="/"){if(!(i===o-1||s===1))if(s===2){if(t.length<2||r!==2||t[t.length-1]!=="."||t[t.length-2]!=="."){if(t.length>2){const l=t.lastIndexOf("/");l===-1?(t="",r=0):(t=t.slice(0,l),r=t.length-1-t.lastIndexOf("/")),i=o,s=0;continue}else if(t.length>0){t="",r=0,i=o,s=0;continue}}e&&(t+=t.length>0?"/..":"..",r=2)}else t.length>0?t+=`/${n.slice(i+1,o)}`:t=n.slice(i+1,o),r=o-i-1;i=o,s=0}else a==="."&&s!==-1?++s:s=-1}return t}const ie=function(n){return Ke.test(n)},pe=/^\s*at .*(?:\S:\d+|\(native\))/m,Qe=/^(?:eval@)?(?:\[native code\])?$/;function he(n){if(!n.includes(":"))return[n];const t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(n.replace(/^\(|\)$/g,""));if(!t)return[n];let r=t[1];if(r.startsWith("async ")&&(r=r.slice(6)),r.startsWith("http:")||r.startsWith("https:")){const i=new URL(r);i.searchParams.delete("import"),i.searchParams.delete("browserv"),r=i.pathname+i.hash+i.search}if(r.startsWith("/@fs/")){const i=/^\/@fs\/[a-zA-Z]:\//.test(r);r=r.slice(i?5:4)}return[r,t[2]||void 0,t[3]||void 0]}function et(n){let e=n.trim();if(Qe.test(e)||(e.includes(" > eval")&&(e=e.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),!e.includes("@")&&!e.includes(":")))return null;const t=/((.*".+"[^@]*)?[^@]*)(@)/,r=e.match(t),i=r&&r[1]?r[1]:void 0,[s,a,o]=he(e.replace(t,""));return!s||!a||!o?null:{file:s,method:i||"",line:Number.parseInt(a),column:Number.parseInt(o)}}function tt(n){const e=n.trim();return pe.test(e)?rt(e):et(e)}function rt(n){let e=n.trim();if(!pe.test(e))return null;e.includes("(eval ")&&(e=e.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));let t=e.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,"");const r=t.match(/ (\(.+\)$)/);t=r?t.replace(r[0],""):t;const[i,s,a]=he(r?r[1]:t);let o=r&&t||"",l=i&&["eval","<anonymous>"].includes(i)?void 0:i;return!l||!s||!a?null:(o.startsWith("async ")&&(o=o.slice(6)),l.startsWith("file://")&&(l=l.slice(7)),l=l.startsWith("node:")||l.startsWith("internal:")?l:Ze(l),o&&(o=o.replace(/__vite_ssr_import_\d+__\./g,"")),{method:o,file:l,line:Number.parseInt(s),column:Number.parseInt(a)})}function nt(n){const e=n?.globalThisKey||"__vitest_mocker__";function t(){return typeof globalThis[e]<"u"?globalThis[e]:new Proxy({},{get(r,i){throw new Error(`Vitest mocker was not initialized in this environment. vi.${String(i)}() is forbidden.`)}})}return{hoisted(r){if(typeof r!="function")throw new TypeError(`vi.hoisted() expects a function, but received a ${typeof r}`);return r()},mock(r,i){if(typeof r!="string")throw new TypeError(`vi.mock() expects a string path, but received a ${typeof r}`);const s=A("mock");t().queueMock(r,s,typeof i=="function"?()=>i(()=>t().importActual(r,s)):i)},unmock(r){if(typeof r!="string")throw new TypeError(`vi.unmock() expects a string path, but received a ${typeof r}`);t().queueUnmock(r,A("unmock"))},doMock(r,i){if(typeof r!="string")throw new TypeError(`vi.doMock() expects a string path, but received a ${typeof r}`);const s=A("doMock");t().queueMock(r,s,typeof i=="function"?()=>i(()=>t().importActual(r,s)):i)},doUnmock(r){if(typeof r!="string")throw new TypeError(`vi.doUnmock() expects a string path, but received a ${typeof r}`);t().queueUnmock(r,A("doUnmock"))},async importActual(r){return t().importActual(r,A("importActual"))},async importMock(r){return t().importMock(r,A("importMock"))}}}function A(n){const t=Ue({stackTraceLimit:5}).split(`
`),r=t.findIndex(s=>s.includes(` at Object.${n}`)||s.includes(`${n}@`)),i=tt(t[r+1]);return i?.file||""}const{now:oe}=Date;class it{registry=new se;queue=new Set;mockedIds=new Set;constructor(e,t,r,i){this.interceptor=e,this.rpc=t,this.spyOn=r,this.config=i}async prepare(){this.queue.size&&await Promise.all([...this.queue.values()])}async resolveFactoryModule(e){const t=this.registry.get(e);if(!t||t.type!=="manual")throw new Error(`Mock ${e} wasn't registered. This is probably a Vitest error. Please, open a new issue with reproduction.`);return await t.resolve()}getFactoryModule(e){const t=this.registry.get(e);if(!t||t.type!=="manual")throw new Error(`Mock ${e} wasn't registered. This is probably a Vitest error. Please, open a new issue with reproduction.`);if(!t.cache)throw new Error(`Mock ${e} wasn't resolved. This is probably a Vitest error. Please, open a new issue with reproduction.`);return t.cache}async invalidate(){const e=Array.from(this.mockedIds);e.length&&(await this.rpc.invalidate(e),await this.interceptor.invalidate(),this.registry.clear())}async importActual(e,t){const r=await this.rpc.resolveId(e,t);if(r==null)throw new Error(`[vitest] Cannot resolve "${e}" imported from "${t}"`);const i=$e(r.id),s=new URL(r.url,location.href),a=`_vitest_original&ext${i}`,o=`${s.pathname}${s.search?`${s.search}&${a}`:`?${a}`}${s.hash}`;return this.wrapDynamicImport(()=>import(o)).then(l=>{if(!r.optimized||typeof l.default>"u")return l;const d=l.default;return d?.__esModule?d:{...typeof d=="object"&&!Array.isArray(d)||typeof d=="function"?d:{},default:d}})}async importMock(e,t){await this.prepare();const{resolvedId:r,resolvedUrl:i,redirectUrl:s}=await this.rpc.resolveMock(e,t,{mock:"auto"}),a=this.resolveMockPath(X(i));let o=this.registry.get(a);if(!o)if(s){const l=new URL(this.resolveMockPath(X(s)),location.href).toString();o=new C(e,r,a,l)}else o=new q(e,r,a);if(o.type==="manual")return await o.resolve();if(o.type==="automock"||o.type==="autospy"){const l=new URL(`/@id/${r}`,location.href),d=l.search?`${l.search}&t=${oe()}`:`?t=${oe()}`,h=await me(()=>import(`${l.pathname}${d}&mock=${o.type}${l.hash}`),[],import.meta.url);return this.mockObject(h,o.type)}return import(o.redirect)}mockObject(e,t="automock"){return ye({globalConstructors:{Object,Function,Array,Map,RegExp},spyOn:this.spyOn,type:t},e)}queueMock(e,t,r){const i=this.rpc.resolveMock(e,t,{mock:typeof r=="function"?"factory":r?.spy?"spy":"auto"}).then(async({redirectUrl:s,resolvedId:a,resolvedUrl:o,needsInterop:l,mockType:d})=>{const h=this.resolveMockPath(X(o));this.mockedIds.add(a);const g=typeof r=="function"?async()=>{const y=await r();return l?{default:y}:y}:void 0,w=typeof s=="string"?new URL(this.resolveMockPath(X(s)),location.href).toString():null;let b;d==="manual"?b=this.registry.register("manual",e,a,h,g):d==="autospy"?b=this.registry.register("autospy",e,a,h):d==="redirect"?b=this.registry.register("redirect",e,a,h,w):b=this.registry.register("automock",e,a,h),await this.interceptor.register(b)}).finally(()=>{this.queue.delete(i)});this.queue.add(i)}queueUnmock(e,t){const r=this.rpc.resolveId(e,t).then(async i=>{if(!i)return;const s=this.resolveMockPath(X(i.url));this.mockedIds.add(i.id),this.registry.delete(s),await this.interceptor.delete(s)}).finally(()=>{this.queue.delete(r)});this.queue.add(r)}wrapDynamicImport(e){return typeof e=="function"?new Promise((r,i)=>{this.prepare().finally(()=>{e().then(r,i)})}):e}resolveMockPath(e){const t=this.config,r=xe("/@fs/",t.root);return e.startsWith(t.root)?e.slice(t.root.length):e.startsWith(r)?e.slice(r.length):e}}const ot=/(\?|&)v=\w{8}/;function X(n){return n.replace(ot,"")}class st{mocks=new se;constructor(){}async register(e){this.mocks.add(e)}async delete(e){this.mocks.delete(e)}async invalidate(){this.mocks.clear()}}const W=n=>{switch(n){case"resolveId":return Promise.resolve({id:"",url:"",optimized:!1});case"resolveMock":return Promise.resolve({mockType:"dummy",resolvedId:"",resolvedUrl:"",redirectUrl:"",needsInterop:!1});case"invalidate":return Promise.resolve()}};class ct extends it{queueMock(){}}function at(n){const e=new ct(n("__vitest_mocker__"),{resolveId(t,r){return W("resolveId")},resolveMock(t,r,i){return W("resolveMock")},async invalidate(t){return W("invalidate")}},(...t)=>globalThis.__STORYBOOK_MODULE_TEST__.spyOn(...t),{root:""});return globalThis.__vitest_mocker__=e,nt({globalThisKey:"__vitest_mocker__"})}globalThis.__STORYBOOK_MOCKER__=at(()=>new st);
