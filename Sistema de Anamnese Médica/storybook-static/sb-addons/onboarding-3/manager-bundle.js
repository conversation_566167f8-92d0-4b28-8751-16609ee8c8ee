try{
(()=>{var So=Object.defineProperty;var pe=(e,t)=>()=>(e&&(t=e(e=0)),t);var wo=(e,t)=>{for(var n in t)So(e,n,{get:t[n],enumerable:!0})};var Z=pe(()=>{});var Q=pe(()=>{});var ee=pe(()=>{});var m,il,De,al,sl,ll,cl,vn,ul,pl,Wt,dl,V,fl,hl,En,Ht,On,ml,yl,gl,nt,bl,vl,El,se,Ol,Sl,wl,Tl,Il,Cl,Ut,ae,Pl,Rl,xl,Et=pe(()=>{Z();Q();ee();m=__REACT__,{Children:il,Component:De,Fragment:al,Profiler:sl,PureComponent:ll,StrictMode:cl,Suspense:vn,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ul,act:pl,cloneElement:Wt,createContext:dl,createElement:V,createFactory:fl,createRef:hl,forwardRef:En,isValidElement:Ht,lazy:On,memo:ml,startTransition:yl,unstable_act:gl,useCallback:nt,useContext:bl,useDebugValue:vl,useDeferredValue:El,useEffect:se,useId:Ol,useImperativeHandle:Sl,useInsertionEffect:wl,useLayoutEffect:Tl,useMemo:Il,useReducer:Cl,useRef:Ut,useState:ae,useSyncExternalStore:Pl,useTransition:Rl,version:xl}=__REACT__});var Fe,kl,Ot,Ll,jl,Ml,Dl,Fl,Bl,Sn,Wl,wn,Hl,St=pe(()=>{Z();Q();ee();Fe=__REACT_DOM__,{__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:kl,createPortal:Ot,createRoot:Ll,findDOMNode:jl,flushSync:Ml,hydrate:Dl,hydrateRoot:Fl,render:Bl,unmountComponentAtNode:Sn,unstable_batchedUpdates:Wl,unstable_renderSubtreeIntoContainer:wn,version:Hl}=__REACT_DOM__});var wt,zt=pe(()=>{Z();Q();ee();wt="addon-controls"});var Kl,Jl,Xl,Zl,Ql,ec,tc,nc,rc,oc,ic,ac,sc,lc,cc,uc,pc,dc,fc,hc,mc,yc,gc,bc,vc,Ec,Tn,Oc,Sc,wc,Tc,Ic,Cc,Pc,Rc,xc,_c,Ac,Nc,kc,Lc,jc,Mc,Dc,Fc,Bc,Wc,Hc,Uc,In,zc,Gc,Yc,qc,$c,Vc,Kc,Jc,Gt=pe(()=>{Z();Q();ee();Kl=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:Jl,ARGTYPES_INFO_RESPONSE:Xl,CHANNEL_CREATED:Zl,CHANNEL_WS_DISCONNECT:Ql,CONFIG_ERROR:ec,CREATE_NEW_STORYFILE_REQUEST:tc,CREATE_NEW_STORYFILE_RESPONSE:nc,CURRENT_STORY_WAS_SET:rc,DOCS_PREPARED:oc,DOCS_RENDERED:ic,FILE_COMPONENT_SEARCH_REQUEST:ac,FILE_COMPONENT_SEARCH_RESPONSE:sc,FORCE_REMOUNT:lc,FORCE_RE_RENDER:cc,GLOBALS_UPDATED:uc,NAVIGATE_URL:pc,PLAY_FUNCTION_THREW_EXCEPTION:dc,PRELOAD_ENTRIES:fc,PREVIEW_BUILDER_PROGRESS:hc,PREVIEW_KEYDOWN:mc,REGISTER_SUBSCRIPTION:yc,REQUEST_WHATS_NEW_DATA:gc,RESET_STORY_ARGS:bc,RESULT_WHATS_NEW_DATA:vc,SAVE_STORY_REQUEST:Ec,SAVE_STORY_RESPONSE:Tn,SELECT_STORY:Oc,SET_CONFIG:Sc,SET_CURRENT_STORY:wc,SET_FILTER:Tc,SET_GLOBALS:Ic,SET_INDEX:Cc,SET_STORIES:Pc,SET_WHATS_NEW_CACHE:Rc,SHARED_STATE_CHANGED:xc,SHARED_STATE_SET:_c,STORIES_COLLAPSE_ALL:Ac,STORIES_EXPAND_ALL:Nc,STORY_ARGS_UPDATED:kc,STORY_CHANGED:Lc,STORY_ERRORED:jc,STORY_FINISHED:Mc,STORY_HOT_UPDATED:Dc,STORY_INDEX_INVALIDATED:Fc,STORY_MISSING:Bc,STORY_PREPARED:Wc,STORY_RENDERED:Hc,STORY_RENDER_PHASE_CHANGED:Uc,STORY_SPECIFIED:In,STORY_THREW_EXCEPTION:zc,STORY_UNCHANGED:Gc,TELEMETRY_ERROR:Yc,TOGGLE_WHATS_NEW_NOTIFICATIONS:qc,UNHANDLED_ERRORS_WHILE_PLAYING:$c,UPDATE_GLOBALS:Vc,UPDATE_QUERY_PARAMS:Kc,UPDATE_STORY_ARGS:Jc}=__STORYBOOK_CORE_EVENTS__});var Vu,Ku,Ju,Xu,Zu,Qu,ep,Pn,tp,np,rp,op,ip,ap,sp,lp,Tt,cp,up,pp,dp,fp,hp,mp,Rn,yp,gp,bp,vp,Ep,ze,Op,Sp,wp,Tp,Ip,Cp,Pp,Rp,xp,_p,Ap,Np,xn,kp,Lp,jp,Mp,Dp,Fp,Bp,Wp,Hp,Up,zp,Gp,Yp,qp,$p,Vp,Kp,Jp,Xp,Zp,Qp,ed,_n=pe(()=>{Z();Q();ee();Vu=__STORYBOOK_COMPONENTS__,{A:Ku,ActionBar:Ju,AddonPanel:Xu,Badge:Zu,Bar:Qu,Blockquote:ep,Button:Pn,ClipboardCode:tp,Code:np,DL:rp,Div:op,DocumentWrapper:ip,EmptyTabContent:ap,ErrorFormatter:sp,FlexBar:lp,Form:Tt,H1:cp,H2:up,H3:pp,H4:dp,H5:fp,H6:hp,HR:mp,IconButton:Rn,Img:yp,LI:gp,Link:bp,ListItem:vp,Loader:Ep,Modal:ze,OL:Op,P:Sp,Placeholder:wp,Pre:Tp,ProgressSpinner:Ip,ResetWrapper:Cp,ScrollArea:Pp,Separator:Rp,Spaced:xp,Span:_p,StorybookIcon:Ap,StorybookLogo:Np,SyntaxHighlighter:xn,TT:kp,TabBar:Lp,TabButton:jp,TabWrapper:Mp,Table:Dp,Tabs:Fp,TabsState:Bp,TooltipLinkList:Wp,TooltipMessage:Hp,TooltipNote:Up,UL:zp,WithTooltip:Gp,WithTooltipPure:Yp,Zoom:qp,codeCommon:$p,components:Vp,createCopyToClipboardFunction:Kp,getStoryHref:Jp,interleaveSeparators:Xp,nameSpaceClassNames:Zp,resetComponents:Qp,withReset:ed}=__STORYBOOK_COMPONENTS__});var id,ad,sd,ld,Yt,cd,It,qt,ud,pd,dd,fd,hd,md,yd,gd,bd,vd,rt,Ed,J,An,Od,Nn,Sd,kn=pe(()=>{Z();Q();ee();id=__STORYBOOK_THEMING__,{CacheProvider:ad,ClassNames:sd,Global:ld,ThemeProvider:Yt,background:cd,color:It,convert:qt,create:ud,createCache:pd,createGlobal:dd,createReset:fd,css:hd,darken:md,ensure:yd,ignoreSsrWarning:gd,isPropValid:bd,jsx:vd,keyframes:rt,lighten:Ed,styled:J,themes:An,typography:Od,useTheme:Nn,withTheme:Sd}=__STORYBOOK_THEMING__});var Pd,Rd,xd,_d,Ad,Nd,kd,Ld,jd,Md,Dd,Fd,Bd,Wd,Hd,Ln,Ud,zd,Gd,Yd,qd,$d,Vd,Kd,Jd,Xd,Zd,Qd,ef,tf,nf,rf,of,af,sf,lf,cf,uf,pf,df,ff,hf,mf,yf,gf,bf,vf,Ef,Of,Sf,wf,Tf,If,Cf,Pf,Rf,xf,_f,Af,Nf,kf,Lf,jf,jn,Mf,Df,Ff,Bf,Wf,Hf,Uf,zf,Gf,Yf,qf,$f,Vf,Kf,Jf,Xf,Zf,Qf,eh,th,nh,rh,oh,ih,ah,sh,lh,ch,uh,ph,dh,fh,hh,mh,yh,gh,bh,vh,Eh,Oh,Sh,wh,Th,Ih,Ch,Ph,Rh,xh,_h,Ah,Nh,kh,Lh,jh,Mh,Dh,Fh,Bh,Wh,Hh,Uh,zh,Gh,Yh,qh,$h,Vh,Kh,Jh,Xh,Zh,Qh,em,tm,nm,rm,om,im,am,sm,lm,cm,um,pm,dm,fm,hm,mm,ym,gm,bm,vm,Em,Om,Sm,wm,Tm,Im,Cm,Pm,Rm,xm,_m,Am,Nm,km,Lm,jm,Mm,Dm,Fm,Bm,Wm,Hm,Um,zm,Gm,Ym,qm,$m,Vm,Km,Jm,Xm,Zm,Qm,ey,ty,ny,ry,oy,iy,ay,sy,ly,cy,uy,py,dy,fy,hy,my,yy,gy,by,vy,Ey,Oy,Sy,wy,Ty,Iy,Cy,Py,Ry,xy,_y,Ay,Ny,ky,Ly,jy,My,Dy,Fy,By,Wy,Hy,Uy,zy,Gy,Yy,qy,$y,Vy,Ky,Jy,Xy,Zy,Qy,eg,tg,ng,Mn=pe(()=>{Z();Q();ee();Pd=__STORYBOOK_ICONS__,{AccessibilityAltIcon:Rd,AccessibilityIcon:xd,AccessibilityIgnoredIcon:_d,AddIcon:Ad,AdminIcon:Nd,AlertAltIcon:kd,AlertIcon:Ld,AlignLeftIcon:jd,AlignRightIcon:Md,AppleIcon:Dd,ArrowBottomLeftIcon:Fd,ArrowBottomRightIcon:Bd,ArrowDownIcon:Wd,ArrowLeftIcon:Hd,ArrowRightIcon:Ln,ArrowSolidDownIcon:Ud,ArrowSolidLeftIcon:zd,ArrowSolidRightIcon:Gd,ArrowSolidUpIcon:Yd,ArrowTopLeftIcon:qd,ArrowTopRightIcon:$d,ArrowUpIcon:Vd,AzureDevOpsIcon:Kd,BackIcon:Jd,BasketIcon:Xd,BatchAcceptIcon:Zd,BatchDenyIcon:Qd,BeakerIcon:ef,BellIcon:tf,BitbucketIcon:nf,BoldIcon:rf,BookIcon:of,BookmarkHollowIcon:af,BookmarkIcon:sf,BottomBarIcon:lf,BottomBarToggleIcon:cf,BoxIcon:uf,BranchIcon:pf,BrowserIcon:df,ButtonIcon:ff,CPUIcon:hf,CalendarIcon:mf,CameraIcon:yf,CameraStabilizeIcon:gf,CategoryIcon:bf,CertificateIcon:vf,ChangedIcon:Ef,ChatIcon:Of,CheckIcon:Sf,ChevronDownIcon:wf,ChevronLeftIcon:Tf,ChevronRightIcon:If,ChevronSmallDownIcon:Cf,ChevronSmallLeftIcon:Pf,ChevronSmallRightIcon:Rf,ChevronSmallUpIcon:xf,ChevronUpIcon:_f,ChromaticIcon:Af,ChromeIcon:Nf,CircleHollowIcon:kf,CircleIcon:Lf,ClearIcon:jf,CloseAltIcon:jn,CloseIcon:Mf,CloudHollowIcon:Df,CloudIcon:Ff,CogIcon:Bf,CollapseIcon:Wf,CommandIcon:Hf,CommentAddIcon:Uf,CommentIcon:zf,CommentsIcon:Gf,CommitIcon:Yf,CompassIcon:qf,ComponentDrivenIcon:$f,ComponentIcon:Vf,ContrastIcon:Kf,ContrastIgnoredIcon:Jf,ControlsIcon:Xf,CopyIcon:Zf,CreditIcon:Qf,CrossIcon:eh,DashboardIcon:th,DatabaseIcon:nh,DeleteIcon:rh,DiamondIcon:oh,DirectionIcon:ih,DiscordIcon:ah,DocChartIcon:sh,DocListIcon:lh,DocumentIcon:ch,DownloadIcon:uh,DragIcon:ph,EditIcon:dh,EllipsisIcon:fh,EmailIcon:hh,ExpandAltIcon:mh,ExpandIcon:yh,EyeCloseIcon:gh,EyeIcon:bh,FaceHappyIcon:vh,FaceNeutralIcon:Eh,FaceSadIcon:Oh,FacebookIcon:Sh,FailedIcon:wh,FastForwardIcon:Th,FigmaIcon:Ih,FilterIcon:Ch,FlagIcon:Ph,FolderIcon:Rh,FormIcon:xh,GDriveIcon:_h,GithubIcon:Ah,GitlabIcon:Nh,GlobeIcon:kh,GoogleIcon:Lh,GraphBarIcon:jh,GraphLineIcon:Mh,GraphqlIcon:Dh,GridAltIcon:Fh,GridIcon:Bh,GrowIcon:Wh,HeartHollowIcon:Hh,HeartIcon:Uh,HomeIcon:zh,HourglassIcon:Gh,InfoIcon:Yh,ItalicIcon:qh,JumpToIcon:$h,KeyIcon:Vh,LightningIcon:Kh,LightningOffIcon:Jh,LinkBrokenIcon:Xh,LinkIcon:Zh,LinkedinIcon:Qh,LinuxIcon:em,ListOrderedIcon:tm,ListUnorderedIcon:nm,LocationIcon:rm,LockIcon:om,MarkdownIcon:im,MarkupIcon:am,MediumIcon:sm,MemoryIcon:lm,MenuIcon:cm,MergeIcon:um,MirrorIcon:pm,MobileIcon:dm,MoonIcon:fm,NutIcon:hm,OutboxIcon:mm,OutlineIcon:ym,PaintBrushIcon:gm,PaperClipIcon:bm,ParagraphIcon:vm,PassedIcon:Em,PhoneIcon:Om,PhotoDragIcon:Sm,PhotoIcon:wm,PhotoStabilizeIcon:Tm,PinAltIcon:Im,PinIcon:Cm,PlayAllHollowIcon:Pm,PlayBackIcon:Rm,PlayHollowIcon:xm,PlayIcon:_m,PlayNextIcon:Am,PlusIcon:Nm,PointerDefaultIcon:km,PointerHandIcon:Lm,PowerIcon:jm,PrintIcon:Mm,ProceedIcon:Dm,ProfileIcon:Fm,PullRequestIcon:Bm,QuestionIcon:Wm,RSSIcon:Hm,RedirectIcon:Um,ReduxIcon:zm,RefreshIcon:Gm,ReplyIcon:Ym,RepoIcon:qm,RequestChangeIcon:$m,RewindIcon:Vm,RulerIcon:Km,SaveIcon:Jm,SearchIcon:Xm,ShareAltIcon:Zm,ShareIcon:Qm,ShieldIcon:ey,SideBySideIcon:ty,SidebarAltIcon:ny,SidebarAltToggleIcon:ry,SidebarIcon:oy,SidebarToggleIcon:iy,SpeakerIcon:ay,StackedIcon:sy,StarHollowIcon:ly,StarIcon:cy,StatusFailIcon:uy,StatusIcon:py,StatusPassIcon:dy,StatusWarnIcon:fy,StickerIcon:hy,StopAltHollowIcon:my,StopAltIcon:yy,StopIcon:gy,StorybookIcon:by,StructureIcon:vy,SubtractIcon:Ey,SunIcon:Oy,SupportIcon:Sy,SweepIcon:wy,SwitchAltIcon:Ty,SyncIcon:Iy,TabletIcon:Cy,ThumbsUpIcon:Py,TimeIcon:Ry,TimerIcon:xy,TransferIcon:_y,TrashIcon:Ay,TwitterIcon:Ny,TypeIcon:ky,UbuntuIcon:Ly,UndoIcon:jy,UnfoldIcon:My,UnlockIcon:Dy,UnpinIcon:Fy,UploadIcon:By,UserAddIcon:Wy,UserAltIcon:Hy,UserIcon:Uy,UsersIcon:zy,VSCodeIcon:Gy,VerifiedIcon:Yy,VideoIcon:qy,WandIcon:$y,WatchIcon:Vy,WindowsIcon:Ky,WrenchIcon:Jy,XIcon:Xy,YoutubeIcon:Zy,ZoomIcon:Qy,ZoomOutIcon:eg,ZoomResetIcon:tg,iconList:ng}=__STORYBOOK_ICONS__});var Eo={};wo(Eo,{default:()=>Js});function Ho(e,t={}){let{colors:n=Fn,duration:r=Bn,force:o=Wn,particleCount:i=Hn,particleShape:a=Un,particleSize:s=zn,particleClass:c=Gn,destroyAfterDone:l=Yn,stageHeight:p=qn,stageWidth:u=$n}=t;(function(y){if(document.querySelector("style[data-neoconfetti]"))return;let v=$t("style");v.dataset.neoconfetti="",v.textContent=y,Vt(document.head,v)})(Bo),e.classList.add(Wo),e.style.setProperty("--sh",p+"px");let d=[],h=[],f=()=>ct(Ee()*(qo-1)),g=(y,v)=>a!=="rectangles"&&(y==="circles"||$o(v));function S(y,v){let w=f(),U=g(a,w),I=(Ie,je)=>y.style.setProperty(Ie,je+"");I("--xlp",Kt(Ct(Xn(v,90)-180),0,180,-u/2,u/2)+"px"),I("--dc",r-ct(1e3*Ee())+"ms");let z=Ee()<zo?Ge(Ee()*Go,2):0;I("--x1",z),I("--x2",-1*z),I("--x3",z),I("--x4",Ge(Ct(Kt(Ct(Xn(v,90)-180),0,180,-1,1)),4)),I("--y1",Ge(Ee()*Kn,4)),I("--y2",Ge(Ee()*o*(Mr()?1:-1),4)),I("--y3",Kn),I("--y4",Ge(Yo(Kt(Ct(v-180),0,180,o,-o),0),4)),I("--w",(U?s:ct(4*Ee())+s/2)+"px"),I("--h",(U?s:ct(2*Ee())+s)+"px");let te=w.toString(2).padStart(3,"0").split("");I("--hr",te.map(Ie=>+Ie/2+"").join(" ")),I("--r",te.join(" ")),I("--rd",Ge(Ee()*(Uo-Vn)+Vn)+"ms"),I("--br",U?"50%":0)}let b;function x(){e.innerHTML="",clearTimeout(b),d=Jn(i,n),h=(function(y,v=[],w){let U=[];for(let{color:I}of v){let z=$t("div");z.className=`${Dn} ${w}`,z.style.setProperty("--bgc",I);let te=$t("div");Vt(z,te),Vt(y,z),U.push(z)}return U})(e,d,c);for(let[y,v]of Zn(h))S(v,d[+y].degree);b=setTimeout(()=>{l&&(e.innerHTML="")},r)}return x(),{update(y){let v=y.particleCount??Hn,w=y.particleShape??Un,U=y.particleSize??zn,I=y.particleClass??Gn,z=y.colors??Fn,te=y.stageHeight??qn,Ie=y.duration??Bn,je=y.force??Wn,et=y.stageWidth??$n,Me=y.destroyAfterDone??Yn;d=Jn(v,z);let Ue=!1;if(v===i){h=Array.from(e.querySelectorAll(`.${Dn}`));for(let[tt,{color:xe}]of Zn(d)){let _e=h[+tt];JSON.stringify(n)!==JSON.stringify(z)&&_e.style.setProperty("--bgc",xe),w!==a&&_e.style.setProperty("--br",g(w,f())?"50%":"0"),I!==c&&(c&&_e.classList.remove(c),I&&_e.classList.add(I))}}else Ue=!0;l&&!Me&&clearTimeout(b),e.style.setProperty("--sh",te+"px"),r=Ie,n=z,o=je,i=v,a=w,s=U,c=I,l=Me,p=te,u=et,Ue&&x()},destroy(){e.innerHTML="",clearTimeout(b)}}}function Vo({class:e,...t}){let n=Ut(null),r=Ut();return se(()=>{if(typeof window<"u"&&n.current){if(r.current)return r.current.update(t),r.current.destroy;r.current=Ho(n.current,t)}},[t]),V("div",{ref:n,className:e})}function Qn({targetSelector:e,pulsating:t=!1}){return se(()=>{let n=document.querySelector(e);if(n)if(t){n.style.animation="pulsate 3s infinite",n.style.transformOrigin="center",n.style.animationTimingFunction="ease-in-out";let r=`
        @keyframes pulsate {
          0% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 0 rgba(2, 156, 253, 0.7), 0 0 0 0 rgba(2, 156, 253, 0.4);
          }
          50% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 20px rgba(2, 156, 253, 0), 0 0 0 40px rgba(2, 156, 253, 0);
          }
          100% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 0 rgba(2, 156, 253, 0), 0 0 0 0 rgba(2, 156, 253, 0);
          }
        }
      `,o=document.createElement("style");o.id="sb-onboarding-pulsating-effect",o.innerHTML=r,document.head.appendChild(o)}else n.style.boxShadow="rgba(2,156,253,1) 0 0 2px 1px";return()=>{let r=document.querySelector("#sb-onboarding-pulsating-effect");r&&r.remove(),n&&(n.style.animation="",n.style.boxShadow="")}},[e,t]),null}function Dr(e){return t=>typeof t===e}function ei(e,t){let{length:n}=e;if(n!==t.length)return!1;for(let r=n;r--!==0;)if(!oe(e[r],t[r]))return!1;return!0}function ti(e,t){if(e.byteLength!==t.byteLength)return!1;let n=new DataView(e.buffer),r=new DataView(t.buffer),o=e.byteLength;for(;o--;)if(n.getUint8(o)!==r.getUint8(o))return!1;return!0}function ni(e,t){if(e.size!==t.size)return!1;for(let n of e.entries())if(!t.has(n[0]))return!1;for(let n of e.entries())if(!oe(n[1],t.get(n[0])))return!1;return!0}function ri(e,t){if(e.size!==t.size)return!1;for(let n of e.entries())if(!t.has(n[0]))return!1;return!0}function oe(e,t){if(e===t)return!0;if(e&&tr(e)&&t&&tr(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return ei(e,t);if(e instanceof Map&&t instanceof Map)return ni(e,t);if(e instanceof Set&&t instanceof Set)return ri(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return ti(e,t);if(er(e)&&er(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();let n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let o=n.length;o--!==0;)if(!Object.prototype.hasOwnProperty.call(t,n[o]))return!1;for(let o=n.length;o--!==0;){let i=n[o];if(!(i==="_owner"&&e.$$typeof)&&!oe(e[i],t[i]))return!1}return!0}return Number.isNaN(e)&&Number.isNaN(t)?!0:e===t}function jt(e){let t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(ai(t))return t}function fe(e){return t=>jt(t)===e}function ai(e){return oi.includes(e)}function Je(e){return t=>typeof t===e}function si(e){return ii.includes(e)}function C(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}return C.array(e)?"Array":C.plainFunction(e)?"Function":jt(e)||"Object"}function ci(...e){return e.every(t=>_.string(t)||_.array(t)||_.plainObject(t))}function ui(e,t,n){return Fr(e,t)?[e,t].every(_.array)?!e.some(ar(n))&&t.some(ar(n)):[e,t].every(_.plainObject)?!Object.entries(e).some(ir(n))&&Object.entries(t).some(ir(n)):t===n:!1}function nr(e,t,n){let{actual:r,key:o,previous:i,type:a}=n,s=we(e,o),c=we(t,o),l=[s,c].every(_.number)&&(a==="increased"?s<c:s>c);return _.undefined(r)||(l=l&&c===r),_.undefined(i)||(l=l&&s===i),l}function rr(e,t,n){let{key:r,type:o,value:i}=n,a=we(e,r),s=we(t,r),c=o==="added"?a:s,l=o==="added"?s:a;if(!_.nullOrUndefined(i)){if(_.defined(c)){if(_.array(c)||_.plainObject(c))return ui(c,l,i)}else return oe(l,i);return!1}return[a,s].every(_.array)?!l.every(un(c)):[a,s].every(_.plainObject)?pi(Object.keys(c),Object.keys(l)):![a,s].every(p=>_.primitive(p)&&_.defined(p))&&(o==="added"?!_.defined(a)&&_.defined(s):_.defined(a)&&!_.defined(s))}function or(e,t,{key:n}={}){let r=we(e,n),o=we(t,n);if(!Fr(r,o))throw new TypeError("Inputs have different types");if(!ci(r,o))throw new TypeError("Inputs don't have length");return[r,o].every(_.plainObject)&&(r=Object.keys(r),o=Object.keys(o)),[r,o]}function ir(e){return([t,n])=>_.array(e)?oe(e,n)||e.some(r=>oe(r,n)||_.array(n)&&un(n)(r)):_.plainObject(e)&&e[t]?!!e[t]&&oe(e[t],n):oe(e,n)}function pi(e,t){return t.some(n=>!e.includes(n))}function ar(e){return t=>_.array(e)?e.some(n=>oe(n,t)||_.array(t)&&un(t)(n)):oe(e,t)}function ot(e,t){return _.array(e)?e.some(n=>oe(n,t)):oe(e,t)}function un(e){return t=>e.some(n=>oe(n,t))}function Fr(...e){return e.every(_.array)||e.every(_.number)||e.every(_.plainObject)||e.every(_.string)}function we(e,t){return _.plainObject(e)||_.array(e)?_.string(t)?t.split(".").reduce((n,r)=>n&&n[r],e):_.number(t)?e[t]:e:e}function At(e,t){if([e,t].some(_.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every(n=>_.plainObject(n)||_.array(n)))throw new Error("Expected plain objects or array");return{added:(n,r)=>{try{return rr(e,t,{key:n,type:"added",value:r})}catch{return!1}},changed:(n,r,o)=>{try{let i=we(e,n),a=we(t,n),s=_.defined(r),c=_.defined(o);if(s||c){let l=c?ot(o,i):!ot(r,i),p=ot(r,a);return l&&p}return[i,a].every(_.array)||[i,a].every(_.plainObject)?!oe(i,a):i!==a}catch{return!1}},changedFrom:(n,r,o)=>{if(!_.defined(n))return!1;try{let i=we(e,n),a=we(t,n),s=_.defined(o);return ot(r,i)&&(s?ot(o,a):!s)}catch{return!1}},decreased:(n,r,o)=>{if(!_.defined(n))return!1;try{return nr(e,t,{key:n,actual:r,previous:o,type:"decreased"})}catch{return!1}},emptied:n=>{try{let[r,o]=or(e,t,{key:n});return!!r.length&&!o.length}catch{return!1}},filled:n=>{try{let[r,o]=or(e,t,{key:n});return!r.length&&!!o.length}catch{return!1}},increased:(n,r,o)=>{if(!_.defined(n))return!1;try{return nr(e,t,{key:n,actual:r,previous:o,type:"increased"})}catch{return!1}},removed:(n,r)=>{try{return rr(e,t,{key:n,type:"removed",value:r})}catch{return!1}}}}function hi(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}function mi(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},fi))}}function Wr(e){var t={};return e&&t.toString.call(e)==="[object Function]"}function He(e,t){if(e.nodeType!==1)return[];var n=e.ownerDocument.defaultView,r=n.getComputedStyle(e,null);return t?r[t]:r}function pn(e){return e.nodeName==="HTML"?e:e.parentNode||e.host}function dt(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=He(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/(auto|scroll|overlay)/.test(n+o+r)?e:dt(pn(e))}function Hr(e){return e&&e.referenceNode?e.referenceNode:e}function Xe(e){return e===11?lr:e===10?cr:lr||cr}function qe(e){if(!e)return document.documentElement;for(var t=Xe(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var r=n&&n.nodeName;return!r||r==="BODY"||r==="HTML"?e?e.ownerDocument.documentElement:document.documentElement:["TH","TD","TABLE"].indexOf(n.nodeName)!==-1&&He(n,"position")==="static"?qe(n):n}function bi(e){var t=e.nodeName;return t==="BODY"?!1:t==="HTML"||qe(e.firstElementChild)===e}function tn(e){return e.parentNode!==null?tn(e.parentNode):e}function Nt(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?e:t,o=n?t:e,i=document.createRange();i.setStart(r,0),i.setEnd(o,0);var a=i.commonAncestorContainer;if(e!==a&&t!==a||r.contains(o))return bi(a)?a:qe(a);var s=tn(e);return s.host?Nt(s.host,t):Nt(e,tn(t).host)}function $e(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"top",n=t==="top"?"scrollTop":"scrollLeft",r=e.nodeName;if(r==="BODY"||r==="HTML"){var o=e.ownerDocument.documentElement,i=e.ownerDocument.scrollingElement||o;return i[n]}return e[n]}function vi(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,r=$e(t,"top"),o=$e(t,"left"),i=n?-1:1;return e.top+=r*i,e.bottom+=r*i,e.left+=o*i,e.right+=o*i,e}function ur(e,t){var n=t==="x"?"Left":"Top",r=n==="Left"?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+r+"Width"])}function pr(e,t,n,r){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],Xe(10)?parseInt(n["offset"+e])+parseInt(r["margin"+(e==="Height"?"Top":"Left")])+parseInt(r["margin"+(e==="Height"?"Bottom":"Right")]):0)}function Ur(e){var t=e.body,n=e.documentElement,r=Xe(10)&&getComputedStyle(n);return{height:pr("Height",t,n,r),width:pr("Width",t,n,r)}}function ke(e){return ce({},e,{right:e.left+e.width,bottom:e.top+e.height})}function nn(e){var t={};try{if(Xe(10)){t=e.getBoundingClientRect();var n=$e(e,"top"),r=$e(e,"left");t.top+=n,t.left+=r,t.bottom+=n,t.right+=r}else t=e.getBoundingClientRect()}catch{}var o={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},i=e.nodeName==="HTML"?Ur(e.ownerDocument):{},a=i.width||e.clientWidth||o.width,s=i.height||e.clientHeight||o.height,c=e.offsetWidth-a,l=e.offsetHeight-s;if(c||l){var p=He(e);c-=ur(p,"x"),l-=ur(p,"y"),o.width-=c,o.height-=l}return ke(o)}function dn(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,r=Xe(10),o=t.nodeName==="HTML",i=nn(e),a=nn(t),s=dt(e),c=He(t),l=parseFloat(c.borderTopWidth),p=parseFloat(c.borderLeftWidth);n&&o&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var u=ke({top:i.top-a.top-l,left:i.left-a.left-p,width:i.width,height:i.height});if(u.marginTop=0,u.marginLeft=0,!r&&o){var d=parseFloat(c.marginTop),h=parseFloat(c.marginLeft);u.top-=l-d,u.bottom-=l-d,u.left-=p-h,u.right-=p-h,u.marginTop=d,u.marginLeft=h}return(r&&!n?t.contains(s):t===s&&s.nodeName!=="BODY")&&(u=vi(u,t)),u}function Si(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=e.ownerDocument.documentElement,r=dn(e,n),o=Math.max(n.clientWidth,window.innerWidth||0),i=Math.max(n.clientHeight,window.innerHeight||0),a=t?0:$e(n),s=t?0:$e(n,"left"),c={top:a-r.top+r.marginTop,left:s-r.left+r.marginLeft,width:o,height:i};return ke(c)}function zr(e){var t=e.nodeName;if(t==="BODY"||t==="HTML")return!1;if(He(e,"position")==="fixed")return!0;var n=pn(e);return n?zr(n):!1}function Gr(e){if(!e||!e.parentElement||Xe())return document.documentElement;for(var t=e.parentElement;t&&He(t,"transform")==="none";)t=t.parentElement;return t||document.documentElement}function fn(e,t,n,r){var o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,i={top:0,left:0},a=o?Gr(e):Nt(e,Hr(t));if(r==="viewport")i=Si(a,o);else{var s=void 0;r==="scrollParent"?(s=dt(pn(t)),s.nodeName==="BODY"&&(s=e.ownerDocument.documentElement)):r==="window"?s=e.ownerDocument.documentElement:s=r;var c=dn(s,a,o);if(s.nodeName==="HTML"&&!zr(a)){var l=Ur(e.ownerDocument),p=l.height,u=l.width;i.top+=c.top-c.marginTop,i.bottom=p+c.top,i.left+=c.left-c.marginLeft,i.right=u+c.left}else i=c}n=n||0;var d=typeof n=="number";return i.left+=d?n:n.left||0,i.top+=d?n:n.top||0,i.right-=d?n:n.right||0,i.bottom-=d?n:n.bottom||0,i}function wi(e){var t=e.width,n=e.height;return t*n}function Yr(e,t,n,r,o){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:0;if(e.indexOf("auto")===-1)return e;var a=fn(n,r,i,o),s={top:{width:a.width,height:t.top-a.top},right:{width:a.right-t.right,height:a.height},bottom:{width:a.width,height:a.bottom-t.bottom},left:{width:t.left-a.left,height:a.height}},c=Object.keys(s).map(function(d){return ce({key:d},s[d],{area:wi(s[d])})}).sort(function(d,h){return h.area-d.area}),l=c.filter(function(d){var h=d.width,f=d.height;return h>=n.clientWidth&&f>=n.clientHeight}),p=l.length>0?l[0].key:c[0].key,u=e.split("-")[1];return p+(u?"-"+u:"")}function qr(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,o=r?Gr(t):Nt(t,Hr(n));return dn(n,o,r)}function $r(e){var t=e.ownerDocument.defaultView,n=t.getComputedStyle(e),r=parseFloat(n.marginTop||0)+parseFloat(n.marginBottom||0),o=parseFloat(n.marginLeft||0)+parseFloat(n.marginRight||0),i={width:e.offsetWidth+o,height:e.offsetHeight+r};return i}function kt(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(n){return t[n]})}function Vr(e,t,n){n=n.split("-")[0];var r=$r(e),o={width:r.width,height:r.height},i=["right","left"].indexOf(n)!==-1,a=i?"top":"left",s=i?"left":"top",c=i?"height":"width",l=i?"width":"height";return o[a]=t[a]+t[c]/2-r[c]/2,n===s?o[s]=t[s]-r[l]:o[s]=t[kt(s)],o}function ft(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function Ti(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(o){return o[t]===n});var r=ft(e,function(o){return o[t]===n});return e.indexOf(r)}function Kr(e,t,n){var r=n===void 0?e:e.slice(0,Ti(e,"name",n));return r.forEach(function(o){o.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=o.function||o.fn;o.enabled&&Wr(i)&&(t.offsets.popper=ke(t.offsets.popper),t.offsets.reference=ke(t.offsets.reference),t=i(t,o))}),t}function Ii(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=qr(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=Yr(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=Vr(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=Kr(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function Jr(e,t){return e.some(function(n){var r=n.name,o=n.enabled;return o&&r===t})}function hn(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var o=t[r],i=o?""+o+n:e;if(typeof document.body.style[i]<"u")return i}return null}function Ci(){return this.state.isDestroyed=!0,Jr(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[hn("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function Xr(e){var t=e.ownerDocument;return t?t.defaultView:window}function Zr(e,t,n,r){var o=e.nodeName==="BODY",i=o?e.ownerDocument.defaultView:e;i.addEventListener(t,n,{passive:!0}),o||Zr(dt(i.parentNode),t,n,r),r.push(i)}function Pi(e,t,n,r){n.updateBound=r,Xr(e).addEventListener("resize",n.updateBound,{passive:!0});var o=dt(e);return Zr(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function Ri(){this.state.eventsEnabled||(this.state=Pi(this.reference,this.options,this.state,this.scheduleUpdate))}function xi(e,t){return Xr(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(n){n.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function _i(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=xi(this.reference,this.state))}function mn(e){return e!==""&&!isNaN(parseFloat(e))&&isFinite(e)}function rn(e,t){Object.keys(t).forEach(function(n){var r="";["width","height","top","right","bottom","left"].indexOf(n)!==-1&&mn(t[n])&&(r="px"),e.style[n]=t[n]+r})}function Ai(e,t){Object.keys(t).forEach(function(n){var r=t[n];r!==!1?e.setAttribute(n,t[n]):e.removeAttribute(n)})}function Ni(e){return rn(e.instance.popper,e.styles),Ai(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&rn(e.arrowElement,e.arrowStyles),e}function ki(e,t,n,r,o){var i=qr(o,t,e,n.positionFixed),a=Yr(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",a),rn(t,{position:n.positionFixed?"fixed":"absolute"}),n}function Li(e,t){var n=e.offsets,r=n.popper,o=n.reference,i=Math.round,a=Math.floor,s=function(S){return S},c=i(o.width),l=i(r.width),p=["left","right"].indexOf(e.placement)!==-1,u=e.placement.indexOf("-")!==-1,d=c%2===l%2,h=c%2===1&&l%2===1,f=t?p||u||d?i:a:s,g=t?i:s;return{left:f(h&&!u&&t?r.left-1:r.left),top:g(r.top),bottom:g(r.bottom),right:f(r.right)}}function Mi(e,t){var n=t.x,r=t.y,o=e.offsets.popper,i=ft(e.instance.modifiers,function(y){return y.name==="applyStyle"}).gpuAcceleration;i!==void 0&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a=i!==void 0?i:t.gpuAcceleration,s=qe(e.instance.popper),c=nn(s),l={position:o.position},p=Li(e,window.devicePixelRatio<2||!ji),u=n==="bottom"?"top":"bottom",d=r==="right"?"left":"right",h=hn("transform"),f=void 0,g=void 0;if(u==="bottom"?s.nodeName==="HTML"?g=-s.clientHeight+p.bottom:g=-c.height+p.bottom:g=p.top,d==="right"?s.nodeName==="HTML"?f=-s.clientWidth+p.right:f=-c.width+p.right:f=p.left,a&&h)l[h]="translate3d("+f+"px, "+g+"px, 0)",l[u]=0,l[d]=0,l.willChange="transform";else{var S=u==="bottom"?-1:1,b=d==="right"?-1:1;l[u]=g*S,l[d]=f*b,l.willChange=u+", "+d}var x={"x-placement":e.placement};return e.attributes=ce({},x,e.attributes),e.styles=ce({},l,e.styles),e.arrowStyles=ce({},e.offsets.arrow,e.arrowStyles),e}function Qr(e,t,n){var r=ft(e,function(s){var c=s.name;return c===t}),o=!!r&&e.some(function(s){return s.name===n&&s.enabled&&s.order<r.order});if(!o){var i="`"+t+"`",a="`"+n+"`";console.warn(a+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")}return o}function Di(e,t){var n;if(!Qr(e.instance.modifiers,"arrow","keepTogether"))return e;var r=t.element;if(typeof r=="string"){if(r=e.instance.popper.querySelector(r),!r)return e}else if(!e.instance.popper.contains(r))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var o=e.placement.split("-")[0],i=e.offsets,a=i.popper,s=i.reference,c=["left","right"].indexOf(o)!==-1,l=c?"height":"width",p=c?"Top":"Left",u=p.toLowerCase(),d=c?"left":"top",h=c?"bottom":"right",f=$r(r)[l];s[h]-f<a[u]&&(e.offsets.popper[u]-=a[u]-(s[h]-f)),s[u]+f>a[h]&&(e.offsets.popper[u]+=s[u]+f-a[h]),e.offsets.popper=ke(e.offsets.popper);var g=s[u]+s[l]/2-f/2,S=He(e.instance.popper),b=parseFloat(S["margin"+p]),x=parseFloat(S["border"+p+"Width"]),y=g-e.offsets.popper[u]-b-x;return y=Math.max(Math.min(a[l]-f,y),0),e.arrowElement=r,e.offsets.arrow=(n={},Ve(n,u,Math.round(y)),Ve(n,d,""),n),e}function Fi(e){return e==="end"?"start":e==="start"?"end":e}function dr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=Xt.indexOf(e),r=Xt.slice(n+1).concat(Xt.slice(0,n));return t?r.reverse():r}function Bi(e,t){if(Jr(e.instance.modifiers,"inner")||e.flipped&&e.placement===e.originalPlacement)return e;var n=fn(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),r=e.placement.split("-")[0],o=kt(r),i=e.placement.split("-")[1]||"",a=[];switch(t.behavior){case Zt.FLIP:a=[r,o];break;case Zt.CLOCKWISE:a=dr(r);break;case Zt.COUNTERCLOCKWISE:a=dr(r,!0);break;default:a=t.behavior}return a.forEach(function(s,c){if(r!==s||a.length===c+1)return e;r=e.placement.split("-")[0],o=kt(r);var l=e.offsets.popper,p=e.offsets.reference,u=Math.floor,d=r==="left"&&u(l.right)>u(p.left)||r==="right"&&u(l.left)<u(p.right)||r==="top"&&u(l.bottom)>u(p.top)||r==="bottom"&&u(l.top)<u(p.bottom),h=u(l.left)<u(n.left),f=u(l.right)>u(n.right),g=u(l.top)<u(n.top),S=u(l.bottom)>u(n.bottom),b=r==="left"&&h||r==="right"&&f||r==="top"&&g||r==="bottom"&&S,x=["top","bottom"].indexOf(r)!==-1,y=!!t.flipVariations&&(x&&i==="start"&&h||x&&i==="end"&&f||!x&&i==="start"&&g||!x&&i==="end"&&S),v=!!t.flipVariationsByContent&&(x&&i==="start"&&f||x&&i==="end"&&h||!x&&i==="start"&&S||!x&&i==="end"&&g),w=y||v;(d||b||w)&&(e.flipped=!0,(d||b)&&(r=a[c+1]),w&&(i=Fi(i)),e.placement=r+(i?"-"+i:""),e.offsets.popper=ce({},e.offsets.popper,Vr(e.instance.popper,e.offsets.reference,e.placement)),e=Kr(e.instance.modifiers,e,"flip"))}),e}function Wi(e){var t=e.offsets,n=t.popper,r=t.reference,o=e.placement.split("-")[0],i=Math.floor,a=["top","bottom"].indexOf(o)!==-1,s=a?"right":"bottom",c=a?"left":"top",l=a?"width":"height";return n[s]<i(r[c])&&(e.offsets.popper[c]=i(r[c])-n[l]),n[c]>i(r[s])&&(e.offsets.popper[c]=i(r[s])),e}function Hi(e,t,n,r){var o=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),i=+o[1],a=o[2];if(!i)return e;if(a.indexOf("%")===0){var s=void 0;switch(a){case"%p":s=n;break;case"%":case"%r":default:s=r}var c=ke(s);return c[t]/100*i}else if(a==="vh"||a==="vw"){var l=void 0;return a==="vh"?l=Math.max(document.documentElement.clientHeight,window.innerHeight||0):l=Math.max(document.documentElement.clientWidth,window.innerWidth||0),l/100*i}else return i}function Ui(e,t,n,r){var o=[0,0],i=["right","left"].indexOf(r)!==-1,a=e.split(/(\+|\-)/).map(function(p){return p.trim()}),s=a.indexOf(ft(a,function(p){return p.search(/,|\s/)!==-1}));a[s]&&a[s].indexOf(",")===-1&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var c=/\s*,\s*|\s+/,l=s!==-1?[a.slice(0,s).concat([a[s].split(c)[0]]),[a[s].split(c)[1]].concat(a.slice(s+1))]:[a];return l=l.map(function(p,u){var d=(u===1?!i:i)?"height":"width",h=!1;return p.reduce(function(f,g){return f[f.length-1]===""&&["+","-"].indexOf(g)!==-1?(f[f.length-1]=g,h=!0,f):h?(f[f.length-1]+=g,h=!1,f):f.concat(g)},[]).map(function(f){return Hi(f,d,t,n)})}),l.forEach(function(p,u){p.forEach(function(d,h){mn(d)&&(o[u]+=d*(p[h-1]==="-"?-1:1))})}),o}function zi(e,t){var n=t.offset,r=e.placement,o=e.offsets,i=o.popper,a=o.reference,s=r.split("-")[0],c=void 0;return mn(+n)?c=[+n,0]:c=Ui(n,i,a,s),s==="left"?(i.top+=c[0],i.left-=c[1]):s==="right"?(i.top+=c[0],i.left+=c[1]):s==="top"?(i.left+=c[0],i.top-=c[1]):s==="bottom"&&(i.left+=c[0],i.top+=c[1]),e.popper=i,e}function Gi(e,t){var n=t.boundariesElement||qe(e.instance.popper);e.instance.reference===n&&(n=qe(n));var r=hn("transform"),o=e.instance.popper.style,i=o.top,a=o.left,s=o[r];o.top="",o.left="",o[r]="";var c=fn(e.instance.popper,e.instance.reference,t.padding,n,e.positionFixed);o.top=i,o.left=a,o[r]=s,t.boundaries=c;var l=t.priority,p=e.offsets.popper,u={primary:function(d){var h=p[d];return p[d]<c[d]&&!t.escapeWithReference&&(h=Math.max(p[d],c[d])),Ve({},d,h)},secondary:function(d){var h=d==="right"?"left":"top",f=p[h];return p[d]>c[d]&&!t.escapeWithReference&&(f=Math.min(p[h],c[d]-(d==="right"?p.width:p.height))),Ve({},h,f)}};return l.forEach(function(d){var h=["left","top"].indexOf(d)!==-1?"primary":"secondary";p=ce({},p,u[h](d))}),e.offsets.popper=p,e}function Yi(e){var t=e.placement,n=t.split("-")[0],r=t.split("-")[1];if(r){var o=e.offsets,i=o.reference,a=o.popper,s=["bottom","top"].indexOf(n)!==-1,c=s?"left":"top",l=s?"width":"height",p={start:Ve({},c,i[c]),end:Ve({},c,i[c]+i[l]-a[l])};e.offsets.popper=ce({},a,p[r])}return e}function qi(e){if(!Qr(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=ft(e.instance.modifiers,function(r){return r.name==="preventOverflow"}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(e.hide===!0)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(e.hide===!1)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}function $i(e){var t=e.placement,n=t.split("-")[0],r=e.offsets,o=r.popper,i=r.reference,a=["left","right"].indexOf(n)!==-1,s=["top","left"].indexOf(n)===-1;return o[a?"left":"top"]=i[n]-(s?o[a?"width":"height"]:0),e.placement=kt(t),e.offsets.popper=ke(o),e}function Dt(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(Qi(t))return t}function he(e){return function(t){return Dt(t)===e}}function Qi(e){return Xi.includes(e)}function Ze(e){return function(t){return typeof t===e}}function ea(e){return Zi.includes(e)}function P(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}if(P.array(e))return"Array";if(P.plainFunction(e))return"Function";var t=Dt(e);return t||"Object"}function to(e){return function(t){return typeof t===e}}function oa(e,t){var n=e.length;if(n!==t.length)return!1;for(var r=n;r--!==0;)if(!ie(e[r],t[r]))return!1;return!0}function ia(e,t){if(e.byteLength!==t.byteLength)return!1;for(var n=new DataView(e.buffer),r=new DataView(t.buffer),o=e.byteLength;o--;)if(n.getUint8(o)!==r.getUint8(o))return!1;return!0}function aa(e,t){var n,r,o,i;if(e.size!==t.size)return!1;try{for(var a=an(e.entries()),s=a.next();!s.done;s=a.next()){var c=s.value;if(!t.has(c[0]))return!1}}catch(u){n={error:u}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}try{for(var l=an(e.entries()),p=l.next();!p.done;p=l.next()){var c=p.value;if(!ie(c[1],t.get(c[0])))return!1}}catch(u){o={error:u}}finally{try{p&&!p.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}return!0}function sa(e,t){var n,r;if(e.size!==t.size)return!1;try{for(var o=an(e.entries()),i=o.next();!i.done;i=o.next()){var a=i.value;if(!t.has(a[0]))return!1}}catch(s){n={error:s}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return!0}function ie(e,t){if(e===t)return!0;if(e&&mr(e)&&t&&mr(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return oa(e,t);if(e instanceof Map&&t instanceof Map)return aa(e,t);if(e instanceof Set&&t instanceof Set)return sa(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return ia(e,t);if(hr(e)&&hr(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=n.length;o--!==0;)if(!Object.prototype.hasOwnProperty.call(t,n[o]))return!1;for(var o=n.length;o--!==0;){var i=n[o];if(!(i==="_owner"&&e.$$typeof)&&!ie(e[i],t[i]))return!1}return!0}return Number.isNaN(e)&&Number.isNaN(t)?!0:e===t}function Ft(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(pa(t))return t}function me(e){return function(t){return Ft(t)===e}}function pa(e){return ca.includes(e)}function Qe(e){return function(t){return typeof t===e}}function da(e){return ua.includes(e)}function R(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}if(R.array(e))return"Array";if(R.plainFunction(e))return"Function";var t=Ft(e);return t||"Object"}function fa(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every(function(n){return k.string(n)||k.array(n)||k.plainObject(n)})}function ha(e,t,n){return no(e,t)?[e,t].every(k.array)?!e.some(Er(n))&&t.some(Er(n)):[e,t].every(k.plainObject)?!Object.entries(e).some(vr(n))&&Object.entries(t).some(vr(n)):t===n:!1}function yr(e,t,n){var r=n.actual,o=n.key,i=n.previous,a=n.type,s=Te(e,o),c=Te(t,o),l=[s,c].every(k.number)&&(a==="increased"?s<c:s>c);return k.undefined(r)||(l=l&&c===r),k.undefined(i)||(l=l&&s===i),l}function gr(e,t,n){var r=n.key,o=n.type,i=n.value,a=Te(e,r),s=Te(t,r),c=o==="added"?a:s,l=o==="added"?s:a;if(!k.nullOrUndefined(i)){if(k.defined(c)){if(k.array(c)||k.plainObject(c))return ha(c,l,i)}else return ie(l,i);return!1}return[a,s].every(k.array)?!l.every(yn(c)):[a,s].every(k.plainObject)?ma(Object.keys(c),Object.keys(l)):![a,s].every(function(p){return k.primitive(p)&&k.defined(p)})&&(o==="added"?!k.defined(a)&&k.defined(s):k.defined(a)&&!k.defined(s))}function br(e,t,n){var r=n===void 0?{}:n,o=r.key,i=Te(e,o),a=Te(t,o);if(!no(i,a))throw new TypeError("Inputs have different types");if(!fa(i,a))throw new TypeError("Inputs don't have length");return[i,a].every(k.plainObject)&&(i=Object.keys(i),a=Object.keys(a)),[i,a]}function vr(e){return function(t){var n=t[0],r=t[1];return k.array(e)?ie(e,r)||e.some(function(o){return ie(o,r)||k.array(r)&&yn(r)(o)}):k.plainObject(e)&&e[n]?!!e[n]&&ie(e[n],r):ie(e,r)}}function ma(e,t){return t.some(function(n){return!e.includes(n)})}function Er(e){return function(t){return k.array(e)?e.some(function(n){return ie(n,t)||k.array(t)&&yn(t)(n)}):ie(e,t)}}function it(e,t){return k.array(e)?e.some(function(n){return ie(n,t)}):ie(e,t)}function yn(e){return function(t){return e.some(function(n){return ie(n,t)})}}function no(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every(k.array)||e.every(k.number)||e.every(k.plainObject)||e.every(k.string)}function Te(e,t){if(k.plainObject(e)||k.array(e)){if(k.string(t)){var n=t.split(".");return n.reduce(function(r,o){return r&&r[o]},e)}return k.number(t)?e[t]:e}return e}function ya(e,t){if([e,t].some(k.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every(function(u){return k.plainObject(u)||k.array(u)}))throw new Error("Expected plain objects or array");var n=function(u,d){try{return gr(e,t,{key:u,type:"added",value:d})}catch{return!1}},r=function(u,d,h){try{var f=Te(e,u),g=Te(t,u),S=k.defined(d),b=k.defined(h);if(S||b){var x=b?it(h,f):!it(d,f),y=it(d,g);return x&&y}return[f,g].every(k.array)||[f,g].every(k.plainObject)?!ie(f,g):f!==g}catch{return!1}},o=function(u,d,h){if(!k.defined(u))return!1;try{var f=Te(e,u),g=Te(t,u),S=k.defined(h);return it(d,f)&&(S?it(h,g):!S)}catch{return!1}},i=function(u,d){return k.defined(u)?(console.warn("`changedTo` is deprecated! Replace it with `change`"),r(u,d)):!1},a=function(u,d,h){if(!k.defined(u))return!1;try{return yr(e,t,{key:u,actual:d,previous:h,type:"decreased"})}catch{return!1}},s=function(u){try{var d=br(e,t,{key:u}),h=d[0],f=d[1];return!!h.length&&!f.length}catch{return!1}},c=function(u){try{var d=br(e,t,{key:u}),h=d[0],f=d[1];return!h.length&&!!f.length}catch{return!1}},l=function(u,d,h){if(!k.defined(u))return!1;try{return yr(e,t,{key:u,actual:d,previous:h,type:"increased"})}catch{return!1}},p=function(u,d){try{return gr(e,t,{key:u,type:"removed",value:d})}catch{return!1}};return{added:n,changed:r,changedFrom:o,changedTo:i,decreased:a,emptied:s,filled:c,increased:l,removed:p}}function Or(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function X(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Or(Object(n),!0).forEach(function(r){re(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Or(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function ht(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,oo(r.key),r)}}function mt(e,t,n){return t&&Sr(e.prototype,t),n&&Sr(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function re(e,t,n){return t=oo(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yt(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sn(e,t)}function Lt(e){return Lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Lt(e)}function sn(e,t){return sn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},sn(e,t)}function ga(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ba(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function ro(e,t){if(e==null)return{};var n=ba(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Pe(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function va(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Pe(e)}function gt(e){var t=ga();return function(){var n=Lt(e),r;if(t){var o=Lt(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return va(this,r)}}function Ea(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function oo(e){var t=Ea(e,"string");return typeof t=="symbol"?t:String(t)}function Ta(e,t,n,r){return typeof e=="boolean"?e:typeof e=="function"?e(t,n,r):e?!!e:!1}function Ia(e,t){return Object.hasOwnProperty.call(e,t)}function Ca(e,t,n,r){return r?new Error(r):new Error("Required ".concat(e[t]," `").concat(t,"` was not specified in `").concat(n,"`."))}function Pa(e,t){if(typeof e!="function")throw new TypeError(Sa);if(t&&typeof t!="string")throw new TypeError(wa)}function wr(e,t,n){return Pa(e,n),function(r,o,i){for(var a=arguments.length,s=new Array(a>3?a-3:0),c=3;c<a;c++)s[c-3]=arguments[c];return Ta(t,r,o,i)?Ia(r,o)?e.apply(void 0,[r,o,i].concat(s)):Ca(r,o,i,n):e.apply(void 0,[r,o,i].concat(s))}}function Oe(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Qt(){return"ontouchstart"in window&&/Mobi/.test(navigator.userAgent)}function xt(e){var t=e.title,n=e.data,r=e.warn,o=r===void 0?!1:r,i=e.debug,a=i===void 0?!1:i,s=o?console.warn||console.error:console.log;a&&t&&n&&(console.groupCollapsed("%creact-floater: ".concat(t),"color: #9b00ff; font-weight: bold; font-size: 12px;"),Array.isArray(n)?n.forEach(function(c){le.plainObject(c)&&c.key?s.apply(console,[c.key,c.value]):s.apply(console,[c])}):s.apply(console,[n]),console.groupEnd())}function Ra(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;e.addEventListener(t,n,r)}function xa(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;e.removeEventListener(t,n,r)}function _a(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,o;o=function(i){n(i),xa(e,t,o)},Ra(e,t,o,r)}function Tr(){}function so(e){var t=e.handleClick,n=e.styles,r=n.color,o=n.height,i=n.width,a=ro(n,Aa);return m.createElement("button",{"aria-label":"close",onClick:t,style:a,type:"button"},m.createElement("svg",{width:"".concat(i,"px"),height:"".concat(o,"px"),viewBox:"0 0 18 18",version:"1.1",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},m.createElement("g",null,m.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:r}))))}function lo(e){var t=e.content,n=e.footer,r=e.handleClick,o=e.open,i=e.positionWrapper,a=e.showCloseButton,s=e.title,c=e.styles,l={content:m.isValidElement(t)?t:m.createElement("div",{className:"__floater__content",style:c.content},t)};return s&&(l.title=m.isValidElement(s)?s:m.createElement("div",{className:"__floater__title",style:c.title},s)),n&&(l.footer=m.isValidElement(n)?n:m.createElement("div",{className:"__floater__footer",style:c.footer},n)),(a||i)&&!le.boolean(o)&&(l.close=m.createElement(so,{styles:c.close,handleClick:r})),m.createElement("div",{className:"__floater__container",style:c.container},l.close,l.title,l.content,l.footer)}function ka(e){var t=(0,on.default)(Na,e.options||{});return{wrapper:{cursor:"help",display:"inline-flex",flexDirection:"column",zIndex:t.zIndex},wrapperPosition:{left:-1e3,position:"absolute",top:-1e3,visibility:"hidden"},floater:{display:"inline-block",filter:"drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))",maxWidth:300,opacity:0,position:"relative",transition:"opacity 0.3s",visibility:"hidden",zIndex:t.zIndex},floaterOpening:{opacity:1,visibility:"visible"},floaterWithAnimation:{opacity:1,transition:"opacity 0.3s, transform 0.2s",visibility:"visible"},floaterWithComponent:{maxWidth:"100%"},floaterClosing:{opacity:0,visibility:"visible"},floaterCentered:{left:"50%",position:"fixed",top:"50%",transform:"translate(-50%, -50%)"},container:{backgroundColor:"#fff",color:"#666",minHeight:60,minWidth:200,padding:20,position:"relative",zIndex:10},title:{borderBottom:"1px solid #555",color:"#555",fontSize:18,marginBottom:5,paddingBottom:6,paddingRight:18},content:{fontSize:15},close:{backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",fontSize:0,height:15,outline:"none",padding:10,position:"absolute",right:0,top:0,width:15,WebkitAppearance:"none"},footer:{borderTop:"1px solid #ccc",fontSize:13,marginTop:10,paddingTop:5},arrow:{color:"#fff",display:"inline-flex",length:16,margin:8,position:"absolute",spread:32},options:t}}function Ne(){var e;return!!(typeof window<"u"&&(e=window.document)!=null&&e.createElement)}function po(e){return e?e.getBoundingClientRect():null}function Fa(e=!1){let{body:t,documentElement:n}=document;if(!t||!n)return 0;if(e){let r=[t.scrollHeight,t.offsetHeight,n.clientHeight,n.scrollHeight,n.offsetHeight].sort((i,a)=>i-a),o=Math.floor(r.length/2);return r.length%2===0?(r[o-1]+r[o])/2:r[o]}return Math.max(t.scrollHeight,t.offsetHeight,n.clientHeight,n.scrollHeight,n.offsetHeight)}function Re(e){if(typeof e=="string")try{return document.querySelector(e)}catch(t){return console.error(t),null}return e}function Ba(e){return!e||e.nodeType!==1?null:getComputedStyle(e)}function ut(e,t,n){if(!e)return Be();let r=(0,Br.default)(e);if(r){if(r.isSameNode(Be()))return n?document:Be();if(!(r.scrollHeight>r.offsetHeight)&&!t)return r.style.overflow="initial",Be()}return r}function bt(e,t){if(!e)return!1;let n=ut(e,t);return n?!n.isSameNode(Be()):!1}function Wa(e){return e.offsetParent!==document.body}function Ke(e,t="fixed"){if(!e||!(e instanceof HTMLElement))return!1;let{nodeName:n}=e,r=Ba(e);return n==="BODY"||n==="HTML"?!1:r&&r.position===t?!0:e.parentNode?Ke(e.parentNode,t):!1}function Ha(e){var t;if(!e)return!1;let n=e;for(;n&&n!==document.body;){if(n instanceof HTMLElement){let{display:r,visibility:o}=getComputedStyle(n);if(r==="none"||o==="hidden")return!1}n=(t=n.parentElement)!=null?t:null}return!0}function Ua(e,t,n){var r,o,i;let a=po(e),s=ut(e,n),c=bt(e,n),l=Ke(e),p=0,u=(r=a?.top)!=null?r:0;if(c&&l){let d=(o=e?.offsetTop)!=null?o:0,h=(i=s?.scrollTop)!=null?i:0;u=d-h}else s instanceof HTMLElement&&(p=s.scrollTop,!c&&!Ke(e)&&(u+=p),s.isSameNode(Be())||(u+=Be().scrollTop));return Math.floor(u-t)}function za(e,t,n){var r;if(!e)return 0;let{offsetTop:o=0,scrollTop:i=0}=(r=(0,Br.default)(e))!=null?r:{},a=e.getBoundingClientRect().top+i;o&&(bt(e,n)||Wa(e))&&(a-=o);let s=Math.floor(a-t);return s<0?0:s}function Be(){var e;return(e=document.scrollingElement)!=null?e:document.documentElement}function Ga(e,t){let{duration:n,element:r}=t;return new Promise((o,i)=>{let{scrollTop:a}=r,s=e>a?e-a:a-e;di.default.top(r,e,{duration:s<100?50:n},c=>c&&c.message!=="Element already at target scroll position"?i(c):o())})}function fo(e=navigator.userAgent){let t=e;return typeof window>"u"?t="node":document.documentMode?t="ie":/Edge/.test(e)?t="edge":window.opera||e.includes(" OPR/")?t="opera":typeof window.InstallTrigger<"u"?t="firefox":window.chrome?t="chrome":/(Version\/([\d._]+).*Safari|CriOS|FxiOS| Mobile\/)/.test(e)&&(t="safari"),t}function _t(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function Se(e,t={}){let{defaultValue:n,step:r,steps:o}=t,i=(0,sr.default)(e);if(i)(i.includes("{step}")||i.includes("{steps}"))&&r&&o&&(i=i.replace("{step}",r.toString()).replace("{steps}",o.toString()));else if(Ht(e)&&!Object.values(e.props).length&&_t(e.type)==="function"){let a=e.type({});i=Se(a,t)}else i=(0,sr.default)(n);return i}function Ya(e,t){return!_.plainObject(e)||!_.array(t)?!1:Object.keys(e).every(n=>t.includes(n))}function qa(e){let t=/^#?([\da-f])([\da-f])([\da-f])$/i,n=e.replace(t,(o,i,a,s)=>i+i+a+a+s+s),r=/^#?([\da-f]{2})([\da-f]{2})([\da-f]{2})$/i.exec(n);return r?[parseInt(r[1],16),parseInt(r[2],16),parseInt(r[3],16)]:[]}function Ir(e){return e.disableBeacon||e.placement==="center"}function Cr(){return!["chrome","safari","firefox","opera"].includes(fo())}function Le({data:e,debug:t=!1,title:n,warn:r=!1}){let o=r?console.warn||console.error:console.log;t&&(n&&e?(console.groupCollapsed(`%creact-joyride: ${n}`,"color: #ff0044; font-weight: bold; font-size: 12px;"),Array.isArray(e)?e.forEach(i=>{_.plainObject(i)&&i.key?o.apply(console,[i.key,i.value]):o.apply(console,[i])}):o.apply(console,[e]),console.groupEnd()):console.error("Missing title or data props"))}function $a(e){return Object.keys(e)}function ho(e,...t){if(!_.plainObject(e))throw new TypeError("Expected an object");let n={};for(let r in e)({}).hasOwnProperty.call(e,r)&&(t.includes(r)||(n[r]=e[r]));return n}function Va(e,...t){if(!_.plainObject(e))throw new TypeError("Expected an object");if(!t.length)return e;let n={};for(let r in e)({}).hasOwnProperty.call(e,r)&&t.includes(r)&&(n[r]=e[r]);return n}function ln(e,t,n){let r=i=>i.replace("{step}",String(t)).replace("{steps}",String(n));if(_t(e)==="string")return r(e);if(!Ht(e))return e;let{children:o}=e.props;if(_t(o)==="string"&&o.includes("{step}"))return Wt(e,{children:r(o)});if(Array.isArray(o))return Wt(e,{children:o.map(i=>typeof i=="string"?r(i):ln(i,t,n))});if(_t(e.type)==="function"&&!Object.values(e.props).length){let i=e.type({});return ln(i,t,n)}return e}function Ka(e){let{isFirstStep:t,lifecycle:n,previousLifecycle:r,scrollToFirstStep:o,step:i,target:a}=e;return!i.disableScrolling&&(!t||o||n===L.TOOLTIP)&&i.placement!=="center"&&(!i.isFixed||!Ke(a))&&r!==n&&[L.BEACON,L.TOOLTIP].includes(n)}function es(e,t){var n,r,o,i,a;let{floaterProps:s,styles:c}=e,l=(0,Rt.default)((n=t.floaterProps)!=null?n:{},s??{}),p=(0,Rt.default)(c??{},(r=t.styles)!=null?r:{}),u=(0,Rt.default)(Qa,p.options||{}),d=t.placement==="center"||t.disableBeacon,{width:h}=u;window.innerWidth>480&&(h=380),"width"in u&&(h=typeof u.width=="number"&&window.innerWidth<u.width?window.innerWidth-30:u.width);let f={bottom:0,left:0,overflow:"hidden",position:"absolute",right:0,top:0,zIndex:u.zIndex},g={beacon:{...lt,display:d?"none":"inline-block",height:u.beaconSize,position:"relative",width:u.beaconSize,zIndex:u.zIndex},beaconInner:{animation:"joyride-beacon-inner 1.2s infinite ease-in-out",backgroundColor:u.primaryColor,borderRadius:"50%",display:"block",height:"50%",left:"50%",opacity:.7,position:"absolute",top:"50%",transform:"translate(-50%, -50%)",width:"50%"},beaconOuter:{animation:"joyride-beacon-outer 1.2s infinite ease-in-out",backgroundColor:`rgba(${qa(u.primaryColor).join(",")}, 0.2)`,border:`2px solid ${u.primaryColor}`,borderRadius:"50%",boxSizing:"border-box",display:"block",height:"100%",left:0,opacity:.9,position:"absolute",top:0,transformOrigin:"center",width:"100%"},tooltip:{backgroundColor:u.backgroundColor,borderRadius:5,boxSizing:"border-box",color:u.textColor,fontSize:16,maxWidth:"100%",padding:15,position:"relative",width:h},tooltipContainer:{lineHeight:1.4,textAlign:"center"},tooltipTitle:{fontSize:18,margin:0},tooltipContent:{padding:"20px 10px"},tooltipFooter:{alignItems:"center",display:"flex",justifyContent:"flex-end",marginTop:15},tooltipFooterSpacer:{flex:1},buttonNext:{...lt,backgroundColor:u.primaryColor,borderRadius:4,color:"#fff"},buttonBack:{...lt,color:u.primaryColor,marginLeft:"auto",marginRight:5},buttonClose:{...lt,color:u.textColor,height:14,padding:15,position:"absolute",right:0,top:0,width:14},buttonSkip:{...lt,color:u.textColor,fontSize:14},overlay:{...f,backgroundColor:u.overlayColor,mixBlendMode:"hard-light"},overlayLegacy:{...f},overlayLegacyCenter:{...f,backgroundColor:u.overlayColor},spotlight:{...Pr,backgroundColor:"gray"},spotlightLegacy:{...Pr,boxShadow:`0 0 0 9999px ${u.overlayColor}, ${u.spotlightShadow}`},floaterStyles:{arrow:{color:(a=(i=(o=l?.styles)==null?void 0:o.arrow)==null?void 0:i.color)!=null?a:u.arrowColor},options:{zIndex:u.zIndex+100}},options:u};return(0,Rt.default)(g,p)}function ts(e){return Va(e,"beaconComponent","disableCloseOnEsc","disableOverlay","disableOverlayClose","disableScrolling","disableScrollParentFix","floaterProps","hideBackButton","hideCloseButton","locale","showProgress","showSkipButton","spotlightClicks","spotlightPadding","styles","tooltipComponent")}function Ye(e,t){var n,r,o,i,a,s;let c=t??{},l=Pt.default.all([Xa,ts(e),c],{isMergeableObject:_.plainObject}),p=es(e,l),u=bt(Re(l.target),l.disableScrollParentFix),d=Pt.default.all([Ja,(n=e.floaterProps)!=null?n:{},(r=l.floaterProps)!=null?r:{}]);return d.offset=l.offset,d.styles=(0,Pt.default)((o=d.styles)!=null?o:{},p.floaterStyles),d.offset+=(a=(i=e.spotlightPadding)!=null?i:l.spotlightPadding)!=null?a:0,l.placementBeacon&&d.wrapperOptions&&(d.wrapperOptions.placement=l.placementBeacon),u&&d.options.preventOverflow&&(d.options.preventOverflow.boundariesElement="window"),{...l,locale:Pt.default.all([mo,(s=e.locale)!=null?s:{},l.locale||{}]),floaterProps:d,styles:ho(p,"floaterStyles")}}function yo(e,t=!1){return _.plainObject(e)?e.target?!0:(Le({title:"validateStep",data:"target is missing from the step",warn:!0,debug:t}),!1):(Le({title:"validateStep",data:"step must be an object",warn:!0,debug:t}),!1)}function Rr(e,t=!1){return _.array(e)?e.every(n=>yo(n,t)):(Le({title:"validateSteps",data:"steps must be an array",warn:!0,debug:t}),!1)}function rs(e){return new ns(e)}function os({styles:e}){return V("div",{key:"JoyrideSpotlight",className:"react-joyride__spotlight","data-test-id":"spotlight",style:e})}function us({styles:e,...t}){let{color:n,height:r,width:o,...i}=e;return m.createElement("button",{style:i,type:"button",...t},m.createElement("svg",{height:typeof r=="number"?`${r}px`:r,preserveAspectRatio:"xMidYMid",version:"1.1",viewBox:"0 0 18 18",width:typeof o=="number"?`${o}px`:o,xmlns:"http://www.w3.org/2000/svg"},m.createElement("g",null,m.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:n}))))}function ds(e){let{backProps:t,closeProps:n,index:r,isLastStep:o,primaryProps:i,skipProps:a,step:s,tooltipProps:c}=e,{content:l,hideBackButton:p,hideCloseButton:u,hideFooter:d,showSkipButton:h,styles:f,title:g}=s,S={};return S.primary=V("button",{"data-test-id":"button-primary",style:f.buttonNext,type:"button",...i}),h&&!o&&(S.skip=V("button",{"aria-live":"off","data-test-id":"button-skip",style:f.buttonSkip,type:"button",...a})),!p&&r>0&&(S.back=V("button",{"data-test-id":"button-back",style:f.buttonBack,type:"button",...t})),S.close=!u&&V(ps,{"data-test-id":"button-close",styles:f.buttonClose,...n}),V("div",{key:"JoyrideTooltip","aria-label":Se(g??l),className:"react-joyride__tooltip",style:f.tooltip,...c},V("div",{style:f.tooltipContainer},g&&V("h1",{"aria-label":Se(g),style:f.tooltipTitle},g),V("div",{style:f.tooltipContent},l)),!d&&V("div",{style:f.tooltipFooter},V("div",{style:f.tooltipFooterSpacer},S.skip),S.back,S.primary),S.close)}function Ps({step:e,steps:t,onClose:n,onComplete:r}){let[o,i]=ae(null),a=Nn();return se(()=>{let s;return i(c=>{let l=t.findIndex(({key:p})=>p===e);return l===-1?null:l===c?c:(s=setTimeout(i,500,l),null)}),()=>clearTimeout(s)},[e,t]),o===null?null:m.createElement(ys,{continuous:!0,steps:t,stepIndex:o,spotlightPadding:0,disableCloseOnEsc:!0,disableOverlayClose:!0,disableScrolling:!0,callback:s=>{s.action===$.CLOSE&&n(),s.action===$.NEXT&&s.index===s.size-1&&r()},floaterProps:{disableAnimation:!0,styles:{arrow:{length:20,spread:2},floater:{filter:a.base==="light"?"drop-shadow(0px 5px 5px rgba(0,0,0,0.05)) drop-shadow(0 1px 3px rgba(0,0,0,0.1))":"drop-shadow(#fff5 0px 0px 0.5px) drop-shadow(#fff5 0px 0px 0.5px)"}}},tooltipComponent:Cs,styles:{overlay:{mixBlendMode:"unset",backgroundColor:t[o]?.target==="body"?"rgba(27, 28, 29, 0.2)":"none"},spotlight:{backgroundColor:"none",border:`solid 2px ${a.color.secondary}`,boxShadow:"0px 0px 0px 9999px rgba(27, 28, 29, 0.2)"},tooltip:{width:280,color:a.color.lightest,background:a.color.secondary},options:{zIndex:9998,primaryColor:a.color.secondary,arrowColor:a.color.secondary}}})}function Rs(e){let t=e||typeof window<"u"&&window;return!!(t&&(t.navigator.userAgent.match(/Chromatic/)||t.location.href.match(/chromatic=true/)))}function Ms(e){for(let t=e.length-1;t>0;t--){let n=Math.floor(Math.random()*(t+1));[e[t],e[n]]=[e[n],e[t]]}return e}function en(e){return Rs()?e:Object.fromEntries(Ms(Object.entries(e)))}function Js({api:e}){let[t,n]=ae(!0),[r,o]=ae(!1),[i,a]=ae("1:Intro"),[s,c]=ae(),[l,p]=ae(),[u,d]=ae(),[h,f]=ae(),g=nt(I=>{try{let{id:z,refId:te}=e.getCurrentStoryData()||{};(z!==I||te!==void 0)&&e.selectStory(I)}catch{}},[e]),S=nt(()=>{let I=new URL(window.location.href),z=decodeURIComponent(I.searchParams.get("path"));I.search=`?path=${z}&onboarding=false`,history.replaceState({},"",I.href),e.setQueryParams({onboarding:"false"}),n(!1)},[e,n]),b=nt(I=>{e.emit(Jt,{step:"7:FinishedOnboarding",type:"telemetry"}),e.emit(Jt,{answers:I,type:"survey"}),g("configure-your-project--docs"),S()},[e,g,S]);if(se(()=>{e.setQueryParams({onboarding:"true"}),g("example-button--primary"),e.togglePanel(!0),e.togglePanelPosition("bottom"),e.setSelectedPanel(wt)},[e,g]),se(()=>{let I=new MutationObserver(()=>{c(document.getElementById("control-primary")),p(document.getElementById("save-from-controls")),d(document.getElementById("create-new-story-form"))});return I.observe(document.body,{childList:!0,subtree:!0}),()=>I.disconnect()},[]),se(()=>{a(I=>["1:Intro","5:StoryCreated","6:IntentSurvey","7:FinishedOnboarding"].includes(I)?I:u?"4:CreateStory":l?"3:SaveFromControls":s?"2:Controls":"1:Intro")},[u,s,l]),se(()=>e.on(Tn,({payload:I,success:z})=>{!z||!I?.newStoryName||(f(I),o(!0),a("5:StoryCreated"),setTimeout(()=>e.clearNotification("save-story-success")))}),[e]),se(()=>e.emit(Jt,{step:i,type:"telemetry"}),[e,i]),!t)return null;let x=h?.sourceFileContent,y=x?.lastIndexOf(`export const ${h?.newStoryExportName}`),v=x?.slice(y).trim(),w=x?.slice(0,y).split(`
`).length,U=[{key:"2:Controls",target:"#control-primary",title:"Interactive story playground",content:m.createElement(m.Fragment,null,"See how a story renders with different data and state without touching code. Try it out by toggling this button.",m.createElement(Qn,{targetSelector:"#control-primary",pulsating:!0})),offset:20,placement:"right",disableBeacon:!0,disableOverlay:!0,spotlightClicks:!0,onNextButtonClick:()=>{document.querySelector("#control-primary").click()}},{key:"3:SaveFromControls",target:'button[aria-label="Create new story with these settings"]',title:"Save your changes as a new story",content:m.createElement(m.Fragment,null,"Great! Storybook stories represent the key states of each of your components. After modifying a story, you can save your changes from here or reset it.",m.createElement(Qn,{targetSelector:"button[aria-label='Create new story with these settings']"})),offset:6,placement:"top",disableBeacon:!0,disableOverlay:!0,spotlightClicks:!0,onNextButtonClick:()=>{document.querySelector('button[aria-label="Create new story with these settings"]').click()},styles:{tooltip:{width:400}}},{key:"5:StoryCreated",target:'#storybook-explorer-tree [data-selected="true"]',title:"You just added your first story!",content:m.createElement(m.Fragment,null,"Well done! You just created your first story from the Storybook manager. This automatically added a few lines of code in"," ",m.createElement($s,null,h?.sourceFileName),".",v&&m.createElement(Yt,{theme:qt(An.dark)},m.createElement(Vs,null,m.createElement(xn,{language:"jsx",showLineNumbers:!0,startingLineNumber:w},v)))),offset:12,placement:"right",disableBeacon:!0,disableOverlay:!0,styles:{tooltip:{width:400}}}];return m.createElement(Yt,{theme:Ks},r&&m.createElement(Jo,null),i==="1:Intro"?m.createElement(qs,{onDismiss:()=>a("2:Controls")}):i==="6:IntentSurvey"?m.createElement(js,{onComplete:b,onDismiss:S}):m.createElement(Ps,{step:i,steps:U,onClose:S,onComplete:()=>a("6:IntentSurvey")}))}var Co,Ar,Po,Nr,Ro,xo,ue,_o,We,Ao,No,ko,cn,Lo,kr,jo,Lr,jr,Mo,Do,Fo,Bo,Dn,Wo,Fn,Bn,Wn,Hn,Un,zn,Gn,Yn,qn,$n,Vn,Uo,zo,Go,Kn,Ct,Ee,ct,Yo,$t,Vt,Jn,Ge,Kt,Xn,Mr,Zn,qo,$o,Ko,Jo,Jt,Xo,Zo,er,tr,Qo,oi,ii,li,_,di,Br,sr,Pt,Rt,O,pt,fi,yi,gi,lr,cr,Ei,Oi,Ve,ce,ji,eo,Xt,Zt,Vi,Ki,Mt,fr,on,Ji,Xi,Zi,le,ta,na,hr,mr,ra,an,la,ca,ua,k,Oa,Sa,wa,H,at,io,ao,Aa,co,uo,Na,La,ja,gn,Ma,Da,A,$,de,L,B,st,Ja,mo,Xa,Za,Qa,lt,Pr,go,xr,ns,is,as,ss,ls,cs,ps,fs,hs,ms,bo,ys,gs,bs,vs,Es,Os,Ss,ws,Ts,Is,Cs,xs,_s,As,Ns,ks,Ls,js,Ds,vo,Fs,Bs,Ws,Hs,Us,zs,Gs,Ys,_r,qs,$s,Vs,Ks,Oo=pe(()=>{Z();Q();ee();Et();Et();_n();zt();Gt();kn();St();St();Mn();Co=Object.create,Ar=Object.defineProperty,Po=Object.getOwnPropertyDescriptor,Nr=Object.getOwnPropertyNames,Ro=Object.getPrototypeOf,xo=Object.prototype.hasOwnProperty,ue=(e,t)=>function(){return t||(0,e[Nr(e)[0]])((t={exports:{}}).exports,t),t.exports},_o=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Nr(t))!xo.call(e,o)&&o!==n&&Ar(e,o,{get:()=>t[o],enumerable:!(r=Po(t,o))||r.enumerable});return e},We=(e,t,n)=>(n=e!=null?Co(Ro(e)):{},_o(t||!e||!e.__esModule?Ar(n,"default",{value:e,enumerable:!0}):n,e)),Ao=ue({"../../node_modules/scroll/index.js"(e,t){var n=new Error("Element already at target scroll position"),r=new Error("Scroll cancelled"),o=Math.min,i=Date.now;t.exports={left:a("scrollLeft"),top:a("scrollTop")};function a(l){return function(p,u,d,h){d=d||{},typeof d=="function"&&(h=d,d={}),typeof h!="function"&&(h=c);var f=i(),g=p[l],S=d.ease||s,b=isNaN(d.duration)?350:+d.duration,x=!1;return g===u?h(n,p[l]):requestAnimationFrame(v),y;function y(){x=!0}function v(w){if(x)return h(r,p[l]);var U=i(),I=o(1,(U-f)/b),z=S(I);p[l]=z*(u-g)+g,I<1?requestAnimationFrame(v):requestAnimationFrame(function(){h(null,p[l])})}}}function s(l){return .5*(1-Math.cos(Math.PI*l))}function c(){}}}),No=ue({"../../node_modules/scrollparent/scrollparent.js"(e,t){(function(n,r){typeof define=="function"&&define.amd?define([],r):typeof t=="object"&&t.exports?t.exports=r():n.Scrollparent=r()})(e,function(){function n(o){var i=getComputedStyle(o,null).getPropertyValue("overflow");return i.indexOf("scroll")>-1||i.indexOf("auto")>-1}function r(o){if(o instanceof HTMLElement||o instanceof SVGElement){for(var i=o.parentNode;i.parentNode;){if(n(i))return i;i=i.parentNode}return document.scrollingElement||document.documentElement}}return r})}}),ko=ue({"../../node_modules/react-innertext/index.js"(e,t){var n=function(i){return Object.prototype.hasOwnProperty.call(i,"props")},r=function(i,a){return i+o(a)},o=function(i){return i===null||typeof i=="boolean"||typeof i>"u"?"":typeof i=="number"?i.toString():typeof i=="string"?i:Array.isArray(i)?i.reduce(r,""):n(i)&&Object.prototype.hasOwnProperty.call(i.props,"children")?o(i.props.children):""};o.default=o,t.exports=o}}),cn=ue({"../../node_modules/deepmerge/dist/cjs.js"(e,t){var n=function(y){return r(y)&&!o(y)};function r(y){return!!y&&typeof y=="object"}function o(y){var v=Object.prototype.toString.call(y);return v==="[object RegExp]"||v==="[object Date]"||s(y)}var i=typeof Symbol=="function"&&Symbol.for,a=i?Symbol.for("react.element"):60103;function s(y){return y.$$typeof===a}function c(y){return Array.isArray(y)?[]:{}}function l(y,v){return v.clone!==!1&&v.isMergeableObject(y)?b(c(y),y,v):y}function p(y,v,w){return y.concat(v).map(function(U){return l(U,w)})}function u(y,v){if(!v.customMerge)return b;var w=v.customMerge(y);return typeof w=="function"?w:b}function d(y){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(y).filter(function(v){return Object.propertyIsEnumerable.call(y,v)}):[]}function h(y){return Object.keys(y).concat(d(y))}function f(y,v){try{return v in y}catch{return!1}}function g(y,v){return f(y,v)&&!(Object.hasOwnProperty.call(y,v)&&Object.propertyIsEnumerable.call(y,v))}function S(y,v,w){var U={};return w.isMergeableObject(y)&&h(y).forEach(function(I){U[I]=l(y[I],w)}),h(v).forEach(function(I){g(y,I)||(f(y,I)&&w.isMergeableObject(v[I])?U[I]=u(I,w)(y[I],v[I],w):U[I]=l(v[I],w))}),U}function b(y,v,w){w=w||{},w.arrayMerge=w.arrayMerge||p,w.isMergeableObject=w.isMergeableObject||n,w.cloneUnlessOtherwiseSpecified=l;var U=Array.isArray(v),I=Array.isArray(y),z=U===I;return z?U?w.arrayMerge(y,v,w):S(y,v,w):l(v,w)}b.all=function(y,v){if(!Array.isArray(y))throw new Error("first argument should be an array");return y.reduce(function(w,U){return b(w,U,v)},{})};var x=b;t.exports=x}}),Lo=ue({"../../node_modules/react-is/cjs/react-is.development.js"(e){(function(){var t=typeof Symbol=="function"&&Symbol.for,n=t?Symbol.for("react.element"):60103,r=t?Symbol.for("react.portal"):60106,o=t?Symbol.for("react.fragment"):60107,i=t?Symbol.for("react.strict_mode"):60108,a=t?Symbol.for("react.profiler"):60114,s=t?Symbol.for("react.provider"):60109,c=t?Symbol.for("react.context"):60110,l=t?Symbol.for("react.async_mode"):60111,p=t?Symbol.for("react.concurrent_mode"):60111,u=t?Symbol.for("react.forward_ref"):60112,d=t?Symbol.for("react.suspense"):60113,h=t?Symbol.for("react.suspense_list"):60120,f=t?Symbol.for("react.memo"):60115,g=t?Symbol.for("react.lazy"):60116,S=t?Symbol.for("react.block"):60121,b=t?Symbol.for("react.fundamental"):60117,x=t?Symbol.for("react.responder"):60118,y=t?Symbol.for("react.scope"):60119;function v(T){return typeof T=="string"||typeof T=="function"||T===o||T===p||T===a||T===i||T===d||T===h||typeof T=="object"&&T!==null&&(T.$$typeof===g||T.$$typeof===f||T.$$typeof===s||T.$$typeof===c||T.$$typeof===u||T.$$typeof===b||T.$$typeof===x||T.$$typeof===y||T.$$typeof===S)}function w(T){if(typeof T=="object"&&T!==null){var ne=T.$$typeof;switch(ne){case n:var Ce=T.type;switch(Ce){case l:case p:case o:case a:case i:case d:return Ce;default:var bn=Ce&&Ce.$$typeof;switch(bn){case c:case u:case g:case f:case s:return bn;default:return ne}}case r:return ne}}}var U=l,I=p,z=c,te=s,Ie=n,je=u,et=o,Me=g,Ue=f,tt=r,xe=a,_e=i,ye=d,Ae=!1;function Bt(T){return Ae||(Ae=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),vt(T)||w(T)===l}function vt(T){return w(T)===p}function E(T){return w(T)===c}function N(T){return w(T)===s}function W(T){return typeof T=="object"&&T!==null&&T.$$typeof===n}function D(T){return w(T)===u}function j(T){return w(T)===o}function G(T){return w(T)===g}function M(T){return w(T)===f}function F(T){return w(T)===r}function Y(T){return w(T)===a}function K(T){return w(T)===i}function q(T){return w(T)===d}e.AsyncMode=U,e.ConcurrentMode=I,e.ContextConsumer=z,e.ContextProvider=te,e.Element=Ie,e.ForwardRef=je,e.Fragment=et,e.Lazy=Me,e.Memo=Ue,e.Portal=tt,e.Profiler=xe,e.StrictMode=_e,e.Suspense=ye,e.isAsyncMode=Bt,e.isConcurrentMode=vt,e.isContextConsumer=E,e.isContextProvider=N,e.isElement=W,e.isForwardRef=D,e.isFragment=j,e.isLazy=G,e.isMemo=M,e.isPortal=F,e.isProfiler=Y,e.isStrictMode=K,e.isSuspense=q,e.isValidElementType=v,e.typeOf=w})()}}),kr=ue({"../../node_modules/react-is/index.js"(e,t){t.exports=Lo()}}),jo=ue({"../../node_modules/object-assign/index.js"(e,t){var n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function i(s){if(s==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(s)}function a(){try{if(!Object.assign)return!1;var s=new String("abc");if(s[5]="de",Object.getOwnPropertyNames(s)[0]==="5")return!1;for(var c={},l=0;l<10;l++)c["_"+String.fromCharCode(l)]=l;var p=Object.getOwnPropertyNames(c).map(function(d){return c[d]});if(p.join("")!=="**********")return!1;var u={};return"abcdefghijklmnopqrst".split("").forEach(function(d){u[d]=d}),Object.keys(Object.assign({},u)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}t.exports=a()?Object.assign:function(s,c){for(var l,p=i(s),u,d=1;d<arguments.length;d++){l=Object(arguments[d]);for(var h in l)r.call(l,h)&&(p[h]=l[h]);if(n){u=n(l);for(var f=0;f<u.length;f++)o.call(l,u[f])&&(p[u[f]]=l[u[f]])}}return p}}}),Lr=ue({"../../node_modules/prop-types/lib/ReactPropTypesSecret.js"(e,t){var n="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";t.exports=n}}),jr=ue({"../../node_modules/prop-types/lib/has.js"(e,t){t.exports=Function.call.bind(Object.prototype.hasOwnProperty)}}),Mo=ue({"../../node_modules/prop-types/checkPropTypes.js"(e,t){var n=function(){};r=Lr(),o={},i=jr(),n=function(s){var c="Warning: "+s;typeof console<"u"&&console.error(c);try{throw new Error(c)}catch{}};var r,o,i;function a(s,c,l,p,u){for(var d in s)if(i(s,d)){var h;try{if(typeof s[d]!="function"){var f=Error((p||"React class")+": "+l+" type `"+d+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof s[d]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw f.name="Invariant Violation",f}h=s[d](c,d,p,l,null,r)}catch(S){h=S}if(h&&!(h instanceof Error)&&n((p||"React class")+": type specification of "+l+" `"+d+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof h+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),h instanceof Error&&!(h.message in o)){o[h.message]=!0;var g=u?u():"";n("Failed "+l+" type: "+h.message+(g??""))}}}a.resetWarningCache=function(){o={}},t.exports=a}}),Do=ue({"../../node_modules/prop-types/factoryWithTypeCheckers.js"(e,t){var n=kr(),r=jo(),o=Lr(),i=jr(),a=Mo(),s=function(){};s=function(l){var p="Warning: "+l;typeof console<"u"&&console.error(p);try{throw new Error(p)}catch{}};function c(){return null}t.exports=function(l,p){var u=typeof Symbol=="function"&&Symbol.iterator,d="@@iterator";function h(E){var N=E&&(u&&E[u]||E[d]);if(typeof N=="function")return N}var f="<<anonymous>>",g={array:y("array"),bigint:y("bigint"),bool:y("boolean"),func:y("function"),number:y("number"),object:y("object"),string:y("string"),symbol:y("symbol"),any:v(),arrayOf:w,element:U(),elementType:I(),instanceOf:z,node:et(),objectOf:Ie,oneOf:te,oneOfType:je,shape:Ue,exact:tt};function S(E,N){return E===N?E!==0||1/E===1/N:E!==E&&N!==N}function b(E,N){this.message=E,this.data=N&&typeof N=="object"?N:{},this.stack=""}b.prototype=Error.prototype;function x(E){var N={},W=0;function D(G,M,F,Y,K,q,T){if(Y=Y||f,q=q||F,T!==o){if(p){var ne=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw ne.name="Invariant Violation",ne}else if(typeof console<"u"){var Ce=Y+":"+F;!N[Ce]&&W<3&&(s("You are manually calling a React.PropTypes validation function for the `"+q+"` prop on `"+Y+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),N[Ce]=!0,W++)}}return M[F]==null?G?M[F]===null?new b("The "+K+" `"+q+"` is marked as required "+("in `"+Y+"`, but its value is `null`.")):new b("The "+K+" `"+q+"` is marked as required in "+("`"+Y+"`, but its value is `undefined`.")):null:E(M,F,Y,K,q)}var j=D.bind(null,!1);return j.isRequired=D.bind(null,!0),j}function y(E){function N(W,D,j,G,M,F){var Y=W[D],K=ye(Y);if(K!==E){var q=Ae(Y);return new b("Invalid "+G+" `"+M+"` of type "+("`"+q+"` supplied to `"+j+"`, expected ")+("`"+E+"`."),{expectedType:E})}return null}return x(N)}function v(){return x(c)}function w(E){function N(W,D,j,G,M){if(typeof E!="function")return new b("Property `"+M+"` of component `"+j+"` has invalid PropType notation inside arrayOf.");var F=W[D];if(!Array.isArray(F)){var Y=ye(F);return new b("Invalid "+G+" `"+M+"` of type "+("`"+Y+"` supplied to `"+j+"`, expected an array."))}for(var K=0;K<F.length;K++){var q=E(F,K,j,G,M+"["+K+"]",o);if(q instanceof Error)return q}return null}return x(N)}function U(){function E(N,W,D,j,G){var M=N[W];if(!l(M)){var F=ye(M);return new b("Invalid "+j+" `"+G+"` of type "+("`"+F+"` supplied to `"+D+"`, expected a single ReactElement."))}return null}return x(E)}function I(){function E(N,W,D,j,G){var M=N[W];if(!n.isValidElementType(M)){var F=ye(M);return new b("Invalid "+j+" `"+G+"` of type "+("`"+F+"` supplied to `"+D+"`, expected a single ReactElement type."))}return null}return x(E)}function z(E){function N(W,D,j,G,M){if(!(W[D]instanceof E)){var F=E.name||f,Y=vt(W[D]);return new b("Invalid "+G+" `"+M+"` of type "+("`"+Y+"` supplied to `"+j+"`, expected ")+("instance of `"+F+"`."))}return null}return x(N)}function te(E){if(!Array.isArray(E))return arguments.length>1?s("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):s("Invalid argument supplied to oneOf, expected an array."),c;function N(W,D,j,G,M){for(var F=W[D],Y=0;Y<E.length;Y++)if(S(F,E[Y]))return null;var K=JSON.stringify(E,function(q,T){var ne=Ae(T);return ne==="symbol"?String(T):T});return new b("Invalid "+G+" `"+M+"` of value `"+String(F)+"` "+("supplied to `"+j+"`, expected one of "+K+"."))}return x(N)}function Ie(E){function N(W,D,j,G,M){if(typeof E!="function")return new b("Property `"+M+"` of component `"+j+"` has invalid PropType notation inside objectOf.");var F=W[D],Y=ye(F);if(Y!=="object")return new b("Invalid "+G+" `"+M+"` of type "+("`"+Y+"` supplied to `"+j+"`, expected an object."));for(var K in F)if(i(F,K)){var q=E(F,K,j,G,M+"."+K,o);if(q instanceof Error)return q}return null}return x(N)}function je(E){if(!Array.isArray(E))return s("Invalid argument supplied to oneOfType, expected an instance of array."),c;for(var N=0;N<E.length;N++){var W=E[N];if(typeof W!="function")return s("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+Bt(W)+" at index "+N+"."),c}function D(j,G,M,F,Y){for(var K=[],q=0;q<E.length;q++){var T=E[q],ne=T(j,G,M,F,Y,o);if(ne==null)return null;ne.data&&i(ne.data,"expectedType")&&K.push(ne.data.expectedType)}var Ce=K.length>0?", expected one of type ["+K.join(", ")+"]":"";return new b("Invalid "+F+" `"+Y+"` supplied to "+("`"+M+"`"+Ce+"."))}return x(D)}function et(){function E(N,W,D,j,G){return xe(N[W])?null:new b("Invalid "+j+" `"+G+"` supplied to "+("`"+D+"`, expected a ReactNode."))}return x(E)}function Me(E,N,W,D,j){return new b((E||"React class")+": "+N+" type `"+W+"."+D+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+j+"`.")}function Ue(E){function N(W,D,j,G,M){var F=W[D],Y=ye(F);if(Y!=="object")return new b("Invalid "+G+" `"+M+"` of type `"+Y+"` "+("supplied to `"+j+"`, expected `object`."));for(var K in E){var q=E[K];if(typeof q!="function")return Me(j,G,M,K,Ae(q));var T=q(F,K,j,G,M+"."+K,o);if(T)return T}return null}return x(N)}function tt(E){function N(W,D,j,G,M){var F=W[D],Y=ye(F);if(Y!=="object")return new b("Invalid "+G+" `"+M+"` of type `"+Y+"` "+("supplied to `"+j+"`, expected `object`."));var K=r({},W[D],E);for(var q in K){var T=E[q];if(i(E,q)&&typeof T!="function")return Me(j,G,M,q,Ae(T));if(!T)return new b("Invalid "+G+" `"+M+"` key `"+q+"` supplied to `"+j+"`.\nBad object: "+JSON.stringify(W[D],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(E),null,"  "));var ne=T(F,q,j,G,M+"."+q,o);if(ne)return ne}return null}return x(N)}function xe(E){switch(typeof E){case"number":case"string":case"undefined":return!0;case"boolean":return!E;case"object":if(Array.isArray(E))return E.every(xe);if(E===null||l(E))return!0;var N=h(E);if(N){var W=N.call(E),D;if(N!==E.entries){for(;!(D=W.next()).done;)if(!xe(D.value))return!1}else for(;!(D=W.next()).done;){var j=D.value;if(j&&!xe(j[1]))return!1}}else return!1;return!0;default:return!1}}function _e(E,N){return E==="symbol"?!0:N?N["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&N instanceof Symbol:!1}function ye(E){var N=typeof E;return Array.isArray(E)?"array":E instanceof RegExp?"object":_e(N,E)?"symbol":N}function Ae(E){if(typeof E>"u"||E===null)return""+E;var N=ye(E);if(N==="object"){if(E instanceof Date)return"date";if(E instanceof RegExp)return"regexp"}return N}function Bt(E){var N=Ae(E);switch(N){case"array":case"object":return"an "+N;case"boolean":case"date":case"regexp":return"a "+N;default:return N}}function vt(E){return!E.constructor||!E.constructor.name?f:E.constructor.name}return g.checkPropTypes=a,g.resetWarningCache=a.resetWarningCache,g.PropTypes=g,g}}}),Fo=ue({"../../node_modules/prop-types/index.js"(e,t){n=kr(),r=!0,t.exports=Do()(n.isElement,r);var n,r}}),Bo='@keyframes Bc2PgW_ya{to{translate:0 var(--sh)}}@keyframes Bc2PgW_xa{to{translate:var(--xlp)0}}@keyframes Bc2PgW_r{50%{rotate:var(--hr)180deg}to{rotate:var(--r)360deg}}.Bc2PgW_c{z-index:1200;width:0;height:0;position:relative;overflow:visible}.Bc2PgW_p{animation:xa var(--dc)forwards cubic-bezier(var(--x1),var(--x2),var(--x3),var(--x4));animation-name:Bc2PgW_xa}.Bc2PgW_p>div{animation:ya var(--dc)forwards cubic-bezier(var(--y1),var(--y2),var(--y3),var(--y4));width:var(--w);height:var(--h);animation-name:Bc2PgW_ya;position:absolute;top:0;left:0}.Bc2PgW_p>div:before{content:"";background-color:var(--bgc);animation:r var(--rd)infinite linear;border-radius:var(--br);width:100%;height:100%;animation-name:Bc2PgW_r;display:block}',Dn="Bc2PgW_p",Wo="Bc2PgW_c",Fn=["#FFC700","#FF0000","#2E3191","#41BBC7"],Bn=3500,Wn=.5,Hn=150,Un="mix",zn=12,Gn="",Yn=!0,qn=800,$n=1600;Vn=200,Uo=800,zo=.1,Go=.3,Kn=.5,Ct=Math.abs,Ee=Math.random,ct=Math.round,Yo=Math.max,$t=e=>document.createElement(e),Vt=(e,t)=>e.appendChild(t),Jn=(e,t)=>Array.from({length:e},(n,r)=>({color:t[r%t.length],degree:360*r/e})),Ge=(e,t=2)=>ct((e+Number.EPSILON)*10**t)/10**t,Kt=(e,t,n,r,o)=>(e-t)*(o-r)/(n-t)+r,Xn=(e,t)=>e+t>360?e+t-360:e+t,Mr=()=>Ee()>.5,Zn=Object.entries,qo=6,$o=e=>e!==1&&Mr();Ko=J.div({zIndex:9999,position:"fixed",top:0,left:"50%",width:"50%",height:"100%"}),Jo=m.memo(function({timeToFade:e=5e3,colors:t=["#CA90FF","#FC521F","#66BF3C","#FF4785","#FFAE00","#1EA7FD"],...n}){return m.createElement(Ko,null,m.createElement(Vo,{colors:t,particleCount:200,duration:5e3,stageHeight:window.innerHeight,stageWidth:window.innerWidth,destroyAfterDone:!0,...n}))});Jt="STORYBOOK_ADDON_ONBOARDING_CHANNEL";Xo=Dr("function"),Zo=e=>e===null,er=e=>Object.prototype.toString.call(e).slice(8,-1)==="RegExp",tr=e=>!Qo(e)&&!Zo(e)&&(Xo(e)||typeof e=="object"),Qo=Dr("undefined");oi=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],ii=["bigint","boolean","null","number","string","symbol","undefined"];li=["innerHTML","ownerDocument","style","attributes","nodeValue"];C.array=Array.isArray;C.arrayOf=(e,t)=>!C.array(e)&&!C.function(t)?!1:e.every(n=>t(n));C.asyncGeneratorFunction=e=>jt(e)==="AsyncGeneratorFunction";C.asyncFunction=fe("AsyncFunction");C.bigint=Je("bigint");C.boolean=e=>e===!0||e===!1;C.date=fe("Date");C.defined=e=>!C.undefined(e);C.domElement=e=>C.object(e)&&!C.plainObject(e)&&e.nodeType===1&&C.string(e.nodeName)&&li.every(t=>t in e);C.empty=e=>C.string(e)&&e.length===0||C.array(e)&&e.length===0||C.object(e)&&!C.map(e)&&!C.set(e)&&Object.keys(e).length===0||C.set(e)&&e.size===0||C.map(e)&&e.size===0;C.error=fe("Error");C.function=Je("function");C.generator=e=>C.iterable(e)&&C.function(e.next)&&C.function(e.throw);C.generatorFunction=fe("GeneratorFunction");C.instanceOf=(e,t)=>!e||!t?!1:Object.getPrototypeOf(e)===t.prototype;C.iterable=e=>!C.nullOrUndefined(e)&&C.function(e[Symbol.iterator]);C.map=fe("Map");C.nan=e=>Number.isNaN(e);C.null=e=>e===null;C.nullOrUndefined=e=>C.null(e)||C.undefined(e);C.number=e=>Je("number")(e)&&!C.nan(e);C.numericString=e=>C.string(e)&&e.length>0&&!Number.isNaN(Number(e));C.object=e=>!C.nullOrUndefined(e)&&(C.function(e)||typeof e=="object");C.oneOf=(e,t)=>C.array(e)?e.indexOf(t)>-1:!1;C.plainFunction=fe("Function");C.plainObject=e=>{if(jt(e)!=="Object")return!1;let t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};C.primitive=e=>C.null(e)||si(typeof e);C.promise=fe("Promise");C.propertyOf=(e,t,n)=>{if(!C.object(e)||!t)return!1;let r=e[t];return C.function(n)?n(r):C.defined(r)};C.regexp=fe("RegExp");C.set=fe("Set");C.string=Je("string");C.symbol=Je("symbol");C.undefined=Je("undefined");C.weakMap=fe("WeakMap");C.weakSet=fe("WeakSet");_=C;di=We(Ao(),1),Br=We(No(),1),sr=We(ko(),1),Pt=We(cn(),1),Rt=We(cn(),1),O=We(Fo()),pt=typeof window<"u"&&typeof document<"u"&&typeof navigator<"u",fi=(function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(pt&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0})();yi=pt&&window.Promise,gi=yi?hi:mi;lr=pt&&!!(window.MSInputMethodContext&&document.documentMode),cr=pt&&/MSIE 10/.test(navigator.userAgent);Ei=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Oi=(function(){function e(t,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}})(),Ve=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},ce=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};ji=pt&&/Firefox/i.test(navigator.userAgent);eo=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Xt=eo.slice(3);Zt={FLIP:"flip",CLOCKWISE:"clockwise",COUNTERCLOCKWISE:"counterclockwise"};Vi={shift:{order:100,enabled:!0,fn:Yi},offset:{order:200,enabled:!0,fn:zi,offset:0},preventOverflow:{order:300,enabled:!0,fn:Gi,priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:Wi},arrow:{order:500,enabled:!0,fn:Di,element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:Bi,behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:$i},hide:{order:800,enabled:!0,fn:qi},computeStyle:{order:850,enabled:!0,fn:Mi,gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:Ni,onLoad:ki,gpuAcceleration:void 0}},Ki={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:Vi},Mt=(function(){function e(t,n){var r=this,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Ei(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(r.update)},this.update=gi(this.update.bind(this)),this.options=ce({},e.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(ce({},e.Defaults.modifiers,o.modifiers)).forEach(function(a){r.options.modifiers[a]=ce({},e.Defaults.modifiers[a]||{},o.modifiers?o.modifiers[a]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(a){return ce({name:a},r.options.modifiers[a])}).sort(function(a,s){return a.order-s.order}),this.modifiers.forEach(function(a){a.enabled&&Wr(a.onLoad)&&a.onLoad(r.reference,r.popper,r.options,a,r.state)}),this.update();var i=this.options.eventsEnabled;i&&this.enableEventListeners(),this.state.eventsEnabled=i}return Oi(e,[{key:"update",value:function(){return Ii.call(this)}},{key:"destroy",value:function(){return Ci.call(this)}},{key:"enableEventListeners",value:function(){return Ri.call(this)}},{key:"disableEventListeners",value:function(){return _i.call(this)}}]),e})();Mt.Utils=window.PopperUtils;Mt.placements=eo;Mt.Defaults=Ki;fr=Mt,on=We(cn()),Ji=["innerHTML","ownerDocument","style","attributes","nodeValue"],Xi=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],Zi=["bigint","boolean","null","number","string","symbol","undefined"];P.array=Array.isArray;P.arrayOf=function(e,t){return!P.array(e)&&!P.function(t)?!1:e.every(function(n){return t(n)})};P.asyncGeneratorFunction=function(e){return Dt(e)==="AsyncGeneratorFunction"};P.asyncFunction=he("AsyncFunction");P.bigint=Ze("bigint");P.boolean=function(e){return e===!0||e===!1};P.date=he("Date");P.defined=function(e){return!P.undefined(e)};P.domElement=function(e){return P.object(e)&&!P.plainObject(e)&&e.nodeType===1&&P.string(e.nodeName)&&Ji.every(function(t){return t in e})};P.empty=function(e){return P.string(e)&&e.length===0||P.array(e)&&e.length===0||P.object(e)&&!P.map(e)&&!P.set(e)&&Object.keys(e).length===0||P.set(e)&&e.size===0||P.map(e)&&e.size===0};P.error=he("Error");P.function=Ze("function");P.generator=function(e){return P.iterable(e)&&P.function(e.next)&&P.function(e.throw)};P.generatorFunction=he("GeneratorFunction");P.instanceOf=function(e,t){return!e||!t?!1:Object.getPrototypeOf(e)===t.prototype};P.iterable=function(e){return!P.nullOrUndefined(e)&&P.function(e[Symbol.iterator])};P.map=he("Map");P.nan=function(e){return Number.isNaN(e)};P.null=function(e){return e===null};P.nullOrUndefined=function(e){return P.null(e)||P.undefined(e)};P.number=function(e){return Ze("number")(e)&&!P.nan(e)};P.numericString=function(e){return P.string(e)&&e.length>0&&!Number.isNaN(Number(e))};P.object=function(e){return!P.nullOrUndefined(e)&&(P.function(e)||typeof e=="object")};P.oneOf=function(e,t){return P.array(e)?e.indexOf(t)>-1:!1};P.plainFunction=he("Function");P.plainObject=function(e){if(Dt(e)!=="Object")return!1;var t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};P.primitive=function(e){return P.null(e)||ea(typeof e)};P.promise=he("Promise");P.propertyOf=function(e,t,n){if(!P.object(e)||!t)return!1;var r=e[t];return P.function(n)?n(r):P.defined(r)};P.regexp=he("RegExp");P.set=he("Set");P.string=Ze("string");P.symbol=Ze("symbol");P.undefined=Ze("undefined");P.weakMap=he("WeakMap");P.weakSet=he("WeakSet");le=P;ta=to("function"),na=function(e){return e===null},hr=function(e){return Object.prototype.toString.call(e).slice(8,-1)==="RegExp"},mr=function(e){return!ra(e)&&!na(e)&&(ta(e)||typeof e=="object")},ra=to("undefined"),an=function(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};la=["innerHTML","ownerDocument","style","attributes","nodeValue"],ca=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],ua=["bigint","boolean","null","number","string","symbol","undefined"];R.array=Array.isArray;R.arrayOf=function(e,t){return!R.array(e)&&!R.function(t)?!1:e.every(function(n){return t(n)})};R.asyncGeneratorFunction=function(e){return Ft(e)==="AsyncGeneratorFunction"};R.asyncFunction=me("AsyncFunction");R.bigint=Qe("bigint");R.boolean=function(e){return e===!0||e===!1};R.date=me("Date");R.defined=function(e){return!R.undefined(e)};R.domElement=function(e){return R.object(e)&&!R.plainObject(e)&&e.nodeType===1&&R.string(e.nodeName)&&la.every(function(t){return t in e})};R.empty=function(e){return R.string(e)&&e.length===0||R.array(e)&&e.length===0||R.object(e)&&!R.map(e)&&!R.set(e)&&Object.keys(e).length===0||R.set(e)&&e.size===0||R.map(e)&&e.size===0};R.error=me("Error");R.function=Qe("function");R.generator=function(e){return R.iterable(e)&&R.function(e.next)&&R.function(e.throw)};R.generatorFunction=me("GeneratorFunction");R.instanceOf=function(e,t){return!e||!t?!1:Object.getPrototypeOf(e)===t.prototype};R.iterable=function(e){return!R.nullOrUndefined(e)&&R.function(e[Symbol.iterator])};R.map=me("Map");R.nan=function(e){return Number.isNaN(e)};R.null=function(e){return e===null};R.nullOrUndefined=function(e){return R.null(e)||R.undefined(e)};R.number=function(e){return Qe("number")(e)&&!R.nan(e)};R.numericString=function(e){return R.string(e)&&e.length>0&&!Number.isNaN(Number(e))};R.object=function(e){return!R.nullOrUndefined(e)&&(R.function(e)||typeof e=="object")};R.oneOf=function(e,t){return R.array(e)?e.indexOf(t)>-1:!1};R.plainFunction=me("Function");R.plainObject=function(e){if(Ft(e)!=="Object")return!1;var t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};R.primitive=function(e){return R.null(e)||da(typeof e)};R.promise=me("Promise");R.propertyOf=function(e,t,n){if(!R.object(e)||!t)return!1;var r=e[t];return R.function(n)?n(r):R.defined(r)};R.regexp=me("RegExp");R.set=me("Set");R.string=Qe("string");R.symbol=Qe("symbol");R.undefined=Qe("undefined");R.weakMap=me("WeakMap");R.weakSet=me("WeakSet");k=R;Oa={flip:{padding:20},preventOverflow:{padding:10}},Sa="The typeValidator argument must be a function with the signature function(props, propName, componentName).",wa="The error message is optional, but must be a string if provided.";H={INIT:"init",IDLE:"idle",OPENING:"opening",OPEN:"open",CLOSING:"closing",ERROR:"error"},at=Fe.createPortal!==void 0;io=(function(e){yt(n,e);var t=gt(n);function n(){return ht(this,n),t.apply(this,arguments)}return mt(n,[{key:"componentDidMount",value:function(){Oe()&&(this.node||this.appendNode(),at||this.renderPortal())}},{key:"componentDidUpdate",value:function(){Oe()&&(at||this.renderPortal())}},{key:"componentWillUnmount",value:function(){!Oe()||!this.node||(at||Fe.unmountComponentAtNode(this.node),this.node&&this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=void 0))}},{key:"appendNode",value:function(){var r=this.props,o=r.id,i=r.zIndex;this.node||(this.node=document.createElement("div"),o&&(this.node.id=o),i&&(this.node.style.zIndex=i),document.body.appendChild(this.node))}},{key:"renderPortal",value:function(){if(!Oe())return null;var r=this.props,o=r.children,i=r.setRef;if(this.node||this.appendNode(),at)return Fe.createPortal(o,this.node);var a=Fe.unstable_renderSubtreeIntoContainer(this,o.length>1?m.createElement("div",null,o):o[0],this.node);return i(a),null}},{key:"renderReact16",value:function(){var r=this.props,o=r.hasChildren,i=r.placement,a=r.target;return o?this.renderPortal():a||i==="center"?this.renderPortal():null}},{key:"render",value:function(){return at?this.renderReact16():null}}]),n})(m.Component);re(io,"propTypes",{children:O.default.oneOfType([O.default.element,O.default.array]),hasChildren:O.default.bool,id:O.default.oneOfType([O.default.string,O.default.number]),placement:O.default.string,setRef:O.default.func.isRequired,target:O.default.oneOfType([O.default.object,O.default.string]),zIndex:O.default.number});ao=(function(e){yt(n,e);var t=gt(n);function n(){return ht(this,n),t.apply(this,arguments)}return mt(n,[{key:"parentStyle",get:function(){var r=this.props,o=r.placement,i=r.styles,a=i.arrow.length,s={pointerEvents:"none",position:"absolute",width:"100%"};return o.startsWith("top")?(s.bottom=0,s.left=0,s.right=0,s.height=a):o.startsWith("bottom")?(s.left=0,s.right=0,s.top=0,s.height=a):o.startsWith("left")?(s.right=0,s.top=0,s.bottom=0):o.startsWith("right")&&(s.left=0,s.top=0),s}},{key:"render",value:function(){var r=this.props,o=r.placement,i=r.setArrowRef,a=r.styles,s=a.arrow,c=s.color,l=s.display,p=s.length,u=s.margin,d=s.position,h=s.spread,f={display:l,position:d},g,S=h,b=p;return o.startsWith("top")?(g="0,0 ".concat(S/2,",").concat(b," ").concat(S,",0"),f.bottom=0,f.marginLeft=u,f.marginRight=u):o.startsWith("bottom")?(g="".concat(S,",").concat(b," ").concat(S/2,",0 0,").concat(b),f.top=0,f.marginLeft=u,f.marginRight=u):o.startsWith("left")?(b=h,S=p,g="0,0 ".concat(S,",").concat(b/2," 0,").concat(b),f.right=0,f.marginTop=u,f.marginBottom=u):o.startsWith("right")&&(b=h,S=p,g="".concat(S,",").concat(b," ").concat(S,",0 0,").concat(b/2),f.left=0,f.marginTop=u,f.marginBottom=u),m.createElement("div",{className:"__floater__arrow",style:this.parentStyle},m.createElement("span",{ref:i,style:f},m.createElement("svg",{width:S,height:b,version:"1.1",xmlns:"http://www.w3.org/2000/svg"},m.createElement("polygon",{points:g,fill:c}))))}}]),n})(m.Component);re(ao,"propTypes",{placement:O.default.string.isRequired,setArrowRef:O.default.func.isRequired,styles:O.default.object.isRequired});Aa=["color","height","width"];so.propTypes={handleClick:O.default.func.isRequired,styles:O.default.object.isRequired};lo.propTypes={content:O.default.node.isRequired,footer:O.default.node,handleClick:O.default.func.isRequired,open:O.default.bool,positionWrapper:O.default.bool.isRequired,showCloseButton:O.default.bool.isRequired,styles:O.default.object.isRequired,title:O.default.node};co=(function(e){yt(n,e);var t=gt(n);function n(){return ht(this,n),t.apply(this,arguments)}return mt(n,[{key:"style",get:function(){var r=this.props,o=r.disableAnimation,i=r.component,a=r.placement,s=r.hideArrow,c=r.status,l=r.styles,p=l.arrow.length,u=l.floater,d=l.floaterCentered,h=l.floaterClosing,f=l.floaterOpening,g=l.floaterWithAnimation,S=l.floaterWithComponent,b={};return s||(a.startsWith("top")?b.padding="0 0 ".concat(p,"px"):a.startsWith("bottom")?b.padding="".concat(p,"px 0 0"):a.startsWith("left")?b.padding="0 ".concat(p,"px 0 0"):a.startsWith("right")&&(b.padding="0 0 0 ".concat(p,"px"))),[H.OPENING,H.OPEN].indexOf(c)!==-1&&(b=X(X({},b),f)),c===H.CLOSING&&(b=X(X({},b),h)),c===H.OPEN&&!o&&(b=X(X({},b),g)),a==="center"&&(b=X(X({},b),d)),i&&(b=X(X({},b),S)),X(X({},u),b)}},{key:"render",value:function(){var r=this.props,o=r.component,i=r.handleClick,a=r.hideArrow,s=r.setFloaterRef,c=r.status,l={},p=["__floater"];return o?m.isValidElement(o)?l.content=m.cloneElement(o,{closeFn:i}):l.content=o({closeFn:i}):l.content=m.createElement(lo,this.props),c===H.OPEN&&p.push("__floater__open"),a||(l.arrow=m.createElement(ao,this.props)),m.createElement("div",{ref:s,className:p.join(" "),style:this.style},m.createElement("div",{className:"__floater__body"},l.content,l.arrow))}}]),n})(m.Component);re(co,"propTypes",{component:O.default.oneOfType([O.default.func,O.default.element]),content:O.default.node,disableAnimation:O.default.bool.isRequired,footer:O.default.node,handleClick:O.default.func.isRequired,hideArrow:O.default.bool.isRequired,open:O.default.bool,placement:O.default.string.isRequired,positionWrapper:O.default.bool.isRequired,setArrowRef:O.default.func.isRequired,setFloaterRef:O.default.func.isRequired,showCloseButton:O.default.bool,status:O.default.string.isRequired,styles:O.default.object.isRequired,title:O.default.node});uo=(function(e){yt(n,e);var t=gt(n);function n(){return ht(this,n),t.apply(this,arguments)}return mt(n,[{key:"render",value:function(){var r=this.props,o=r.children,i=r.handleClick,a=r.handleMouseEnter,s=r.handleMouseLeave,c=r.setChildRef,l=r.setWrapperRef,p=r.style,u=r.styles,d;if(o)if(m.Children.count(o)===1)if(!m.isValidElement(o))d=m.createElement("span",null,o);else{var h=le.function(o.type)?"innerRef":"ref";d=m.cloneElement(m.Children.only(o),re({},h,c))}else d=o;return d?m.createElement("span",{ref:l,style:X(X({},u),p),onClick:i,onMouseEnter:a,onMouseLeave:s},d):null}}]),n})(m.Component);re(uo,"propTypes",{children:O.default.node,handleClick:O.default.func.isRequired,handleMouseEnter:O.default.func.isRequired,handleMouseLeave:O.default.func.isRequired,setChildRef:O.default.func.isRequired,setWrapperRef:O.default.func.isRequired,style:O.default.object,styles:O.default.object.isRequired});Na={zIndex:100};La=["arrow","flip","offset"],ja=["position","top","right","bottom","left"],gn=(function(e){yt(n,e);var t=gt(n);function n(r){var o;return ht(this,n),o=t.call(this,r),re(Pe(o),"setArrowRef",function(i){o.arrowRef=i}),re(Pe(o),"setChildRef",function(i){o.childRef=i}),re(Pe(o),"setFloaterRef",function(i){o.floaterRef=i}),re(Pe(o),"setWrapperRef",function(i){o.wrapperRef=i}),re(Pe(o),"handleTransitionEnd",function(){var i=o.state.status,a=o.props.callback;o.wrapperPopper&&o.wrapperPopper.instance.update(),o.setState({status:i===H.OPENING?H.OPEN:H.IDLE},function(){var s=o.state.status;a(s===H.OPEN?"open":"close",o.props)})}),re(Pe(o),"handleClick",function(){var i=o.props,a=i.event,s=i.open;if(!le.boolean(s)){var c=o.state,l=c.positionWrapper,p=c.status;(o.event==="click"||o.event==="hover"&&l)&&(xt({title:"click",data:[{event:a,status:p===H.OPEN?"closing":"opening"}],debug:o.debug}),o.toggle())}}),re(Pe(o),"handleMouseEnter",function(){var i=o.props,a=i.event,s=i.open;if(!(le.boolean(s)||Qt())){var c=o.state.status;o.event==="hover"&&c===H.IDLE&&(xt({title:"mouseEnter",data:[{key:"originalEvent",value:a}],debug:o.debug}),clearTimeout(o.eventDelayTimeout),o.toggle())}}),re(Pe(o),"handleMouseLeave",function(){var i=o.props,a=i.event,s=i.eventDelay,c=i.open;if(!(le.boolean(c)||Qt())){var l=o.state,p=l.status,u=l.positionWrapper;o.event==="hover"&&(xt({title:"mouseLeave",data:[{key:"originalEvent",value:a}],debug:o.debug}),s?[H.OPENING,H.OPEN].indexOf(p)!==-1&&!u&&!o.eventDelayTimeout&&(o.eventDelayTimeout=setTimeout(function(){delete o.eventDelayTimeout,o.toggle()},s*1e3)):o.toggle(H.IDLE))}}),o.state={currentPlacement:r.placement,needsUpdate:!1,positionWrapper:r.wrapperOptions.position&&!!r.target,status:H.INIT,statusWrapper:H.INIT},o._isMounted=!1,o.hasMounted=!1,Oe()&&window.addEventListener("load",function(){o.popper&&o.popper.instance.update(),o.wrapperPopper&&o.wrapperPopper.instance.update()}),o}return mt(n,[{key:"componentDidMount",value:function(){if(Oe()){var r=this.state.positionWrapper,o=this.props,i=o.children,a=o.open,s=o.target;this._isMounted=!0,xt({title:"init",data:{hasChildren:!!i,hasTarget:!!s,isControlled:le.boolean(a),positionWrapper:r,target:this.target,floater:this.floaterRef},debug:this.debug}),this.hasMounted||(this.initPopper(),this.hasMounted=!0),!i&&s&&le.boolean(a)}}},{key:"componentDidUpdate",value:function(r,o){if(Oe()){var i=this.props,a=i.autoOpen,s=i.open,c=i.target,l=i.wrapperOptions,p=ya(o,this.state),u=p.changedFrom,d=p.changed;if(r.open!==s){var h;le.boolean(s)&&(h=s?H.OPENING:H.CLOSING),this.toggle(h)}(r.wrapperOptions.position!==l.position||r.target!==c)&&this.changeWrapperPosition(this.props),d("status",H.IDLE)&&s?this.toggle(H.OPEN):u("status",H.INIT,H.IDLE)&&a&&this.toggle(H.OPEN),this.popper&&d("status",H.OPENING)&&this.popper.instance.update(),this.floaterRef&&(d("status",H.OPENING)||d("status",H.CLOSING))&&_a(this.floaterRef,"transitionend",this.handleTransitionEnd),d("needsUpdate",!0)&&this.rebuildPopper()}}},{key:"componentWillUnmount",value:function(){Oe()&&(this._isMounted=!1,this.popper&&this.popper.instance.destroy(),this.wrapperPopper&&this.wrapperPopper.instance.destroy())}},{key:"initPopper",value:function(){var r=this,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.target,i=this.state.positionWrapper,a=this.props,s=a.disableFlip,c=a.getPopper,l=a.hideArrow,p=a.offset,u=a.placement,d=a.wrapperOptions,h=u==="top"||u==="bottom"?"flip":["right","bottom-end","top-end","left","top-start","bottom-start"];if(u==="center")this.setState({status:H.IDLE});else if(o&&this.floaterRef){var f=this.options,g=f.arrow,S=f.flip,b=f.offset,x=ro(f,La);new fr(o,this.floaterRef,{placement:u,modifiers:X({arrow:X({enabled:!l,element:this.arrowRef},g),flip:X({enabled:!s,behavior:h},S),offset:X({offset:"0, ".concat(p,"px")},b)},x),onCreate:function(v){var w;if(r.popper=v,!((w=r.floaterRef)!==null&&w!==void 0&&w.isConnected)){r.setState({needsUpdate:!0});return}c(v,"floater"),r._isMounted&&r.setState({currentPlacement:v.placement,status:H.IDLE}),u!==v.placement&&setTimeout(function(){v.instance.update()},1)},onUpdate:function(v){r.popper=v;var w=r.state.currentPlacement;r._isMounted&&v.placement!==w&&r.setState({currentPlacement:v.placement})}})}if(i){var y=le.undefined(d.offset)?0:d.offset;new fr(this.target,this.wrapperRef,{placement:d.placement||u,modifiers:{arrow:{enabled:!1},offset:{offset:"0, ".concat(y,"px")},flip:{enabled:!1}},onCreate:function(v){r.wrapperPopper=v,r._isMounted&&r.setState({statusWrapper:H.IDLE}),c(v,"wrapper"),u!==v.placement&&setTimeout(function(){v.instance.update()},1)}})}}},{key:"rebuildPopper",value:function(){var r=this;this.floaterRefInterval=setInterval(function(){var o;(o=r.floaterRef)!==null&&o!==void 0&&o.isConnected&&(clearInterval(r.floaterRefInterval),r.setState({needsUpdate:!1}),r.initPopper())},50)}},{key:"changeWrapperPosition",value:function(r){var o=r.target,i=r.wrapperOptions;this.setState({positionWrapper:i.position&&!!o})}},{key:"toggle",value:function(r){var o=this.state.status,i=o===H.OPEN?H.CLOSING:H.OPENING;le.undefined(r)||(i=r),this.setState({status:i})}},{key:"debug",get:function(){var r=this.props.debug;return r||Oe()&&"ReactFloaterDebug"in window&&!!window.ReactFloaterDebug}},{key:"event",get:function(){var r=this.props,o=r.disableHoverToClick,i=r.event;return i==="hover"&&Qt()&&!o?"click":i}},{key:"options",get:function(){var r=this.props.options;return(0,on.default)(Oa,r||{})}},{key:"styles",get:function(){var r=this,o=this.state,i=o.status,a=o.positionWrapper,s=o.statusWrapper,c=this.props.styles,l=(0,on.default)(ka(c),c);if(a){var p;[H.IDLE].indexOf(i)===-1||[H.IDLE].indexOf(s)===-1?p=l.wrapperPosition:p=this.wrapperPopper.styles,l.wrapper=X(X({},l.wrapper),p)}if(this.target){var u=window.getComputedStyle(this.target);this.wrapperStyles?l.wrapper=X(X({},l.wrapper),this.wrapperStyles):["relative","static"].indexOf(u.position)===-1&&(this.wrapperStyles={},a||(ja.forEach(function(d){r.wrapperStyles[d]=u[d]}),l.wrapper=X(X({},l.wrapper),this.wrapperStyles),this.target.style.position="relative",this.target.style.top="auto",this.target.style.right="auto",this.target.style.bottom="auto",this.target.style.left="auto"))}return l}},{key:"target",get:function(){if(!Oe())return null;var r=this.props.target;return r?le.domElement(r)?r:document.querySelector(r):this.childRef||this.wrapperRef}},{key:"render",value:function(){var r=this.state,o=r.currentPlacement,i=r.positionWrapper,a=r.status,s=this.props,c=s.children,l=s.component,p=s.content,u=s.disableAnimation,d=s.footer,h=s.hideArrow,f=s.id,g=s.open,S=s.showCloseButton,b=s.style,x=s.target,y=s.title,v=m.createElement(uo,{handleClick:this.handleClick,handleMouseEnter:this.handleMouseEnter,handleMouseLeave:this.handleMouseLeave,setChildRef:this.setChildRef,setWrapperRef:this.setWrapperRef,style:b,styles:this.styles.wrapper},c),w={};return i?w.wrapperInPortal=v:w.wrapperAsChildren=v,m.createElement("span",null,m.createElement(io,{hasChildren:!!c,id:f,placement:o,setRef:this.setFloaterRef,target:x,zIndex:this.styles.options.zIndex},m.createElement(co,{component:l,content:p,disableAnimation:u,footer:d,handleClick:this.handleClick,hideArrow:h||o==="center",open:g,placement:o,positionWrapper:i,setArrowRef:this.setArrowRef,setFloaterRef:this.setFloaterRef,showCloseButton:S,status:a,styles:this.styles,title:y}),w.wrapperInPortal),w.wrapperAsChildren)}}]),n})(m.Component);re(gn,"propTypes",{autoOpen:O.default.bool,callback:O.default.func,children:O.default.node,component:wr(O.default.oneOfType([O.default.func,O.default.element]),function(e){return!e.content}),content:wr(O.default.node,function(e){return!e.component}),debug:O.default.bool,disableAnimation:O.default.bool,disableFlip:O.default.bool,disableHoverToClick:O.default.bool,event:O.default.oneOf(["hover","click"]),eventDelay:O.default.number,footer:O.default.node,getPopper:O.default.func,hideArrow:O.default.bool,id:O.default.oneOfType([O.default.string,O.default.number]),offset:O.default.number,open:O.default.bool,options:O.default.object,placement:O.default.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto","center"]),showCloseButton:O.default.bool,style:O.default.object,styles:O.default.object,target:O.default.oneOfType([O.default.object,O.default.string]),title:O.default.node,wrapperOptions:O.default.shape({offset:O.default.number,placement:O.default.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto"]),position:O.default.bool})});re(gn,"defaultProps",{autoOpen:!1,callback:Tr,debug:!1,disableAnimation:!1,disableFlip:!1,disableHoverToClick:!1,event:"click",eventDelay:.4,getPopper:Tr,hideArrow:!1,offset:15,placement:"bottom",showCloseButton:!1,styles:{},target:null,wrapperOptions:{position:!1}});Ma=Object.defineProperty,Da=(e,t,n)=>t in e?Ma(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,A=(e,t,n)=>Da(e,typeof t!="symbol"?t+"":t,n),$={INIT:"init",START:"start",STOP:"stop",RESET:"reset",PREV:"prev",NEXT:"next",GO:"go",CLOSE:"close",SKIP:"skip",UPDATE:"update"},de={TOUR_START:"tour:start",STEP_BEFORE:"step:before",BEACON:"beacon",TOOLTIP:"tooltip",STEP_AFTER:"step:after",TOUR_END:"tour:end",TOUR_STATUS:"tour:status",TARGET_NOT_FOUND:"error:target_not_found",ERROR:"error"},L={INIT:"init",READY:"ready",BEACON:"beacon",TOOLTIP:"tooltip",COMPLETE:"complete",ERROR:"error"},B={IDLE:"idle",READY:"ready",WAITING:"waiting",RUNNING:"running",PAUSED:"paused",SKIPPED:"skipped",FINISHED:"finished",ERROR:"error"};st=Ot!==void 0;Ja={options:{preventOverflow:{boundariesElement:"scrollParent"}},wrapperOptions:{offset:-18,position:!0}},mo={back:"Back",close:"Close",last:"Last",next:"Next",nextLabelWithProgress:"Next (Step {step} of {steps})",open:"Open the dialog",skip:"Skip"},Xa={event:"click",placement:"bottom",offset:10,disableBeacon:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrollParentFix:!1,disableScrolling:!1,hideBackButton:!1,hideCloseButton:!1,hideFooter:!1,isFixed:!1,locale:mo,showProgress:!1,showSkipButton:!1,spotlightClicks:!1,spotlightPadding:10},Za={continuous:!1,debug:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrolling:!1,disableScrollParentFix:!1,getHelpers:void 0,hideBackButton:!1,run:!0,scrollOffset:20,scrollDuration:300,scrollToFirstStep:!1,showSkipButton:!1,showProgress:!1,spotlightClicks:!1,spotlightPadding:10,steps:[]},Qa={arrowColor:"#fff",backgroundColor:"#fff",beaconSize:36,overlayColor:"rgba(0, 0, 0, 0.5)",primaryColor:"#f04",spotlightShadow:"0 0 15px rgba(0, 0, 0, 0.5)",textColor:"#333",width:380,zIndex:100},lt={backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",cursor:"pointer",fontSize:16,lineHeight:1,padding:8,WebkitAppearance:"none"},Pr={borderRadius:4,position:"absolute"};go={action:"init",controlled:!1,index:0,lifecycle:L.INIT,origin:null,size:0,status:B.IDLE},xr=$a(ho(go,"controlled","size")),ns=class{constructor(e){A(this,"beaconPopper"),A(this,"tooltipPopper"),A(this,"data",new Map),A(this,"listener"),A(this,"store",new Map),A(this,"addListener",o=>{this.listener=o}),A(this,"setSteps",o=>{let{size:i,status:a}=this.getState(),s={size:o.length,status:a};this.data.set("steps",o),a===B.WAITING&&!i&&o.length&&(s.status=B.RUNNING),this.setState(s)}),A(this,"getPopper",o=>o==="beacon"?this.beaconPopper:this.tooltipPopper),A(this,"setPopper",(o,i)=>{o==="beacon"?this.beaconPopper=i:this.tooltipPopper=i}),A(this,"cleanupPoppers",()=>{this.beaconPopper=null,this.tooltipPopper=null}),A(this,"close",(o=null)=>{let{index:i,status:a}=this.getState();a===B.RUNNING&&this.setState({...this.getNextState({action:$.CLOSE,index:i+1,origin:o})})}),A(this,"go",o=>{let{controlled:i,status:a}=this.getState();if(i||a!==B.RUNNING)return;let s=this.getSteps()[o];this.setState({...this.getNextState({action:$.GO,index:o}),status:s?a:B.FINISHED})}),A(this,"info",()=>this.getState()),A(this,"next",()=>{let{index:o,status:i}=this.getState();i===B.RUNNING&&this.setState(this.getNextState({action:$.NEXT,index:o+1}))}),A(this,"open",()=>{let{status:o}=this.getState();o===B.RUNNING&&this.setState({...this.getNextState({action:$.UPDATE,lifecycle:L.TOOLTIP})})}),A(this,"prev",()=>{let{index:o,status:i}=this.getState();i===B.RUNNING&&this.setState({...this.getNextState({action:$.PREV,index:o-1})})}),A(this,"reset",(o=!1)=>{let{controlled:i}=this.getState();i||this.setState({...this.getNextState({action:$.RESET,index:0}),status:o?B.RUNNING:B.READY})}),A(this,"skip",()=>{let{status:o}=this.getState();o===B.RUNNING&&this.setState({action:$.SKIP,lifecycle:L.INIT,status:B.SKIPPED})}),A(this,"start",o=>{let{index:i,size:a}=this.getState();this.setState({...this.getNextState({action:$.START,index:_.number(o)?o:i},!0),status:a?B.RUNNING:B.WAITING})}),A(this,"stop",(o=!1)=>{let{index:i,status:a}=this.getState();[B.FINISHED,B.SKIPPED].includes(a)||this.setState({...this.getNextState({action:$.STOP,index:i+(o?1:0)}),status:B.PAUSED})}),A(this,"update",o=>{var i,a;if(!Ya(o,xr))throw new Error(`State is not valid. Valid keys: ${xr.join(", ")}`);this.setState({...this.getNextState({...this.getState(),...o,action:(i=o.action)!=null?i:$.UPDATE,origin:(a=o.origin)!=null?a:null},!0)})});let{continuous:t=!1,stepIndex:n,steps:r=[]}=e??{};this.setState({action:$.INIT,controlled:_.number(n),continuous:t,index:_.number(n)?n:0,lifecycle:L.INIT,origin:null,status:r.length?B.READY:B.IDLE},!0),this.beaconPopper=null,this.tooltipPopper=null,this.listener=null,this.setSteps(r)}getState(){return this.store.size?{action:this.store.get("action")||"",controlled:this.store.get("controlled")||!1,index:parseInt(this.store.get("index"),10),lifecycle:this.store.get("lifecycle")||"",origin:this.store.get("origin")||null,size:this.store.get("size")||0,status:this.store.get("status")||""}:{...go}}getNextState(e,t=!1){var n,r,o,i,a;let{action:s,controlled:c,index:l,size:p,status:u}=this.getState(),d=_.number(e.index)?e.index:l,h=c&&!t?l:Math.min(Math.max(d,0),p);return{action:(n=e.action)!=null?n:s,controlled:c,index:h,lifecycle:(r=e.lifecycle)!=null?r:L.INIT,origin:(o=e.origin)!=null?o:null,size:(i=e.size)!=null?i:p,status:h===p?B.FINISHED:(a=e.status)!=null?a:u}}getSteps(){let e=this.data.get("steps");return Array.isArray(e)?e:[]}hasUpdatedState(e){let t=JSON.stringify(e),n=JSON.stringify(this.getState());return t!==n}setState(e,t=!1){let n=this.getState(),{action:r,index:o,lifecycle:i,origin:a=null,size:s,status:c}={...n,...e};this.store.set("action",r),this.store.set("index",o),this.store.set("lifecycle",i),this.store.set("origin",a),this.store.set("size",s),this.store.set("status",c),t&&(this.store.set("controlled",e.controlled),this.store.set("continuous",e.continuous)),this.listener&&this.hasUpdatedState(n)&&this.listener(this.getState())}getHelpers(){return{close:this.close,go:this.go,info:this.info,next:this.next,open:this.open,prev:this.prev,reset:this.reset,skip:this.skip}}};is=os,as=class extends De{constructor(){super(...arguments),A(this,"isActive",!1),A(this,"resizeTimeout"),A(this,"scrollTimeout"),A(this,"scrollParent"),A(this,"state",{isScrolling:!1,mouseOverSpotlight:!1,showSpotlight:!0}),A(this,"hideSpotlight",()=>{let{continuous:e,disableOverlay:t,lifecycle:n}=this.props,r=[L.INIT,L.BEACON,L.COMPLETE,L.ERROR];return t||(e?r.includes(n):n!==L.TOOLTIP)}),A(this,"handleMouseMove",e=>{let{mouseOverSpotlight:t}=this.state,{height:n,left:r,position:o,top:i,width:a}=this.spotlightStyles,s=o==="fixed"?e.clientY:e.pageY,c=o==="fixed"?e.clientX:e.pageX,l=s>=i&&s<=i+n,p=c>=r&&c<=r+a&&l;p!==t&&this.updateState({mouseOverSpotlight:p})}),A(this,"handleScroll",()=>{let{target:e}=this.props,t=Re(e);if(this.scrollParent!==document){let{isScrolling:n}=this.state;n||this.updateState({isScrolling:!0,showSpotlight:!1}),clearTimeout(this.scrollTimeout),this.scrollTimeout=window.setTimeout(()=>{this.updateState({isScrolling:!1,showSpotlight:!0})},50)}else Ke(t,"sticky")&&this.updateState({})}),A(this,"handleResize",()=>{clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout(()=>{this.isActive&&this.forceUpdate()},100)})}componentDidMount(){let{debug:e,disableScrolling:t,disableScrollParentFix:n=!1,target:r}=this.props,o=Re(r);this.scrollParent=ut(o??document.body,n,!0),this.isActive=!0,!t&&bt(o,!0)&&Le({title:"step has a custom scroll parent and can cause trouble with scrolling",data:[{key:"parent",value:this.scrollParent}],debug:e}),window.addEventListener("resize",this.handleResize)}componentDidUpdate(e){var t;let{disableScrollParentFix:n,lifecycle:r,spotlightClicks:o,target:i}=this.props,{changed:a}=At(e,this.props);if(a("target")||a("disableScrollParentFix")){let s=Re(i);this.scrollParent=ut(s??document.body,n,!0)}a("lifecycle",L.TOOLTIP)&&((t=this.scrollParent)==null||t.addEventListener("scroll",this.handleScroll,{passive:!0}),setTimeout(()=>{let{isScrolling:s}=this.state;s||this.updateState({showSpotlight:!0})},100)),(a("spotlightClicks")||a("disableOverlay")||a("lifecycle"))&&(o&&r===L.TOOLTIP?window.addEventListener("mousemove",this.handleMouseMove,!1):r!==L.TOOLTIP&&window.removeEventListener("mousemove",this.handleMouseMove))}componentWillUnmount(){var e;this.isActive=!1,window.removeEventListener("mousemove",this.handleMouseMove),window.removeEventListener("resize",this.handleResize),clearTimeout(this.resizeTimeout),clearTimeout(this.scrollTimeout),(e=this.scrollParent)==null||e.removeEventListener("scroll",this.handleScroll)}get overlayStyles(){let{mouseOverSpotlight:e}=this.state,{disableOverlayClose:t,placement:n,styles:r}=this.props,o=r.overlay;return Cr()&&(o=n==="center"?r.overlayLegacyCenter:r.overlayLegacy),{cursor:t?"default":"pointer",height:Fa(),pointerEvents:e?"none":"auto",...o}}get spotlightStyles(){var e,t,n;let{showSpotlight:r}=this.state,{disableScrollParentFix:o=!1,spotlightClicks:i,spotlightPadding:a=0,styles:s,target:c}=this.props,l=Re(c),p=po(l),u=Ke(l),d=Ua(l,a,o);return{...Cr()?s.spotlightLegacy:s.spotlight,height:Math.round(((e=p?.height)!=null?e:0)+a*2),left:Math.round(((t=p?.left)!=null?t:0)-a),opacity:r?1:0,pointerEvents:i?"none":"auto",position:u?"fixed":"absolute",top:d,transition:"opacity 0.2s",width:Math.round(((n=p?.width)!=null?n:0)+a*2)}}updateState(e){this.isActive&&this.setState(t=>({...t,...e}))}render(){let{showSpotlight:e}=this.state,{onClickOverlay:t,placement:n}=this.props,{hideSpotlight:r,overlayStyles:o,spotlightStyles:i}=this;if(r())return null;let a=n!=="center"&&e&&V(is,{styles:i});if(fo()==="safari"){let{mixBlendMode:s,zIndex:c,...l}=o;a=V("div",{style:{...l}},a),delete o.backgroundColor}return V("div",{className:"react-joyride__overlay","data-test-id":"overlay",onClick:t,role:"presentation",style:o},a)}},ss=class extends De{constructor(){super(...arguments),A(this,"node",null)}componentDidMount(){let{id:e}=this.props;Ne()&&(this.node=document.createElement("div"),this.node.id=e,document.body.appendChild(this.node),st||this.renderReact15())}componentDidUpdate(){Ne()&&(st||this.renderReact15())}componentWillUnmount(){!Ne()||!this.node||(st||Sn(this.node),this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=null))}renderReact15(){if(!Ne())return;let{children:e}=this.props;this.node&&wn(this,e,this.node)}renderReact16(){if(!Ne()||!st)return null;let{children:e}=this.props;return this.node?Ot(e,this.node):null}render(){return st?this.renderReact16():null}},ls=class{constructor(e,t){if(A(this,"element"),A(this,"options"),A(this,"canBeTabbed",n=>{let{tabIndex:r}=n;return r===null||r<0?!1:this.canHaveFocus(n)}),A(this,"canHaveFocus",n=>{let r=/input|select|textarea|button|object/,o=n.nodeName.toLowerCase();return(r.test(o)&&!n.getAttribute("disabled")||o==="a"&&!!n.getAttribute("href"))&&this.isVisible(n)}),A(this,"findValidTabElements",()=>[].slice.call(this.element.querySelectorAll("*"),0).filter(this.canBeTabbed)),A(this,"handleKeyDown",n=>{let{code:r="Tab"}=this.options;n.code===r&&this.interceptTab(n)}),A(this,"interceptTab",n=>{n.preventDefault();let r=this.findValidTabElements(),{shiftKey:o}=n;if(!r.length)return;let i=document.activeElement?r.indexOf(document.activeElement):0;i===-1||!o&&i+1===r.length?i=0:o&&i===0?i=r.length-1:i+=o?-1:1,r[i].focus()}),A(this,"isHidden",n=>{let r=n.offsetWidth<=0&&n.offsetHeight<=0,o=window.getComputedStyle(n);return r&&!n.innerHTML?!0:r&&o.getPropertyValue("overflow")!=="visible"||o.getPropertyValue("display")==="none"}),A(this,"isVisible",n=>{let r=n;for(;r;)if(r instanceof HTMLElement){if(r===document.body)break;if(this.isHidden(r))return!1;r=r.parentNode}return!0}),A(this,"removeScope",()=>{window.removeEventListener("keydown",this.handleKeyDown)}),A(this,"checkFocus",n=>{document.activeElement!==n&&(n.focus(),window.requestAnimationFrame(()=>this.checkFocus(n)))}),A(this,"setFocus",()=>{let{selector:n}=this.options;if(!n)return;let r=this.element.querySelector(n);r&&window.requestAnimationFrame(()=>this.checkFocus(r))}),!(e instanceof HTMLElement))throw new TypeError("Invalid parameter: element must be an HTMLElement");this.element=e,this.options=t,window.addEventListener("keydown",this.handleKeyDown,!1),this.setFocus()}},cs=class extends De{constructor(e){if(super(e),A(this,"beacon",null),A(this,"setBeaconRef",r=>{this.beacon=r}),e.beaconComponent)return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.id="joyride-beacon-animation",e.nonce&&n.setAttribute("nonce",e.nonce),n.appendChild(document.createTextNode(`
        @keyframes joyride-beacon-inner {
          20% {
            opacity: 0.9;
          }
        
          90% {
            opacity: 0.7;
          }
        }
        
        @keyframes joyride-beacon-outer {
          0% {
            transform: scale(1);
          }
        
          45% {
            opacity: 0.7;
            transform: scale(0.75);
          }
        
          100% {
            opacity: 0.9;
            transform: scale(1);
          }
        }
      `)),t.appendChild(n)}componentDidMount(){let{shouldFocus:e}=this.props;_.domElement(this.beacon)||console.warn("beacon is not a valid DOM element"),setTimeout(()=>{_.domElement(this.beacon)&&e&&this.beacon.focus()},0)}componentWillUnmount(){let e=document.getElementById("joyride-beacon-animation");e?.parentNode&&e.parentNode.removeChild(e)}render(){let{beaconComponent:e,continuous:t,index:n,isLastStep:r,locale:o,onClickOrHover:i,size:a,step:s,styles:c}=this.props,l=Se(o.open),p={"aria-label":l,onClick:i,onMouseEnter:i,ref:this.setBeaconRef,title:l},u;return e?u=V(e,{continuous:t,index:n,isLastStep:r,size:a,step:s,...p}):u=V("button",{key:"JoyrideBeacon",className:"react-joyride__beacon","data-test-id":"button-beacon",style:c.beacon,type:"button",...p},V("span",{style:c.beaconInner}),V("span",{style:c.beaconOuter})),u}};ps=us;fs=ds,hs=class extends De{constructor(){super(...arguments),A(this,"handleClickBack",e=>{e.preventDefault();let{helpers:t}=this.props;t.prev()}),A(this,"handleClickClose",e=>{e.preventDefault();let{helpers:t}=this.props;t.close("button_close")}),A(this,"handleClickPrimary",e=>{e.preventDefault();let{continuous:t,helpers:n}=this.props;if(!t){n.close("button_primary");return}n.next()}),A(this,"handleClickSkip",e=>{e.preventDefault();let{helpers:t}=this.props;t.skip()}),A(this,"getElementsProps",()=>{let{continuous:e,index:t,isLastStep:n,setTooltipRef:r,size:o,step:i}=this.props,{back:a,close:s,last:c,next:l,nextLabelWithProgress:p,skip:u}=i.locale,d=Se(a),h=Se(s),f=Se(c),g=Se(l),S=Se(u),b=s,x=h;if(e){if(b=l,x=g,i.showProgress&&!n){let y=Se(p,{step:t+1,steps:o});b=ln(p,t+1,o),x=y}n&&(b=c,x=f)}return{backProps:{"aria-label":d,children:a,"data-action":"back",onClick:this.handleClickBack,role:"button",title:d},closeProps:{"aria-label":h,children:s,"data-action":"close",onClick:this.handleClickClose,role:"button",title:h},primaryProps:{"aria-label":x,children:b,"data-action":"primary",onClick:this.handleClickPrimary,role:"button",title:x},skipProps:{"aria-label":S,children:u,"data-action":"skip",onClick:this.handleClickSkip,role:"button",title:S},tooltipProps:{"aria-modal":!0,ref:r,role:"alertdialog"}}})}render(){let{continuous:e,index:t,isLastStep:n,setTooltipRef:r,size:o,step:i}=this.props,{beaconComponent:a,tooltipComponent:s,...c}=i,l;if(s){let p={...this.getElementsProps(),continuous:e,index:t,isLastStep:n,size:o,step:c,setTooltipRef:r};l=V(s,{...p})}else l=V(fs,{...this.getElementsProps(),continuous:e,index:t,isLastStep:n,size:o,step:i});return l}},ms=class extends De{constructor(){super(...arguments),A(this,"scope",null),A(this,"tooltip",null),A(this,"handleClickHoverBeacon",e=>{let{step:t,store:n}=this.props;e.type==="mouseenter"&&t.event!=="hover"||n.update({lifecycle:L.TOOLTIP})}),A(this,"setTooltipRef",e=>{this.tooltip=e}),A(this,"setPopper",(e,t)=>{var n;let{action:r,lifecycle:o,step:i,store:a}=this.props;t==="wrapper"?a.setPopper("beacon",e):a.setPopper("tooltip",e),a.getPopper("beacon")&&(a.getPopper("tooltip")||i.placement==="center")&&o===L.INIT&&a.update({action:r,lifecycle:L.READY}),(n=i.floaterProps)!=null&&n.getPopper&&i.floaterProps.getPopper(e,t)}),A(this,"renderTooltip",e=>{let{continuous:t,helpers:n,index:r,size:o,step:i}=this.props;return V(hs,{continuous:t,helpers:n,index:r,isLastStep:r+1===o,setTooltipRef:this.setTooltipRef,size:o,step:i,...e})})}componentDidMount(){let{debug:e,index:t}=this.props;Le({title:`step:${t}`,data:[{key:"props",value:this.props}],debug:e})}componentDidUpdate(e){var t;let{action:n,callback:r,continuous:o,controlled:i,debug:a,helpers:s,index:c,lifecycle:l,shouldScroll:p,status:u,step:d,store:h}=this.props,{changed:f,changedFrom:g}=At(e,this.props),S=s.info(),b=o&&n!==$.CLOSE&&(c>0||n===$.PREV),x=f("action")||f("index")||f("lifecycle")||f("status"),y=g("lifecycle",[L.TOOLTIP,L.INIT],L.INIT),v=f("action",[$.NEXT,$.PREV,$.SKIP,$.CLOSE]),w=i&&c===e.index;if(v&&(y||w)&&r({...S,index:e.index,lifecycle:L.COMPLETE,step:e.step,type:de.STEP_AFTER}),d.placement==="center"&&u===B.RUNNING&&f("index")&&n!==$.START&&l===L.INIT&&h.update({lifecycle:L.READY}),x){let U=Re(d.target),I=!!U;I&&Ha(U)?(g("status",B.READY,B.RUNNING)||g("lifecycle",L.INIT,L.READY))&&r({...S,step:d,type:de.STEP_BEFORE}):(console.warn(I?"Target not visible":"Target not mounted",d),r({...S,type:de.TARGET_NOT_FOUND,step:d}),i||h.update({index:c+(n===$.PREV?-1:1)}))}g("lifecycle",L.INIT,L.READY)&&h.update({lifecycle:Ir(d)||b?L.TOOLTIP:L.BEACON}),f("index")&&Le({title:`step:${l}`,data:[{key:"props",value:this.props}],debug:a}),f("lifecycle",L.BEACON)&&r({...S,step:d,type:de.BEACON}),f("lifecycle",L.TOOLTIP)&&(r({...S,step:d,type:de.TOOLTIP}),p&&this.tooltip&&(this.scope=new ls(this.tooltip,{selector:"[data-action=primary]"}),this.scope.setFocus())),g("lifecycle",[L.TOOLTIP,L.INIT],L.INIT)&&((t=this.scope)==null||t.removeScope(),h.cleanupPoppers())}componentWillUnmount(){var e;(e=this.scope)==null||e.removeScope()}get open(){let{lifecycle:e,step:t}=this.props;return Ir(t)||e===L.TOOLTIP}render(){let{continuous:e,debug:t,index:n,nonce:r,shouldScroll:o,size:i,step:a}=this.props,s=Re(a.target);return!yo(a)||!_.domElement(s)?null:V("div",{key:`JoyrideStep-${n}`,className:"react-joyride__step"},V(gn,{...a.floaterProps,component:this.renderTooltip,debug:t,getPopper:this.setPopper,id:`react-joyride-step-${n}`,open:this.open,placement:a.placement,target:a.target},V(cs,{beaconComponent:a.beaconComponent,continuous:e,index:n,isLastStep:n+1===i,locale:a.locale,nonce:r,onClickOrHover:this.handleClickHoverBeacon,shouldFocus:o,size:i,step:a,styles:a.styles})))}},bo=class extends De{constructor(e){super(e),A(this,"helpers"),A(this,"store"),A(this,"callback",a=>{let{callback:s}=this.props;_.function(s)&&s(a)}),A(this,"handleKeyboard",a=>{let{index:s,lifecycle:c}=this.state,{steps:l}=this.props,p=l[s];c===L.TOOLTIP&&a.code==="Escape"&&p&&!p.disableCloseOnEsc&&this.store.close("keyboard")}),A(this,"handleClickOverlay",()=>{let{index:a}=this.state,{steps:s}=this.props;Ye(this.props,s[a]).disableOverlayClose||this.helpers.close("overlay")}),A(this,"syncState",a=>{this.setState(a)});let{debug:t,getHelpers:n,run:r=!0,stepIndex:o}=e;this.store=rs({...e,controlled:r&&_.number(o)}),this.helpers=this.store.getHelpers();let{addListener:i}=this.store;Le({title:"init",data:[{key:"props",value:this.props},{key:"state",value:this.state}],debug:t}),i(this.syncState),n&&n(this.helpers),this.state=this.store.getState()}componentDidMount(){if(!Ne())return;let{debug:e,disableCloseOnEsc:t,run:n,steps:r}=this.props,{start:o}=this.store;Rr(r,e)&&n&&o(),t||document.body.addEventListener("keydown",this.handleKeyboard,{passive:!0})}componentDidUpdate(e,t){if(!Ne())return;let{action:n,controlled:r,index:o,status:i}=this.state,{debug:a,run:s,stepIndex:c,steps:l}=this.props,{stepIndex:p,steps:u}=e,{reset:d,setSteps:h,start:f,stop:g,update:S}=this.store,{changed:b}=At(e,this.props),{changed:x,changedFrom:y}=At(t,this.state),v=Ye(this.props,l[o]),w=!oe(u,l),U=_.number(c)&&b("stepIndex"),I=Re(v.target);if(w&&(Rr(l,a)?h(l):console.warn("Steps are not valid",l)),b("run")&&(s?f(c):g()),U){let te=_.number(p)&&p<c?$.NEXT:$.PREV;n===$.STOP&&(te=$.START),[B.FINISHED,B.SKIPPED].includes(i)||S({action:n===$.CLOSE?$.CLOSE:te,index:c,lifecycle:L.INIT})}!r&&i===B.RUNNING&&o===0&&!I&&(this.store.update({index:o+1}),this.callback({...this.state,type:de.TARGET_NOT_FOUND,step:v}));let z={...this.state,index:o,step:v};if(x("action",[$.NEXT,$.PREV,$.SKIP,$.CLOSE])&&x("status",B.PAUSED)){let te=Ye(this.props,l[t.index]);this.callback({...z,index:t.index,lifecycle:L.COMPLETE,step:te,type:de.STEP_AFTER})}if(x("status",[B.FINISHED,B.SKIPPED])){let te=Ye(this.props,l[t.index]);r||this.callback({...z,index:t.index,lifecycle:L.COMPLETE,step:te,type:de.STEP_AFTER}),this.callback({...z,type:de.TOUR_END,step:te,index:t.index}),d()}else y("status",[B.IDLE,B.READY],B.RUNNING)?this.callback({...z,type:de.TOUR_START}):(x("status")||x("action",$.RESET))&&this.callback({...z,type:de.TOUR_STATUS});this.scrollToStep(t)}componentWillUnmount(){let{disableCloseOnEsc:e}=this.props;e||document.body.removeEventListener("keydown",this.handleKeyboard)}scrollToStep(e){let{index:t,lifecycle:n,status:r}=this.state,{debug:o,disableScrollParentFix:i=!1,scrollDuration:a,scrollOffset:s=20,scrollToFirstStep:c=!1,steps:l}=this.props,p=Ye(this.props,l[t]),u=Re(p.target),d=Ka({isFirstStep:t===0,lifecycle:n,previousLifecycle:e.lifecycle,scrollToFirstStep:c,step:p,target:u});if(r===B.RUNNING&&d){let h=bt(u,i),f=ut(u,i),g=Math.floor(za(u,s,i))||0;Le({title:"scrollToStep",data:[{key:"index",value:t},{key:"lifecycle",value:n},{key:"status",value:r}],debug:o});let S=this.store.getPopper("beacon"),b=this.store.getPopper("tooltip");if(n===L.BEACON&&S){let{offsets:x,placement:y}=S;!["bottom"].includes(y)&&!h&&(g=Math.floor(x.popper.top-s))}else if(n===L.TOOLTIP&&b){let{flipped:x,offsets:y,placement:v}=b;["top","right","left"].includes(v)&&!x&&!h?g=Math.floor(y.popper.top-s):g-=p.spotlightPadding}g=g>=0?g:0,r===B.RUNNING&&Ga(g,{element:f,duration:a}).then(()=>{setTimeout(()=>{var x;(x=this.store.getPopper("tooltip"))==null||x.instance.update()},10)})}}render(){if(!Ne())return null;let{index:e,lifecycle:t,status:n}=this.state,{continuous:r=!1,debug:o=!1,nonce:i,scrollToFirstStep:a=!1,steps:s}=this.props,c=n===B.RUNNING,l={};if(c&&s[e]){let p=Ye(this.props,s[e]);l.step=V(ms,{...this.state,callback:this.callback,continuous:r,debug:o,helpers:this.helpers,nonce:i,shouldScroll:!p.disableScrolling&&(e!==0||a),step:p,store:this.store}),l.overlay=V(ss,{id:"react-joyride-portal"},V(as,{...p,continuous:r,debug:o,lifecycle:t,onClickOverlay:this.handleClickOverlay}))}return V("div",{className:"react-joyride"},l.step,l.overlay)}};A(bo,"defaultProps",Za);ys=bo,gs=J.button`
  all: unset;
  box-sizing: border-box;
  border: 0;
  border-radius: 0.25rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.75rem;
  background: ${({theme:e,variant:t})=>t==="primary"?e.color.secondary:t==="secondary"?e.color.lighter:t==="outline"?"transparent":t==="white"?e.color.lightest:e.color.secondary};
  color: ${({theme:e,variant:t})=>t==="primary"?e.color.lightest:t==="secondary"||t==="outline"?e.darkest:t==="white"?e.color.secondary:e.color.lightest};
  box-shadow: ${({variant:e})=>e==="secondary"||e==="outline"?"#D9E8F2 0 0 0 1px inset":"none"};
  height: 32px;
  font-size: 0.8125rem;
  font-weight: 700;
  font-family: ${({theme:e})=>e.typography.fonts.base};
  transition: background-color, box-shadow, color, opacity;
  transition-duration: 0.16s;
  transition-timing-function: ease-in-out;
  text-decoration: none;

  &:hover {
    background-color: ${({theme:e,variant:t})=>t==="primary"?"#0b94eb":t==="secondary"?"#eef4f9":t==="outline"?"transparent":t==="white"?e.color.lightest:"#0b94eb"};
    color: ${({theme:e,variant:t})=>t==="primary"?e.color.lightest:t==="secondary"||t==="outline"?e.darkest:t==="white"?e.color.darkest:e.color.lightest};
  }

  &:focus {
    box-shadow: ${({variant:e})=>e==="primary"?"inset 0 0 0 1px rgba(0, 0, 0, 0.2)":e==="secondary"||e==="outline"?"inset 0 0 0 1px #0b94eb":e==="white"?"none":"inset 0 0 0 2px rgba(0, 0, 0, 0.1)"};
  }
`,bs=En(function({children:e,onClick:t,variant:n="primary",...r},o){return m.createElement(gs,{ref:o,onClick:t,variant:n,...r},e)}),vs=J.div`
  padding: 15px;
  border-radius: 5px;
`,Es=J.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`,Os=J.div`
  display: flex;
  align-items: center;
  align-self: stretch;
  justify-content: space-between;
  margin: -5px -5px 5px 0;
`,Ss=J.div`
  line-height: 18px;
  font-weight: 700;
  font-size: 14px;
  margin: 5px 5px 5px 0;
`,ws=J.p`
  font-size: 14px;
  line-height: 18px;
  text-align: start;
  text-wrap: balance;
  margin: 0;
  margin-top: 5px;
`,Ts=J.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
`,Is=J.span`
  font-size: 13px;
`,Cs=({index:e,size:t,step:n,closeProps:r,primaryProps:o,tooltipProps:i})=>(se(()=>{let a=document.createElement("style");return a.id="#sb-onboarding-arrow-style",a.innerHTML=`
      .__floater__arrow { container-type: size; }
      .__floater__arrow span { background: ${It.secondary}; }
      .__floater__arrow span::before, .__floater__arrow span::after {
        content: '';
        display: block;
        width: 2px;
        height: 2px;
        background: ${It.secondary};
        box-shadow: 0 0 0 2px ${It.secondary};
        border-radius: 3px;
        flex: 0 0 2px;
      }
      @container (min-height: 1px) {
        .__floater__arrow span { flex-direction: column; }
      }
    `,document.head.appendChild(a),()=>{let s=document.querySelector("#sb-onboarding-arrow-style");s&&s.remove()}},[]),m.createElement(vs,{...i,style:n.styles?.tooltip},m.createElement(Es,null,m.createElement(Os,null,n.title&&m.createElement(Ss,null,n.title),m.createElement(Rn,{...r,onClick:r.onClick,variant:"solid"},m.createElement(jn,null))),m.createElement(ws,null,n.content)),m.createElement(Ts,{id:"buttonNext"},m.createElement(Is,null,e+1," of ",t),!n.hideNextButton&&m.createElement(bs,{...o,onClick:n.onNextButtonClick||o.onClick,variant:"white"},e+1===t?"Done":"Next"))));xs=J(ze.Content)(({theme:e})=>({fontSize:e.typography.size.s2,color:e.color.defaultText,gap:8})),_s=J.div({display:"grid",gridTemplateColumns:"1fr 1fr",gap:14,marginBottom:8}),As=J.div(({theme:e})=>({marginTop:8,marginBottom:2,fontWeight:e.typography.weight.bold})),Ns=J.label({display:"flex",gap:8,'&:has(input[type="checkbox"]:not(:disabled), input[type="radio"]:not(:disabled))':{cursor:"pointer"}}),ks=J(ze.Actions)({marginTop:8}),Ls=J(Tt.Checkbox)({margin:2}),js=({onComplete:e,onDismiss:t})=>{let[n,r]=ae(!1),[o,i]=ae({building:{label:"What are you building?",type:"checkbox",required:!0,options:en({"design-system":{label:"Design system"},"application-ui":{label:"Application UI"}}),values:{"design-system":!1,"application-ui":!1}},interest:{label:"Which of these are you interested in?",type:"checkbox",required:!0,options:en({"ui-documentation":{label:"Generating UI docs"},"functional-testing":{label:"Functional testing"},"accessibility-testing":{label:"Accessibility testing"},"visual-testing":{label:"Visual testing"},"ai-augmented-development":{label:"Building UI with AI"},"team-collaboration":{label:"Team collaboration"},"design-handoff":{label:"Design handoff"}}),values:{"ui-documentation":!1,"functional-testing":!1,"accessibility-testing":!1,"visual-testing":!1,"ai-augmented-development":!1,"team-collaboration":!1,"design-handoff":!1}},referrer:{label:"How did you discover Storybook?",type:"select",required:!0,options:en({"we-use-it-at-work":{label:"We use it at work"},"via-friend-or-colleague":{label:"Via friend or colleague"},"via-social-media":{label:"Via social media"},youtube:{label:"YouTube"},"web-search":{label:"Web Search"},"ai-agent":{label:"AI Agent (e.g. ChatGPT)"}}),values:{"we-use-it-at-work":!1,"via-friend-or-colleague":!1,"via-social-media":!1,youtube:!1,"web-search":!1,"ai-agent":!1}}}),a=(c,l,p)=>{let u=o[c];i(d=>{if(u.type==="checkbox"){let h={...u.values,[l]:!!p};return{...d,[c]:{...u,values:h}}}if(u.type==="select"){let h=Object.fromEntries(Object.entries(u.values).map(([f])=>[f,f===l]));return{...d,[c]:{...u,values:h}}}return d})},s=Object.values(o).every(c=>c.required?Object.values(c.values).some(l=>l===!0):!0);return m.createElement(ze,{defaultOpen:!0,width:420,onEscapeKeyDown:t},m.createElement(Tt,{onSubmit:c=>{s&&(c.preventDefault(),r(!0),e(Object.fromEntries(Object.entries(o).map(([l,p])=>[l,p.values]))))},id:"intent-survey-form"},m.createElement(xs,null,m.createElement(ze.Header,null,m.createElement(ze.Title,null,"Help improve Storybook")),Object.keys(o).map(c=>{let l=o[c];return m.createElement(m.Fragment,{key:c},m.createElement(As,null,l.label),l.type==="checkbox"&&m.createElement(_s,null,Object.entries(l.options).map(([p,u])=>{let d=`${c}:${p}`;return m.createElement("div",{key:d},m.createElement(Ns,{htmlFor:d},m.createElement(Ls,{name:d,id:d,checked:l.values[p],disabled:n,onChange:h=>a(c,p,h.target.checked)}),u.label))})),l.type==="select"&&m.createElement(Tt.Select,{name:c,id:c,value:Object.entries(l.values).find(([,p])=>p)?.[0]||"",required:l.required,disabled:n,onChange:p=>a(c,p.target.value)},m.createElement("option",{disabled:!0,hidden:!0,value:""},"Select an option..."),Object.entries(l.options).map(([p,u])=>m.createElement("option",{key:p,value:p},u.label))))}),m.createElement(ks,null,m.createElement(Pn,{disabled:n||!s,size:"medium",type:"submit",variant:"solid"},"Submit")))))};Ds=rt({from:{opacity:0},to:{opacity:1}}),vo=rt({from:{transform:"translate(0, 20px)",opacity:0},to:{transform:"translate(0, 0)",opacity:1}}),Fs=rt({from:{opacity:0,transform:"scale(0.8)"},to:{opacity:1,transform:"scale(1)"}}),Bs=rt({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}),Ws=J.div(({visible:e})=>({position:"fixed",top:0,left:0,right:0,bottom:0,display:"flex",opacity:e?1:0,alignItems:"center",justifyContent:"center",zIndex:1e3,transition:"opacity 1s 0.5s"})),Hs=J.div({position:"absolute",top:0,left:0,right:0,bottom:0,animation:`${Ds} 2s`,background:`
    radial-gradient(90% 90%, #ff4785 0%, #db5698 30%, #1ea7fdcc 100%),
    radial-gradient(circle, #ff4785 0%, transparent 80%),
    radial-gradient(circle at 30% 40%, #fc521f99 0%, #fc521f66 20%, transparent 40%),
    radial-gradient(circle at 75% 75%, #fc521f99 0%, #fc521f77 18%, transparent 30%)`,"&::before":{opacity:.5,background:`
      radial-gradient(circle at 30% 40%, #fc521f99 0%, #fc521f66 10%, transparent 20%),
      radial-gradient(circle at 75% 75%, #fc521f99 0%, #fc521f77 8%, transparent 20%)`,content:'""',position:"absolute",top:"-50vw",left:"-50vh",transform:"translate(-50%, -50%)",width:"calc(100vw + 100vh)",height:"calc(100vw + 100vh)",animation:`${Bs} 12s linear infinite`}}),Us=J.div(({visible:e})=>({position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:"white",textAlign:"center",width:"90vw",minWidth:290,maxWidth:410,opacity:e?1:0,transition:"opacity 0.5s",h1:{fontSize:45,fontWeight:"bold",animation:`${vo} 1.5s 1s backwards`}})),zs=J.div({display:"flex",marginTop:40,div:{display:"flex",flexBasis:"33.33%",flexDirection:"column",alignItems:"center",animation:`${vo} 1s backwards`,"&:nth-child(1)":{animationDelay:"2s"},"&:nth-child(2)":{animationDelay:"2.5s"},"&:nth-child(3)":{animationDelay:"3s"}},svg:{marginBottom:10}}),Gs=J.button({display:"inline-flex",position:"relative",alignItems:"center",justifyContent:"center",marginTop:40,width:48,height:48,padding:0,borderRadius:"50%",border:0,outline:"none",background:"rgba(255, 255, 255, 0.3)",cursor:"pointer",transition:"background 0.2s",animation:`${Fs} 1.5s 4s backwards`,"&:hover, &:focus":{background:"rgba(255, 255, 255, 0.4)"}}),Ys=J(Ln)({width:30,color:"white"}),_r=J.svg(({progress:e})=>({position:"absolute",top:-1,left:-1,width:"50px!important",height:"50px!important",transform:"rotate(-90deg)",color:"white",circle:{r:"24",cx:"25",cy:"25",fill:"transparent",stroke:e?"currentColor":"transparent",strokeWidth:"1",strokeLinecap:"round",strokeDasharray:Math.PI*48}})),qs=({onDismiss:e,duration:t=6e3})=>{let[n,r]=ae(-4e5/t),[o,i]=ae(!0),a=n>=100,s=nt(()=>{i(!1);let c=setTimeout(e,1500);return()=>clearTimeout(c)},[e]);return se(()=>{if(!t)return;let c=1e3/50,l=100/(t/c),p=setInterval(()=>r(u=>u+l),c);return()=>clearInterval(p)},[t]),se(()=>{a&&s()},[a,s]),m.createElement(Ws,{visible:o},m.createElement(Hs,null),m.createElement(Us,{visible:o},m.createElement("h1",null,"Meet your new frontend workshop"),m.createElement(zs,null,m.createElement("div",null,m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"33",height:"32"},m.createElement("path",{d:"M4.06 0H32.5v28.44h-3.56V32H.5V3.56h3.56V0Zm21.33 7.11H4.06v21.33h21.33V7.11Z",fill:"currentColor"})),"Development"),m.createElement("div",null,m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32"},m.createElement("path",{d:"M15.95 32c-1.85 0-3.1-1.55-3.1-3.54 0-1.1.45-2.78 1.35-5.03.9-2.3 1.35-4.51 1.35-6.81a22.21 22.21 0 0 0-5.1 3.67c-2.5 2.47-4.95 4.9-7.55 4.9-1.6 0-2.9-1.1-2.9-2.43 0-1.46 1.35-2.91 4.3-3.62 1.45-.36 3.1-.75 4.95-1.06 1.8-.31 3.8-1.02 5.9-2.08a23.77 23.77 0 0 0-6.1-2.12C5.3 13.18 2.3 12.6 1 11.28.35 10.6 0 9.9 0 9.14 0 7.82 1.2 6.8 2.95 6.8c2.65 0 5.75 3.1 7.95 5.3 1.1 1.1 2.65 2.21 4.65 3.27v-.57c0-1.77-.15-3.23-.55-4.3-.8-2.11-2.05-5.43-2.05-6.97 0-2.04 1.3-3.54 3.1-3.54 1.75 0 3.1 1.41 3.1 3.54 0 1.06-.45 2.78-1.35 5.12-.9 2.35-1.35 4.6-1.35 6.72 2.85-1.59 2.5-1.41 4.95-3.5 2.35-2.29 4-3.7 4.9-4.23.95-.58 1.9-.84 2.9-.84 1.6 0 2.8.97 2.8 2.34 0 1.5-1.25 2.78-4.15 3.62-1.4.4-3.05.75-4.9 1.1-1.9.36-3.9 1.07-6.1 2.13a23.3 23.3 0 0 0 5.95 2.08c3.65.7 6.75 1.32 8.15 2.6.7.67 1.05 1.33 1.05 2.08 0 1.33-1.2 2.43-2.95 2.43-2.95 0-6.75-4.15-8.2-5.61-.7-.7-2.2-1.72-4.4-2.96v.57c0 1.9.45 4.03 1.3 6.32.85 2.3 1.3 3.94 1.3 4.95 0 2.08-1.35 3.54-3.1 3.54Z",fill:"currentColor"})),"Testing"),m.createElement("div",null,m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"33",height:"32"},m.createElement("path",{d:"M.5 16a16 16 0 1 1 32 0 16 16 0 0 1-32 0Zm16 12.44A12.44 12.44 0 0 1 4.3 13.53a8 8 0 1 0 9.73-9.73 12.44 12.44 0 1 1 2.47 24.64ZM12.06 16a4.44 4.44 0 1 1 0-8.89 4.44 4.44 0 0 1 0 8.89Z",fill:"currentColor",fillRule:"evenodd"})),"Documentation")),m.createElement(Gs,{onClick:s},m.createElement(Ys,null),m.createElement(_r,{xmlns:"http://www.w3.org/2000/svg"},m.createElement("circle",null)),m.createElement(_r,{xmlns:"http://www.w3.org/2000/svg",progress:!0},m.createElement("circle",{strokeDashoffset:Math.PI*48*(1-Math.max(0,Math.min(n,100))/100)})))))},$s=J.span(({theme:e})=>({display:"inline-flex",borderRadius:3,padding:"0 5px",marginBottom:-2,opacity:.8,fontFamily:e.typography.fonts.mono,fontSize:11,border:e.base==="dark"?e.color.darkest:e.color.lightest,color:e.base==="dark"?e.color.lightest:e.color.darkest,backgroundColor:e.base==="dark"?"black":e.color.light,boxSizing:"border-box",lineHeight:"17px"})),Vs=J.div(({theme:e})=>({background:e.background.content,borderRadius:3,marginTop:15,padding:10,fontSize:e.typography.size.s1,".linenumber":{opacity:.5}})),Ks=qt()});Z();Q();ee();Z();Q();ee();Et();St();zt();Gt();Z();Q();ee();var tu=__STORYBOOK_API__,{ActiveTabs:nu,Consumer:ru,ManagerContext:ou,Provider:iu,RequestResponseError:au,addons:Cn,combineParameters:su,controlOrMetaKey:lu,controlOrMetaSymbol:cu,eventMatchesShortcut:uu,eventToShortcut:pu,experimental_MockUniversalStore:du,experimental_UniversalStore:fu,experimental_getStatusStore:hu,experimental_getTestProviderStore:mu,experimental_requestResponse:yu,experimental_useStatusStore:gu,experimental_useTestProviderStore:bu,experimental_useUniversalStore:vu,internal_fullStatusStore:Eu,internal_fullTestProviderStore:Ou,internal_universalStatusStore:Su,internal_universalTestProviderStore:wu,isMacLike:Tu,isShortcutTaken:Iu,keyToSymbol:Cu,merge:Pu,mockChannel:Ru,optionOrAltSymbol:xu,shortcutMatchesShortcut:_u,shortcutToHumanString:Au,types:Nu,useAddonState:ku,useArgTypes:Lu,useArgs:ju,useChannel:Mu,useGlobalTypes:Du,useGlobals:Fu,useParameter:Bu,useSharedState:Wu,useStoryPrepared:Hu,useStorybookApi:Uu,useStorybookState:zu}=__STORYBOOK_API__;var Xs=On(()=>Promise.resolve().then(()=>(Oo(),Eo)));Cn.register("@storybook/addon-onboarding",async e=>{let t=e.getUrlState(),n=t.path==="/onboarding"||t.queryParams.onboarding==="true";e.once(In,()=>{if(!(e.getData("example-button--primary")||document.getElementById("example-button--primary"))){console.warn("[@storybook/addon-onboarding] It seems like you have finished the onboarding experience in Storybook! Therefore this addon is not necessary anymore and will not be loaded. You are free to remove it from your project. More info: https://github.com/storybookjs/storybook/tree/next/code/addons/onboarding#uninstalling");return}if(!n||window.innerWidth<730)return;e.togglePanel(!0),e.togglePanelPosition("bottom"),e.setSelectedPanel(wt);let r=document.createElement("div");r.id="storybook-addon-onboarding",document.body.appendChild(r),Fe.render(m.createElement(vn,{fallback:m.createElement("div",null)},m.createElement(Xs,{api:e})),r)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
