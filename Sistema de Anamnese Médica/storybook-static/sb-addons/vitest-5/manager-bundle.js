try{
(()=>{var n=__REACT__,{Children:Lt,Component:Ut,Fragment:jt,Profiler:Mt,PureComponent:Nt,StrictMode:Ht,Suspense:Ft,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Gt,act:$t,cloneElement:zt,createContext:Wt,createElement:Kt,createFactory:Yt,createRef:Vt,forwardRef:Zt,isValidElement:qt,lazy:Jt,memo:Qt,startTransition:Xt,unstable_act:en,useCallback:L,useContext:V,useDebugValue:tn,useDeferredValue:nn,useEffect:U,useId:on,useImperativeHandle:rn,useInsertionEffect:an,useLayoutEffect:ln,useMemo:Z,useReducer:cn,useRef:q,useState:R,useSyncExternalStore:sn,useTransition:un,version:dn}=__REACT__;var hn=__STORYBOOK_TYPES__,{Addon_TypesEnum:J}=__STORYBOOK_TYPES__;var Tn=__STORYBOOK_API__,{ActiveTabs:bn,Consumer:yn,ManagerContext:Cn,Provider:_n,RequestResponseError:An,addons:x,combineParameters:wn,controlOrMetaKey:On,controlOrMetaSymbol:Rn,eventMatchesShortcut:xn,eventToShortcut:Pn,experimental_MockUniversalStore:kn,experimental_UniversalStore:Q,experimental_getStatusStore:j,experimental_getTestProviderStore:X,experimental_requestResponse:Bn,experimental_useStatusStore:M,experimental_useTestProviderStore:ee,experimental_useUniversalStore:te,internal_fullStatusStore:Dn,internal_fullTestProviderStore:Ln,internal_universalStatusStore:Un,internal_universalTestProviderStore:jn,isMacLike:Mn,isShortcutTaken:Nn,keyToSymbol:Hn,merge:Fn,mockChannel:Gn,optionOrAltSymbol:$n,shortcutMatchesShortcut:zn,shortcutToHumanString:Wn,types:Kn,useAddonState:Yn,useArgTypes:Vn,useArgs:Zn,useChannel:qn,useGlobalTypes:Jn,useGlobals:Qn,useParameter:Xn,useSharedState:eo,useStoryPrepared:to,useStorybookApi:ne,useStorybookState:no}=__STORYBOOK_API__;var co=__STORYBOOK_COMPONENTS__,{A:so,ActionBar:io,AddonPanel:uo,Badge:mo,Bar:go,Blockquote:Io,Button:N,ClipboardCode:po,Code:ho,DL:fo,Div:vo,DocumentWrapper:So,EmptyTabContent:Eo,ErrorFormatter:To,FlexBar:bo,Form:P,H1:yo,H2:Co,H3:_o,H4:Ao,H5:wo,H6:Oo,HR:Ro,IconButton:f,Img:xo,LI:Po,Link:H,ListItem:k,Loader:ko,Modal:F,OL:Bo,P:Do,Placeholder:Lo,Pre:Uo,ProgressSpinner:oe,ResetWrapper:jo,ScrollArea:Mo,Separator:No,Spaced:Ho,Span:Fo,StorybookIcon:Go,StorybookLogo:$o,SyntaxHighlighter:zo,TT:Wo,TabBar:Ko,TabButton:Yo,TabWrapper:Vo,Table:Zo,Tabs:qo,TabsState:Jo,TooltipLinkList:Qo,TooltipMessage:Xo,TooltipNote:b,UL:er,WithTooltip:y,WithTooltipPure:tr,Zoom:nr,codeCommon:or,components:rr,createCopyToClipboardFunction:ar,getStoryHref:lr,interleaveSeparators:cr,nameSpaceClassNames:sr,resetComponents:ir,withReset:ur}=__STORYBOOK_COMPONENTS__;var pr=__STORYBOOK_ICONS__,{AccessibilityAltIcon:hr,AccessibilityIcon:fr,AccessibilityIgnoredIcon:vr,AddIcon:Sr,AdminIcon:Er,AlertAltIcon:Tr,AlertIcon:br,AlignLeftIcon:yr,AlignRightIcon:Cr,AppleIcon:_r,ArrowBottomLeftIcon:Ar,ArrowBottomRightIcon:wr,ArrowDownIcon:Or,ArrowLeftIcon:Rr,ArrowRightIcon:xr,ArrowSolidDownIcon:Pr,ArrowSolidLeftIcon:kr,ArrowSolidRightIcon:Br,ArrowSolidUpIcon:Dr,ArrowTopLeftIcon:Lr,ArrowTopRightIcon:Ur,ArrowUpIcon:jr,AzureDevOpsIcon:Mr,BackIcon:Nr,BasketIcon:Hr,BatchAcceptIcon:Fr,BatchDenyIcon:Gr,BeakerIcon:$r,BellIcon:zr,BitbucketIcon:Wr,BoldIcon:Kr,BookIcon:Yr,BookmarkHollowIcon:Vr,BookmarkIcon:Zr,BottomBarIcon:qr,BottomBarToggleIcon:Jr,BoxIcon:Qr,BranchIcon:Xr,BrowserIcon:ea,ButtonIcon:ta,CPUIcon:na,CalendarIcon:oa,CameraIcon:ra,CameraStabilizeIcon:aa,CategoryIcon:la,CertificateIcon:ca,ChangedIcon:sa,ChatIcon:ia,CheckIcon:ua,ChevronDownIcon:da,ChevronLeftIcon:ma,ChevronRightIcon:ga,ChevronSmallDownIcon:Ia,ChevronSmallLeftIcon:pa,ChevronSmallRightIcon:ha,ChevronSmallUpIcon:fa,ChevronUpIcon:va,ChromaticIcon:Sa,ChromeIcon:Ea,CircleHollowIcon:Ta,CircleIcon:ba,ClearIcon:ya,CloseAltIcon:Ca,CloseIcon:re,CloudHollowIcon:_a,CloudIcon:Aa,CogIcon:wa,CollapseIcon:Oa,CommandIcon:Ra,CommentAddIcon:xa,CommentIcon:Pa,CommentsIcon:ka,CommitIcon:Ba,CompassIcon:Da,ComponentDrivenIcon:La,ComponentIcon:Ua,ContrastIcon:ja,ContrastIgnoredIcon:Ma,ControlsIcon:Na,CopyIcon:Ha,CreditIcon:Fa,CrossIcon:Ga,DashboardIcon:$a,DatabaseIcon:za,DeleteIcon:Wa,DiamondIcon:Ka,DirectionIcon:Ya,DiscordIcon:Va,DocChartIcon:Za,DocListIcon:qa,DocumentIcon:Ja,DownloadIcon:Qa,DragIcon:Xa,EditIcon:el,EllipsisIcon:tl,EmailIcon:nl,ExpandAltIcon:ol,ExpandIcon:rl,EyeCloseIcon:al,EyeIcon:ae,FaceHappyIcon:ll,FaceNeutralIcon:cl,FaceSadIcon:sl,FacebookIcon:il,FailedIcon:ul,FastForwardIcon:dl,FigmaIcon:ml,FilterIcon:gl,FlagIcon:Il,FolderIcon:pl,FormIcon:hl,GDriveIcon:fl,GithubIcon:vl,GitlabIcon:Sl,GlobeIcon:El,GoogleIcon:Tl,GraphBarIcon:bl,GraphLineIcon:yl,GraphqlIcon:Cl,GridAltIcon:_l,GridIcon:Al,GrowIcon:wl,HeartHollowIcon:Ol,HeartIcon:Rl,HomeIcon:xl,HourglassIcon:Pl,InfoIcon:le,ItalicIcon:kl,JumpToIcon:Bl,KeyIcon:Dl,LightningIcon:Ll,LightningOffIcon:Ul,LinkBrokenIcon:jl,LinkIcon:Ml,LinkedinIcon:Nl,LinuxIcon:Hl,ListOrderedIcon:Fl,ListUnorderedIcon:Gl,LocationIcon:$l,LockIcon:zl,MarkdownIcon:Wl,MarkupIcon:Kl,MediumIcon:Yl,MemoryIcon:Vl,MenuIcon:Zl,MergeIcon:ql,MirrorIcon:Jl,MobileIcon:Ql,MoonIcon:Xl,NutIcon:ec,OutboxIcon:tc,OutlineIcon:nc,PaintBrushIcon:oc,PaperClipIcon:rc,ParagraphIcon:ac,PassedIcon:lc,PhoneIcon:cc,PhotoDragIcon:sc,PhotoIcon:ic,PhotoStabilizeIcon:uc,PinAltIcon:dc,PinIcon:mc,PlayAllHollowIcon:gc,PlayBackIcon:Ic,PlayHollowIcon:ce,PlayIcon:pc,PlayNextIcon:hc,PlusIcon:fc,PointerDefaultIcon:vc,PointerHandIcon:Sc,PowerIcon:Ec,PrintIcon:Tc,ProceedIcon:bc,ProfileIcon:yc,PullRequestIcon:Cc,QuestionIcon:_c,RSSIcon:Ac,RedirectIcon:wc,ReduxIcon:Oc,RefreshIcon:Rc,ReplyIcon:xc,RepoIcon:Pc,RequestChangeIcon:kc,RewindIcon:Bc,RulerIcon:Dc,SaveIcon:Lc,SearchIcon:Uc,ShareAltIcon:jc,ShareIcon:Mc,ShieldIcon:Nc,SideBySideIcon:Hc,SidebarAltIcon:Fc,SidebarAltToggleIcon:Gc,SidebarIcon:$c,SidebarToggleIcon:zc,SpeakerIcon:Wc,StackedIcon:Kc,StarHollowIcon:Yc,StarIcon:Vc,StatusFailIcon:Zc,StatusIcon:qc,StatusPassIcon:Jc,StatusWarnIcon:Qc,StickerIcon:Xc,StopAltHollowIcon:es,StopAltIcon:se,StopIcon:ts,StorybookIcon:ns,StructureIcon:os,SubtractIcon:rs,SunIcon:as,SupportIcon:ls,SweepIcon:cs,SwitchAltIcon:ss,SyncIcon:ie,TabletIcon:is,ThumbsUpIcon:us,TimeIcon:ds,TimerIcon:ms,TransferIcon:gs,TrashIcon:Is,TwitterIcon:ps,TypeIcon:hs,UbuntuIcon:fs,UndoIcon:vs,UnfoldIcon:Ss,UnlockIcon:Es,UnpinIcon:Ts,UploadIcon:bs,UserAddIcon:ys,UserAltIcon:Cs,UserIcon:_s,UsersIcon:As,VSCodeIcon:ws,VerifiedIcon:Os,VideoIcon:Rs,WandIcon:xs,WatchIcon:Ps,WindowsIcon:ks,WrenchIcon:Bs,XIcon:Ds,YoutubeIcon:Ls,ZoomIcon:Us,ZoomOutIcon:js,ZoomResetIcon:Ms,iconList:Ns}=__STORYBOOK_ICONS__;var zs=__STORYBOOK_THEMING__,{CacheProvider:Ws,ClassNames:Ks,Global:Ys,ThemeProvider:Vs,background:Zs,color:qs,convert:Js,create:Qs,createCache:Xs,createGlobal:ei,createReset:ti,css:ni,darken:oi,ensure:ri,ignoreSsrWarning:ai,isPropValid:li,jsx:ci,keyframes:si,lighten:ii,styled:I,themes:ui,typography:di,useTheme:mi,withTheme:gi}=__STORYBOOK_THEMING__;var Re="storybook/interactions",ve=`${Re}/panel`,Se="storybook/a11y",Ee=`${Se}/panel`,O="storybook/test",xe=`${O}/test-provider`,Pe="writing-tests/integrations/vitest-addon",ke=`${Pe}#what-happens-if-vitest-itself-has-an-error`,Be={id:O,initialState:{config:{coverage:!1,a11y:!1},watching:!1,cancelling:!1,fatalError:void 0,indexUrl:void 0,previewAnnotations:[],currentRun:{triggeredBy:void 0,config:{coverage:!1,a11y:!1},componentTestCount:{success:0,error:0},a11yCount:{success:0,warning:0,error:0},storyIds:void 0,totalTestCount:void 0,startedAt:void 0,finishedAt:void 0,unhandledErrors:[],coverageSummary:void 0}}},ue=["global","run-all"],Te="storybook/component-test",be="storybook/a11y",v=Q.create({...Be,leader:globalThis.CONFIG_TYPE==="PRODUCTION"}),De=j(Te),Le=j(be),ye=X(O),Ue=I.div({display:"flex",justifyContent:"space-between",alignItems:"center",padding:"6px 6px 6px 20px"}),je=I.div({display:"flex",justifyContent:"space-between",alignItems:"center"}),Me=I(F.Title)(({theme:{typography:e}})=>({fontSize:e.size.s2,fontWeight:e.weight.bold})),Ne=I.pre(({theme:e})=>({whiteSpace:"pre-wrap",wordWrap:"break-word",overflow:"auto",maxHeight:"60vh",margin:0,padding:"20px",fontFamily:e.typography.fonts.mono,fontSize:"12px",borderTop:`1px solid ${e.appBorderColor}`,borderRadius:0})),He=I.a(({theme:e})=>({color:e.color.defaultText})),W=n.createContext({isModalOpen:!1,setModalOpen:void 0});function z({error:e}){return e?n.createElement("div",null,n.createElement("h4",null,"Caused by: ",e.name||"Error",": ",e.message),e.stack&&n.createElement("pre",null,e.stack),e.cause&&n.createElement(z,{error:e.cause})):null}function Fe({onRerun:e,storeState:t}){let o=ne(),{isModalOpen:c,setModalOpen:u}=V(W),r=()=>u?.(!1),l=o.getDocsUrl({subpath:ke,versioned:!0,renderer:!0}),{fatalError:i,currentRun:{unhandledErrors:s}}=t,g=i?n.createElement(n.Fragment,null,n.createElement("p",null,i.error.name||"Error"),i.message&&n.createElement("p",null,i.message),i.error.message&&n.createElement("p",null,i.error.message),i.error.stack&&n.createElement("p",null,i.error.stack),i.error.cause&&n.createElement(z,{error:i.error.cause})):s.length>0?n.createElement("ol",null,s.map(a=>n.createElement("li",{key:a.name+a.message},n.createElement("p",null,a.name,": ",a.message),a.VITEST_TEST_PATH&&n.createElement("p",null,'This error originated in "',n.createElement("b",null,a.VITEST_TEST_PATH),`". It doesn't mean the error was thrown inside the file itself, but while it was running.`),a.VITEST_TEST_NAME&&n.createElement(n.Fragment,null,n.createElement("p",null,`The latest test that might've caused the error is "`,n.createElement("b",null,a.VITEST_TEST_NAME),'". It might mean one of the following:'),n.createElement("ul",null,n.createElement("li",null,"The error was thrown, while Vitest was running this test."),n.createElement("li",null,"If the error occurred after the test had been completed, this was the last documented test before it was thrown."))),a.stacks&&n.createElement(n.Fragment,null,n.createElement("p",null,n.createElement("b",null,"Stacks:")),n.createElement("ul",null,a.stacks.map(d=>n.createElement("li",{key:d.file+d.line+d.column},d.file,":",d.line,":",d.column," - ",d.method||"unknown method")))),a.stack&&n.createElement("p",null,a.stack),a.cause?n.createElement(z,{error:a.cause}):null))):null;return n.createElement(F,{onEscapeKeyDown:r,onInteractOutside:r,open:c},n.createElement(Ue,null,n.createElement(Me,null,"Storybook Tests error details"),n.createElement(je,null,n.createElement(N,{onClick:e,variant:"ghost"},n.createElement(ie,null),"Rerun"),n.createElement(N,{variant:"ghost",asChild:!0},n.createElement("a",{target:"_blank",href:l,rel:"noreferrer"},"Troubleshoot")),n.createElement(f,{onClick:r,"aria-label":"Close modal"},n.createElement(re,null)))),n.createElement(Ne,null,g,n.createElement("br",null),n.createElement("br",null),"Troubleshoot:"," ",n.createElement(He,{target:"_blank",href:l},l)))}function Ge(){}function de(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}function me(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}var $e="[object RegExp]",ze="[object String]",We="[object Number]",Ke="[object Boolean]",ge="[object Arguments]",Ye="[object Symbol]",Ve="[object Date]",Ze="[object Map]",qe="[object Set]",Je="[object Array]",Qe="[object Function]",Xe="[object ArrayBuffer]",G="[object Object]",et="[object Error]",tt="[object DataView]",nt="[object Uint8Array]",ot="[object Uint8ClampedArray]",rt="[object Uint16Array]",at="[object Uint32Array]",lt="[object BigUint64Array]",ct="[object Int8Array]",st="[object Int16Array]",it="[object Int32Array]",ut="[object BigInt64Array]",dt="[object Float32Array]",mt="[object Float64Array]";function Ie(e){if(!e||typeof e!="object")return!1;let t=Object.getPrototypeOf(e);return t===null||t===Object.prototype||Object.getPrototypeOf(t)===null?Object.prototype.toString.call(e)==="[object Object]":!1}function gt(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}function It(e,t,o){return A(e,t,void 0,void 0,void 0,void 0,o)}function A(e,t,o,c,u,r,l){let i=l(e,t,o,c,u,r);if(i!==void 0)return i;if(typeof e==typeof t)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return e===t;case"number":return e===t||Object.is(e,t);case"function":return e===t;case"object":return w(e,t,r,l)}return w(e,t,r,l)}function w(e,t,o,c){if(Object.is(e,t))return!0;let u=me(e),r=me(t);if(u===ge&&(u=G),r===ge&&(r=G),u!==r)return!1;switch(u){case ze:return e.toString()===t.toString();case We:{let s=e.valueOf(),g=t.valueOf();return gt(s,g)}case Ke:case Ve:case Ye:return Object.is(e.valueOf(),t.valueOf());case $e:return e.source===t.source&&e.flags===t.flags;case Qe:return e===t}o=o??new Map;let l=o.get(e),i=o.get(t);if(l!=null&&i!=null)return l===t;o.set(e,t),o.set(t,e);try{switch(u){case Ze:{if(e.size!==t.size)return!1;for(let[s,g]of e.entries())if(!t.has(s)||!A(g,t.get(s),s,e,t,o,c))return!1;return!0}case qe:{if(e.size!==t.size)return!1;let s=Array.from(e.values()),g=Array.from(t.values());for(let a=0;a<s.length;a++){let d=s[a],m=g.findIndex(p=>A(d,p,void 0,e,t,o,c));if(m===-1)return!1;g.splice(m,1)}return!0}case Je:case nt:case ot:case rt:case at:case lt:case ct:case st:case it:case ut:case dt:case mt:{if(typeof Buffer<"u"&&Buffer.isBuffer(e)!==Buffer.isBuffer(t)||e.length!==t.length)return!1;for(let s=0;s<e.length;s++)if(!A(e[s],t[s],s,e,t,o,c))return!1;return!0}case Xe:return e.byteLength!==t.byteLength?!1:w(new Uint8Array(e),new Uint8Array(t),o,c);case tt:return e.byteLength!==t.byteLength||e.byteOffset!==t.byteOffset?!1:w(new Uint8Array(e),new Uint8Array(t),o,c);case et:return e.name===t.name&&e.message===t.message;case G:{if(!(w(e.constructor,t.constructor,o,c)||Ie(e)&&Ie(t)))return!1;let s=[...Object.keys(e),...de(e)],g=[...Object.keys(t),...de(t)];if(s.length!==g.length)return!1;for(let a=0;a<s.length;a++){let d=s[a],m=e[d];if(!Object.hasOwn(t,d))return!1;let p=t[d];if(!A(m,p,d,e,t,o,c))return!1}return!0}default:return!1}}finally{o.delete(e),o.delete(t)}}function pt(e,t){return It(e,t,Ge)}var pe=(e,t,o)=>{let c={"status-value:pending":[],"status-value:success":[],"status-value:error":[],"status-value:warning":[],"status-value:unknown":[]};return(o?o.map(u=>e[u]).filter(Boolean):Object.values(e)).forEach(u=>{let r=u[t];r&&c[r.value].push(r.storyId)}),c},Ce=(e,t)=>{let o=ee(p=>p[O]),[c,u]=te(v),[r,l]=R(!1),i=q();U(()=>{let p=v.onStateChange((C,D)=>{pt(C.config,D.config)||(ye.settingsChanged(),l(!0),clearTimeout(i.current),i.current=setTimeout(()=>{l(!1)},1e3))});return()=>{p(),clearTimeout(i.current)}},[]);let s=Z(()=>t?e.findAllLeafStoryIds(t):void 0,[t,e]),g=L(p=>pe(p,Te,s),[s]),a=M(g),d=L(p=>pe(p,be,s),[s]),m=M(d);return{storeState:c,setStoreState:u,testProviderState:o,componentTestStatusValueToStoryIds:a,a11yStatusValueToStoryIds:m,isSettingsUpdated:r}},ht=({timestamp:e})=>{let[t,o]=R(null);if(U(()=>{if(e){o(Date.now()-e);let i=setInterval(()=>o(Date.now()-e),1e4);return()=>clearInterval(i)}},[e]),t===null)return null;let c=Math.round(t/1e3);if(c<60)return"just now";let u=Math.floor(c/60);if(u<60)return u===1?"a minute ago":`${u} minutes ago`;let r=Math.floor(u/60);if(r<24)return r===1?"an hour ago":`${r} hours ago`;let l=Math.floor(r/24);return l===1?"yesterday":`${l} days ago`},ft=I.div(({theme:e})=>({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",fontSize:e.typography.size.s1,color:e.textMutedColor})),vt=I.span(({theme:e})=>({color:e.color.positiveText}));function St({entryId:e,storeState:t,testProviderState:o,isSettingsUpdated:c,...u}){let{setModalOpen:r}=n.useContext(W),{componentTestCount:l,totalTestCount:i,unhandledErrors:s,finishedAt:g}=t.currentRun,a=l.success+l.error,d="Not run";if(!e&&c)d=n.createElement(vt,null,"Settings updated");else if(o==="test-provider-state:running")d=(a??0)===0?"Starting...":`Testing... ${a}/${i}`;else if(!e&&o==="test-provider-state:crashed")d=r?n.createElement(H,{isButton:!0,onClick:()=>r(!0)},"View full error"):"Crashed";else if(!e&&s.length>0){let m=`View ${s.length} unhandled error${s?.length>1?"s":""}`;d=r?n.createElement(H,{isButton:!0,onClick:()=>r(!0)},m):m}else e&&i?d=`Ran ${i} ${i===1?"test":"tests"}`:g?d=n.createElement(n.Fragment,null,"Ran ",i," ",i===1?"test":"tests"," ",n.createElement(ht,{timestamp:g})):t.watching&&(d="Watching for file changes");return n.createElement(ft,{...u},d)}var B=I.div(({percentage:e})=>({width:e?12:6,height:e?12:6,margin:e?1:4,background:e?`conic-gradient(var(--status-color) ${e}%, var(--status-background) ${e+1}%)`:"var(--status-color)",borderRadius:"50%"}),({isRunning:e,theme:t})=>e&&{animation:`${t.animation.glow} 1.5s ease-in-out infinite`},({status:e,theme:t})=>e==="positive"&&{"--status-color":t.color.positive,"--status-background":`${t.color.positive}66`},({status:e,theme:t})=>e==="warning"&&{"--status-color":t.color.gold,"--status-background":`${t.color.gold}66`},({status:e,theme:t})=>e==="negative"&&{"--status-color":t.color.negative,"--status-background":`${t.color.negative}66`},({status:e,theme:t})=>e==="critical"&&{"--status-color":t.color.defaultText,"--status-background":`${t.color.defaultText}66`},({status:e,theme:t})=>e==="unknown"&&{"--status-color":t.color.mediumdark,"--status-background":`${t.color.mediumdark}66`}),Et=I.div({display:"flex",flexDirection:"column"}),Tt=I.div({display:"flex",justifyContent:"space-between",padding:"8px 0",gap:12}),bt=I.div({display:"flex",flexDirection:"column",marginLeft:8,minWidth:0}),he=I.div(({crashed:e,theme:t})=>({fontSize:t.typography.size.s1,fontWeight:e?"bold":"normal",color:e?t.color.negativeText:t.color.defaultText})),yt=I.div({display:"flex",gap:4}),Ct=I.div({marginBottom:2}),_t=I.span(({theme:e})=>({color:e.textMutedColor})),At=I(oe)({margin:4}),$=I.div({display:"flex",gap:4}),wt=I(se)({width:10}),fe=({api:e,panelId:t,entryId:o})=>{let c=o?e.findAllLeafStoryIds(o)[0]:void 0;c&&e.selectStory(c),e.setSelectedPanel(t),e.togglePanel(!0)},_e=({api:e,entry:t,testProviderState:o,storeState:c,setStoreState:u,componentTestStatusValueToStoryIds:r,a11yStatusValueToStoryIds:l,isSettingsUpdated:i,...s})=>{let{config:g,watching:a,cancelling:d,currentRun:m,fatalError:p}=c,C=m.componentTestCount.success+m.componentTestCount.error,D=x.experimental_getRegisteredAddons().includes(Se),h=o==="test-provider-state:running",Ae=h&&C===0,[we,K]=p?["critical","Component tests crashed"]:r["status-value:error"].length>0?["negative","Component tests failed"]:h?["unknown","Testing in progress"]:r["status-value:success"].length>0?["positive","Component tests passed"]:["unknown","Run tests to see results"],[Oe,Y]=p?["critical","Component tests crashed"]:l["status-value:error"].length>0?["negative","Accessibility tests failed"]:l["status-value:warning"].length>0?["warning","Accessibility tests failed"]:h?["unknown","Testing in progress"]:l["status-value:success"].length>0?["positive","Accessibility tests passed"]:["unknown","Run tests to see accessibility results"];return n.createElement(Et,{...s},n.createElement(Tt,null,n.createElement(bt,null,t?n.createElement(he,{id:"testing-module-title"},"Run component tests"):n.createElement(he,{id:"testing-module-title",crashed:o==="test-provider-state:crashed"||p!==void 0||m.unhandledErrors.length>0},m.unhandledErrors.length===1?"Component tests completed with an error":m.unhandledErrors.length>1?"Component tests completed with errors":p?"Component tests didn\u2019t complete":"Run component tests"),n.createElement(St,{id:"testing-module-description",storeState:c,testProviderState:o,entryId:t?.id,isSettingsUpdated:i})),n.createElement(yt,null,!t&&n.createElement(y,{hasChrome:!1,trigger:"hover",tooltip:n.createElement(b,{note:`${a?"Disable":"Enable"} watch mode`})},n.createElement(f,{"aria-label":`${a?"Disable":"Enable"} watch mode`,size:"medium",active:a,onClick:()=>v.send({type:"TOGGLE_WATCHING",payload:{to:!a}}),disabled:h},n.createElement(ae,null))),h?n.createElement(y,{hasChrome:!1,trigger:"hover",tooltip:n.createElement(b,{note:d?"Stopping...":"Stop test run"})},n.createElement(f,{"aria-label":d?"Stopping...":"Stop test run",padding:"none",size:"medium",onClick:()=>v.send({type:"CANCEL_RUN"}),disabled:d||Ae},n.createElement(At,{percentage:C&&c.currentRun.totalTestCount?C/c.currentRun.totalTestCount*100:void 0},n.createElement(wt,null)))):n.createElement(y,{hasChrome:!1,trigger:"hover",tooltip:n.createElement(b,{note:"Start test run"})},n.createElement(f,{"aria-label":"Start test run",size:"medium",onClick:()=>v.send({type:"TRIGGER_RUN",payload:{storyIds:t?e.findAllLeafStoryIds(t.id):void 0,triggeredBy:t?t.type:"global"}})},n.createElement(ce,null))))),n.createElement(Ct,null,n.createElement($,null,n.createElement(k,{as:"label",title:"Interactions",icon:t?null:n.createElement(P.Checkbox,{checked:!0,disabled:!0})}),n.createElement(y,{hasChrome:!1,trigger:"hover",tooltip:n.createElement(b,{note:K})},n.createElement(f,{size:"medium",disabled:r["status-value:error"].length===0&&r["status-value:warning"].length===0&&r["status-value:success"].length===0,onClick:()=>{fe({api:e,panelId:ve,entryId:r["status-value:error"][0]??r["status-value:warning"][0]??r["status-value:success"][0]??t?.id})}},n.createElement(B,{status:we,"aria-label":K,isRunning:h}),r["status-value:error"].length+r["status-value:warning"].length||null))),!t&&n.createElement($,null,n.createElement(k,{as:"label",title:a?n.createElement(_t,null,"Coverage (unavailable)"):"Coverage",icon:n.createElement(P.Checkbox,{checked:g.coverage,disabled:h,onChange:()=>u(_=>({..._,config:{..._.config,coverage:!g.coverage}}))})}),n.createElement(y,{hasChrome:!1,trigger:"hover",tooltip:n.createElement(b,{note:a?"Unavailable in watch mode":m.triggeredBy&&!ue.includes(m.triggeredBy)?"Unavailable when running focused tests":h?"Testing in progress":m.coverageSummary?"View coverage report":p?"Component tests crashed":"Run tests to calculate coverage"})},a||m.triggeredBy&&!ue.includes(m.triggeredBy)?n.createElement(f,{size:"medium",disabled:!0},n.createElement(le,{"aria-label":a?"Coverage is unavailable in watch mode":"Coverage is unavailable when running focused tests"})):m.coverageSummary?n.createElement(f,{asChild:!0,size:"medium"},n.createElement("a",{href:"/coverage/index.html",target:"_blank","aria-label":"Open coverage report"},n.createElement(B,{isRunning:h,percentage:m.coverageSummary.percentage,status:m.coverageSummary.status,"aria-label":`Coverage status: ${m.coverageSummary.status}`}),n.createElement("span",{"aria-label":`${m.coverageSummary.percentage} percent coverage`},m.coverageSummary.percentage,"%"))):n.createElement(f,{size:"medium",disabled:!0},n.createElement(B,{isRunning:h,status:p?"critical":"unknown","aria-label":"Coverage status: unknown"})))),D&&n.createElement($,null,n.createElement(k,{as:"label",title:"Accessibility",icon:t?null:n.createElement(P.Checkbox,{checked:g.a11y,disabled:h,onChange:()=>u(_=>({..._,config:{..._.config,a11y:!g.a11y}}))})}),n.createElement(y,{hasChrome:!1,trigger:"hover",tooltip:n.createElement(b,{note:Y})},n.createElement(f,{size:"medium",disabled:l["status-value:error"].length===0&&l["status-value:warning"].length===0&&l["status-value:success"].length===0,onClick:()=>{fe({api:e,entryId:l["status-value:error"][0]??l["status-value:warning"][0]??l["status-value:success"][0]??t?.id,panelId:Ee})}},n.createElement(B,{status:Oe,"aria-label":Y,isRunning:h}),l["status-value:error"].length+l["status-value:warning"].length||null)))))},Ot=({context:e,api:t})=>{let{testProviderState:o,componentTestStatusValueToStoryIds:c,a11yStatusValueToStoryIds:u,storeState:r,setStoreState:l}=Ce(t,e.id);return n.createElement(_e,{api:t,entry:e,style:{minWidth:240},testProviderState:o,componentTestStatusValueToStoryIds:c,a11yStatusValueToStoryIds:u,storeState:r,setStoreState:l,isSettingsUpdated:!1})};x.register(O,e=>{if((globalThis.STORYBOOK_BUILDER||"").includes("vite")){let t=o=>{e.setSelectedPanel(o),e.togglePanel(!0)};De.onSelect(()=>{t(ve)}),Le.onSelect(()=>{t(Ee)}),ye.onRunAll(()=>{v.send({type:"TRIGGER_RUN",payload:{triggeredBy:"run-all"}})}),v.untilReady().then(()=>{v.setState(o=>({...o,indexUrl:new URL("index.json",window.location.href).toString()}))}),x.add(xe,{type:J.experimental_TEST_PROVIDER,render:()=>{let[o,c]=R(!1),{storeState:u,setStoreState:r,testProviderState:l,componentTestStatusValueToStoryIds:i,a11yStatusValueToStoryIds:s,isSettingsUpdated:g}=Ce(e);return n.createElement(W.Provider,{value:{isModalOpen:o,setModalOpen:c}},n.createElement(_e,{api:e,storeState:u,setStoreState:r,isSettingsUpdated:g,testProviderState:l,componentTestStatusValueToStoryIds:i,a11yStatusValueToStoryIds:s}),n.createElement(Fe,{storeState:u,onRerun:()=>{c(!1),v.send({type:"TRIGGER_RUN",payload:{triggeredBy:"global"}})}}))},sidebarContextMenu:({context:o})=>o.type==="docs"||o.type==="story"&&!o.tags.includes("test")?null:n.createElement(Ot,{context:o,api:e})})}});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
