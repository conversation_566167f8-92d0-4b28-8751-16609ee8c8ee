import{j as e}from"./jsx-runtime-DfLt7GBH.js";import{useMDXComponents as r}from"./index-BQ5W4Lgr.js";import{b as a}from"./blocks-Djlil9Cu.js";import"./iframe-CAQEOFTZ.js";import"./preload-helper-D9Z9MdNV.js";import"./index-TKk9mXQ8.js";const n="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='32'%20height='32'%20fill='none'%20viewBox='0%200%2032%2032'%3e%3cpath%20fill='%23161614'%20d='M16.0001%200C7.16466%200%200%207.17472%200%2016.0256C0%2023.1061%204.58452%2029.1131%2010.9419%2031.2322C11.7415%2031.3805%2012.0351%2030.8845%2012.0351%2030.4613C12.0351%2030.0791%2012.0202%2028.8167%2012.0133%2027.4776C7.56209%2028.447%206.62283%2025.5868%206.62283%2025.5868C5.89499%2023.7345%204.8463%2023.2419%204.8463%2023.2419C3.39461%2022.2473%204.95573%2022.2678%204.95573%2022.2678C6.56242%2022.3808%207.40842%2023.9192%207.40842%2023.9192C8.83547%2026.3691%2011.1514%2025.6609%2012.0645%2025.2514C12.2081%2024.2156%2012.6227%2023.5087%2013.0803%2023.1085C9.52648%2022.7032%205.7906%2021.3291%205.7906%2015.1886C5.7906%2013.4389%206.41563%2012.0094%207.43916%2010.8871C7.27303%2010.4834%206.72537%208.85349%207.59415%206.64609C7.59415%206.64609%208.93774%206.21539%2011.9953%208.28877C13.2716%207.9337%2014.6404%207.75563%2016.0001%207.74953C17.3599%207.75563%2018.7297%207.9337%2020.0084%208.28877C23.0623%206.21539%2024.404%206.64609%2024.404%206.64609C25.2749%208.85349%2024.727%2010.4834%2024.5608%2010.8871C25.5868%2012.0094%2026.2075%2013.4389%2026.2075%2015.1886C26.2075%2021.3437%2022.4645%2022.699%2018.9017%2023.0957C19.4756%2023.593%2019.9869%2024.5683%2019.9869%2026.0634C19.9869%2028.2077%2019.9684%2029.9334%2019.9684%2030.4613C19.9684%2030.8877%2020.2564%2031.3874%2021.0674%2031.2301C27.4213%2029.1086%2032%2023.1037%2032%2016.0256C32%207.17472%2024.8364%200%2016.0001%200ZM5.99257%2022.8288C5.95733%2022.9084%205.83227%2022.9322%205.71834%2022.8776C5.60229%2022.8253%205.53711%2022.7168%205.57474%2022.6369C5.60918%2022.5549%205.7345%2022.5321%205.85029%2022.587C5.9666%2022.6393%206.03284%2022.7489%205.99257%2022.8288ZM6.7796%2023.5321C6.70329%2023.603%206.55412%2023.5701%206.45291%2023.4581C6.34825%2023.3464%206.32864%2023.197%206.40601%2023.125C6.4847%2023.0542%206.62937%2023.0874%206.73429%2023.1991C6.83895%2023.3121%206.85935%2023.4605%206.7796%2023.5321ZM7.31953%2024.4321C7.2215%2024.5003%207.0612%2024.4363%206.96211%2024.2938C6.86407%2024.1513%206.86407%2023.9804%206.96422%2023.9119C7.06358%2023.8435%207.2215%2023.905%207.32191%2024.0465C7.41968%2024.1914%207.41968%2024.3623%207.31953%2024.4321ZM8.23267%2025.4743C8.14497%2025.5712%207.95818%2025.5452%207.82146%2025.413C7.68156%2025.2838%207.64261%2025.1004%207.73058%2025.0035C7.81934%2024.9064%208.00719%2024.9337%208.14497%2025.0648C8.28381%2025.1938%208.3262%2025.3785%208.23267%2025.4743ZM9.41281%2025.8262C9.37413%2025.9517%209.19423%2026.0088%209.013%2025.9554C8.83203%2025.9005%208.7136%2025.7535%208.75016%2025.6266C8.78778%2025.5003%208.96848%2025.4408%209.15104%2025.4979C9.33174%2025.5526%209.45044%2025.6985%209.41281%2025.8262ZM10.7559%2025.9754C10.7604%2026.1076%2010.6067%2026.2172%2010.4165%2026.2196C10.2252%2026.2238%2010.0704%2026.1169%2010.0683%2025.9868C10.0683%2025.8534%2010.2185%2025.7448%2010.4098%2025.7416C10.6001%2025.7379%2010.7559%2025.8441%2010.7559%2025.9754ZM12.0753%2025.9248C12.0981%2026.0537%2011.9658%2026.1862%2011.7769%2026.2215C11.5912%2026.2554%2011.4192%2026.1758%2011.3957%2026.0479C11.3726%2025.9157%2011.5072%2025.7833%2011.6927%2025.7491C11.8819%2025.7162%2012.0512%2025.7937%2012.0753%2025.9248Z'/%3e%3c/svg%3e",c="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='33'%20height='32'%20fill='none'%20viewBox='0%200%2033%2032'%3e%3cg%20clip-path='url(%23clip0_10031_177575)'%3e%3cmask%20id='mask0_10031_177575'%20style='mask-type:luminance'%20width='33'%20height='25'%20x='0'%20y='4'%20maskUnits='userSpaceOnUse'%3e%3cpath%20fill='%23fff'%20d='M32.5034%204.00195H0.503906V28.7758H32.5034V4.00195Z'/%3e%3c/mask%3e%3cg%20mask='url(%23mask0_10031_177575)'%3e%3cpath%20fill='%235865F2'%20d='M27.5928%206.20817C25.5533%205.27289%2023.3662%204.58382%2021.0794%204.18916C21.0378%204.18154%2020.9962%204.20057%2020.9747%204.23864C20.6935%204.73863%2020.3819%205.3909%2020.1637%205.90358C17.7042%205.53558%2015.2573%205.53558%2012.8481%205.90358C12.6299%205.37951%2012.307%204.73863%2012.0245%204.23864C12.003%204.20184%2011.9614%204.18281%2011.9198%204.18916C9.63431%204.58255%207.44721%205.27163%205.40641%206.20817C5.38874%206.21578%205.3736%206.22848%205.36355%206.24497C1.21508%2012.439%200.078646%2018.4809%200.636144%2024.4478C0.638667%2024.477%200.655064%2024.5049%200.677768%2024.5227C3.41481%2026.5315%206.06609%2027.7511%208.66815%2028.5594C8.70979%2028.5721%208.75392%2028.5569%208.78042%2028.5226C9.39594%2027.6826%209.94461%2026.7968%2010.4151%2025.8653C10.4428%2025.8107%2010.4163%2025.746%2010.3596%2025.7244C9.48927%2025.3945%208.66058%2024.9922%207.86343%2024.5354C7.80038%2024.4986%207.79533%2024.4084%207.85333%2024.3653C8.02108%2024.2397%208.18888%2024.109%208.34906%2023.977C8.37804%2023.9529%208.41842%2023.9478%208.45249%2023.963C13.6894%2026.3526%2019.359%2026.3526%2024.5341%2023.963C24.5682%2023.9465%2024.6086%2023.9516%2024.6388%2023.9757C24.799%2024.1077%2024.9668%2024.2397%2025.1358%2024.3653C25.1938%2024.4084%2025.19%2024.4986%2025.127%2024.5354C24.3298%2025.0011%2023.5011%2025.3945%2022.6296%2025.7232C22.5728%2025.7447%2022.5476%2025.8107%2022.5754%2025.8653C23.0559%2026.7955%2023.6046%2027.6812%2024.2087%2028.5213C24.234%2028.5569%2024.2794%2028.5721%2024.321%2028.5594C26.9357%2027.7511%2029.5869%2026.5315%2032.324%2024.5227C32.348%2024.5049%2032.3631%2024.4783%2032.3656%2024.4491C33.0328%2017.5506%2031.2481%2011.5584%2027.6344%206.24623C27.6256%206.22848%2027.6105%206.21578%2027.5928%206.20817ZM11.1971%2020.8146C9.62043%2020.8146%208.32129%2019.3679%208.32129%2017.5913C8.32129%2015.8146%209.59523%2014.368%2011.1971%2014.368C12.8115%2014.368%2014.0981%2015.8273%2014.0729%2017.5913C14.0729%2019.3679%2012.7989%2020.8146%2011.1971%2020.8146ZM21.8299%2020.8146C20.2533%2020.8146%2018.9541%2019.3679%2018.9541%2017.5913C18.9541%2015.8146%2020.228%2014.368%2021.8299%2014.368C23.4444%2014.368%2024.7309%2015.8273%2024.7057%2017.5913C24.7057%2019.3679%2023.4444%2020.8146%2021.8299%2020.8146Z'/%3e%3c/g%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_10031_177575'%3e%3crect%20width='32'%20height='32'%20fill='%23fff'%20transform='translate(0.5)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",l="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='32'%20height='32'%20fill='none'%20viewBox='0%200%2032%2032'%3e%3cpath%20fill='%23ED1D24'%20d='M31.3313%208.44657C30.9633%207.08998%2029.8791%206.02172%2028.5022%205.65916C26.0067%205.00026%2016%205.00026%2016%205.00026C16%205.00026%205.99333%205.00026%203.4978%205.65916C2.12102%206.02172%201.03665%207.08998%200.668678%208.44657C0%2010.9053%200%2016.0353%200%2016.0353C0%2016.0353%200%2021.1652%200.668678%2023.6242C1.03665%2024.9806%202.12102%2026.0489%203.4978%2026.4116C5.99333%2027.0703%2016%2027.0703%2016%2027.0703C16%2027.0703%2026.0067%2027.0703%2028.5022%2026.4116C29.8791%2026.0489%2030.9633%2024.9806%2031.3313%2023.6242C32%2021.1652%2032%2016.0353%2032%2016.0353C32%2016.0353%2032%2010.9053%2031.3313%208.44657Z'/%3e%3cpath%20fill='%23fff'%20d='M12.7266%2020.6934L21.0902%2016.036L12.7266%2011.3781V20.6934Z'/%3e%3c/svg%3e",d="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='33'%20height='32'%20fill='none'%20viewBox='0%200%2033%2032'%3e%3cg%20clip-path='url(%23clip0_10031_177597)'%3e%3cpath%20fill='%23B7F0EF'%20fill-rule='evenodd'%20d='M17%207.87059C17%206.48214%2017.9812%205.28722%2019.3431%205.01709L29.5249%202.99755C31.3238%202.64076%2033%204.01717%2033%205.85105V22.1344C33%2023.5229%2032.0188%2024.7178%2030.6569%2024.9879L20.4751%2027.0074C18.6762%2027.3642%2017%2025.9878%2017%2024.1539L17%207.87059Z'%20clip-rule='evenodd'%20opacity='.7'/%3e%3cpath%20fill='%2387E6E5'%20fill-rule='evenodd'%20d='M1%205.85245C1%204.01857%202.67623%202.64215%204.47507%202.99895L14.6569%205.01848C16.0188%205.28861%2017%206.48354%2017%207.87198V24.1553C17%2025.9892%2015.3238%2027.3656%2013.5249%2027.0088L3.34311%2024.9893C1.98119%2024.7192%201%2023.5242%201%2022.1358V5.85245Z'%20clip-rule='evenodd'/%3e%3cpath%20fill='%2361C1FD'%20fill-rule='evenodd'%20d='M15.543%205.71289C15.543%205.71289%2016.8157%205.96289%2017.4002%206.57653C17.9847%207.19016%2018.4521%209.03107%2018.4521%209.03107C18.4521%209.03107%2018.4521%2025.1106%2018.4521%2026.9629C18.4521%2028.8152%2019.3775%2031.4174%2019.3775%2031.4174L17.4002%2028.8947L16.2575%2031.4174C16.2575%2031.4174%2015.543%2029.0765%2015.543%2027.122C15.543%2025.1674%2015.543%205.71289%2015.543%205.71289Z'%20clip-rule='evenodd'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_10031_177597'%3e%3crect%20width='32'%20height='32'%20fill='%23fff'%20transform='translate(0.5)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",g=""+new URL("styling-Bk6zjRzU.png",import.meta.url).href,h=""+new URL("context-C0qIqeS4.png",import.meta.url).href,m="data:image/png;base64,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",p=""+new URL("docs---vsFbMi.png",import.meta.url).href,x=""+new URL("share-DGA-UcQf.png",import.meta.url).href,b=""+new URL("figma-plugin-CH2hELiO.png",import.meta.url).href,C=""+new URL("testing-cbzR9l9r.png",import.meta.url).href,f=""+new URL("accessibility-W_h2acOZ.png",import.meta.url).href,u=""+new URL("theming-D6WJLNoW.png",import.meta.url).href,j=""+new URL("addon-library-BWUCAmyN.png",import.meta.url).href,s=()=>e.jsx("svg",{viewBox:"0 0 14 14",width:"8px",height:"14px",style:{marginLeft:"4px",display:"inline-block",shapeRendering:"inherit",verticalAlign:"middle",fill:"currentColor","path fill":"currentColor"},children:e.jsx("path",{d:"m11.1 7.35-5.5 5.5a.5.5 0 0 1-.7-.7L10.04 7 4.9 1.85a.5.5 0 1 1 .7-.7l5.5 5.5c.2.2.2.5 0 .7Z"})});function o(t){const i={code:"code",h1:"h1",p:"p",...r(),...t.components};return e.jsxs(e.Fragment,{children:[e.jsx(a,{title:"Configure your project"}),`
`,e.jsxs("div",{className:"sb-container",children:[e.jsxs("div",{className:"sb-section-title",children:[e.jsx(i.h1,{id:"configure-your-project",children:"Configure your project"}),e.jsx(i.p,{children:"Because Storybook works separately from your app, you'll need to configure it for your specific stack and setup. Below, explore guides for configuring Storybook with popular frameworks and tools. If you get stuck, learn how you can ask for help from our community."})]}),e.jsxs("div",{className:"sb-section",children:[e.jsxs("div",{className:"sb-section-item",children:[e.jsx("img",{src:g,alt:"A wall of logos representing different styling technologies"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Add styling and CSS"}),e.jsx("p",{className:"sb-section-item-paragraph",children:"Like with web applications, there are many ways to include CSS within Storybook. Learn more about setting up styling within Storybook."}),e.jsxs("a",{href:"https://storybook.js.org/docs/configure/styling-and-css/?renderer=react&ref=configure",target:"_blank",children:["Learn more",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-section-item",children:[e.jsx("img",{src:h,alt:"An abstraction representing the composition of data for a component"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Provide context and mocking"}),e.jsx("p",{className:"sb-section-item-paragraph",children:"Often when a story doesn't render, it's because your component is expecting a specific environment or context (like a theme provider) to be available."}),e.jsxs("a",{href:"https://storybook.js.org/docs/writing-stories/decorators/?renderer=react&ref=configure#context-for-mocking",target:"_blank",children:["Learn more",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-section-item",children:[e.jsx("img",{src:m,alt:"A representation of typography and image assets"}),e.jsxs("div",{children:[e.jsx("h4",{className:"sb-section-item-heading",children:"Load assets and resources"}),e.jsxs("p",{className:"sb-section-item-paragraph",children:[`To link static files (like fonts) to your projects and stories, use the
`,e.jsx(i.code,{children:"staticDirs"}),` configuration option to specify folders to load when
starting Storybook.`]}),e.jsxs("a",{href:"https://storybook.js.org/docs/configure/images-and-assets/?renderer=react&ref=configure",target:"_blank",children:["Learn more",e.jsx(s,{})]})]})]})]})]}),`
`,e.jsxs("div",{className:"sb-container",children:[e.jsxs("div",{className:"sb-section-title",children:[e.jsx(i.h1,{id:"do-more-with-storybook",children:"Do more with Storybook"}),e.jsx(i.p,{children:"Now that you know the basics, let's explore other parts of Storybook that will improve your experience. This list is just to get you started. You can customise Storybook in many ways to fit your needs."})]}),e.jsx("div",{className:"sb-section",children:e.jsxs("div",{className:"sb-features-grid",children:[e.jsxs("div",{className:"sb-grid-item",children:[e.jsx("img",{src:p,alt:"A screenshot showing the autodocs tag being set, pointing a docs page being generated"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Autodocs"}),e.jsx("p",{className:"sb-section-item-paragraph",children:`Auto-generate living,
interactive reference documentation from your components and stories.`}),e.jsxs("a",{href:"https://storybook.js.org/docs/writing-docs/autodocs/?renderer=react&ref=configure",target:"_blank",children:["Learn more",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-grid-item",children:[e.jsx("img",{src:x,alt:"A browser window showing a Storybook being published to a chromatic.com URL"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Publish to Chromatic"}),e.jsx("p",{className:"sb-section-item-paragraph",children:"Publish your Storybook to review and collaborate with your entire team."}),e.jsxs("a",{href:"https://storybook.js.org/docs/sharing/publish-storybook/?renderer=react&ref=configure#publish-storybook-with-chromatic",target:"_blank",children:["Learn more",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-grid-item",children:[e.jsx("img",{src:b,alt:"Windows showing the Storybook plugin in Figma"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Figma Plugin"}),e.jsx("p",{className:"sb-section-item-paragraph",children:`Embed your stories into Figma to cross-reference the design and live
implementation in one place.`}),e.jsxs("a",{href:"https://storybook.js.org/docs/sharing/design-integrations/?renderer=react&ref=configure#embed-storybook-in-figma-with-the-plugin",target:"_blank",children:["Learn more",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-grid-item",children:[e.jsx("img",{src:C,alt:"Screenshot of tests passing and failing"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Testing"}),e.jsx("p",{className:"sb-section-item-paragraph",children:`Use stories to test a component in all its variations, no matter how
complex.`}),e.jsxs("a",{href:"https://storybook.js.org/docs/writing-tests/?renderer=react&ref=configure",target:"_blank",children:["Learn more",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-grid-item",children:[e.jsx("img",{src:f,alt:"Screenshot of accessibility tests passing and failing"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Accessibility"}),e.jsx("p",{className:"sb-section-item-paragraph",children:"Automatically test your components for a11y issues as you develop."}),e.jsxs("a",{href:"https://storybook.js.org/docs/writing-tests/accessibility-testing/?renderer=react&ref=configure",target:"_blank",children:["Learn more",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-grid-item",children:[e.jsx("img",{src:u,alt:"Screenshot of Storybook in light and dark mode"}),e.jsx("h4",{className:"sb-section-item-heading",children:"Theming"}),e.jsx("p",{className:"sb-section-item-paragraph",children:"Theme Storybook's UI to personalize it to your project."}),e.jsxs("a",{href:"https://storybook.js.org/docs/configure/theming/?renderer=react&ref=configure",target:"_blank",children:["Learn more",e.jsx(s,{})]})]})]})})]}),`
`,e.jsxs("div",{className:"sb-addon",children:[e.jsxs("div",{className:"sb-addon-text",children:[e.jsx("h4",{children:"Addons"}),e.jsx("p",{className:"sb-section-item-paragraph",children:"Integrate your tools with Storybook to connect workflows."}),e.jsxs("a",{href:"https://storybook.js.org/addons/?ref=configure",target:"_blank",children:["Discover all addons",e.jsx(s,{})]})]}),e.jsx("div",{className:"sb-addon-img",children:e.jsx("img",{src:j,alt:"Integrate your tools with Storybook to connect workflows."})})]}),`
`,e.jsxs("div",{className:"sb-section sb-socials",children:[e.jsxs("div",{className:"sb-section-item",children:[e.jsx("img",{src:n,alt:"Github logo",className:"sb-explore-image"}),e.jsx(i.p,{children:"Join our contributors building the future of UI development."}),e.jsxs("a",{href:"https://github.com/storybookjs/storybook",target:"_blank",children:["Star on GitHub",e.jsx(s,{})]})]}),e.jsxs("div",{className:"sb-section-item",children:[e.jsx("img",{src:c,alt:"Discord logo",className:"sb-explore-image"}),e.jsxs("div",{children:[e.jsx(i.p,{children:"Get support and chat with frontend developers."}),e.jsxs("a",{href:"https://discord.gg/storybook",target:"_blank",children:["Join Discord server",e.jsx(s,{})]})]})]}),e.jsxs("div",{className:"sb-section-item",children:[e.jsx("img",{src:l,alt:"Youtube logo",className:"sb-explore-image"}),e.jsxs("div",{children:[e.jsx(i.p,{children:"Watch tutorials, feature previews and interviews."}),e.jsxs("a",{href:"https://www.youtube.com/@chromaticui",target:"_blank",children:["Watch on YouTube",e.jsx(s,{})]})]})]}),e.jsxs("div",{className:"sb-section-item",children:[e.jsx("img",{src:d,alt:"A book",className:"sb-explore-image"}),e.jsx("p",{children:"Follow guided walkthroughs on for key workflows."}),e.jsxs("a",{href:"https://storybook.js.org/tutorials/?ref=configure",target:"_blank",children:["Discover tutorials",e.jsx(s,{})]})]})]}),`
`,e.jsx("style",{children:`
.sb-container {
  margin-bottom: 48px;
}

.sb-section {
  width: 100%;
  display: flex;
  flex-direction: row;
  gap: 20px;
}

img {
  object-fit: cover;
}

.sb-section-title {
  margin-bottom: 32px;
}

.sb-section a:not(h1 a, h2 a, h3 a) {
  font-size: 14px;
}

.sb-section-item, .sb-grid-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sb-section-item-heading {
  padding-top: 20px !important;
  padding-bottom: 5px !important;
  margin: 0 !important;
}
.sb-section-item-paragraph {
  margin: 0;
  padding-bottom: 10px;
}

.sb-chevron {
  margin-left: 5px;
}

.sb-features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 32px 20px;
}

.sb-socials {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}

.sb-socials p {
  margin-bottom: 10px;
}

.sb-explore-image {
  max-height: 32px;
  align-self: flex-start;
}

.sb-addon {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  background-color: #EEF3F8;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: #EEF3F8;
  height: 180px;
  margin-bottom: 48px;
  overflow: hidden;
}

.sb-addon-text {
  padding-left: 48px;
  max-width: 240px;
}

.sb-addon-text h4 {
  padding-top: 0px;
}

.sb-addon-img {
  position: absolute;
  left: 345px;
  top: 0;
  height: 100%;
  width: 200%;
  overflow: hidden;
}

.sb-addon-img img {
  width: 650px;
  transform: rotate(-15deg);
  margin-left: 40px;
  margin-top: -72px;
  box-shadow: 0 0 1px rgba(255, 255, 255, 0);
  backface-visibility: hidden;
}

@media screen and (max-width: 800px) {
  .sb-addon-img {
    left: 300px;
  }
}

@media screen and (max-width: 600px) {
  .sb-section {
    flex-direction: column;
  }

  .sb-features-grid {
    grid-template-columns: repeat(1, 1fr);
  }

  .sb-socials {
    grid-template-columns: repeat(2, 1fr);
  }

  .sb-addon {
    height: 280px;
    align-items: flex-start;
    padding-top: 32px;
    overflow: hidden;
  }

  .sb-addon-text {
    padding-left: 24px;
  }

  .sb-addon-img {
    right: 0;
    left: 0;
    top: 130px;
    bottom: 0;
    overflow: hidden;
    height: auto;
    width: 124%;
  }

  .sb-addon-img img {
    width: 1200px;
    transform: rotate(-12deg);
    margin-left: 0;
    margin-top: 48px;
    margin-bottom: -40px;
    margin-left: -24px;
  }
}
`})]})}function I(t={}){const{wrapper:i}={...r(),...t.components};return i?e.jsx(i,{...t,children:e.jsx(o,{...t})}):o(t)}export{s as RightArrow,I as default};
