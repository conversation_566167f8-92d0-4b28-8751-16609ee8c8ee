const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-BQ5W4Lgr.js","./iframe-CAQEOFTZ.js","./preload-helper-D9Z9MdNV.js"])))=>i.map(i=>d[i]);
import{_ as i}from"./preload-helper-D9Z9MdNV.js";import{e as t,r as p}from"./iframe-CAQEOFTZ.js";import{renderElement as l,unmountElement as u}from"./react-18-CfKjugBE.js";import{H as d,A as h,C as E,D as x}from"./blocks-Djlil9Cu.js";import"./index-TKk9mXQ8.js";import"./jsx-runtime-DfLt7GBH.js";var D={code:E,a:h,...d},_=class extends p.Component{constructor(){super(...arguments),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(r){let{showException:e}=this.props;e(r)}render(){let{hasError:r}=this.state,{children:e}=this.props;return r?null:t.createElement(t.Fragment,null,e)}},g=class{constructor(){this.render=async(r,e,n)=>{let s={...D,...e?.components},a=x;return new Promise((m,c)=>{i(async()=>{const{MDXProvider:o}=await import("./index-BQ5W4Lgr.js");return{MDXProvider:o}},__vite__mapDeps([0,1,2]),import.meta.url).then(({MDXProvider:o})=>l(t.createElement(_,{showException:c,key:Math.random()},t.createElement(o,{components:s},t.createElement(a,{context:r,docsParameter:e}))),n)).then(()=>m())})},this.unmount=r=>{u(r)}}};export{g as DocsRenderer,D as defaultComponents};
