import{H as a}from"./Header-qLysd8mS.js";import"./jsx-runtime-DfLt7GBH.js";import"./iframe-CAQEOFTZ.js";import"./preload-helper-D9Z9MdNV.js";import"./Button-Bihu6Z08.js";const{fn:r}=__STORYBOOK_MODULE_TEST__,p={title:"Example/Header",component:a,tags:["autodocs"],parameters:{layout:"fullscreen"},args:{onLogin:r(),onLogout:r(),onCreateAccount:r()}},e={args:{user:{name:"<PERSON>"}}},o={};e.parameters={...e.parameters,docs:{...e.parameters?.docs,source:{originalSource:`{
  args: {
    user: {
      name: '<PERSON>'
    }
  }
}`,...e.parameters?.docs?.source}}};o.parameters={...o.parameters,docs:{...o.parameters?.docs,source:{originalSource:"{}",...o.parameters?.docs?.source}}};const d=["LoggedIn","LoggedOut"];export{e as LoggedIn,o as LoggedOut,d as __namedExportsOrder,p as default};
