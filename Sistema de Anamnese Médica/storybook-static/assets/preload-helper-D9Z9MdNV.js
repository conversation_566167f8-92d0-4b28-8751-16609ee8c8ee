const p="modulepreload",y=function(u,i){return new URL(u,i).href},v={},P=function(i,a,f){let d=Promise.resolve();if(a&&a.length>0){let E=function(e){return Promise.all(e.map(s=>Promise.resolve(s).then(o=>({status:"fulfilled",value:o}),o=>({status:"rejected",reason:o}))))};const r=document.getElementsByTagName("link"),t=document.querySelector("meta[property=csp-nonce]"),m=t?.nonce||t?.getAttribute("nonce");d=E(a.map(e=>{if(e=y(e,f),e in v)return;v[e]=!0;const s=e.endsWith(".css"),o=s?'[rel="stylesheet"]':"";if(!!f)for(let l=r.length-1;l>=0;l--){const c=r[l];if(c.href===e&&(!s||c.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${e}"]${o}`))return;const n=document.createElement("link");if(n.rel=s?"stylesheet":p,s||(n.as="script"),n.crossOrigin="",n.href=e,m&&n.setAttribute("nonce",m),document.head.appendChild(n),s)return new Promise((l,c)=>{n.addEventListener("load",l),n.addEventListener("error",()=>c(new Error(`Unable to preload CSS for ${e}`)))})}))}function h(r){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=r,window.dispatchEvent(t),!t.defaultPrevented)throw r}return d.then(r=>{for(const t of r||[])t.status==="rejected"&&h(t.reason);return i().catch(h)})};export{P as _};
