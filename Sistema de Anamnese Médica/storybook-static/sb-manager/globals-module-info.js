import ESM_COMPAT_Module from "node:module";
import { fileURLToPath as ESM_COMPAT_fileURLToPath } from 'node:url';
import { dirname as ESM_COMPAT_dirname } from 'node:path';
const __filename = ESM_COMPAT_fileURLToPath(import.meta.url);
const __dirname = ESM_COMPAT_dirname(__filename);
const require = ESM_COMPAT_Module.createRequire(import.meta.url);

// src/manager/globals/exports.ts
var n = {
  react: [
    "Children",
    "Component",
    "Fragment",
    "Profiler",
    "PureComponent",
    "StrictMode",
    "Suspense",
    "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED",
    "act",
    "cloneElement",
    "createContext",
    "createElement",
    "createFactory",
    "createRef",
    "forwardRef",
    "isValidElement",
    "lazy",
    "memo",
    "startTransition",
    "unstable_act",
    "useCallback",
    "useContext",
    "useDebugValue",
    "useDeferredValue",
    "useEffect",
    "useId",
    "useImperativeHandle",
    "useInsertionEffect",
    "useLayoutEffect",
    "useMemo",
    "useReducer",
    "useRef",
    "useState",
    "useSyncExternalStore",
    "useTransition",
    "version"
  ],
  "react-dom": [
    "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED",
    "createPortal",
    "createRoot",
    "findDOMNode",
    "flushSync",
    "hydrate",
    "hydrateRoot",
    "render",
    "unmountComponentAtNode",
    "unstable_batchedUpdates",
    "unstable_renderSubtreeIntoContainer",
    "version"
  ],
  "react-dom/client": ["createRoot", "hydrateRoot"],
  "@storybook/icons": [
    "AccessibilityAltIcon",
    "AccessibilityIcon",
    "AccessibilityIgnoredIcon",
    "AddIcon",
    "AdminIcon",
    "AlertAltIcon",
    "AlertIcon",
    "AlignLeftIcon",
    "AlignRightIcon",
    "AppleIcon",
    "ArrowBottomLeftIcon",
    "ArrowBottomRightIcon",
    "ArrowDownIcon",
    "ArrowLeftIcon",
    "ArrowRightIcon",
    "ArrowSolidDownIcon",
    "ArrowSolidLeftIcon",
    "ArrowSolidRightIcon",
    "ArrowSolidUpIcon",
    "ArrowTopLeftIcon",
    "ArrowTopRightIcon",
    "ArrowUpIcon",
    "AzureDevOpsIcon",
    "BackIcon",
    "BasketIcon",
    "BatchAcceptIcon",
    "BatchDenyIcon",
    "BeakerIcon",
    "BellIcon",
    "BitbucketIcon",
    "BoldIcon",
    "BookIcon",
    "BookmarkHollowIcon",
    "BookmarkIcon",
    "BottomBarIcon",
    "BottomBarToggleIcon",
    "BoxIcon",
    "BranchIcon",
    "BrowserIcon",
    "ButtonIcon",
    "CPUIcon",
    "CalendarIcon",
    "CameraIcon",
    "CameraStabilizeIcon",
    "CategoryIcon",
    "CertificateIcon",
    "ChangedIcon",
    "ChatIcon",
    "CheckIcon",
    "ChevronDownIcon",
    "ChevronLeftIcon",
    "ChevronRightIcon",
    "ChevronSmallDownIcon",
    "ChevronSmallLeftIcon",
    "ChevronSmallRightIcon",
    "ChevronSmallUpIcon",
    "ChevronUpIcon",
    "ChromaticIcon",
    "ChromeIcon",
    "CircleHollowIcon",
    "CircleIcon",
    "ClearIcon",
    "CloseAltIcon",
    "CloseIcon",
    "CloudHollowIcon",
    "CloudIcon",
    "CogIcon",
    "CollapseIcon",
    "CommandIcon",
    "CommentAddIcon",
    "CommentIcon",
    "CommentsIcon",
    "CommitIcon",
    "CompassIcon",
    "ComponentDrivenIcon",
    "ComponentIcon",
    "ContrastIcon",
    "ContrastIgnoredIcon",
    "ControlsIcon",
    "CopyIcon",
    "CreditIcon",
    "CrossIcon",
    "DashboardIcon",
    "DatabaseIcon",
    "DeleteIcon",
    "DiamondIcon",
    "DirectionIcon",
    "DiscordIcon",
    "DocChartIcon",
    "DocListIcon",
    "DocumentIcon",
    "DownloadIcon",
    "DragIcon",
    "EditIcon",
    "EllipsisIcon",
    "EmailIcon",
    "ExpandAltIcon",
    "ExpandIcon",
    "EyeCloseIcon",
    "EyeIcon",
    "FaceHappyIcon",
    "FaceNeutralIcon",
    "FaceSadIcon",
    "FacebookIcon",
    "FailedIcon",
    "FastForwardIcon",
    "FigmaIcon",
    "FilterIcon",
    "FlagIcon",
    "FolderIcon",
    "FormIcon",
    "GDriveIcon",
    "GithubIcon",
    "GitlabIcon",
    "GlobeIcon",
    "GoogleIcon",
    "GraphBarIcon",
    "GraphLineIcon",
    "GraphqlIcon",
    "GridAltIcon",
    "GridIcon",
    "GrowIcon",
    "HeartHollowIcon",
    "HeartIcon",
    "HomeIcon",
    "HourglassIcon",
    "InfoIcon",
    "ItalicIcon",
    "JumpToIcon",
    "KeyIcon",
    "LightningIcon",
    "LightningOffIcon",
    "LinkBrokenIcon",
    "LinkIcon",
    "LinkedinIcon",
    "LinuxIcon",
    "ListOrderedIcon",
    "ListUnorderedIcon",
    "LocationIcon",
    "LockIcon",
    "MarkdownIcon",
    "MarkupIcon",
    "MediumIcon",
    "MemoryIcon",
    "MenuIcon",
    "MergeIcon",
    "MirrorIcon",
    "MobileIcon",
    "MoonIcon",
    "NutIcon",
    "OutboxIcon",
    "OutlineIcon",
    "PaintBrushIcon",
    "PaperClipIcon",
    "ParagraphIcon",
    "PassedIcon",
    "PhoneIcon",
    "PhotoDragIcon",
    "PhotoIcon",
    "PhotoStabilizeIcon",
    "PinAltIcon",
    "PinIcon",
    "PlayAllHollowIcon",
    "PlayBackIcon",
    "PlayHollowIcon",
    "PlayIcon",
    "PlayNextIcon",
    "PlusIcon",
    "PointerDefaultIcon",
    "PointerHandIcon",
    "PowerIcon",
    "PrintIcon",
    "ProceedIcon",
    "ProfileIcon",
    "PullRequestIcon",
    "QuestionIcon",
    "RSSIcon",
    "RedirectIcon",
    "ReduxIcon",
    "RefreshIcon",
    "ReplyIcon",
    "RepoIcon",
    "RequestChangeIcon",
    "RewindIcon",
    "RulerIcon",
    "SaveIcon",
    "SearchIcon",
    "ShareAltIcon",
    "ShareIcon",
    "ShieldIcon",
    "SideBySideIcon",
    "SidebarAltIcon",
    "SidebarAltToggleIcon",
    "SidebarIcon",
    "SidebarToggleIcon",
    "SpeakerIcon",
    "StackedIcon",
    "StarHollowIcon",
    "StarIcon",
    "StatusFailIcon",
    "StatusIcon",
    "StatusPassIcon",
    "StatusWarnIcon",
    "StickerIcon",
    "StopAltHollowIcon",
    "StopAltIcon",
    "StopIcon",
    "StorybookIcon",
    "StructureIcon",
    "SubtractIcon",
    "SunIcon",
    "SupportIcon",
    "SweepIcon",
    "SwitchAltIcon",
    "SyncIcon",
    "TabletIcon",
    "ThumbsUpIcon",
    "TimeIcon",
    "TimerIcon",
    "TransferIcon",
    "TrashIcon",
    "TwitterIcon",
    "TypeIcon",
    "UbuntuIcon",
    "UndoIcon",
    "UnfoldIcon",
    "UnlockIcon",
    "UnpinIcon",
    "UploadIcon",
    "UserAddIcon",
    "UserAltIcon",
    "UserIcon",
    "UsersIcon",
    "VSCodeIcon",
    "VerifiedIcon",
    "VideoIcon",
    "WandIcon",
    "WatchIcon",
    "WindowsIcon",
    "WrenchIcon",
    "XIcon",
    "YoutubeIcon",
    "ZoomIcon",
    "ZoomOutIcon",
    "ZoomResetIcon",
    "iconList"
  ],
  "storybook/manager-api": [
    "ActiveTabs",
    "Consumer",
    "ManagerContext",
    "Provider",
    "RequestResponseError",
    "addons",
    "combineParameters",
    "controlOrMetaKey",
    "controlOrMetaSymbol",
    "eventMatchesShortcut",
    "eventToShortcut",
    "experimental_MockUniversalStore",
    "experimental_UniversalStore",
    "experimental_getStatusStore",
    "experimental_getTestProviderStore",
    "experimental_requestResponse",
    "experimental_useStatusStore",
    "experimental_useTestProviderStore",
    "experimental_useUniversalStore",
    "internal_fullStatusStore",
    "internal_fullTestProviderStore",
    "internal_universalStatusStore",
    "internal_universalTestProviderStore",
    "isMacLike",
    "isShortcutTaken",
    "keyToSymbol",
    "merge",
    "mockChannel",
    "optionOrAltSymbol",
    "shortcutMatchesShortcut",
    "shortcutToHumanString",
    "types",
    "useAddonState",
    "useArgTypes",
    "useArgs",
    "useChannel",
    "useGlobalTypes",
    "useGlobals",
    "useParameter",
    "useSharedState",
    "useStoryPrepared",
    "useStorybookApi",
    "useStorybookState"
  ],
  "storybook/theming": [
    "CacheProvider",
    "ClassNames",
    "Global",
    "ThemeProvider",
    "background",
    "color",
    "convert",
    "create",
    "createCache",
    "createGlobal",
    "createReset",
    "css",
    "darken",
    "ensure",
    "ignoreSsrWarning",
    "isPropValid",
    "jsx",
    "keyframes",
    "lighten",
    "styled",
    "themes",
    "typography",
    "useTheme",
    "withTheme"
  ],
  "storybook/theming/create": ["create", "themes"],
  "storybook/test": [
    "buildQueries",
    "clearAllMocks",
    "configure",
    "createEvent",
    "expect",
    "findAllByAltText",
    "findAllByDisplayValue",
    "findAllByLabelText",
    "findAllByPlaceholderText",
    "findAllByRole",
    "findAllByTestId",
    "findAllByText",
    "findAllByTitle",
    "findByAltText",
    "findByDisplayValue",
    "findByLabelText",
    "findByPlaceholderText",
    "findByRole",
    "findByTestId",
    "findByText",
    "findByTitle",
    "fireEvent",
    "fn",
    "getAllByAltText",
    "getAllByDisplayValue",
    "getAllByLabelText",
    "getAllByPlaceholderText",
    "getAllByRole",
    "getAllByTestId",
    "getAllByText",
    "getAllByTitle",
    "getByAltText",
    "getByDisplayValue",
    "getByLabelText",
    "getByPlaceholderText",
    "getByRole",
    "getByTestId",
    "getByText",
    "getByTitle",
    "getConfig",
    "getDefaultNormalizer",
    "getElementError",
    "getNodeText",
    "getQueriesForElement",
    "getRoles",
    "getSuggestedQuery",
    "isInaccessible",
    "isMockFunction",
    "logDOM",
    "logRoles",
    "mocked",
    "mocks",
    "onMockCall",
    "prettyDOM",
    "prettyFormat",
    "queries",
    "queryAllByAltText",
    "queryAllByAttribute",
    "queryAllByDisplayValue",
    "queryAllByLabelText",
    "queryAllByPlaceholderText",
    "queryAllByRole",
    "queryAllByTestId",
    "queryAllByText",
    "queryAllByTitle",
    "queryByAltText",
    "queryByAttribute",
    "queryByDisplayValue",
    "queryByLabelText",
    "queryByPlaceholderText",
    "queryByRole",
    "queryByTestId",
    "queryByText",
    "queryByTitle",
    "queryHelpers",
    "resetAllMocks",
    "restoreAllMocks",
    "sb",
    "screen",
    "spyOn",
    "uninstrumentedUserEvent",
    "userEvent",
    "waitFor",
    "waitForElementToBeRemoved",
    "within"
  ],
  "storybook/internal/channels": [
    "Channel",
    "HEARTBEAT_INTERVAL",
    "HEARTBEAT_MAX_LATENCY",
    "PostMessageTransport",
    "WebsocketTransport",
    "createBrowserChannel"
  ],
  "storybook/internal/client-logger": ["deprecate", "logger", "once", "pretty"],
  "storybook/internal/components": [
    "A",
    "ActionBar",
    "AddonPanel",
    "Badge",
    "Bar",
    "Blockquote",
    "Button",
    "ClipboardCode",
    "Code",
    "DL",
    "Div",
    "DocumentWrapper",
    "EmptyTabContent",
    "ErrorFormatter",
    "FlexBar",
    "Form",
    "H1",
    "H2",
    "H3",
    "H4",
    "H5",
    "H6",
    "HR",
    "IconButton",
    "Img",
    "LI",
    "Link",
    "ListItem",
    "Loader",
    "Modal",
    "OL",
    "P",
    "Placeholder",
    "Pre",
    "ProgressSpinner",
    "ResetWrapper",
    "ScrollArea",
    "Separator",
    "Spaced",
    "Span",
    "StorybookIcon",
    "StorybookLogo",
    "SyntaxHighlighter",
    "TT",
    "TabBar",
    "TabButton",
    "TabWrapper",
    "Table",
    "Tabs",
    "TabsState",
    "TooltipLinkList",
    "TooltipMessage",
    "TooltipNote",
    "UL",
    "WithTooltip",
    "WithTooltipPure",
    "Zoom",
    "codeCommon",
    "components",
    "createCopyToClipboardFunction",
    "getStoryHref",
    "interleaveSeparators",
    "nameSpaceClassNames",
    "resetComponents",
    "withReset"
  ],
  "storybook/internal/core-errors": [
    "ARGTYPES_INFO_REQUEST",
    "ARGTYPES_INFO_RESPONSE",
    "CHANNEL_CREATED",
    "CHANNEL_WS_DISCONNECT",
    "CONFIG_ERROR",
    "CREATE_NEW_STORYFILE_REQUEST",
    "CREATE_NEW_STORYFILE_RESPONSE",
    "CURRENT_STORY_WAS_SET",
    "DOCS_PREPARED",
    "DOCS_RENDERED",
    "FILE_COMPONENT_SEARCH_REQUEST",
    "FILE_COMPONENT_SEARCH_RESPONSE",
    "FORCE_REMOUNT",
    "FORCE_RE_RENDER",
    "GLOBALS_UPDATED",
    "NAVIGATE_URL",
    "PLAY_FUNCTION_THREW_EXCEPTION",
    "PRELOAD_ENTRIES",
    "PREVIEW_BUILDER_PROGRESS",
    "PREVIEW_KEYDOWN",
    "REGISTER_SUBSCRIPTION",
    "REQUEST_WHATS_NEW_DATA",
    "RESET_STORY_ARGS",
    "RESULT_WHATS_NEW_DATA",
    "SAVE_STORY_REQUEST",
    "SAVE_STORY_RESPONSE",
    "SELECT_STORY",
    "SET_CONFIG",
    "SET_CURRENT_STORY",
    "SET_FILTER",
    "SET_GLOBALS",
    "SET_INDEX",
    "SET_STORIES",
    "SET_WHATS_NEW_CACHE",
    "SHARED_STATE_CHANGED",
    "SHARED_STATE_SET",
    "STORIES_COLLAPSE_ALL",
    "STORIES_EXPAND_ALL",
    "STORY_ARGS_UPDATED",
    "STORY_CHANGED",
    "STORY_ERRORED",
    "STORY_FINISHED",
    "STORY_HOT_UPDATED",
    "STORY_INDEX_INVALIDATED",
    "STORY_MISSING",
    "STORY_PREPARED",
    "STORY_RENDERED",
    "STORY_RENDER_PHASE_CHANGED",
    "STORY_SPECIFIED",
    "STORY_THREW_EXCEPTION",
    "STORY_UNCHANGED",
    "TELEMETRY_ERROR",
    "TOGGLE_WHATS_NEW_NOTIFICATIONS",
    "UNHANDLED_ERRORS_WHILE_PLAYING",
    "UPDATE_GLOBALS",
    "UPDATE_QUERY_PARAMS",
    "UPDATE_STORY_ARGS"
  ],
  "storybook/internal/core-events": [
    "ARGTYPES_INFO_REQUEST",
    "ARGTYPES_INFO_RESPONSE",
    "CHANNEL_CREATED",
    "CHANNEL_WS_DISCONNECT",
    "CONFIG_ERROR",
    "CREATE_NEW_STORYFILE_REQUEST",
    "CREATE_NEW_STORYFILE_RESPONSE",
    "CURRENT_STORY_WAS_SET",
    "DOCS_PREPARED",
    "DOCS_RENDERED",
    "FILE_COMPONENT_SEARCH_REQUEST",
    "FILE_COMPONENT_SEARCH_RESPONSE",
    "FORCE_REMOUNT",
    "FORCE_RE_RENDER",
    "GLOBALS_UPDATED",
    "NAVIGATE_URL",
    "PLAY_FUNCTION_THREW_EXCEPTION",
    "PRELOAD_ENTRIES",
    "PREVIEW_BUILDER_PROGRESS",
    "PREVIEW_KEYDOWN",
    "REGISTER_SUBSCRIPTION",
    "REQUEST_WHATS_NEW_DATA",
    "RESET_STORY_ARGS",
    "RESULT_WHATS_NEW_DATA",
    "SAVE_STORY_REQUEST",
    "SAVE_STORY_RESPONSE",
    "SELECT_STORY",
    "SET_CONFIG",
    "SET_CURRENT_STORY",
    "SET_FILTER",
    "SET_GLOBALS",
    "SET_INDEX",
    "SET_STORIES",
    "SET_WHATS_NEW_CACHE",
    "SHARED_STATE_CHANGED",
    "SHARED_STATE_SET",
    "STORIES_COLLAPSE_ALL",
    "STORIES_EXPAND_ALL",
    "STORY_ARGS_UPDATED",
    "STORY_CHANGED",
    "STORY_ERRORED",
    "STORY_FINISHED",
    "STORY_HOT_UPDATED",
    "STORY_INDEX_INVALIDATED",
    "STORY_MISSING",
    "STORY_PREPARED",
    "STORY_RENDERED",
    "STORY_RENDER_PHASE_CHANGED",
    "STORY_SPECIFIED",
    "STORY_THREW_EXCEPTION",
    "STORY_UNCHANGED",
    "TELEMETRY_ERROR",
    "TOGGLE_WHATS_NEW_NOTIFICATIONS",
    "UNHANDLED_ERRORS_WHILE_PLAYING",
    "UPDATE_GLOBALS",
    "UPDATE_QUERY_PARAMS",
    "UPDATE_STORY_ARGS"
  ],
  "storybook/internal/manager-errors": [
    "Category",
    "ProviderDoesNotExtendBaseProviderError",
    "StatusTypeIdMismatchError",
    "UncaughtManagerError"
  ],
  "storybook/internal/router": [
    "BaseLocationProvider",
    "DEEPLY_EQUAL",
    "Link",
    "Location",
    "LocationProvider",
    "Match",
    "Route",
    "buildArgsParam",
    "deepDiff",
    "getMatch",
    "parsePath",
    "queryFromLocation",
    "stringifyQuery",
    "useNavigate"
  ],
  "storybook/internal/types": ["Addon_TypesEnum"],
  "storybook/internal/manager-api": [
    "ActiveTabs",
    "Consumer",
    "ManagerContext",
    "Provider",
    "RequestResponseError",
    "addons",
    "combineParameters",
    "controlOrMetaKey",
    "controlOrMetaSymbol",
    "eventMatchesShortcut",
    "eventToShortcut",
    "experimental_MockUniversalStore",
    "experimental_UniversalStore",
    "experimental_getStatusStore",
    "experimental_getTestProviderStore",
    "experimental_requestResponse",
    "experimental_useStatusStore",
    "experimental_useTestProviderStore",
    "experimental_useUniversalStore",
    "internal_fullStatusStore",
    "internal_fullTestProviderStore",
    "internal_universalStatusStore",
    "internal_universalTestProviderStore",
    "isMacLike",
    "isShortcutTaken",
    "keyToSymbol",
    "merge",
    "mockChannel",
    "optionOrAltSymbol",
    "shortcutMatchesShortcut",
    "shortcutToHumanString",
    "types",
    "useAddonState",
    "useArgTypes",
    "useArgs",
    "useChannel",
    "useGlobalTypes",
    "useGlobals",
    "useParameter",
    "useSharedState",
    "useStoryPrepared",
    "useStorybookApi",
    "useStorybookState"
  ],
  "storybook/internal/theming": [
    "CacheProvider",
    "ClassNames",
    "Global",
    "ThemeProvider",
    "background",
    "color",
    "convert",
    "create",
    "createCache",
    "createGlobal",
    "createReset",
    "css",
    "darken",
    "ensure",
    "ignoreSsrWarning",
    "isPropValid",
    "jsx",
    "keyframes",
    "lighten",
    "styled",
    "themes",
    "typography",
    "useTheme",
    "withTheme"
  ],
  "storybook/internal/theming/create": ["create", "themes"]
};

// src/manager/globals/globals.ts
var o = {
  react: "__REACT__",
  "react-dom": "__REACT_DOM__",
  "react-dom/client": "__REACT_DOM_CLIENT__",
  "@storybook/icons": "__STORYBOOK_ICONS__",
  "storybook/manager-api": "__STORYBOOK_API__",
  "storybook/test": "__STORYBOOK_TEST__",
  "storybook/theming": "__STORYBOOK_THEMING__",
  "storybook/theming/create": "__STORYBOOK_THEMING_CREATE__",
  "storybook/internal/channels": "__STORYBOOK_CHANNELS__",
  "storybook/internal/client-logger": "__STORYBOOK_CLIENT_LOGGER__",
  "storybook/internal/components": "__STORYBOOK_COMPONENTS__",
  "storybook/internal/core-errors": "__STORYBOOK_CORE_EVENTS__",
  "storybook/internal/core-events": "__STORYBOOK_CORE_EVENTS__",
  "storybook/internal/manager-errors": "__STORYBOOK_CORE_EVENTS_MANAGER_ERRORS__",
  "storybook/internal/router": "__STORYBOOK_ROUTER__",
  "storybook/internal/types": "__STORYBOOK_TYPES__",
  // @deprecated TODO: delete in 9.1
  "storybook/internal/manager-api": "__STORYBOOK_API__",
  "storybook/internal/theming": "__STORYBOOK_THEMING__",
  "storybook/internal/theming/create": "__STORYBOOK_THEMING_CREATE__"
}, r = Object.keys(o);

// src/manager/globals/globals-module-info.ts
var E = r.reduce(
  (t, e) => (t[e] = {
    type: "esm",
    varName: o[e],
    namedExports: n[e],
    defaultExport: !0
  }, t),
  {}
);
export {
  E as globalsModuleInfoMap
};
