/*! tailwindcss v4.1.3 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x: 0;--tw-translate-y: 0;--tw-translate-z: 0;--tw-rotate-x: rotateX(0);--tw-rotate-y: rotateY(0);--tw-rotate-z: rotateZ(0);--tw-skew-x: skewX(0);--tw-skew-y: skewY(0);--tw-space-y-reverse: 0;--tw-space-x-reverse: 0;--tw-border-style: solid;--tw-gradient-position: initial;--tw-gradient-from: #0000;--tw-gradient-via: #0000;--tw-gradient-to: #0000;--tw-gradient-stops: initial;--tw-gradient-via-stops: initial;--tw-gradient-from-position: 0%;--tw-gradient-via-position: 50%;--tw-gradient-to-position: 100%;--tw-leading: initial;--tw-font-weight: initial;--tw-tracking: initial;--tw-ordinal: initial;--tw-slashed-zero: initial;--tw-numeric-figure: initial;--tw-numeric-spacing: initial;--tw-numeric-fraction: initial;--tw-shadow: 0 0 #0000;--tw-shadow-color: initial;--tw-shadow-alpha: 100%;--tw-inset-shadow: 0 0 #0000;--tw-inset-shadow-color: initial;--tw-inset-shadow-alpha: 100%;--tw-ring-color: initial;--tw-ring-shadow: 0 0 #0000;--tw-inset-ring-color: initial;--tw-inset-ring-shadow: 0 0 #0000;--tw-ring-inset: initial;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-offset-shadow: 0 0 #0000;--tw-outline-style: solid;--tw-blur: initial;--tw-brightness: initial;--tw-contrast: initial;--tw-grayscale: initial;--tw-hue-rotate: initial;--tw-invert: initial;--tw-opacity: initial;--tw-saturate: initial;--tw-sepia: initial;--tw-drop-shadow: initial;--tw-drop-shadow-color: initial;--tw-drop-shadow-alpha: 100%;--tw-drop-shadow-size: initial;--tw-backdrop-blur: initial;--tw-backdrop-brightness: initial;--tw-backdrop-contrast: initial;--tw-backdrop-grayscale: initial;--tw-backdrop-hue-rotate: initial;--tw-backdrop-invert: initial;--tw-backdrop-opacity: initial;--tw-backdrop-saturate: initial;--tw-backdrop-sepia: initial;--tw-duration: initial;--tw-ease: initial;--tw-scale-x: 1;--tw-scale-y: 1;--tw-scale-z: 1;--tw-content: ""}}}@layer theme{:root,:host{--font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";--font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;--color-red-50: oklch(.971 .013 17.38);--color-red-100: oklch(.936 .032 17.717);--color-red-200: oklch(.885 .062 18.334);--color-red-300: oklch(.808 .114 19.571);--color-red-400: oklch(.704 .191 22.216);--color-red-500: oklch(.637 .237 25.331);--color-red-600: oklch(.577 .245 27.325);--color-red-700: oklch(.505 .213 27.518);--color-red-800: oklch(.444 .177 26.899);--color-red-900: oklch(.396 .141 25.723);--color-red-950: oklch(.258 .092 26.042);--color-orange-50: oklch(.98 .016 73.684);--color-orange-100: oklch(.954 .038 75.164);--color-orange-200: oklch(.901 .076 70.697);--color-orange-300: oklch(.837 .128 66.29);--color-orange-500: oklch(.705 .213 47.604);--color-orange-600: oklch(.646 .222 41.116);--color-orange-700: oklch(.553 .195 38.402);--color-orange-800: oklch(.47 .157 37.304);--color-orange-900: oklch(.408 .123 38.172);--color-orange-950: oklch(.266 .079 36.259);--color-amber-50: oklch(.987 .022 95.277);--color-amber-100: oklch(.962 .059 95.617);--color-amber-200: oklch(.924 .12 95.746);--color-amber-300: oklch(.879 .169 91.605);--color-amber-400: oklch(.828 .189 84.429);--color-amber-500: oklch(.769 .188 70.08);--color-amber-600: oklch(.666 .179 58.318);--color-amber-700: oklch(.555 .163 48.998);--color-amber-800: oklch(.473 .137 46.201);--color-amber-900: oklch(.414 .112 45.904);--color-amber-950: oklch(.279 .077 45.635);--color-yellow-50: oklch(.987 .026 102.212);--color-yellow-100: oklch(.973 .071 103.193);--color-yellow-200: oklch(.945 .129 101.54);--color-yellow-300: oklch(.905 .182 98.111);--color-yellow-500: oklch(.795 .184 86.047);--color-yellow-600: oklch(.681 .162 75.834);--color-yellow-700: oklch(.554 .135 66.442);--color-yellow-800: oklch(.476 .114 61.907);--color-yellow-900: oklch(.421 .095 57.708);--color-yellow-950: oklch(.286 .066 53.813);--color-green-50: oklch(.982 .018 155.826);--color-green-100: oklch(.962 .044 156.743);--color-green-200: oklch(.925 .084 155.995);--color-green-300: oklch(.871 .15 154.449);--color-green-400: oklch(.792 .209 151.711);--color-green-500: oklch(.723 .219 149.579);--color-green-600: oklch(.627 .194 149.214);--color-green-700: oklch(.527 .154 150.069);--color-green-800: oklch(.448 .119 151.328);--color-green-900: oklch(.393 .095 152.535);--color-green-950: oklch(.266 .065 152.934);--color-emerald-50: oklch(.979 .021 166.113);--color-emerald-200: oklch(.905 .093 164.15);--color-emerald-500: oklch(.696 .17 162.48);--color-emerald-600: oklch(.596 .145 163.225);--color-emerald-800: oklch(.432 .095 166.913);--color-emerald-900: oklch(.378 .077 168.94);--color-blue-50: oklch(.97 .014 254.604);--color-blue-100: oklch(.932 .032 255.585);--color-blue-200: oklch(.882 .059 254.128);--color-blue-300: oklch(.809 .105 251.813);--color-blue-400: oklch(.707 .165 254.624);--color-blue-500: oklch(.623 .214 259.815);--color-blue-600: oklch(.546 .245 262.881);--color-blue-700: oklch(.488 .243 264.376);--color-blue-800: oklch(.424 .199 265.638);--color-blue-900: oklch(.379 .146 265.522);--color-blue-950: oklch(.282 .091 267.935);--color-indigo-50: oklch(.962 .018 272.314);--color-indigo-300: oklch(.785 .115 274.713);--color-indigo-500: oklch(.585 .233 277.117);--color-indigo-600: oklch(.511 .262 276.966);--color-indigo-700: oklch(.457 .24 277.023);--color-indigo-900: oklch(.359 .144 278.697);--color-purple-50: oklch(.977 .014 308.299);--color-purple-100: oklch(.946 .033 307.174);--color-purple-200: oklch(.902 .063 306.703);--color-purple-500: oklch(.627 .265 303.9);--color-purple-600: oklch(.558 .288 302.321);--color-purple-800: oklch(.438 .218 303.724);--color-purple-900: oklch(.381 .176 304.987);--color-purple-950: oklch(.291 .149 302.717);--color-pink-100: oklch(.948 .028 342.258);--color-pink-400: oklch(.718 .202 349.761);--color-pink-600: oklch(.592 .249 .584);--color-pink-800: oklch(.459 .187 3.815);--color-slate-50: oklch(.984 .003 247.858);--color-slate-100: oklch(.968 .007 247.896);--color-slate-200: oklch(.929 .013 255.508);--color-slate-300: oklch(.869 .022 252.894);--color-slate-400: oklch(.704 .04 256.788);--color-slate-500: oklch(.554 .046 257.417);--color-slate-600: oklch(.446 .043 257.281);--color-slate-700: oklch(.372 .044 257.287);--color-slate-800: oklch(.279 .041 260.031);--color-slate-900: oklch(.208 .042 265.755);--color-gray-50: oklch(.985 .002 247.839);--color-gray-100: oklch(.967 .003 264.542);--color-gray-200: oklch(.928 .006 264.531);--color-gray-300: oklch(.872 .01 258.338);--color-gray-400: oklch(.707 .022 261.325);--color-gray-500: oklch(.551 .027 264.364);--color-gray-600: oklch(.446 .03 256.802);--color-gray-700: oklch(.373 .034 259.733);--color-gray-800: oklch(.278 .033 256.848);--color-gray-900: oklch(.21 .034 264.665);--color-gray-950: oklch(.13 .028 261.692);--color-black: #000;--color-white: #fff;--spacing: .25rem;--breakpoint-2xl: 96rem;--container-xs: 20rem;--container-sm: 24rem;--container-md: 28rem;--container-lg: 32rem;--container-2xl: 42rem;--container-3xl: 48rem;--container-4xl: 56rem;--container-7xl: 80rem;--text-xs: .75rem;--text-xs--line-height: calc(1 / .75);--text-sm: .875rem;--text-sm--line-height: calc(1.25 / .875);--text-base: 1rem;--text-base--line-height: 1.5 ;--text-lg: 1.125rem;--text-lg--line-height: calc(1.75 / 1.125);--text-xl: 1.25rem;--text-xl--line-height: calc(1.75 / 1.25);--text-2xl: 1.5rem;--text-2xl--line-height: calc(2 / 1.5);--text-3xl: 1.875rem;--text-3xl--line-height: 1.2 ;--text-6xl: 3.75rem;--text-6xl--line-height: 1;--font-weight-normal: 400;--font-weight-medium: 500;--font-weight-semibold: 600;--font-weight-bold: 700;--tracking-tight: -.025em;--tracking-wide: .025em;--tracking-wider: .05em;--tracking-widest: .1em;--leading-tight: 1.25;--leading-relaxed: 1.625;--radius-xs: .125rem;--radius-lg: var(--radius);--radius-2xl: 1rem;--radius-3xl: 1.5rem;--shadow-sm: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a;--shadow-md: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;--shadow-lg: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;--shadow-xl: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;--ease-out: cubic-bezier(0, 0, .2, 1);--ease-in-out: cubic-bezier(.4, 0, .2, 1);--animate-spin: spin 1s linear infinite;--animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;--blur-sm: 8px;--blur-md: 12px;--blur-xl: 24px;--aspect-video: 16 / 9;--default-transition-duration: .15s;--default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);--default-font-family: var(--font-sans);--default-font-feature-settings: var(--font-sans--font-feature-settings);--default-font-variation-settings: var(--font-sans--font-variation-settings);--default-mono-font-family: var(--font-mono);--default-mono-font-feature-settings: var(--font-mono--font-feature-settings);--default-mono-font-variation-settings: var(--font-mono--font-variation-settings);--color-primary: var(--primary);--color-border: var(--border)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings, normal);font-variation-settings:var(--default-font-variation-settings, normal);-webkit-tap-highlight-color:transparent}body{line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);font-feature-settings:var(--default-mono-font-feature-settings, normal);font-variation-settings:var(--default-mono-font-variation-settings, normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1;color:currentColor}@supports (color: color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentColor 50%,transparent)}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}body{background-color:var(--background);color:var(--foreground)}*{border-color:var(--border);outline-color:var(--ring)}@supports (color: color-mix(in lab,red,red)){*{outline-color:color-mix(in oklab,var(--ring) 50%,transparent)}}body{background-color:var(--background);color:var(--foreground);font-family:var(--font-family-base);font-size:var(--font-size);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"kern" 1,"liga" 1,"calt" 1;background:var(--bg-primary);line-height:1.5}.using-system-fonts{--font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif}.using-inter-font{--font-family-base: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif}@font-face{font-family:Inter-Optimized;font-style:normal;font-weight:400 700;font-display:swap;src:local(Inter),local(Inter-Regular);unicode-range:U+??,U+131,U+152-153,U+2BB-2BC,U+2C6,U+2DA,U+2DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}*{transition-property:background-color,border-color,color,fill,stroke,opacity,box-shadow,transform;transition-duration:var(--openai-duration-normal);transition-timing-function:var(--openai-ease-in-out)}.app-container{background:var(--bg-primary);height:100vh;font-family:var(--font-family-base);font-size:var(--font-size);color:var(--text-primary);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;line-height:1.5;display:flex}.grid-container{max-width:var(--grid-container-max-width);padding:0 var(--grid-container-padding);margin:0 auto}.grid{grid-template-columns:repeat(var(--grid-columns),1fr);gap:var(--grid-gap);display:grid}.grid-gap-sm{gap:var(--grid-gap-sm)}.grid-gap-lg{gap:var(--grid-gap-lg)}.col-1{grid-column:span 1}.col-2{grid-column:span 2}.col-3{grid-column:span 3}.col-4{grid-column:span 4}.col-5{grid-column:span 5}.col-6{grid-column:span 6}.col-7{grid-column:span 7}.col-8{grid-column:span 8}.col-9{grid-column:span 9}.col-10{grid-column:span 10}.col-11{grid-column:span 11}.col-12{grid-column:span 12}@media (width <= 768px){.col-1,.col-2,.col-3,.col-4,.col-5,.col-6,.col-7,.col-8,.col-9,.col-10,.col-11,.col-12{grid-column:span 12}}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) h1{color:var(--text-primary);font-size:32px;font-weight:700;line-height:1.2;font-family:var(--font-family-heading)}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) h2{color:var(--text-primary);font-size:28px;font-weight:600;line-height:1.3;font-family:var(--font-family-heading)}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) h3{color:var(--text-primary);font-size:24px;font-weight:600;line-height:1.3;font-family:var(--font-family-heading)}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) h4{color:var(--text-primary);font-size:20px;font-weight:600;line-height:1.4;font-family:var(--font-family-heading)}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) h5{color:var(--text-primary);font-size:16px;font-weight:600;line-height:1.4;font-family:var(--font-family-heading)}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) h6{color:var(--text-primary);font-size:14px;font-weight:600;line-height:1.4;font-family:var(--font-family-heading)}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) p{font-size:var(--font-size);font-weight:var(--font-weight-normal);color:var(--text-secondary);line-height:1.6}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) label{font-size:var(--font-size);font-weight:var(--font-weight-medium);color:var(--text-primary);line-height:1.5}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) button{font-size:var(--font-size);font-weight:var(--font-weight-medium);line-height:1.5;font-family:var(--font-family-base)}:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) input,:where(:not(:has([class*=" text-"]),:not(:has([class^=text-])))) textarea{font-size:var(--font-size);font-weight:var(--font-weight-normal);line-height:1.5;font-family:var(--font-family-base)}}@layer utilities{.\@container\/card-header{container:card-header / inline-size}.pointer-events-auto{pointer-events:auto}.pointer-events-none{pointer-events:none}.collapse{visibility:collapse}.invisible{visibility:hidden}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.inset-0{inset:calc(var(--spacing) * 0)}.inset-x-0{inset-inline:calc(var(--spacing) * 0)}.inset-y-0{inset-block:calc(var(--spacing) * 0)}.-top-0\.5{top:calc(var(--spacing) * -.5)}.-top-12{top:calc(var(--spacing) * -12)}.top-0{top:calc(var(--spacing) * 0)}.top-1\.5{top:calc(var(--spacing) * 1.5)}.top-1\/2{top:50%}.top-3{top:calc(var(--spacing) * 3)}.top-3\.5{top:calc(var(--spacing) * 3.5)}.top-4{top:calc(var(--spacing) * 4)}.top-6{top:calc(var(--spacing) * 6)}.top-\[1px\]{top:1px}.top-\[50\%\]{top:50%}.top-\[60\%\]{top:60%}.top-full{top:100%}.-right-0\.5{right:calc(var(--spacing) * -.5)}.-right-1{right:calc(var(--spacing) * -1)}.-right-4{right:calc(var(--spacing) * -4)}.-right-12{right:calc(var(--spacing) * -12)}.right-0{right:calc(var(--spacing) * 0)}.right-1{right:calc(var(--spacing) * 1)}.right-2{right:calc(var(--spacing) * 2)}.right-3{right:calc(var(--spacing) * 3)}.right-4{right:calc(var(--spacing) * 4)}.-bottom-1{bottom:calc(var(--spacing) * -1)}.-bottom-12{bottom:calc(var(--spacing) * -12)}.bottom-0{bottom:calc(var(--spacing) * 0)}.bottom-2{bottom:calc(var(--spacing) * 2)}.bottom-4{bottom:calc(var(--spacing) * 4)}.bottom-20{bottom:calc(var(--spacing) * 20)}.bottom-full{bottom:100%}.-left-4{left:calc(var(--spacing) * -4)}.-left-12{left:calc(var(--spacing) * -12)}.left-0{left:calc(var(--spacing) * 0)}.left-1{left:calc(var(--spacing) * 1)}.left-1\/2{left:50%}.left-2{left:calc(var(--spacing) * 2)}.left-3{left:calc(var(--spacing) * 3)}.left-4{left:calc(var(--spacing) * 4)}.left-6{left:calc(var(--spacing) * 6)}.left-\[50\%\]{left:50%}.left-full{left:100%}.isolate{isolation:isolate}.z-10{z-index:10}.z-20{z-index:20}.z-40{z-index:40}.z-50{z-index:50}.z-\[1\]{z-index:1}.order-1{order:1}.col-start-2{grid-column-start:2}.row-span-2{grid-row:span 2 / span 2}.row-start-1{grid-row-start:1}.container{width:100%}@media (width >= 40rem){.container{max-width:40rem}}@media (width >= 48rem){.container{max-width:48rem}}@media (width >= 64rem){.container{max-width:64rem}}@media (width >= 80rem){.container{max-width:80rem}}@media (width >= 96rem){.container{max-width:96rem}}.-mx-1{margin-inline:calc(var(--spacing) * -1)}.mx-2{margin-inline:calc(var(--spacing) * 2)}.mx-3\.5{margin-inline:calc(var(--spacing) * 3.5)}.mx-auto{margin-inline:auto}.my-0\.5{margin-block:calc(var(--spacing) * .5)}.my-1{margin-block:calc(var(--spacing) * 1)}.my-6{margin-block:calc(var(--spacing) * 6)}.-mt-4{margin-top:calc(var(--spacing) * -4)}.mt-0\.5{margin-top:calc(var(--spacing) * .5)}.mt-1{margin-top:calc(var(--spacing) * 1)}.mt-1\.5{margin-top:calc(var(--spacing) * 1.5)}.mt-2{margin-top:calc(var(--spacing) * 2)}.mt-3{margin-top:calc(var(--spacing) * 3)}.mt-4{margin-top:calc(var(--spacing) * 4)}.mt-6{margin-top:calc(var(--spacing) * 6)}.mt-8{margin-top:calc(var(--spacing) * 8)}.mt-auto{margin-top:auto}.mr-1{margin-right:calc(var(--spacing) * 1)}.mr-2{margin-right:calc(var(--spacing) * 2)}.mb-0\.5{margin-bottom:calc(var(--spacing) * .5)}.mb-1{margin-bottom:calc(var(--spacing) * 1)}.mb-2{margin-bottom:calc(var(--spacing) * 2)}.mb-3{margin-bottom:calc(var(--spacing) * 3)}.mb-4{margin-bottom:calc(var(--spacing) * 4)}.mb-6{margin-bottom:calc(var(--spacing) * 6)}.-ml-4{margin-left:calc(var(--spacing) * -4)}.ml-1{margin-left:calc(var(--spacing) * 1)}.ml-2{margin-left:calc(var(--spacing) * 2)}.ml-3{margin-left:calc(var(--spacing) * 3)}.ml-auto{margin-left:auto}.line-clamp-1{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-2{-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.table{display:table}.table-caption{display:table-caption}.table-cell{display:table-cell}.table-row{display:table-row}.field-sizing-content{field-sizing:content}.aspect-square{aspect-ratio:1}.aspect-video{aspect-ratio:var(--aspect-video)}.size-2{width:calc(var(--spacing) * 2);height:calc(var(--spacing) * 2)}.size-2\.5{width:calc(var(--spacing) * 2.5);height:calc(var(--spacing) * 2.5)}.size-3{width:calc(var(--spacing) * 3);height:calc(var(--spacing) * 3)}.size-3\.5{width:calc(var(--spacing) * 3.5);height:calc(var(--spacing) * 3.5)}.size-4{width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.size-7{width:calc(var(--spacing) * 7);height:calc(var(--spacing) * 7)}.size-8{width:calc(var(--spacing) * 8);height:calc(var(--spacing) * 8)}.size-9{width:calc(var(--spacing) * 9);height:calc(var(--spacing) * 9)}.size-10{width:calc(var(--spacing) * 10);height:calc(var(--spacing) * 10)}.size-full{width:100%;height:100%}.h-0{height:calc(var(--spacing) * 0)}.h-0\.5{height:calc(var(--spacing) * .5)}.h-1{height:calc(var(--spacing) * 1)}.h-1\.5{height:calc(var(--spacing) * 1.5)}.h-2{height:calc(var(--spacing) * 2)}.h-2\.5{height:calc(var(--spacing) * 2.5)}.h-3{height:calc(var(--spacing) * 3)}.h-4{height:calc(var(--spacing) * 4)}.h-5{height:calc(var(--spacing) * 5)}.h-6{height:calc(var(--spacing) * 6)}.h-7{height:calc(var(--spacing) * 7)}.h-8{height:calc(var(--spacing) * 8)}.h-9{height:calc(var(--spacing) * 9)}.h-10{height:calc(var(--spacing) * 10)}.h-12{height:calc(var(--spacing) * 12)}.h-14{height:calc(var(--spacing) * 14)}.h-16{height:calc(var(--spacing) * 16)}.h-20{height:calc(var(--spacing) * 20)}.h-64{height:calc(var(--spacing) * 64)}.h-\[1\.15rem\]{height:1.15rem}.h-\[calc\(100\%-1px\)\]{height:calc(100% - 1px)}.h-\[var\(--radix-navigation-menu-viewport-height\)\]{height:var(--radix-navigation-menu-viewport-height)}.h-\[var\(--radix-select-trigger-height\)\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.h-svh{height:100svh}.max-h-\(--radix-context-menu-content-available-height\){max-height:var(--radix-context-menu-content-available-height)}.max-h-\(--radix-dropdown-menu-content-available-height\){max-height:var(--radix-dropdown-menu-content-available-height)}.max-h-\(--radix-select-content-available-height\){max-height:var(--radix-select-content-available-height)}.max-h-96{max-height:calc(var(--spacing) * 96)}.max-h-\[80vh\]{max-height:80vh}.max-h-\[300px\]{max-height:300px}.max-h-\[500px\]{max-height:500px}.min-h-0{min-height:calc(var(--spacing) * 0)}.min-h-4{min-height:calc(var(--spacing) * 4)}.min-h-16{min-height:calc(var(--spacing) * 16)}.min-h-\[80px\]{min-height:80px}.min-h-\[300px\]{min-height:300px}.min-h-screen{min-height:100vh}.min-h-svh{min-height:100svh}.w-\(--sidebar-width\){width:var(--sidebar-width)}.w-0{width:calc(var(--spacing) * 0)}.w-0\.5{width:calc(var(--spacing) * .5)}.w-1{width:calc(var(--spacing) * 1)}.w-1\.5{width:calc(var(--spacing) * 1.5)}.w-2{width:calc(var(--spacing) * 2)}.w-2\.5{width:calc(var(--spacing) * 2.5)}.w-3{width:calc(var(--spacing) * 3)}.w-3\/4{width:75%}.w-4{width:calc(var(--spacing) * 4)}.w-5{width:calc(var(--spacing) * 5)}.w-6{width:calc(var(--spacing) * 6)}.w-7{width:calc(var(--spacing) * 7)}.w-8{width:calc(var(--spacing) * 8)}.w-9{width:calc(var(--spacing) * 9)}.w-10{width:calc(var(--spacing) * 10)}.w-12{width:calc(var(--spacing) * 12)}.w-16{width:calc(var(--spacing) * 16)}.w-32{width:calc(var(--spacing) * 32)}.w-40{width:calc(var(--spacing) * 40)}.w-48{width:calc(var(--spacing) * 48)}.w-64{width:calc(var(--spacing) * 64)}.w-72{width:calc(var(--spacing) * 72)}.w-80{width:calc(var(--spacing) * 80)}.w-96{width:calc(var(--spacing) * 96)}.w-\[100px\]{width:100px}.w-auto{width:auto}.w-fit{width:fit-content}.w-full{width:100%}.w-max{width:max-content}.w-px{width:1px}.max-w-\(--skeleton-width\){max-width:var(--skeleton-width)}.max-w-2xl{max-width:var(--container-2xl)}.max-w-3xl{max-width:var(--container-3xl)}.max-w-4xl{max-width:var(--container-4xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-\[80px\]{max-width:80px}.max-w-\[calc\(100\%-2rem\)\]{max-width:calc(100% - 2rem)}.max-w-max{max-width:max-content}.max-w-md{max-width:var(--container-md)}.max-w-screen-2xl{max-width:var(--breakpoint-2xl)}.max-w-sm{max-width:var(--container-sm)}.max-w-xs{max-width:var(--container-xs)}.min-w-0{min-width:calc(var(--spacing) * 0)}.min-w-5{min-width:calc(var(--spacing) * 5)}.min-w-8{min-width:calc(var(--spacing) * 8)}.min-w-9{min-width:calc(var(--spacing) * 9)}.min-w-10{min-width:calc(var(--spacing) * 10)}.min-w-\[8rem\]{min-width:8rem}.min-w-\[12rem\]{min-width:12rem}.min-w-\[var\(--radix-select-trigger-width\)\]{min-width:var(--radix-select-trigger-width)}.flex-1{flex:1}.flex-shrink-0,.shrink-0{flex-shrink:0}.grow{flex-grow:1}.grow-0{flex-grow:0}.basis-full{flex-basis:100%}.caption-bottom{caption-side:bottom}.border-collapse{border-collapse:collapse}.origin-\(--radix-context-menu-content-transform-origin\){transform-origin:var(--radix-context-menu-content-transform-origin)}.origin-\(--radix-dropdown-menu-content-transform-origin\){transform-origin:var(--radix-dropdown-menu-content-transform-origin)}.origin-\(--radix-hover-card-content-transform-origin\){transform-origin:var(--radix-hover-card-content-transform-origin)}.origin-\(--radix-menubar-content-transform-origin\){transform-origin:var(--radix-menubar-content-transform-origin)}.origin-\(--radix-popover-content-transform-origin\){transform-origin:var(--radix-popover-content-transform-origin)}.origin-\(--radix-select-content-transform-origin\){transform-origin:var(--radix-select-content-transform-origin)}.origin-\(--radix-tooltip-content-transform-origin\){transform-origin:var(--radix-tooltip-content-transform-origin)}.-translate-x-1\/2{--tw-translate-x: -50% ;translate:var(--tw-translate-x) var(--tw-translate-y)}.-translate-x-full{--tw-translate-x: -100%;translate:var(--tw-translate-x) var(--tw-translate-y)}.-translate-x-px{--tw-translate-x: -1px;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-x-0{--tw-translate-x: calc(var(--spacing) * 0);translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-x-\[-50\%\]{--tw-translate-x: -50%;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-x-full{--tw-translate-x: 100%;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-x-px{--tw-translate-x: 1px;translate:var(--tw-translate-x) var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y: -50% ;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-y-0\.5{--tw-translate-y: calc(var(--spacing) * .5);translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-y-\[-50\%\]{--tw-translate-y: -50%;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-y-\[calc\(-50\%_-_2px\)\]{--tw-translate-y: calc(-50% - 2px) ;translate:var(--tw-translate-x) var(--tw-translate-y)}.rotate-45{rotate:45deg}.rotate-90{rotate:90deg}.transform{transform:var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y)}.animate-caret-blink{animation:1.25s ease-out infinite caret-blink}.animate-in{animation:enter var(--tw-duration, .15s) var(--tw-ease, ease)}.animate-pulse{animation:var(--animate-pulse)}.animate-spin{animation:var(--animate-spin)}.cursor-col-resize{cursor:col-resize}.cursor-default{cursor:default}.cursor-move{cursor:move}.cursor-not-allowed{cursor:not-allowed}.cursor-pointer{cursor:pointer}.touch-manipulation{touch-action:manipulation}.touch-none{touch-action:none}.resize{resize:both}.resize-none{resize:none}.scroll-my-1{scroll-margin-block:calc(var(--spacing) * 1)}.scroll-py-1{scroll-padding-block:calc(var(--spacing) * 1)}.list-none{list-style-type:none}.auto-rows-min{grid-auto-rows:min-content}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.grid-cols-\[0_1fr\]{grid-template-columns:0 1fr}.grid-rows-\[auto_auto\]{grid-template-rows:auto auto}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.justify-items-start{justify-items:start}.gap-1{gap:calc(var(--spacing) * 1)}.gap-1\.5{gap:calc(var(--spacing) * 1.5)}.gap-2{gap:calc(var(--spacing) * 2)}.gap-3{gap:calc(var(--spacing) * 3)}.gap-4{gap:calc(var(--spacing) * 4)}.gap-6{gap:calc(var(--spacing) * 6)}:where(.space-y-0>:not(:last-child)){--tw-space-y-reverse: 0;margin-block-start:calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse: 0;margin-block-start:calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse: 0;margin-block-start:calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse: 0;margin-block-start:calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse: 0;margin-block-start:calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse: 0;margin-block-start:calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-1>:not(:last-child)){--tw-space-x-reverse: 0;margin-inline-start:calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse: 0;margin-inline-start:calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-3>:not(:last-child)){--tw-space-x-reverse: 0;margin-inline-start:calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)))}.gap-y-0\.5{row-gap:calc(var(--spacing) * .5)}.self-start{align-self:flex-start}.justify-self-end{justify-self:flex-end}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:.25rem}.rounded-2xl{border-radius:var(--radius-2xl)}.rounded-\[2px\]{border-radius:2px}.rounded-\[4px\]{border-radius:4px}.rounded-\[inherit\]{border-radius:inherit}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.rounded-none{border-radius:0}.rounded-sm{border-radius:calc(var(--radius) - 4px)}.rounded-xl{border-radius:calc(var(--radius) + 4px)}.rounded-xs{border-radius:var(--radius-xs)}.rounded-t-lg{border-top-left-radius:var(--radius);border-top-right-radius:var(--radius)}.rounded-l-3xl{border-top-left-radius:var(--radius-3xl);border-bottom-left-radius:var(--radius-3xl)}.rounded-tl-sm{border-top-left-radius:calc(var(--radius) - 4px)}.rounded-r-full{border-top-right-radius:3.40282e38px;border-bottom-right-radius:3.40282e38px}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-\[1\.5px\]{border-style:var(--tw-border-style);border-width:1.5px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-l-4{border-left-style:var(--tw-border-style);border-left-width:4px}.border-dashed{--tw-border-style: dashed;border-style:dashed}.border-\(--color-border\){border-color:var(--color-border)}.border-amber-200{border-color:var(--color-amber-200)}.border-amber-500{border-color:var(--color-amber-500)}.border-background{border-color:var(--background)}.border-blue-200{border-color:var(--color-blue-200)}.border-blue-500{border-color:var(--color-blue-500)}.border-border,.border-border\/50{border-color:var(--border)}@supports (color: color-mix(in lab,red,red)){.border-border\/50{border-color:color-mix(in oklab,var(--border) 50%,transparent)}}.border-emerald-200{border-color:var(--color-emerald-200)}.border-gray-200{border-color:var(--color-gray-200)}.border-gray-300{border-color:var(--color-gray-300)}.border-gray-500{border-color:var(--color-gray-500)}.border-green-200{border-color:var(--color-green-200)}.border-green-300{border-color:var(--color-green-300)}.border-green-500{border-color:var(--color-green-500)}.border-indigo-500{border-color:var(--color-indigo-500)}.border-input{border-color:var(--input)}.border-muted-foreground\/20{border-color:var(--muted-foreground)}@supports (color: color-mix(in lab,red,red)){.border-muted-foreground\/20{border-color:color-mix(in oklab,var(--muted-foreground) 20%,transparent)}}.border-orange-500{border-color:var(--color-orange-500)}.border-primary,.border-primary\/20{border-color:var(--primary)}@supports (color: color-mix(in lab,red,red)){.border-primary\/20{border-color:color-mix(in oklab,var(--primary) 20%,transparent)}}.border-purple-200{border-color:var(--color-purple-200)}.border-purple-500{border-color:var(--color-purple-500)}.border-red-200{border-color:var(--color-red-200)}.border-red-400{border-color:var(--color-red-400)}.border-red-500{border-color:var(--color-red-500)}.border-sidebar-border,.border-sidebar-border\/50{border-color:var(--sidebar-border)}@supports (color: color-mix(in lab,red,red)){.border-sidebar-border\/50{border-color:color-mix(in oklab,var(--sidebar-border) 50%,transparent)}}.border-slate-200{border-color:var(--color-slate-200)}.border-slate-200\/60{border-color:color-mix(in srgb,oklch(.929 .013 255.508) 60%,transparent)}@supports (color: color-mix(in lab,red,red)){.border-slate-200\/60{border-color:color-mix(in oklab,var(--color-slate-200) 60%,transparent)}}.border-slate-300{border-color:var(--color-slate-300)}.border-transparent{border-color:#0000}.border-yellow-200{border-color:var(--color-yellow-200)}.border-yellow-500{border-color:var(--color-yellow-500)}.border-t-transparent{border-top-color:#0000}.border-l-amber-500{border-left-color:var(--color-amber-500)}.border-l-green-500{border-left-color:var(--color-green-500)}.border-l-red-500{border-left-color:var(--color-red-500)}.border-l-transparent{border-left-color:#0000}.bg-\(--color-bg\){background-color:var(--color-bg)}.bg-accent,.bg-accent\/50{background-color:var(--accent)}@supports (color: color-mix(in lab,red,red)){.bg-accent\/50{background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}.bg-amber-50{background-color:var(--color-amber-50)}.bg-amber-100{background-color:var(--color-amber-100)}.bg-amber-500{background-color:var(--color-amber-500)}.bg-background,.bg-background\/80{background-color:var(--background)}@supports (color: color-mix(in lab,red,red)){.bg-background\/80{background-color:color-mix(in oklab,var(--background) 80%,transparent)}}.bg-black{background-color:var(--color-black)}.bg-black\/10{background-color:#0000001a}@supports (color: color-mix(in lab,red,red)){.bg-black\/10{background-color:color-mix(in oklab,var(--color-black) 10%,transparent)}}.bg-black\/20{background-color:#0003}@supports (color: color-mix(in lab,red,red)){.bg-black\/20{background-color:color-mix(in oklab,var(--color-black) 20%,transparent)}}.bg-black\/40{background-color:#0006}@supports (color: color-mix(in lab,red,red)){.bg-black\/40{background-color:color-mix(in oklab,var(--color-black) 40%,transparent)}}.bg-black\/50{background-color:#00000080}@supports (color: color-mix(in lab,red,red)){.bg-black\/50{background-color:color-mix(in oklab,var(--color-black) 50%,transparent)}}.bg-black\/80{background-color:#000c}@supports (color: color-mix(in lab,red,red)){.bg-black\/80{background-color:color-mix(in oklab,var(--color-black) 80%,transparent)}}.bg-blue-50{background-color:var(--color-blue-50)}.bg-blue-100{background-color:var(--color-blue-100)}.bg-blue-500{background-color:var(--color-blue-500)}.bg-border{background-color:var(--border)}.bg-card,.bg-card\/95{background-color:var(--card)}@supports (color: color-mix(in lab,red,red)){.bg-card\/95{background-color:color-mix(in oklab,var(--card) 95%,transparent)}}.bg-current{background-color:currentColor}.bg-destructive{background-color:var(--destructive)}.bg-foreground{background-color:var(--foreground)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-gray-100{background-color:var(--color-gray-100)}.bg-green-50{background-color:var(--color-green-50)}.bg-green-100{background-color:var(--color-green-100)}.bg-green-500{background-color:var(--color-green-500)}.bg-green-600{background-color:var(--color-green-600)}.bg-indigo-50{background-color:var(--color-indigo-50)}.bg-input-background{background-color:var(--input-background)}.bg-muted,.bg-muted\/30{background-color:var(--muted)}@supports (color: color-mix(in lab,red,red)){.bg-muted\/30{background-color:color-mix(in oklab,var(--muted) 30%,transparent)}}.bg-muted\/50{background-color:var(--muted)}@supports (color: color-mix(in lab,red,red)){.bg-muted\/50{background-color:color-mix(in oklab,var(--muted) 50%,transparent)}}.bg-orange-50{background-color:var(--color-orange-50)}.bg-orange-100{background-color:var(--color-orange-100)}.bg-orange-500{background-color:var(--color-orange-500)}.bg-pink-100{background-color:var(--color-pink-100)}.bg-popover{background-color:var(--popover)}.bg-primary,.bg-primary\/5{background-color:var(--primary)}@supports (color: color-mix(in lab,red,red)){.bg-primary\/5{background-color:color-mix(in oklab,var(--primary) 5%,transparent)}}.bg-primary\/10{background-color:var(--primary)}@supports (color: color-mix(in lab,red,red)){.bg-primary\/10{background-color:color-mix(in oklab,var(--primary) 10%,transparent)}}.bg-primary\/20{background-color:var(--primary)}@supports (color: color-mix(in lab,red,red)){.bg-primary\/20{background-color:color-mix(in oklab,var(--primary) 20%,transparent)}}.bg-purple-50{background-color:var(--color-purple-50)}.bg-purple-500{background-color:var(--color-purple-500)}.bg-red-50{background-color:var(--color-red-50)}.bg-red-100{background-color:var(--color-red-100)}.bg-red-500{background-color:var(--color-red-500)}.bg-secondary{background-color:var(--secondary)}.bg-sidebar{background-color:var(--sidebar)}.bg-sidebar-border{background-color:var(--sidebar-border)}.bg-slate-50{background-color:var(--color-slate-50)}.bg-slate-50\/80{background-color:color-mix(in srgb,oklch(.984 .003 247.858) 80%,transparent)}@supports (color: color-mix(in lab,red,red)){.bg-slate-50\/80{background-color:color-mix(in oklab,var(--color-slate-50) 80%,transparent)}}.bg-slate-100{background-color:var(--color-slate-100)}.bg-slate-200{background-color:var(--color-slate-200)}.bg-transparent{background-color:#0000}.bg-white{background-color:var(--color-white)}.bg-white\/20{background-color:#fff3}@supports (color: color-mix(in lab,red,red)){.bg-white\/20{background-color:color-mix(in oklab,var(--color-white) 20%,transparent)}}.bg-white\/50{background-color:#ffffff80}@supports (color: color-mix(in lab,red,red)){.bg-white\/50{background-color:color-mix(in oklab,var(--color-white) 50%,transparent)}}.bg-white\/80{background-color:#fffc}@supports (color: color-mix(in lab,red,red)){.bg-white\/80{background-color:color-mix(in oklab,var(--color-white) 80%,transparent)}}.bg-white\/90{background-color:#ffffffe6}@supports (color: color-mix(in lab,red,red)){.bg-white\/90{background-color:color-mix(in oklab,var(--color-white) 90%,transparent)}}.bg-white\/95{background-color:#fffffff2}@supports (color: color-mix(in lab,red,red)){.bg-white\/95{background-color:color-mix(in oklab,var(--color-white) 95%,transparent)}}.bg-yellow-50{background-color:var(--color-yellow-50)}.bg-yellow-100{background-color:var(--color-yellow-100)}.bg-gradient-to-br{--tw-gradient-position: to bottom right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-r{--tw-gradient-position: to right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.from-amber-500{--tw-gradient-from: var(--color-amber-500);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-blue-500{--tw-gradient-from: var(--color-blue-500);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-emerald-50{--tw-gradient-from: var(--color-emerald-50);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-emerald-500{--tw-gradient-from: var(--color-emerald-500);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-green-500{--tw-gradient-from: var(--color-green-500);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-purple-500{--tw-gradient-from: var(--color-purple-500);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-red-500{--tw-gradient-from: var(--color-red-500);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-slate-50{--tw-gradient-from: var(--color-slate-50);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-white\/10{--tw-gradient-from: #ffffff1a;--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}@supports (color: color-mix(in lab,red,red)){.from-white\/10{--tw-gradient-from: color-mix(in oklab, var(--color-white) 10%, transparent)}}.via-blue-50{--tw-gradient-via: var(--color-blue-50);--tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-via-stops)}.via-blue-50\/20{--tw-gradient-via: color-mix(in srgb, oklch(.97 .014 254.604) 20%, transparent);--tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-via-stops)}@supports (color: color-mix(in lab,red,red)){.via-blue-50\/20{--tw-gradient-via: color-mix(in oklab, var(--color-blue-50) 20%, transparent)}}.to-amber-50{--tw-gradient-to: var(--color-amber-50);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-blue-600{--tw-gradient-to: var(--color-blue-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-emerald-600{--tw-gradient-to: var(--color-emerald-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-indigo-50{--tw-gradient-to: var(--color-indigo-50);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-indigo-50\/30{--tw-gradient-to: color-mix(in srgb, oklch(.962 .018 272.314) 30%, transparent);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}@supports (color: color-mix(in lab,red,red)){.to-indigo-50\/30{--tw-gradient-to: color-mix(in oklab, var(--color-indigo-50) 30%, transparent)}}.to-indigo-600{--tw-gradient-to: var(--color-indigo-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-orange-500{--tw-gradient-to: var(--color-orange-500);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-orange-600{--tw-gradient-to: var(--color-orange-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-pink-600{--tw-gradient-to: var(--color-pink-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-transparent{--tw-gradient-to: transparent;--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.fill-current{fill:currentColor}.fill-primary{fill:var(--primary)}.object-contain{object-fit:contain}.object-cover{object-fit:cover}.p-0{padding:calc(var(--spacing) * 0)}.p-1{padding:calc(var(--spacing) * 1)}.p-1\.5{padding:calc(var(--spacing) * 1.5)}.p-2{padding:calc(var(--spacing) * 2)}.p-3{padding:calc(var(--spacing) * 3)}.p-4{padding:calc(var(--spacing) * 4)}.p-5{padding:calc(var(--spacing) * 5)}.p-6{padding:calc(var(--spacing) * 6)}.p-\[3px\]{padding:3px}.p-px{padding:1px}.px-1{padding-inline:calc(var(--spacing) * 1)}.px-1\.5{padding-inline:calc(var(--spacing) * 1.5)}.px-2{padding-inline:calc(var(--spacing) * 2)}.px-2\.5{padding-inline:calc(var(--spacing) * 2.5)}.px-3{padding-inline:calc(var(--spacing) * 3)}.px-4{padding-inline:calc(var(--spacing) * 4)}.px-5{padding-inline:calc(var(--spacing) * 5)}.px-6{padding-inline:calc(var(--spacing) * 6)}.px-8{padding-inline:calc(var(--spacing) * 8)}.py-0\.5{padding-block:calc(var(--spacing) * .5)}.py-1{padding-block:calc(var(--spacing) * 1)}.py-1\.5{padding-block:calc(var(--spacing) * 1.5)}.py-2{padding-block:calc(var(--spacing) * 2)}.py-3{padding-block:calc(var(--spacing) * 3)}.py-4{padding-block:calc(var(--spacing) * 4)}.py-6{padding-block:calc(var(--spacing) * 6)}.py-8{padding-block:calc(var(--spacing) * 8)}.pt-0{padding-top:calc(var(--spacing) * 0)}.pt-1{padding-top:calc(var(--spacing) * 1)}.pt-2{padding-top:calc(var(--spacing) * 2)}.pt-3{padding-top:calc(var(--spacing) * 3)}.pt-4{padding-top:calc(var(--spacing) * 4)}.pt-6{padding-top:calc(var(--spacing) * 6)}.pr-1{padding-right:calc(var(--spacing) * 1)}.pr-2{padding-right:calc(var(--spacing) * 2)}.pr-2\.5{padding-right:calc(var(--spacing) * 2.5)}.pr-4{padding-right:calc(var(--spacing) * 4)}.pr-8{padding-right:calc(var(--spacing) * 8)}.pr-10{padding-right:calc(var(--spacing) * 10)}.pr-12{padding-right:calc(var(--spacing) * 12)}.pb-2{padding-bottom:calc(var(--spacing) * 2)}.pb-3{padding-bottom:calc(var(--spacing) * 3)}.pb-4{padding-bottom:calc(var(--spacing) * 4)}.pb-6{padding-bottom:calc(var(--spacing) * 6)}.pl-2{padding-left:calc(var(--spacing) * 2)}.pl-4{padding-left:calc(var(--spacing) * 4)}.pl-8{padding-left:calc(var(--spacing) * 8)}.pl-10{padding-left:calc(var(--spacing) * 10)}.pl-12{padding-left:calc(var(--spacing) * 12)}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.align-middle{vertical-align:middle}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading, var(--text-2xl--line-height))}.text-6xl{font-size:var(--text-6xl);line-height:var(--tw-leading, var(--text-6xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading, var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading, var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading, var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height))}.text-\[0\.8rem\]{font-size:.8rem}.text-\[10px\]{font-size:10px}.leading-none{--tw-leading: 1;line-height:1}.leading-relaxed{--tw-leading: var(--leading-relaxed);line-height:var(--leading-relaxed)}.leading-tight{--tw-leading: var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight: var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight: var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-tight{--tw-tracking: var(--tracking-tight);letter-spacing:var(--tracking-tight)}.tracking-wide{--tw-tracking: var(--tracking-wide);letter-spacing:var(--tracking-wide)}.tracking-wider{--tw-tracking: var(--tracking-wider);letter-spacing:var(--tracking-wider)}.tracking-widest{--tw-tracking: var(--tracking-widest);letter-spacing:var(--tracking-widest)}.text-balance{text-wrap:balance}.break-words{overflow-wrap:break-word}.whitespace-nowrap{white-space:nowrap}.whitespace-pre-line{white-space:pre-line}.whitespace-pre-wrap{white-space:pre-wrap}.text-accent-foreground{color:var(--accent-foreground)}.text-amber-500{color:var(--color-amber-500)}.text-amber-600{color:var(--color-amber-600)}.text-amber-700{color:var(--color-amber-700)}.text-amber-800{color:var(--color-amber-800)}.text-blue-500{color:var(--color-blue-500)}.text-blue-600{color:var(--color-blue-600)}.text-blue-700{color:var(--color-blue-700)}.text-blue-800{color:var(--color-blue-800)}.text-card-foreground{color:var(--card-foreground)}.text-current{color:currentColor}.text-destructive{color:var(--destructive)}.text-destructive-foreground{color:var(--destructive-foreground)}.text-emerald-800{color:var(--color-emerald-800)}.text-foreground{color:var(--foreground)}.text-gray-400{color:var(--color-gray-400)}.text-gray-500{color:var(--color-gray-500)}.text-gray-600{color:var(--color-gray-600)}.text-gray-700{color:var(--color-gray-700)}.text-gray-900{color:var(--color-gray-900)}.text-green-500{color:var(--color-green-500)}.text-green-600{color:var(--color-green-600)}.text-green-700{color:var(--color-green-700)}.text-green-800{color:var(--color-green-800)}.text-indigo-700{color:var(--color-indigo-700)}.text-muted-foreground,.text-muted-foreground\/70{color:var(--muted-foreground)}@supports (color: color-mix(in lab,red,red)){.text-muted-foreground\/70{color:color-mix(in oklab,var(--muted-foreground) 70%,transparent)}}.text-orange-500{color:var(--color-orange-500)}.text-orange-600{color:var(--color-orange-600)}.text-orange-700{color:var(--color-orange-700)}.text-orange-800{color:var(--color-orange-800)}.text-pink-600{color:var(--color-pink-600)}.text-pink-800{color:var(--color-pink-800)}.text-popover-foreground{color:var(--popover-foreground)}.text-primary{color:var(--primary)}.text-primary-foreground{color:var(--primary-foreground)}.text-purple-600{color:var(--color-purple-600)}.text-red-500{color:var(--color-red-500)}.text-red-600{color:var(--color-red-600)}.text-red-700{color:var(--color-red-700)}.text-red-800{color:var(--color-red-800)}.text-secondary-foreground{color:var(--secondary-foreground)}.text-sidebar-foreground,.text-sidebar-foreground\/60{color:var(--sidebar-foreground)}@supports (color: color-mix(in lab,red,red)){.text-sidebar-foreground\/60{color:color-mix(in oklab,var(--sidebar-foreground) 60%,transparent)}}.text-sidebar-foreground\/70{color:var(--sidebar-foreground)}@supports (color: color-mix(in lab,red,red)){.text-sidebar-foreground\/70{color:color-mix(in oklab,var(--sidebar-foreground) 70%,transparent)}}.text-slate-400{color:var(--color-slate-400)}.text-slate-500{color:var(--color-slate-500)}.text-slate-600{color:var(--color-slate-600)}.text-slate-700{color:var(--color-slate-700)}.text-slate-900{color:var(--color-slate-900)}.text-white{color:var(--color-white)}.text-white\/70{color:#ffffffb3}@supports (color: color-mix(in lab,red,red)){.text-white\/70{color:color-mix(in oklab,var(--color-white) 70%,transparent)}}.text-yellow-600{color:var(--color-yellow-600)}.text-yellow-700{color:var(--color-yellow-700)}.text-yellow-800{color:var(--color-yellow-800)}.capitalize{text-transform:capitalize}.lowercase{text-transform:lowercase}.uppercase{text-transform:uppercase}.tabular-nums{--tw-numeric-spacing: tabular-nums;font-variant-numeric:var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, )}.underline-offset-4{text-underline-offset:4px}.opacity-0{opacity:0}.opacity-40{opacity:.4}.opacity-50{opacity:.5}.opacity-70{opacity:.7}.opacity-80{opacity:.8}.opacity-100{opacity:1}.shadow{--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-2xl{--tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\]{--tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-none{--tw-shadow: 0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-0{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-4{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-amber-500\/10{--tw-shadow-color: color-mix(in srgb, oklch(.769 .188 70.08) 10%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-amber-500\/10{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-amber-500) 10%, transparent) var(--tw-shadow-alpha), transparent)}}.shadow-blue-500\/5{--tw-shadow-color: color-mix(in srgb, oklch(.623 .214 259.815) 5%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-blue-500\/5{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 5%, transparent) var(--tw-shadow-alpha), transparent)}}.shadow-blue-500\/10{--tw-shadow-color: color-mix(in srgb, oklch(.623 .214 259.815) 10%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-blue-500\/10{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 10%, transparent) var(--tw-shadow-alpha), transparent)}}.shadow-blue-500\/25{--tw-shadow-color: color-mix(in srgb, oklch(.623 .214 259.815) 25%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-blue-500\/25{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 25%, transparent) var(--tw-shadow-alpha), transparent)}}.shadow-emerald-500\/10{--tw-shadow-color: color-mix(in srgb, oklch(.696 .17 162.48) 10%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-emerald-500\/10{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-emerald-500) 10%, transparent) var(--tw-shadow-alpha), transparent)}}.shadow-green-500\/25{--tw-shadow-color: color-mix(in srgb, oklch(.723 .219 149.579) 25%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-green-500\/25{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-green-500) 25%, transparent) var(--tw-shadow-alpha), transparent)}}.shadow-indigo-500\/10{--tw-shadow-color: color-mix(in srgb, oklch(.585 .233 277.117) 10%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-indigo-500\/10{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-indigo-500) 10%, transparent) var(--tw-shadow-alpha), transparent)}}.shadow-slate-900\/5{--tw-shadow-color: color-mix(in srgb, oklch(.208 .042 265.755) 5%, transparent)}@supports (color: color-mix(in lab,red,red)){.shadow-slate-900\/5{--tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-slate-900) 5%, transparent) var(--tw-shadow-alpha), transparent)}}.ring-ring\/50{--tw-ring-color: var(--ring)}@supports (color: color-mix(in lab,red,red)){.ring-ring\/50{--tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent)}}.ring-sidebar-ring{--tw-ring-color: var(--sidebar-ring)}.ring-white{--tw-ring-color: var(--color-white)}.ring-offset-2{--tw-ring-offset-width: 2px;--tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}.ring-offset-background{--tw-ring-offset-color: var(--background)}.outline-hidden{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.outline-hidden{outline-offset:2px;outline:2px solid #0000}}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.blur{--tw-blur: blur(8px);filter:var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, )}.drop-shadow{--tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, #0000001a)) drop-shadow(0 1px 1px var(--tw-drop-shadow-color, #0000000f));--tw-drop-shadow: drop-shadow(0 1px 2px #0000001a) drop-shadow(0 1px 1px #0000000f);filter:var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, )}.invert{--tw-invert: invert(100%);filter:var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, )}.filter{filter:var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, )}.backdrop-blur-md{--tw-backdrop-blur: blur(var(--blur-md));-webkit-backdrop-filter:var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );backdrop-filter:var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, )}.backdrop-blur-sm{--tw-backdrop-blur: blur(var(--blur-sm));-webkit-backdrop-filter:var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );backdrop-filter:var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, )}.backdrop-blur-xl{--tw-backdrop-blur: blur(var(--blur-xl));-webkit-backdrop-filter:var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );backdrop-filter:var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, )}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-\[color\,box-shadow\]{transition-property:color,box-shadow;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-\[left\,right\,width\]{transition-property:left,right,width;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-\[margin\,opacity\]{transition-property:margin,opacity;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-\[width\,height\,padding\]{transition-property:width,height,padding;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-\[width\]{transition-property:width;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-shadow{transition-property:box-shadow;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.transition-none{transition-property:none}.duration-75{--tw-duration: 75ms;transition-duration:75ms}.duration-150{--tw-duration: .15s;transition-duration:.15s}.duration-200{--tw-duration: .2s;transition-duration:.2s}.duration-300{--tw-duration: .3s;transition-duration:.3s}.duration-1000{--tw-duration: 1s;transition-duration:1s}.ease-in-out{--tw-ease: var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-linear{--tw-ease: linear;transition-timing-function:linear}.ease-out{--tw-ease: var(--ease-out);transition-timing-function:var(--ease-out)}.outline-none{--tw-outline-style: none;outline-style:none}.select-none{-webkit-user-select:none;user-select:none}.fade-in-0{--tw-enter-opacity: 0}.zoom-in-95{--tw-enter-scale: .95}.group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *){opacity:1}@media (hover: hover){.group-hover\:visible:is(:where(.group):hover *){visibility:visible}}@media (hover: hover){.group-hover\:scale-110:is(:where(.group):hover *){--tw-scale-x: 110%;--tw-scale-y: 110%;--tw-scale-z: 110%;scale:var(--tw-scale-x) var(--tw-scale-y)}}@media (hover: hover){.group-hover\:text-muted-foreground:is(:where(.group):hover *){color:var(--muted-foreground)}}@media (hover: hover){.group-hover\:opacity-100:is(:where(.group):hover *){opacity:1}}@media (hover: hover){.group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *){opacity:1}}.group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar=menu-action]) *){padding-right:calc(var(--spacing) * 8)}.group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible=icon] *){margin-top:calc(var(--spacing) * -8)}.group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible=icon] *){display:none}.group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--spacing) * 8)!important;height:calc(var(--spacing) * 8)!important}.group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible=icon] *){width:var(--sidebar-width-icon)}.group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)))}.group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px)}.group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible=icon] *){overflow:hidden}.group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing) * 0)!important}.group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing) * 2)!important}.group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible=icon] *){opacity:0}.group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible=offcanvas] *){right:calc(var(--sidebar-width) * -1)}.group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible=offcanvas] *){left:calc(var(--sidebar-width) * -1)}.group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible=offcanvas] *){width:calc(var(--spacing) * 0)}.group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible=offcanvas] *){--tw-translate-x: calc(var(--spacing) * 0);translate:var(--tw-translate-x) var(--tw-translate-y)}.group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled=true] *){pointer-events:none}.group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled=true] *){opacity:.5}.group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side=left] *){right:calc(var(--spacing) * -4)}.group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side=left] *){border-right-style:var(--tw-border-style);border-right-width:1px}.group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side=right] *){left:calc(var(--spacing) * 0)}.group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side=right] *){rotate:180deg}.group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side=right] *){border-left-style:var(--tw-border-style);border-left-width:1px}.group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state=open] *){rotate:180deg}.group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant=floating] *){border-radius:var(--radius)}.group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant=floating] *){border-style:var(--tw-border-style);border-width:1px}.group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant=floating] *){border-color:var(--sidebar-border)}.group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant=floating] *){--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block:is(:where(.group\/drawer-content)[data-vaul-drawer-direction=bottom] *){display:block}.group-data-\[viewport\=false\]\/navigation-menu\:top-full:is(:where(.group\/navigation-menu)[data-viewport=false] *){top:100%}.group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5:is(:where(.group\/navigation-menu)[data-viewport=false] *){margin-top:calc(var(--spacing) * 1.5)}.group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden:is(:where(.group\/navigation-menu)[data-viewport=false] *){overflow:hidden}.group-data-\[viewport\=false\]\/navigation-menu\:rounded-md:is(:where(.group\/navigation-menu)[data-viewport=false] *){border-radius:calc(var(--radius) - 2px)}.group-data-\[viewport\=false\]\/navigation-menu\:border:is(:where(.group\/navigation-menu)[data-viewport=false] *){border-style:var(--tw-border-style);border-width:1px}.group-data-\[viewport\=false\]\/navigation-menu\:bg-popover:is(:where(.group\/navigation-menu)[data-viewport=false] *){background-color:var(--popover)}.group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground:is(:where(.group\/navigation-menu)[data-viewport=false] *){color:var(--popover-foreground)}.group-data-\[viewport\=false\]\/navigation-menu\:shadow:is(:where(.group\/navigation-menu)[data-viewport=false] *){--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport=false] *){--tw-duration: .2s;transition-duration:.2s}@media (hover: hover){.peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover~*){color:var(--sidebar-accent-foreground)}}.peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled~*){cursor:not-allowed}.peer-disabled\:opacity-50:is(:where(.peer):disabled~*){opacity:.5}.peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active=true]~*){color:var(--sidebar-accent-foreground)}.peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size=default]~*){top:calc(var(--spacing) * 1.5)}.peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size=lg]~*){top:calc(var(--spacing) * 2.5)}.peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size=sm]~*){top:calc(var(--spacing) * 1)}.selection\:bg-primary ::selection,.selection\:bg-primary::selection{background-color:var(--primary)}.selection\:text-primary-foreground ::selection,.selection\:text-primary-foreground::selection{color:var(--primary-foreground)}.file\:inline-flex::file-selector-button{display:inline-flex}.file\:h-7::file-selector-button{height:calc(var(--spacing) * 7)}.file\:border-0::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file\:bg-transparent::file-selector-button{background-color:#0000}.file\:text-sm::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height))}.file\:font-medium::file-selector-button{--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\:text-foreground::file-selector-button{color:var(--foreground)}.placeholder\:text-muted-foreground::placeholder{color:var(--muted-foreground)}.after\:absolute:after{content:var(--tw-content);position:absolute}.after\:-inset-2:after{content:var(--tw-content);inset:calc(var(--spacing) * -2)}.after\:inset-y-0:after{content:var(--tw-content);inset-block:calc(var(--spacing) * 0)}.after\:left-1\/2:after{content:var(--tw-content);left:50%}.after\:w-1:after{content:var(--tw-content);width:calc(var(--spacing) * 1)}.after\:w-\[2px\]:after{content:var(--tw-content);width:2px}.after\:-translate-x-1\/2:after{content:var(--tw-content);--tw-translate-x: -50% ;translate:var(--tw-translate-x) var(--tw-translate-y)}.group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible=offcanvas] *):after{content:var(--tw-content);left:100%}.first\:rounded-l-md:first-child{border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}.first\:border-l:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}.last\:rounded-r-md:last-child{border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.last\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.last\:pb-0:last-child{padding-bottom:calc(var(--spacing) * 0)}.focus-within\:relative:focus-within{position:relative}.focus-within\:z-20:focus-within{z-index:20}@media (hover: hover){.hover\:scale-110:hover{--tw-scale-x: 110%;--tw-scale-y: 110%;--tw-scale-z: 110%;scale:var(--tw-scale-x) var(--tw-scale-y)}}@media (hover: hover){.hover\:scale-\[1\.02\]:hover{scale:1.02}}@media (hover: hover){.hover\:border-amber-300:hover{border-color:var(--color-amber-300)}}@media (hover: hover){.hover\:border-primary\/50:hover{border-color:var(--primary)}@supports (color: color-mix(in lab,red,red)){.hover\:border-primary\/50:hover{border-color:color-mix(in oklab,var(--primary) 50%,transparent)}}}@media (hover: hover){.hover\:border-slate-300:hover{border-color:var(--color-slate-300)}}@media (hover: hover){.hover\:bg-accent:hover{background-color:var(--accent)}}@media (hover: hover){.hover\:bg-accent\/50:hover{background-color:var(--accent)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-accent\/50:hover{background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}}@media (hover: hover){.hover\:bg-amber-50:hover{background-color:var(--color-amber-50)}}@media (hover: hover){.hover\:bg-amber-100:hover{background-color:var(--color-amber-100)}}@media (hover: hover){.hover\:bg-amber-200:hover{background-color:var(--color-amber-200)}}@media (hover: hover){.hover\:bg-amber-300\/50:hover{background-color:color-mix(in srgb,oklch(.879 .169 91.605) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-amber-300\/50:hover{background-color:color-mix(in oklab,var(--color-amber-300) 50%,transparent)}}}@media (hover: hover){.hover\:bg-amber-600:hover{background-color:var(--color-amber-600)}}@media (hover: hover){.hover\:bg-background\/50:hover{background-color:var(--background)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-background\/50:hover{background-color:color-mix(in oklab,var(--background) 50%,transparent)}}}@media (hover: hover){.hover\:bg-black\/10:hover{background-color:#0000001a}@supports (color: color-mix(in lab,red,red)){.hover\:bg-black\/10:hover{background-color:color-mix(in oklab,var(--color-black) 10%,transparent)}}}@media (hover: hover){.hover\:bg-blue-100:hover{background-color:var(--color-blue-100)}}@media (hover: hover){.hover\:bg-blue-600:hover{background-color:var(--color-blue-600)}}@media (hover: hover){.hover\:bg-destructive:hover{background-color:var(--destructive)}}@media (hover: hover){.hover\:bg-destructive\/90:hover{background-color:var(--destructive)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-destructive\/90:hover{background-color:color-mix(in oklab,var(--destructive) 90%,transparent)}}}@media (hover: hover){.hover\:bg-gray-100:hover{background-color:var(--color-gray-100)}}@media (hover: hover){.hover\:bg-green-100:hover{background-color:var(--color-green-100)}}@media (hover: hover){.hover\:bg-green-600:hover{background-color:var(--color-green-600)}}@media (hover: hover){.hover\:bg-green-700:hover{background-color:var(--color-green-700)}}@media (hover: hover){.hover\:bg-muted:hover{background-color:var(--muted)}}@media (hover: hover){.hover\:bg-muted\/30:hover{background-color:var(--muted)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-muted\/30:hover{background-color:color-mix(in oklab,var(--muted) 30%,transparent)}}}@media (hover: hover){.hover\:bg-muted\/50:hover{background-color:var(--muted)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-muted\/50:hover{background-color:color-mix(in oklab,var(--muted) 50%,transparent)}}}@media (hover: hover){.hover\:bg-orange-600:hover{background-color:var(--color-orange-600)}}@media (hover: hover){.hover\:bg-primary:hover{background-color:var(--primary)}}@media (hover: hover){.hover\:bg-primary\/90:hover{background-color:var(--primary)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--primary) 90%,transparent)}}}@media (hover: hover){.hover\:bg-purple-100:hover{background-color:var(--color-purple-100)}}@media (hover: hover){.hover\:bg-purple-600:hover{background-color:var(--color-purple-600)}}@media (hover: hover){.hover\:bg-red-50:hover{background-color:var(--color-red-50)}}@media (hover: hover){.hover\:bg-secondary\/80:hover{background-color:var(--secondary)}@supports (color: color-mix(in lab,red,red)){.hover\:bg-secondary\/80:hover{background-color:color-mix(in oklab,var(--secondary) 80%,transparent)}}}@media (hover: hover){.hover\:bg-sidebar-accent:hover{background-color:var(--sidebar-accent)}}@media (hover: hover){.hover\:bg-slate-50:hover{background-color:var(--color-slate-50)}}@media (hover: hover){.hover\:bg-slate-100:hover{background-color:var(--color-slate-100)}}@media (hover: hover){.hover\:bg-slate-300:hover{background-color:var(--color-slate-300)}}@media (hover: hover){.hover\:bg-white\/60:hover{background-color:#fff9}@supports (color: color-mix(in lab,red,red)){.hover\:bg-white\/60:hover{background-color:color-mix(in oklab,var(--color-white) 60%,transparent)}}}@media (hover: hover){.hover\:from-amber-600:hover{--tw-gradient-from: var(--color-amber-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}}@media (hover: hover){.hover\:from-blue-600:hover{--tw-gradient-from: var(--color-blue-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}}@media (hover: hover){.hover\:to-indigo-700:hover{--tw-gradient-to: var(--color-indigo-700);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}}@media (hover: hover){.hover\:to-orange-600:hover{--tw-gradient-to: var(--color-orange-600);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}}@media (hover: hover){.hover\:text-accent-foreground:hover{color:var(--accent-foreground)}}@media (hover: hover){.hover\:text-amber-700:hover{color:var(--color-amber-700)}}@media (hover: hover){.hover\:text-destructive:hover{color:var(--destructive)}}@media (hover: hover){.hover\:text-destructive-foreground:hover{color:var(--destructive-foreground)}}@media (hover: hover){.hover\:text-foreground:hover{color:var(--foreground)}}@media (hover: hover){.hover\:text-gray-700:hover{color:var(--color-gray-700)}}@media (hover: hover){.hover\:text-muted-foreground:hover{color:var(--muted-foreground)}}@media (hover: hover){.hover\:text-primary-foreground:hover{color:var(--primary-foreground)}}@media (hover: hover){.hover\:text-red-700:hover{color:var(--color-red-700)}}@media (hover: hover){.hover\:text-sidebar-accent-foreground:hover{color:var(--sidebar-accent-foreground)}}@media (hover: hover){.hover\:text-white:hover{color:var(--color-white)}}@media (hover: hover){.hover\:underline:hover{text-decoration-line:underline}}@media (hover: hover){.hover\:opacity-20:hover{opacity:.2}}@media (hover: hover){.hover\:opacity-100:hover{opacity:1}}@media (hover: hover){.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover{--tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}@media (hover: hover){.hover\:shadow-lg:hover{--tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}@media (hover: hover){.hover\:shadow-md:hover{--tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}@media (hover: hover){.hover\:ring-4:hover{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}@media (hover: hover){.hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible=offcanvas] *){background-color:var(--sidebar)}}@media (hover: hover){.hover\:after\:bg-sidebar-border:hover:after{content:var(--tw-content);background-color:var(--sidebar-border)}}.focus\:z-10:focus{z-index:10}.focus\:border-amber-500:focus{border-color:var(--color-amber-500)}.focus\:border-blue-500:focus{border-color:var(--color-blue-500)}.focus\:border-green-500:focus{border-color:var(--color-green-500)}.focus\:border-orange-500:focus{border-color:var(--color-orange-500)}.focus\:border-primary:focus{border-color:var(--primary)}.focus\:border-red-500:focus{border-color:var(--color-red-500)}.focus\:border-transparent:focus{border-color:#0000}.focus\:bg-accent:focus{background-color:var(--accent)}.focus\:bg-primary:focus{background-color:var(--primary)}.focus\:text-accent-foreground:focus{color:var(--accent-foreground)}.focus\:text-primary-foreground:focus{color:var(--primary-foreground)}.focus\:ring-2:focus{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-amber-500\/20:focus{--tw-ring-color: color-mix(in srgb, oklch(.769 .188 70.08) 20%, transparent)}@supports (color: color-mix(in lab,red,red)){.focus\:ring-amber-500\/20:focus{--tw-ring-color: color-mix(in oklab, var(--color-amber-500) 20%, transparent)}}.focus\:ring-blue-500:focus{--tw-ring-color: var(--color-blue-500)}.focus\:ring-blue-500\/20:focus{--tw-ring-color: color-mix(in srgb, oklch(.623 .214 259.815) 20%, transparent)}@supports (color: color-mix(in lab,red,red)){.focus\:ring-blue-500\/20:focus{--tw-ring-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent)}}.focus\:ring-green-500\/20:focus{--tw-ring-color: color-mix(in srgb, oklch(.723 .219 149.579) 20%, transparent)}@supports (color: color-mix(in lab,red,red)){.focus\:ring-green-500\/20:focus{--tw-ring-color: color-mix(in oklab, var(--color-green-500) 20%, transparent)}}.focus\:ring-orange-500\/20:focus{--tw-ring-color: color-mix(in srgb, oklch(.705 .213 47.604) 20%, transparent)}@supports (color: color-mix(in lab,red,red)){.focus\:ring-orange-500\/20:focus{--tw-ring-color: color-mix(in oklab, var(--color-orange-500) 20%, transparent)}}.focus\:ring-primary\/20:focus{--tw-ring-color: var(--primary)}@supports (color: color-mix(in lab,red,red)){.focus\:ring-primary\/20:focus{--tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent)}}.focus\:ring-red-500\/20:focus{--tw-ring-color: color-mix(in srgb, oklch(.637 .237 25.331) 20%, transparent)}@supports (color: color-mix(in lab,red,red)){.focus\:ring-red-500\/20:focus{--tw-ring-color: color-mix(in oklab, var(--color-red-500) 20%, transparent)}}.focus\:ring-ring:focus{--tw-ring-color: var(--ring)}.focus\:ring-offset-2:focus{--tw-ring-offset-width: 2px;--tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}.focus\:outline-hidden:focus{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.focus\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.focus\:outline-none:focus{--tw-outline-style: none;outline-style:none}.focus-visible\:z-10:focus-visible{z-index:10}.focus-visible\:border-ring:focus-visible{border-color:var(--ring)}.focus-visible\:ring-0:focus-visible{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-1:focus-visible{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-2:focus-visible{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-4:focus-visible{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-\[3px\]:focus-visible{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-destructive\/20:focus-visible{--tw-ring-color: var(--destructive)}@supports (color: color-mix(in lab,red,red)){.focus-visible\:ring-destructive\/20:focus-visible{--tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent)}}.focus-visible\:ring-ring:focus-visible{--tw-ring-color: var(--ring)}.focus-visible\:ring-ring\/50:focus-visible{--tw-ring-color: var(--ring)}@supports (color: color-mix(in lab,red,red)){.focus-visible\:ring-ring\/50:focus-visible{--tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent)}}.focus-visible\:ring-sidebar-ring:focus-visible{--tw-ring-color: var(--sidebar-ring)}.focus-visible\:ring-offset-0:focus-visible{--tw-ring-offset-width: 0px;--tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}.focus-visible\:ring-offset-1:focus-visible{--tw-ring-offset-width: 1px;--tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}.focus-visible\:ring-offset-2:focus-visible{--tw-ring-offset-width: 2px;--tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}.focus-visible\:outline-hidden:focus-visible{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.focus-visible\:outline-hidden:focus-visible{outline-offset:2px;outline:2px solid #0000}}.focus-visible\:outline-1:focus-visible{outline-style:var(--tw-outline-style);outline-width:1px}.focus-visible\:outline-ring:focus-visible{outline-color:var(--ring)}.focus-visible\:outline-none:focus-visible{--tw-outline-style: none;outline-style:none}.active\:bg-sidebar-accent:active{background-color:var(--sidebar-accent)}.active\:text-sidebar-accent-foreground:active{color:var(--sidebar-accent-foreground)}.disabled\:pointer-events-none:disabled{pointer-events:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}.disabled\:shadow-none:disabled{--tw-shadow: 0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:where([data-side=left]) .in-data-\[side\=left\]\:cursor-w-resize{cursor:w-resize}:where([data-side=right]) .in-data-\[side\=right\]\:cursor-e-resize{cursor:e-resize}.has-disabled\:opacity-50:has(:disabled){opacity:.5}.has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot=card-action]){grid-template-columns:1fr auto}.has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant=inset]){background-color:var(--sidebar)}.has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has(>svg){grid-template-columns:calc(var(--spacing) * 4) 1fr}.has-\[\>svg\]\:gap-x-3:has(>svg){column-gap:calc(var(--spacing) * 3)}.aria-disabled\:pointer-events-none[aria-disabled=true]{pointer-events:none}.aria-disabled\:opacity-50[aria-disabled=true]{opacity:.5}.aria-invalid\:border-destructive[aria-invalid=true]{border-color:var(--destructive)}.aria-invalid\:ring-destructive\/20[aria-invalid=true]{--tw-ring-color: var(--destructive)}@supports (color: color-mix(in lab,red,red)){.aria-invalid\:ring-destructive\/20[aria-invalid=true]{--tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent)}}.aria-selected\:bg-accent[aria-selected=true]{background-color:var(--accent)}.aria-selected\:bg-primary[aria-selected=true]{background-color:var(--primary)}.aria-selected\:text-accent-foreground[aria-selected=true]{color:var(--accent-foreground)}.aria-selected\:text-muted-foreground[aria-selected=true]{color:var(--muted-foreground)}.aria-selected\:text-primary-foreground[aria-selected=true]{color:var(--primary-foreground)}.aria-selected\:opacity-100[aria-selected=true]{opacity:1}.data-\[active\=true\]\:z-10[data-active=true]{z-index:10}.data-\[active\=true\]\:border-ring[data-active=true]{border-color:var(--ring)}.data-\[active\=true\]\:bg-accent\/50[data-active=true]{background-color:var(--accent)}@supports (color: color-mix(in lab,red,red)){.data-\[active\=true\]\:bg-accent\/50[data-active=true]{background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}.data-\[active\=true\]\:bg-sidebar-accent[data-active=true]{background-color:var(--sidebar-accent)}.data-\[active\=true\]\:font-medium[data-active=true]{--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium)}.data-\[active\=true\]\:text-accent-foreground[data-active=true]{color:var(--accent-foreground)}.data-\[active\=true\]\:text-sidebar-accent-foreground[data-active=true]{color:var(--sidebar-accent-foreground)}.data-\[active\=true\]\:ring-\[3px\][data-active=true]{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\[active\=true\]\:ring-ring\/50[data-active=true]{--tw-ring-color: var(--ring)}@supports (color: color-mix(in lab,red,red)){.data-\[active\=true\]\:ring-ring\/50[data-active=true]{--tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent)}}@media (hover: hover){.data-\[active\=true\]\:hover\:bg-accent[data-active=true]:hover{background-color:var(--accent)}}.data-\[active\=true\]\:focus\:bg-accent[data-active=true]:focus{background-color:var(--accent)}.data-\[active\=true\]\:aria-invalid\:border-destructive[data-active=true][aria-invalid=true]{border-color:var(--destructive)}.data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active=true][aria-invalid=true]{--tw-ring-color: var(--destructive)}@supports (color: color-mix(in lab,red,red)){.data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active=true][aria-invalid=true]{--tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent)}}.data-\[disabled\]\:pointer-events-none[data-disabled]{pointer-events:none}.data-\[disabled\]\:opacity-50[data-disabled]{opacity:.5}.data-\[disabled\=true\]\:pointer-events-none[data-disabled=true]{pointer-events:none}.data-\[disabled\=true\]\:opacity-50[data-disabled=true]{opacity:.5}.data-\[error\=true\]\:text-destructive[data-error=true]{color:var(--destructive)}.data-\[inset\]\:pl-8[data-inset]{padding-left:calc(var(--spacing) * 8)}.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion=from-end]{--tw-enter-translate-x: calc(52 * var(--spacing))}.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion=from-start]{--tw-enter-translate-x: calc(52 * var(--spacing) * -1)}.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion=to-end]{--tw-exit-translate-x: calc(52 * var(--spacing))}.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion=to-start]{--tw-exit-translate-x: calc(52 * var(--spacing) * -1)}.data-\[motion\^\=from-\]\:animate-in[data-motion^=from-]{animation:enter var(--tw-duration, .15s) var(--tw-ease, ease)}.data-\[motion\^\=from-\]\:fade-in[data-motion^=from-]{--tw-enter-opacity: 0}.data-\[motion\^\=to-\]\:animate-out[data-motion^=to-]{animation:exit var(--tw-duration, .15s) var(--tw-ease, ease)}.data-\[motion\^\=to-\]\:fade-out[data-motion^=to-]{--tw-exit-opacity: 0}.data-\[orientation\=horizontal\]\:h-4[data-orientation=horizontal]{height:calc(var(--spacing) * 4)}.data-\[orientation\=horizontal\]\:h-full[data-orientation=horizontal]{height:100%}.data-\[orientation\=horizontal\]\:h-px[data-orientation=horizontal]{height:1px}.data-\[orientation\=horizontal\]\:w-full[data-orientation=horizontal]{width:100%}.data-\[orientation\=vertical\]\:h-full[data-orientation=vertical]{height:100%}.data-\[orientation\=vertical\]\:min-h-44[data-orientation=vertical]{min-height:calc(var(--spacing) * 44)}.data-\[orientation\=vertical\]\:w-1\.5[data-orientation=vertical]{width:calc(var(--spacing) * 1.5)}.data-\[orientation\=vertical\]\:w-auto[data-orientation=vertical]{width:auto}.data-\[orientation\=vertical\]\:w-full[data-orientation=vertical]{width:100%}.data-\[orientation\=vertical\]\:w-px[data-orientation=vertical]{width:1px}.data-\[orientation\=vertical\]\:flex-col[data-orientation=vertical]{flex-direction:column}.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction=vertical]{height:1px}.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction=vertical]{width:100%}.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction=vertical]{flex-direction:column}.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);left:calc(var(--spacing) * 0)}.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction=vertical]:after{content:var(--tw-content);height:calc(var(--spacing) * 1)}.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction=vertical]:after{content:var(--tw-content);width:100%}.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-x: calc(var(--spacing) * 0);translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-y: -50% ;translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[placeholder\]\:text-muted-foreground[data-placeholder]{color:var(--muted-foreground)}.data-\[selected\=true\]\:bg-accent[data-selected=true]{background-color:var(--accent)}.data-\[selected\=true\]\:text-accent-foreground[data-selected=true]{color:var(--accent-foreground)}.data-\[side\=bottom\]\:translate-y-1[data-side=bottom]{--tw-translate-y: calc(var(--spacing) * 1);translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[side\=bottom\]\:slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y: calc(2 * var(--spacing) * -1)}.data-\[side\=left\]\:-translate-x-1[data-side=left]{--tw-translate-x: calc(var(--spacing) * -1);translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[side\=left\]\:slide-in-from-right-2[data-side=left]{--tw-enter-translate-x: calc(2 * var(--spacing))}.data-\[side\=right\]\:translate-x-1[data-side=right]{--tw-translate-x: calc(var(--spacing) * 1);translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[side\=right\]\:slide-in-from-left-2[data-side=right]{--tw-enter-translate-x: calc(2 * var(--spacing) * -1)}.data-\[side\=top\]\:-translate-y-1[data-side=top]{--tw-translate-y: calc(var(--spacing) * -1);translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[side\=top\]\:slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y: calc(2 * var(--spacing))}.data-\[size\=default\]\:h-9[data-size=default]{height:calc(var(--spacing) * 9)}.data-\[size\=sm\]\:h-8[data-size=sm]{height:calc(var(--spacing) * 8)}:is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90>*)[data-slot=alert-description]{color:var(--destructive)}@supports (color: color-mix(in lab,red,red)){:is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90>*)[data-slot=alert-description]{color:color-mix(in oklab,var(--destructive) 90%,transparent)}}:is(.\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 *)[data-slot=command-input-wrapper]{height:calc(var(--spacing) * 12)}:is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 *)[data-slot=navigation-menu-link]:focus{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none *)[data-slot=navigation-menu-link]:focus{--tw-outline-style: none;outline-style:none}:is(.\*\:data-\[slot\=select-value\]\:line-clamp-1>*)[data-slot=select-value]{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}:is(.\*\:data-\[slot\=select-value\]\:flex>*)[data-slot=select-value]{display:flex}:is(.\*\:data-\[slot\=select-value\]\:items-center>*)[data-slot=select-value]{align-items:center}:is(.\*\:data-\[slot\=select-value\]\:gap-2>*)[data-slot=select-value]{gap:calc(var(--spacing) * 2)}.data-\[state\=active\]\:bg-card[data-state=active]{background-color:var(--card)}.data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state=checked]{--tw-translate-x: calc(100% - 2px) ;translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[state\=checked\]\:border-amber-500[data-state=checked]{border-color:var(--color-amber-500)}.data-\[state\=checked\]\:border-green-500[data-state=checked]{border-color:var(--color-green-500)}.data-\[state\=checked\]\:border-primary[data-state=checked]{border-color:var(--primary)}.data-\[state\=checked\]\:bg-amber-500[data-state=checked]{background-color:var(--color-amber-500)}.data-\[state\=checked\]\:bg-blue-500[data-state=checked]{background-color:var(--color-blue-500)}.data-\[state\=checked\]\:bg-green-500[data-state=checked]{background-color:var(--color-green-500)}.data-\[state\=checked\]\:bg-primary[data-state=checked]{background-color:var(--primary)}.data-\[state\=checked\]\:text-primary-foreground[data-state=checked]{color:var(--primary-foreground)}.data-\[state\=closed\]\:animate-accordion-up[data-state=closed]{animation:accordion-up var(--tw-duration, .2s) ease-out}.data-\[state\=closed\]\:animate-out[data-state=closed]{animation:exit var(--tw-duration, .15s) var(--tw-ease, ease)}.data-\[state\=closed\]\:duration-300[data-state=closed]{--tw-duration: .3s;transition-duration:.3s}.data-\[state\=closed\]\:fade-out-0[data-state=closed]{--tw-exit-opacity: 0}.data-\[state\=closed\]\:slide-out-to-bottom[data-state=closed]{--tw-exit-translate-y: 100%}.data-\[state\=closed\]\:slide-out-to-left[data-state=closed]{--tw-exit-translate-x: -100%}.data-\[state\=closed\]\:slide-out-to-right[data-state=closed]{--tw-exit-translate-x: 100%}.data-\[state\=closed\]\:slide-out-to-top[data-state=closed]{--tw-exit-translate-y: -100%}.data-\[state\=closed\]\:zoom-out-95[data-state=closed]{--tw-exit-scale: .95}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=closed]{animation:exit var(--tw-duration, .15s) var(--tw-ease, ease)}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=closed]{--tw-exit-opacity: 0}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=closed]{--tw-exit-scale: .95}.data-\[state\=hidden\]\:animate-out[data-state=hidden]{animation:exit var(--tw-duration, .15s) var(--tw-ease, ease)}.data-\[state\=hidden\]\:fade-out[data-state=hidden]{--tw-exit-opacity: 0}.data-\[state\=on\]\:bg-accent[data-state=on]{background-color:var(--accent)}.data-\[state\=on\]\:text-accent-foreground[data-state=on]{color:var(--accent-foreground)}.data-\[state\=open\]\:animate-accordion-down[data-state=open]{animation:accordion-down var(--tw-duration, .2s) ease-out}.data-\[state\=open\]\:animate-in[data-state=open]{animation:enter var(--tw-duration, .15s) var(--tw-ease, ease)}.data-\[state\=open\]\:bg-accent[data-state=open],.data-\[state\=open\]\:bg-accent\/50[data-state=open]{background-color:var(--accent)}@supports (color: color-mix(in lab,red,red)){.data-\[state\=open\]\:bg-accent\/50[data-state=open]{background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}.data-\[state\=open\]\:bg-secondary[data-state=open]{background-color:var(--secondary)}.data-\[state\=open\]\:text-accent-foreground[data-state=open]{color:var(--accent-foreground)}.data-\[state\=open\]\:text-muted-foreground[data-state=open]{color:var(--muted-foreground)}.data-\[state\=open\]\:opacity-100[data-state=open]{opacity:1}.data-\[state\=open\]\:duration-500[data-state=open]{--tw-duration: .5s;transition-duration:.5s}.data-\[state\=open\]\:fade-in-0[data-state=open]{--tw-enter-opacity: 0}.data-\[state\=open\]\:slide-in-from-bottom[data-state=open]{--tw-enter-translate-y: 100%}.data-\[state\=open\]\:slide-in-from-left[data-state=open]{--tw-enter-translate-x: -100%}.data-\[state\=open\]\:slide-in-from-right[data-state=open]{--tw-enter-translate-x: 100%}.data-\[state\=open\]\:slide-in-from-top[data-state=open]{--tw-enter-translate-y: -100%}.data-\[state\=open\]\:zoom-in-90[data-state=open]{--tw-enter-scale: .9}.data-\[state\=open\]\:zoom-in-95[data-state=open]{--tw-enter-scale: .95}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=open]{animation:enter var(--tw-duration, .15s) var(--tw-ease, ease)}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=open]{--tw-enter-opacity: 0}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=open]{--tw-enter-scale: .95}@media (hover: hover){.data-\[state\=open\]\:hover\:bg-accent[data-state=open]:hover{background-color:var(--accent)}}@media (hover: hover){.data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state=open]:hover{background-color:var(--sidebar-accent)}}@media (hover: hover){.data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state=open]:hover{color:var(--sidebar-accent-foreground)}}.data-\[state\=open\]\:focus\:bg-accent[data-state=open]:focus{background-color:var(--accent)}.data-\[state\=selected\]\:bg-muted[data-state=selected]{background-color:var(--muted)}.data-\[state\=unchecked\]\:translate-x-0[data-state=unchecked]{--tw-translate-x: calc(var(--spacing) * 0);translate:var(--tw-translate-x) var(--tw-translate-y)}.data-\[state\=unchecked\]\:bg-switch-background[data-state=unchecked]{background-color:var(--switch-background)}.data-\[state\=visible\]\:animate-in[data-state=visible]{animation:enter var(--tw-duration, .15s) var(--tw-ease, ease)}.data-\[state\=visible\]\:fade-in[data-state=visible]{--tw-enter-opacity: 0}.data-\[variant\=destructive\]\:text-destructive[data-variant=destructive]{color:var(--destructive)}.data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant=destructive]:focus{background-color:var(--destructive)}@supports (color: color-mix(in lab,red,red)){.data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant=destructive]:focus{background-color:color-mix(in oklab,var(--destructive) 10%,transparent)}}.data-\[variant\=destructive\]\:focus\:text-destructive[data-variant=destructive]:focus{color:var(--destructive)}.data-\[variant\=outline\]\:border-l-0[data-variant=outline]{border-left-style:var(--tw-border-style);border-left-width:0}.data-\[variant\=outline\]\:shadow-xs[data-variant=outline]{--tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\[variant\=outline\]\:first\:border-l[data-variant=outline]:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}.data-\[vaul-drawer-direction\=bottom\]\:inset-x-0[data-vaul-drawer-direction=bottom]{inset-inline:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=bottom\]\:bottom-0[data-vaul-drawer-direction=bottom]{bottom:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=bottom\]\:mt-24[data-vaul-drawer-direction=bottom]{margin-top:calc(var(--spacing) * 24)}.data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\][data-vaul-drawer-direction=bottom]{max-height:80vh}.data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg[data-vaul-drawer-direction=bottom]{border-top-left-radius:var(--radius);border-top-right-radius:var(--radius)}.data-\[vaul-drawer-direction\=bottom\]\:border-t[data-vaul-drawer-direction=bottom]{border-top-style:var(--tw-border-style);border-top-width:1px}.data-\[vaul-drawer-direction\=left\]\:inset-y-0[data-vaul-drawer-direction=left]{inset-block:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=left\]\:left-0[data-vaul-drawer-direction=left]{left:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=left\]\:w-3\/4[data-vaul-drawer-direction=left]{width:75%}.data-\[vaul-drawer-direction\=left\]\:border-r[data-vaul-drawer-direction=left]{border-right-style:var(--tw-border-style);border-right-width:1px}.data-\[vaul-drawer-direction\=right\]\:inset-y-0[data-vaul-drawer-direction=right]{inset-block:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=right\]\:right-0[data-vaul-drawer-direction=right]{right:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=right\]\:w-3\/4[data-vaul-drawer-direction=right]{width:75%}.data-\[vaul-drawer-direction\=right\]\:border-l[data-vaul-drawer-direction=right]{border-left-style:var(--tw-border-style);border-left-width:1px}.data-\[vaul-drawer-direction\=top\]\:inset-x-0[data-vaul-drawer-direction=top]{inset-inline:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=top\]\:top-0[data-vaul-drawer-direction=top]{top:calc(var(--spacing) * 0)}.data-\[vaul-drawer-direction\=top\]\:mb-24[data-vaul-drawer-direction=top]{margin-bottom:calc(var(--spacing) * 24)}.data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\][data-vaul-drawer-direction=top]{max-height:80vh}.data-\[vaul-drawer-direction\=top\]\:rounded-b-lg[data-vaul-drawer-direction=top]{border-bottom-right-radius:var(--radius);border-bottom-left-radius:var(--radius)}.data-\[vaul-drawer-direction\=top\]\:border-b[data-vaul-drawer-direction=top]{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}@media (width >= 40rem){.sm\:-top-1{top:calc(var(--spacing) * -1)}}@media (width >= 40rem){.sm\:-right-1{right:calc(var(--spacing) * -1)}}@media (width >= 40rem){.sm\:right-6{right:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:bottom-24{bottom:calc(var(--spacing) * 24)}}@media (width >= 40rem){.sm\:left-3{left:calc(var(--spacing) * 3)}}@media (width >= 40rem){.sm\:-mx-2{margin-inline:calc(var(--spacing) * -2)}}@media (width >= 40rem){.sm\:mx-8{margin-inline:calc(var(--spacing) * 8)}}@media (width >= 40rem){.sm\:mt-1{margin-top:calc(var(--spacing) * 1)}}@media (width >= 40rem){.sm\:mt-2{margin-top:calc(var(--spacing) * 2)}}@media (width >= 40rem){.sm\:mt-12{margin-top:calc(var(--spacing) * 12)}}@media (width >= 40rem){.sm\:mr-2{margin-right:calc(var(--spacing) * 2)}}@media (width >= 40rem){.sm\:mb-3{margin-bottom:calc(var(--spacing) * 3)}}@media (width >= 40rem){.sm\:mb-4{margin-bottom:calc(var(--spacing) * 4)}}@media (width >= 40rem){.sm\:mb-6{margin-bottom:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:mb-8{margin-bottom:calc(var(--spacing) * 8)}}@media (width >= 40rem){.sm\:block{display:block}}@media (width >= 40rem){.sm\:flex{display:flex}}@media (width >= 40rem){.sm\:hidden{display:none}}@media (width >= 40rem){.sm\:inline{display:inline}}@media (width >= 40rem){.sm\:h-2{height:calc(var(--spacing) * 2)}}@media (width >= 40rem){.sm\:h-4{height:calc(var(--spacing) * 4)}}@media (width >= 40rem){.sm\:h-5{height:calc(var(--spacing) * 5)}}@media (width >= 40rem){.sm\:h-6{height:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:h-8{height:calc(var(--spacing) * 8)}}@media (width >= 40rem){.sm\:h-9{height:calc(var(--spacing) * 9)}}@media (width >= 40rem){.sm\:h-10{height:calc(var(--spacing) * 10)}}@media (width >= 40rem){.sm\:h-12{height:calc(var(--spacing) * 12)}}@media (width >= 40rem){.sm\:min-h-\[100px\]{min-height:100px}}@media (width >= 40rem){.sm\:w-2{width:calc(var(--spacing) * 2)}}@media (width >= 40rem){.sm\:w-4{width:calc(var(--spacing) * 4)}}@media (width >= 40rem){.sm\:w-5{width:calc(var(--spacing) * 5)}}@media (width >= 40rem){.sm\:w-6{width:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:w-8{width:calc(var(--spacing) * 8)}}@media (width >= 40rem){.sm\:w-9{width:calc(var(--spacing) * 9)}}@media (width >= 40rem){.sm\:w-10{width:calc(var(--spacing) * 10)}}@media (width >= 40rem){.sm\:w-12{width:calc(var(--spacing) * 12)}}@media (width >= 40rem){.sm\:w-56{width:calc(var(--spacing) * 56)}}@media (width >= 40rem){.sm\:w-80{width:calc(var(--spacing) * 80)}}@media (width >= 40rem){.sm\:w-auto{width:auto}}@media (width >= 40rem){.sm\:max-w-2xl{max-width:var(--container-2xl)}}@media (width >= 40rem){.sm\:max-w-lg{max-width:var(--container-lg)}}@media (width >= 40rem){.sm\:max-w-md{max-width:var(--container-md)}}@media (width >= 40rem){.sm\:max-w-none{max-width:none}}@media (width >= 40rem){.sm\:max-w-sm{max-width:var(--container-sm)}}@media (width >= 40rem){.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}}@media (width >= 40rem){.sm\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (width >= 40rem){.sm\:flex-row{flex-direction:row}}@media (width >= 40rem){.sm\:flex-wrap{flex-wrap:wrap}}@media (width >= 40rem){.sm\:items-center{align-items:center}}@media (width >= 40rem){.sm\:justify-between{justify-content:space-between}}@media (width >= 40rem){.sm\:justify-end{justify-content:flex-end}}@media (width >= 40rem){.sm\:gap-2{gap:calc(var(--spacing) * 2)}}@media (width >= 40rem){.sm\:gap-2\.5{gap:calc(var(--spacing) * 2.5)}}@media (width >= 40rem){.sm\:gap-3{gap:calc(var(--spacing) * 3)}}@media (width >= 40rem){.sm\:gap-4{gap:calc(var(--spacing) * 4)}}@media (width >= 40rem){.sm\:gap-6{gap:calc(var(--spacing) * 6)}}@media (width >= 40rem){:where(.sm\:space-y-6>:not(:last-child)){--tw-space-y-reverse: 0;margin-block-start:calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)))}}@media (width >= 40rem){.sm\:p-2{padding:calc(var(--spacing) * 2)}}@media (width >= 40rem){.sm\:p-3{padding:calc(var(--spacing) * 3)}}@media (width >= 40rem){.sm\:p-4{padding:calc(var(--spacing) * 4)}}@media (width >= 40rem){.sm\:p-6{padding:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:px-2{padding-inline:calc(var(--spacing) * 2)}}@media (width >= 40rem){.sm\:px-3{padding-inline:calc(var(--spacing) * 3)}}@media (width >= 40rem){.sm\:px-6{padding-inline:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:py-4{padding-block:calc(var(--spacing) * 4)}}@media (width >= 40rem){.sm\:py-6{padding-block:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:py-8{padding-block:calc(var(--spacing) * 8)}}@media (width >= 40rem){.sm\:pt-6{padding-top:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:pr-2\.5{padding-right:calc(var(--spacing) * 2.5)}}@media (width >= 40rem){.sm\:pb-0{padding-bottom:calc(var(--spacing) * 0)}}@media (width >= 40rem){.sm\:pb-4{padding-bottom:calc(var(--spacing) * 4)}}@media (width >= 40rem){.sm\:pb-6{padding-bottom:calc(var(--spacing) * 6)}}@media (width >= 40rem){.sm\:pl-2\.5{padding-left:calc(var(--spacing) * 2.5)}}@media (width >= 40rem){.sm\:pl-10{padding-left:calc(var(--spacing) * 10)}}@media (width >= 40rem){.sm\:text-left{text-align:left}}@media (width >= 40rem){.sm\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading, var(--text-2xl--line-height))}}@media (width >= 40rem){.sm\:text-base{font-size:var(--text-base);line-height:var(--tw-leading, var(--text-base--line-height))}}@media (width >= 40rem){.sm\:text-lg{font-size:var(--text-lg);line-height:var(--tw-leading, var(--text-lg--line-height))}}@media (width >= 40rem){.sm\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height))}}@media (width >= 40rem){.sm\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading, var(--text-xl--line-height))}}@media (width >= 40rem){.sm\:whitespace-normal{white-space:normal}}@media (width >= 40rem){.sm\:ring-8{--tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}@media (width >= 40rem){.data-\[vaul-drawer-direction\=left\]\:sm\:max-w-sm[data-vaul-drawer-direction=left]{max-width:var(--container-sm)}}@media (width >= 40rem){.data-\[vaul-drawer-direction\=right\]\:sm\:max-w-sm[data-vaul-drawer-direction=right]{max-width:var(--container-sm)}}@media (width >= 48rem){.md\:absolute{position:absolute}}@media (width >= 48rem){.md\:block{display:block}}@media (width >= 48rem){.md\:flex{display:flex}}@media (width >= 48rem){.md\:hidden{display:none}}@media (width >= 48rem){.md\:w-\[var\(--radix-navigation-menu-viewport-width\)\]{width:var(--radix-navigation-menu-viewport-width)}}@media (width >= 48rem){.md\:w-auto{width:auto}}@media (width >= 48rem){.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}}@media (width >= 48rem){.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (width >= 48rem){.md\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}}@media (width >= 48rem){.md\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height))}}@media (width >= 48rem){.md\:opacity-0{opacity:0}}@media (width >= 48rem){.md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant=inset]~*){margin:calc(var(--spacing) * 2)}}@media (width >= 48rem){.md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant=inset]~*){margin-left:calc(var(--spacing) * 0)}}@media (width >= 48rem){.md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant=inset]~*){border-radius:calc(var(--radius) + 4px)}}@media (width >= 48rem){.md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant=inset]~*){--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}@media (width >= 48rem){.md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant=inset]~*):is(:where(.peer)[data-state=collapsed]~*){margin-left:calc(var(--spacing) * 2)}}@media (width >= 48rem){.md\:after\:hidden:after{content:var(--tw-content);display:none}}@media (width >= 64rem){.lg\:block{display:block}}@media (width >= 64rem){.lg\:flex{display:flex}}@media (width >= 64rem){.lg\:hidden{display:none}}@media (width >= 64rem){.lg\:h-12{height:calc(var(--spacing) * 12)}}@media (width >= 64rem){.lg\:w-12{width:calc(var(--spacing) * 12)}}@media (width >= 64rem){.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}}@media (width >= 64rem){.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (width >= 64rem){.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}}@media (width >= 64rem){.lg\:gap-3{gap:calc(var(--spacing) * 3)}}@media (width >= 64rem){.lg\:gap-8{gap:calc(var(--spacing) * 8)}}@media (width >= 64rem){.lg\:p-8{padding:calc(var(--spacing) * 8)}}@media (width >= 64rem){.lg\:px-8{padding-inline:calc(var(--spacing) * 8)}}@media (width >= 64rem){.lg\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading, var(--text-3xl--line-height))}}@media (width >= 80rem){.xl\:col-span-2{grid-column:span 2 / span 2}}@media (width >= 80rem){.xl\:mt-0{margin-top:calc(var(--spacing) * 0)}}@media (width >= 80rem){.xl\:block{display:block}}@media (width >= 80rem){.xl\:hidden{display:none}}@media (width >= 80rem){.xl\:w-64{width:calc(var(--spacing) * 64)}}@media (width >= 80rem){.xl\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}.dark\:border-amber-800:is(.dark *){border-color:var(--color-amber-800)}.dark\:border-amber-800\/50:is(.dark *){border-color:color-mix(in srgb,oklch(.473 .137 46.201) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:border-amber-800\/50:is(.dark *){border-color:color-mix(in oklab,var(--color-amber-800) 50%,transparent)}}.dark\:border-blue-800:is(.dark *){border-color:var(--color-blue-800)}.dark\:border-emerald-800\/50:is(.dark *){border-color:color-mix(in srgb,oklch(.432 .095 166.913) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:border-emerald-800\/50:is(.dark *){border-color:color-mix(in oklab,var(--color-emerald-800) 50%,transparent)}}.dark\:border-gray-600:is(.dark *){border-color:var(--color-gray-600)}.dark\:border-gray-700:is(.dark *){border-color:var(--color-gray-700)}.dark\:border-gray-800:is(.dark *){border-color:var(--color-gray-800)}.dark\:border-green-700:is(.dark *){border-color:var(--color-green-700)}.dark\:border-green-800:is(.dark *){border-color:var(--color-green-800)}.dark\:border-green-800\/50:is(.dark *){border-color:color-mix(in srgb,oklch(.448 .119 151.328) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:border-green-800\/50:is(.dark *){border-color:color-mix(in oklab,var(--color-green-800) 50%,transparent)}}.dark\:border-purple-800:is(.dark *){border-color:var(--color-purple-800)}.dark\:border-red-800:is(.dark *){border-color:var(--color-red-800)}.dark\:border-slate-600:is(.dark *){border-color:var(--color-slate-600)}.dark\:border-slate-700:is(.dark *){border-color:var(--color-slate-700)}.dark\:border-slate-700\/50:is(.dark *){border-color:color-mix(in srgb,oklch(.372 .044 257.287) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:border-slate-700\/50:is(.dark *){border-color:color-mix(in oklab,var(--color-slate-700) 50%,transparent)}}.dark\:bg-amber-900\/20:is(.dark *){background-color:color-mix(in srgb,oklch(.414 .112 45.904) 20%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-amber-900\/20:is(.dark *){background-color:color-mix(in oklab,var(--color-amber-900) 20%,transparent)}}.dark\:bg-amber-950:is(.dark *){background-color:var(--color-amber-950)}.dark\:bg-blue-900:is(.dark *){background-color:var(--color-blue-900)}.dark\:bg-blue-900\/20:is(.dark *){background-color:color-mix(in srgb,oklch(.379 .146 265.522) 20%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-blue-900\/20:is(.dark *){background-color:color-mix(in oklab,var(--color-blue-900) 20%,transparent)}}.dark\:bg-blue-950:is(.dark *){background-color:var(--color-blue-950)}.dark\:bg-destructive\/60:is(.dark *){background-color:var(--destructive)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-destructive\/60:is(.dark *){background-color:color-mix(in oklab,var(--destructive) 60%,transparent)}}.dark\:bg-gray-800:is(.dark *){background-color:var(--color-gray-800)}.dark\:bg-gray-800\/50:is(.dark *){background-color:color-mix(in srgb,oklch(.278 .033 256.848) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-gray-800\/50:is(.dark *){background-color:color-mix(in oklab,var(--color-gray-800) 50%,transparent)}}.dark\:bg-gray-900:is(.dark *){background-color:var(--color-gray-900)}.dark\:bg-gray-900\/80:is(.dark *){background-color:color-mix(in srgb,oklch(.21 .034 264.665) 80%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-gray-900\/80:is(.dark *){background-color:color-mix(in oklab,var(--color-gray-900) 80%,transparent)}}.dark\:bg-gray-900\/90:is(.dark *){background-color:color-mix(in srgb,oklch(.21 .034 264.665) 90%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-gray-900\/90:is(.dark *){background-color:color-mix(in oklab,var(--color-gray-900) 90%,transparent)}}.dark\:bg-gray-950:is(.dark *){background-color:var(--color-gray-950)}.dark\:bg-green-900:is(.dark *){background-color:var(--color-green-900)}.dark\:bg-green-900\/20:is(.dark *){background-color:color-mix(in srgb,oklch(.393 .095 152.535) 20%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-green-900\/20:is(.dark *){background-color:color-mix(in oklab,var(--color-green-900) 20%,transparent)}}.dark\:bg-green-900\/30:is(.dark *){background-color:color-mix(in srgb,oklch(.393 .095 152.535) 30%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-green-900\/30:is(.dark *){background-color:color-mix(in oklab,var(--color-green-900) 30%,transparent)}}.dark\:bg-green-950:is(.dark *){background-color:var(--color-green-950)}.dark\:bg-indigo-900\/20:is(.dark *){background-color:color-mix(in srgb,oklch(.359 .144 278.697) 20%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-indigo-900\/20:is(.dark *){background-color:color-mix(in oklab,var(--color-indigo-900) 20%,transparent)}}.dark\:bg-input\/30:is(.dark *){background-color:var(--input)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-input\/30:is(.dark *){background-color:color-mix(in oklab,var(--input) 30%,transparent)}}.dark\:bg-orange-900:is(.dark *){background-color:var(--color-orange-900)}.dark\:bg-orange-900\/20:is(.dark *){background-color:color-mix(in srgb,oklch(.408 .123 38.172) 20%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-orange-900\/20:is(.dark *){background-color:color-mix(in oklab,var(--color-orange-900) 20%,transparent)}}.dark\:bg-orange-950:is(.dark *){background-color:var(--color-orange-950)}.dark\:bg-purple-950:is(.dark *){background-color:var(--color-purple-950)}.dark\:bg-red-900:is(.dark *){background-color:var(--color-red-900)}.dark\:bg-red-900\/20:is(.dark *){background-color:color-mix(in srgb,oklch(.396 .141 25.723) 20%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-red-900\/20:is(.dark *){background-color:color-mix(in oklab,var(--color-red-900) 20%,transparent)}}.dark\:bg-red-950:is(.dark *){background-color:var(--color-red-950)}.dark\:bg-slate-700:is(.dark *){background-color:var(--color-slate-700)}.dark\:bg-slate-800:is(.dark *){background-color:var(--color-slate-800)}.dark\:bg-slate-800\/50:is(.dark *){background-color:color-mix(in srgb,oklch(.279 .041 260.031) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-slate-800\/50:is(.dark *){background-color:color-mix(in oklab,var(--color-slate-800) 50%,transparent)}}.dark\:bg-slate-900\/80:is(.dark *){background-color:color-mix(in srgb,oklch(.208 .042 265.755) 80%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-slate-900\/80:is(.dark *){background-color:color-mix(in oklab,var(--color-slate-900) 80%,transparent)}}.dark\:bg-slate-900\/90:is(.dark *){background-color:color-mix(in srgb,oklch(.208 .042 265.755) 90%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-slate-900\/90:is(.dark *){background-color:color-mix(in oklab,var(--color-slate-900) 90%,transparent)}}.dark\:bg-slate-900\/95:is(.dark *){background-color:color-mix(in srgb,oklch(.208 .042 265.755) 95%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:bg-slate-900\/95:is(.dark *){background-color:color-mix(in oklab,var(--color-slate-900) 95%,transparent)}}.dark\:bg-yellow-900:is(.dark *){background-color:var(--color-yellow-900)}.dark\:bg-yellow-950:is(.dark *){background-color:var(--color-yellow-950)}.dark\:from-emerald-900\/20:is(.dark *){--tw-gradient-from: color-mix(in srgb, oklch(.378 .077 168.94) 20%, transparent);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}@supports (color: color-mix(in lab,red,red)){.dark\:from-emerald-900\/20:is(.dark *){--tw-gradient-from: color-mix(in oklab, var(--color-emerald-900) 20%, transparent)}}.dark\:from-slate-800:is(.dark *){--tw-gradient-from: var(--color-slate-800);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.dark\:from-slate-900:is(.dark *){--tw-gradient-from: var(--color-slate-900);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.dark\:via-blue-900\/20:is(.dark *){--tw-gradient-via: color-mix(in srgb, oklch(.379 .146 265.522) 20%, transparent);--tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-via-stops)}@supports (color: color-mix(in lab,red,red)){.dark\:via-blue-900\/20:is(.dark *){--tw-gradient-via: color-mix(in oklab, var(--color-blue-900) 20%, transparent)}}.dark\:via-slate-800:is(.dark *){--tw-gradient-via: var(--color-slate-800);--tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-via-stops)}.dark\:to-indigo-900\/20:is(.dark *){--tw-gradient-to: color-mix(in srgb, oklch(.359 .144 278.697) 20%, transparent);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}@supports (color: color-mix(in lab,red,red)){.dark\:to-indigo-900\/20:is(.dark *){--tw-gradient-to: color-mix(in oklab, var(--color-indigo-900) 20%, transparent)}}.dark\:to-slate-700:is(.dark *){--tw-gradient-to: var(--color-slate-700);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.dark\:to-slate-900:is(.dark *){--tw-gradient-to: var(--color-slate-900);--tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))}.dark\:text-amber-200:is(.dark *){color:var(--color-amber-200)}.dark\:text-amber-300:is(.dark *){color:var(--color-amber-300)}.dark\:text-amber-400:is(.dark *){color:var(--color-amber-400)}.dark\:text-blue-200:is(.dark *){color:var(--color-blue-200)}.dark\:text-blue-300:is(.dark *){color:var(--color-blue-300)}.dark\:text-blue-400:is(.dark *){color:var(--color-blue-400)}.dark\:text-emerald-200:is(.dark *){color:var(--color-emerald-200)}.dark\:text-gray-300:is(.dark *){color:var(--color-gray-300)}.dark\:text-gray-400:is(.dark *){color:var(--color-gray-400)}.dark\:text-green-200:is(.dark *){color:var(--color-green-200)}.dark\:text-green-300:is(.dark *){color:var(--color-green-300)}.dark\:text-green-400:is(.dark *){color:var(--color-green-400)}.dark\:text-indigo-300:is(.dark *){color:var(--color-indigo-300)}.dark\:text-muted-foreground:is(.dark *){color:var(--muted-foreground)}.dark\:text-orange-200:is(.dark *){color:var(--color-orange-200)}.dark\:text-orange-300:is(.dark *){color:var(--color-orange-300)}.dark\:text-pink-400:is(.dark *){color:var(--color-pink-400)}.dark\:text-red-200:is(.dark *){color:var(--color-red-200)}.dark\:text-red-300:is(.dark *){color:var(--color-red-300)}.dark\:text-red-400:is(.dark *){color:var(--color-red-400)}.dark\:text-slate-300:is(.dark *){color:var(--color-slate-300)}.dark\:text-slate-400:is(.dark *){color:var(--color-slate-400)}.dark\:text-slate-600:is(.dark *){color:var(--color-slate-600)}.dark\:text-white:is(.dark *){color:var(--color-white)}.dark\:text-yellow-200:is(.dark *){color:var(--color-yellow-200)}.dark\:text-yellow-300:is(.dark *){color:var(--color-yellow-300)}.dark\:ring-slate-900:is(.dark *){--tw-ring-color: var(--color-slate-900)}@media (hover: hover){.dark\:hover\:border-slate-600:is(.dark *):hover{border-color:var(--color-slate-600)}}@media (hover: hover){.dark\:hover\:bg-amber-900:is(.dark *):hover{background-color:var(--color-amber-900)}}@media (hover: hover){.dark\:hover\:bg-blue-900:is(.dark *):hover{background-color:var(--color-blue-900)}}@media (hover: hover){.dark\:hover\:bg-green-900:is(.dark *):hover{background-color:var(--color-green-900)}}@media (hover: hover){.dark\:hover\:bg-input\/50:is(.dark *):hover{background-color:var(--input)}@supports (color: color-mix(in lab,red,red)){.dark\:hover\:bg-input\/50:is(.dark *):hover{background-color:color-mix(in oklab,var(--input) 50%,transparent)}}}@media (hover: hover){.dark\:hover\:bg-purple-900:is(.dark *):hover{background-color:var(--color-purple-900)}}@media (hover: hover){.dark\:hover\:bg-red-950:is(.dark *):hover{background-color:var(--color-red-950)}}@media (hover: hover){.dark\:hover\:bg-slate-600:is(.dark *):hover{background-color:var(--color-slate-600)}}@media (hover: hover){.dark\:hover\:bg-slate-600\/60:is(.dark *):hover{background-color:color-mix(in srgb,oklch(.446 .043 257.281) 60%,transparent)}@supports (color: color-mix(in lab,red,red)){.dark\:hover\:bg-slate-600\/60:is(.dark *):hover{background-color:color-mix(in oklab,var(--color-slate-600) 60%,transparent)}}}@media (hover: hover){.dark\:hover\:bg-slate-700:is(.dark *):hover{background-color:var(--color-slate-700)}}@media (hover: hover){.dark\:hover\:bg-slate-800:is(.dark *):hover{background-color:var(--color-slate-800)}}@media (hover: hover){.dark\:hover\:text-gray-200:is(.dark *):hover{color:var(--color-gray-200)}}.dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible{--tw-ring-color: var(--destructive)}@supports (color: color-mix(in lab,red,red)){.dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible{--tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent)}}.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color: var(--destructive)}@supports (color: color-mix(in lab,red,red)){.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent)}}.dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active=true][aria-invalid=true]{--tw-ring-color: var(--destructive)}@supports (color: color-mix(in lab,red,red)){.dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active=true][aria-invalid=true]{--tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent)}}.dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state=active]{border-color:var(--input)}.dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state=active]{background-color:var(--input)}@supports (color: color-mix(in lab,red,red)){.dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state=active]{background-color:color-mix(in oklab,var(--input) 30%,transparent)}}.dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state=active]{color:var(--foreground)}.dark\:data-\[state\=checked\]\:bg-primary:is(.dark *)[data-state=checked]{background-color:var(--primary)}.dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state=checked]{background-color:var(--primary-foreground)}.dark\:data-\[state\=unchecked\]\:bg-card-foreground:is(.dark *)[data-state=unchecked]{background-color:var(--card-foreground)}.dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state=unchecked]{background-color:var(--input)}@supports (color: color-mix(in lab,red,red)){.dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state=unchecked]{background-color:color-mix(in oklab,var(--input) 80%,transparent)}}.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant=destructive]:focus{background-color:var(--destructive)}@supports (color: color-mix(in lab,red,red)){.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant=destructive]:focus{background-color:color-mix(in oklab,var(--destructive) 20%,transparent)}}.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text{fill:var(--muted-foreground)}.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"]{stroke:var(--border)}@supports (color: color-mix(in lab,red,red)){.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"]{stroke:color-mix(in oklab,var(--border) 50%,transparent)}}.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor{stroke:var(--border)}.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"]{stroke:#0000}.\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer{outline-offset:2px;outline:2px solid #0000}}.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"]{stroke:var(--border)}.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector,.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{fill:var(--muted)}.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"]{stroke:var(--border)}.\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector{outline-offset:2px;outline:2px solid #0000}}.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"]{stroke:#0000}.\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface{outline-offset:2px;outline:2px solid #0000}}.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{padding-inline:calc(var(--spacing) * 2)}.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading]{padding-block:calc(var(--spacing) * 1.5)}.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading]{font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height))}.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium)}.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading]{color:var(--muted-foreground)}.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group]{padding-inline:calc(var(--spacing) * 2)}.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden])~[cmdk-group]{padding-top:calc(var(--spacing) * 0)}.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg{height:calc(var(--spacing) * 5)}.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg{width:calc(var(--spacing) * 5)}.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input]{height:calc(var(--spacing) * 12)}.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item]{padding-inline:calc(var(--spacing) * 2)}.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item]{padding-block:calc(var(--spacing) * 3)}.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg{height:calc(var(--spacing) * 5)}.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg{width:calc(var(--spacing) * 5)}.\[\&_p\]\:leading-relaxed p{--tw-leading: var(--leading-relaxed);line-height:var(--leading-relaxed)}.\[\&_svg\]\:pointer-events-none svg{pointer-events:none}.\[\&_svg\]\:size-4 svg{width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.\[\&_svg\]\:shrink-0 svg{flex-shrink:0}.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*=size-]){width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*=text-]){color:var(--muted-foreground)}.\[\&_tr\]\:border-b tr{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.\[\&_tr\:last-child\]\:border-0 tr:last-child{border-style:var(--tw-border-style);border-width:0}.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has(>.day-range-end){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has(>.day-range-start){border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]){border-radius:calc(var(--radius) - 2px)}.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]){background-color:var(--accent)}.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:first-child:has([aria-selected]){border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:last-child:has([aria-selected]){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]){padding-right:calc(var(--spacing) * 0)}.\[\.border-b\]\:pb-6.border-b{padding-bottom:calc(var(--spacing) * 6)}.\[\.border-t\]\:pt-6.border-t{padding-top:calc(var(--spacing) * 6)}:is(.\*\:\[span\]\:last\:flex>*):is(span):last-child{display:flex}:is(.\*\:\[span\]\:last\:items-center>*):is(span):last-child{align-items:center}:is(.\*\:\[span\]\:last\:gap-2>*):is(span):last-child{gap:calc(var(--spacing) * 2)}:is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant=destructive]>*):is(svg){color:var(--destructive)!important}.\[\&\:last-child\]\:pb-6:last-child{padding-bottom:calc(var(--spacing) * 6)}.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\]>[role=checkbox]{--tw-translate-y: 2px;translate:var(--tw-translate-x) var(--tw-translate-y)}.\[\&\>button\]\:hidden>button{display:none}.\[\&\>span\:last-child\]\:truncate>span:last-child{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.\[\&\>svg\]\:pointer-events-none>svg{pointer-events:none}.\[\&\>svg\]\:size-3>svg{width:calc(var(--spacing) * 3);height:calc(var(--spacing) * 3)}.\[\&\>svg\]\:size-3\.5>svg{width:calc(var(--spacing) * 3.5);height:calc(var(--spacing) * 3.5)}.\[\&\>svg\]\:size-4>svg{width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.\[\&\>svg\]\:h-2\.5>svg{height:calc(var(--spacing) * 2.5)}.\[\&\>svg\]\:h-3>svg{height:calc(var(--spacing) * 3)}.\[\&\>svg\]\:w-2\.5>svg{width:calc(var(--spacing) * 2.5)}.\[\&\>svg\]\:w-3>svg{width:calc(var(--spacing) * 3)}.\[\&\>svg\]\:shrink-0>svg{flex-shrink:0}.\[\&\>svg\]\:translate-y-0\.5>svg{--tw-translate-y: calc(var(--spacing) * .5);translate:var(--tw-translate-x) var(--tw-translate-y)}.\[\&\>svg\]\:text-current>svg{color:currentColor}.\[\&\>svg\]\:text-muted-foreground>svg{color:var(--muted-foreground)}.\[\&\>svg\]\:text-sidebar-accent-foreground>svg{color:var(--sidebar-accent-foreground)}.\[\&\>tr\]\:last\:border-b-0>tr:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div{rotate:90deg}.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg{rotate:180deg}[data-side=left][data-collapsible=offcanvas] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2{right:calc(var(--spacing) * -2)}[data-side=left][data-state=collapsed] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize{cursor:e-resize}[data-side=right][data-collapsible=offcanvas] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2{left:calc(var(--spacing) * -2)}[data-side=right][data-state=collapsed] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize{cursor:w-resize}@media (hover: hover){a.\[a\&\]\:hover\:bg-accent:hover{background-color:var(--accent)}}@media (hover: hover){a.\[a\&\]\:hover\:bg-destructive\/90:hover{background-color:var(--destructive)}@supports (color: color-mix(in lab,red,red)){a.\[a\&\]\:hover\:bg-destructive\/90:hover{background-color:color-mix(in oklab,var(--destructive) 90%,transparent)}}}@media (hover: hover){a.\[a\&\]\:hover\:bg-primary\/90:hover{background-color:var(--primary)}@supports (color: color-mix(in lab,red,red)){a.\[a\&\]\:hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--primary) 90%,transparent)}}}@media (hover: hover){a.\[a\&\]\:hover\:bg-secondary\/90:hover{background-color:var(--secondary)}@supports (color: color-mix(in lab,red,red)){a.\[a\&\]\:hover\:bg-secondary\/90:hover{background-color:color-mix(in oklab,var(--secondary) 90%,transparent)}}}@media (hover: hover){a.\[a\&\]\:hover\:text-accent-foreground:hover{color:var(--accent-foreground)}}.transition-wave{transition:all var(--openai-duration-normal) var(--openai-ease-in-out)}.transition-wave-fast{transition:all var(--openai-duration-fast) var(--openai-ease-in-out)}.transition-wave-slow{transition:all var(--openai-duration-slow) var(--openai-ease-out)}.shadow-wave-xs{box-shadow:var(--shadow-wave-xs)}.shadow-wave-sm{box-shadow:var(--shadow-wave-sm)}.shadow-wave-md{box-shadow:var(--shadow-wave-md)}.shadow-wave-lg{box-shadow:var(--shadow-wave-lg)}.shadow-wave-xl{box-shadow:var(--shadow-wave-xl)}.shadow-wave-glow{box-shadow:var(--shadow-wave-glow)}.bg-gradient-wave{background:var(--gradient-wave)}.bg-gradient-wave-soft{background:var(--gradient-wave-soft)}.bg-gradient-wave-reverse{background:var(--gradient-wave-reverse)}.bg-gradient-premium{background:var(--gradient-premium)}.bg-gradient-primary{background:var(--gradient-primary)}.bg-gradient-success{background:var(--gradient-success)}.bg-gradient-warning{background:var(--gradient-warning)}.bg-gradient-danger{background:var(--gradient-danger)}.text-wave-primary{color:var(--wave-primary)}.text-wave-medium{color:var(--wave-medium)}.text-wave-deep{color:var(--wave-deep)}.text-medical-primary{color:var(--medical-primary)}.text-medical-success{color:var(--medical-success)}.text-medical-warning{color:var(--medical-warning)}.text-medical-danger{color:var(--medical-danger)}.text-medical-info{color:var(--medical-info)}.border-wave{border-color:var(--wave-primary)}.border-wave-light{border-color:var(--wave-light)}.border-medical-primary{border-color:var(--medical-primary)}.font-inter{font-family:var(--font-family-base)}.font-mono{font-family:var(--font-family-mono)}.font-optimized{font-family:"Inter-Optimized",var(--font-family-base)}.p-spacing-xs{padding:var(--spacing-xs)}.p-spacing-sm{padding:var(--spacing-sm)}.p-spacing-md{padding:var(--spacing-md)}.p-spacing-lg{padding:var(--spacing-lg)}.p-spacing-xl{padding:var(--spacing-xl)}.p-spacing-2xl{padding:var(--spacing-2xl)}.p-spacing-3xl{padding:var(--spacing-3xl)}.m-spacing-xs{margin:var(--spacing-xs)}.m-spacing-sm{margin:var(--spacing-sm)}.m-spacing-md{margin:var(--spacing-md)}.m-spacing-lg{margin:var(--spacing-lg)}.m-spacing-xl{margin:var(--spacing-xl)}.m-spacing-2xl{margin:var(--spacing-2xl)}.m-spacing-3xl{margin:var(--spacing-3xl)}.gap-spacing-xs{gap:var(--spacing-xs)}.gap-spacing-sm{gap:var(--spacing-sm)}.gap-spacing-md{gap:var(--spacing-md)}.gap-spacing-lg{gap:var(--spacing-lg)}.gap-spacing-xl{gap:var(--spacing-xl)}.gap-spacing-2xl{gap:var(--spacing-2xl)}.gap-spacing-3xl{gap:var(--spacing-3xl)}}:root{--ratio-golden: 1.618;--spacing-unit: 8px;--spacing-xs: calc(var(--spacing-unit) * .5);--spacing-sm: var(--spacing-unit);--spacing-md: calc(var(--spacing-unit) * 2);--spacing-lg: calc(var(--spacing-unit) * 3);--spacing-xl: calc(var(--spacing-unit) * 4);--spacing-2xl: calc(var(--spacing-unit) * 6);--spacing-3xl: calc(var(--spacing-unit) * 8);--card-ratio: 1.5;--button-ratio: 2.5;--input-ratio: 3;--modal-ratio: 1.333;--medical-primary: #5aa9e6;--medical-secondary: #7fc8f8;--medical-tertiary: #b8e0ff;--medical-success: #00c896;--medical-warning: #ffb800;--medical-danger: #ff5a5f;--medical-info: #8b7fff;--neutral-0: #fff;--neutral-50: #fafbfc;--neutral-100: #f6f8fa;--neutral-200: #e1e4e8;--neutral-300: #d1d5da;--neutral-400: #959da5;--neutral-500: #6a737d;--neutral-600: #586069;--neutral-700: #444d56;--neutral-800: #2f363d;--neutral-900: #24292e;--neutral-1000: #1b1f23;--gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);--gradient-success: linear-gradient(135deg, #56ccf2 0%, #2f80ed 100%);--gradient-warning: linear-gradient(135deg, #f2c94c 0%, #f2994a 100%);--gradient-danger: linear-gradient(135deg, #eb5757 0%, #9b51e0 100%);--gradient-premium: linear-gradient(180deg, #667eea1a 0%, #764ba20d 50%, transparent 100%);--wave-lightest: #e3f5ff;--wave-light: #87ceeb;--wave-medium: #4a90e2;--wave-primary: #2e7cd6;--wave-deep: #1e5ba8;--wave-darkest: #0f3a6b;--gradient-wave: linear-gradient(135deg, #e3f5ff 0%, #87ceeb 25%, #4a90e2 50%, #2e7cd6 75%, #1e5ba8 100%);--gradient-wave-soft: linear-gradient(180deg, #e3f5ff 0%, #87ceeb 50%, #4a90e2 100%);--gradient-wave-reverse: linear-gradient(135deg, #1e5ba8 0%, #2e7cd6 25%, #4a90e2 50%, #87ceeb 75%, #e3f5ff 100%);--gradient-wave-subtle: linear-gradient(90deg, #e3f5ff1a 0%, #4a90e21a 50%, #1e5ba81a 100%);--bg-primary: #fff;--bg-secondary: #f7f9fb;--bg-tertiary: #eff5fb;--bg-accent: #e3f5ff;--text-primary: #1a1a1a;--text-secondary: #4a5568;--text-tertiary: #718096;--text-disabled: #a0aec0;--text-accent: var(--wave-primary);--border-default: #2e7cd61a;--border-hover: #2e7cd633;--border-focus: #2e7cd666;--border-accent: #4a90e24d;--shadow-xs: 0 1px 2px 0 #0000000d;--shadow-sm: 0 1px 3px 0 #0000001a, 0 1px 2px 0 #0000000f;--shadow-md: 0 4px 6px -1px #0000001a, 0 2px 4px -1px #0000000f;--shadow-lg: 0 10px 15px -3px #0000001a, 0 4px 6px -2px #0000000d;--shadow-xl: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;--shadow-2xl: 0 25px 50px -12px #00000040;--shadow-inner: inset 0 2px 4px 0 #0000000f;--shadow-glow: 0 0 40px #667eea33;--shadow-wave-xs: 0 1px 2px #1e5ba80d;--shadow-wave-sm: 0 2px 4px #1e5ba814;--shadow-wave-md: 0 4px 8px #1e5ba81a;--shadow-wave-lg: 0 8px 16px #1e5ba81f;--shadow-wave-xl: 0 16px 32px #1e5ba826;--shadow-wave-glow: 0 0 40px #4a90e233;--shadow-wave-focus: 0 0 0 4px #4a90e21a;--glass-bg: #ffffffb3;--glass-border: #ffffff2e;--glass-shadow: 0 8px 32px 0 #1f268726;--backdrop-blur: saturate(180%) blur(20px);--dark-bg-primary: #0f1419;--dark-bg-secondary: #1a2332;--dark-bg-tertiary: #243447;--dark-text-primary: #fff;--dark-text-secondary: #b8c5d6;--dark-border: #87ceeb1a;--font-size: 14px;--background: var(--bg-primary);--foreground: var(--text-primary);--card: var(--bg-primary);--card-foreground: var(--text-primary);--popover: var(--bg-primary);--popover-foreground: var(--text-primary);--primary: var(--wave-primary);--primary-foreground: #fff;--secondary: var(--bg-secondary);--secondary-foreground: var(--text-primary);--muted: var(--bg-tertiary);--muted-foreground: var(--text-secondary);--accent: var(--bg-accent);--accent-foreground: var(--wave-primary);--destructive: #ef4444;--destructive-foreground: #fff;--border: var(--border-default);--input: transparent;--input-background: var(--bg-secondary);--switch-background: #cbced4;--font-weight-medium: 500;--font-weight-normal: 400;--ring: var(--wave-primary);--chart-1: var(--wave-primary);--chart-2: var(--wave-medium);--chart-3: var(--wave-deep);--chart-4: var(--wave-light);--chart-5: var(--wave-lightest);--radius: .625rem;--sidebar: var(--bg-secondary);--sidebar-foreground: var(--text-primary);--sidebar-primary: var(--wave-primary);--sidebar-primary-foreground: #fff;--sidebar-accent: var(--bg-accent);--sidebar-accent-foreground: var(--wave-deep);--sidebar-border: var(--border-default);--sidebar-ring: var(--wave-primary);--sidebar-width: 280px;--sidebar-width-collapsed: 80px;--sidebar-shadow: var(--shadow-wave-lg);--sidebar-border-radius: 1rem;--header-height: 64px;--header-bg: var(--glass-bg);--header-border: var(--glass-border);--header-shadow: var(--glass-shadow);--openai-spring-stiffness: 260;--openai-spring-damping: 20;--openai-spring-mass: 1;--openai-ease-out: cubic-bezier(0, 0, .2, 1);--openai-ease-in-out: cubic-bezier(.4, 0, .2, 1);--openai-ease-smooth: cubic-bezier(.25, .1, .25, 1);--openai-ease-bounce: cubic-bezier(.68, -.55, .265, 1.55);--openai-duration-instant: 80ms;--openai-duration-fast: .12s;--openai-duration-normal: .15s;--openai-duration-slow: .2s;--openai-duration-slower: .3s;--openai-duration-slowest: .5s;--openai-shadow-sm: var(--shadow-wave-sm);--openai-shadow-md: var(--shadow-wave-md);--openai-shadow-lg: var(--shadow-wave-lg);--openai-shadow-xl: var(--shadow-wave-xl);--font-family-base: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;--font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;--font-family-heading: var(--font-family-base);--grid-columns: 12;--grid-gap: var(--spacing-md);--grid-gap-sm: var(--spacing-sm);--grid-gap-lg: var(--spacing-lg);--grid-container-max-width: 1440px;--grid-container-padding: var(--spacing-xl)}[data-theme=light]{--bg: var(--bg-primary);--bg-sidebar: var(--bg-secondary);--text: var(--text-primary);--border: var(--border-default)}.dark,[data-theme=dark]{--background: var(--dark-bg-primary);--foreground: var(--dark-text-primary);--card: var(--dark-bg-secondary);--card-foreground: var(--dark-text-primary);--popover: var(--dark-bg-secondary);--popover-foreground: var(--dark-text-primary);--primary: var(--wave-light);--primary-foreground: var(--dark-bg-primary);--secondary: var(--dark-bg-tertiary);--secondary-foreground: var(--dark-text-primary);--muted: var(--dark-bg-tertiary);--muted-foreground: var(--dark-text-secondary);--accent: var(--dark-bg-tertiary);--accent-foreground: var(--wave-light);--destructive: #ef4444;--destructive-foreground: #fff;--border: var(--dark-border);--input: var(--dark-bg-tertiary);--ring: var(--wave-light);--sidebar: var(--dark-bg-secondary);--sidebar-foreground: var(--dark-text-primary);--sidebar-primary: var(--wave-light);--sidebar-primary-foreground: var(--dark-bg-primary);--sidebar-accent: var(--dark-bg-tertiary);--sidebar-accent-foreground: var(--dark-text-primary);--sidebar-border: var(--dark-border);--sidebar-ring: var(--wave-light);--shadow-xs: 0 1px 2px #0003;--shadow-sm: 0 2px 4px #0000004d;--shadow-md: 0 4px 6px #0000004d, 0 1px 3px #0003;--shadow-lg: 0 10px 15px #0006, 0 4px 6px #0000004d;--shadow-xl: 0 20px 25px #00000080, 0 10px 10px #0003;--shadow-2xl: 0 25px 50px #0009;--shadow-wave-xs: 0 1px 2px #0003;--shadow-wave-sm: 0 2px 4px #0000004d;--shadow-wave-md: 0 4px 6px #0000004d, 0 1px 3px #0003;--shadow-wave-lg: 0 10px 15px #0006, 0 4px 6px #0000004d;--shadow-wave-xl: 0 20px 25px #00000080, 0 10px 10px #0003;--sidebar-shadow: 0 4px 12px #0000004d, 0 2px 4px #0003;--glass-bg: #0d1117b3;--glass-border: #30363d4d;--glass-shadow: 0 8px 32px 0 #0000005e}@layer components{@keyframes slideInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes fadeInScale{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}@keyframes pulseGlow{0%,to{box-shadow:0 0 20px #667eea80}50%{box-shadow:0 0 40px #667eeacc}}@keyframes rotateIn{0%{opacity:0;transform:rotate(-180deg)scale(.5)}to{opacity:1;transform:rotate(0)scale(1)}}@keyframes heartbeat{0%,to{transform:scale(1)}14%{transform:scale(1.1)}28%{transform:scale(1)}42%{transform:scale(1.1)}70%{transform:scale(1)}}@keyframes waveMotion{0%,to{transform:translateY(0)scaleY(1)}25%{transform:translateY(-2px)scaleY(.98)}50%{transform:translateY(-4px)scaleY(.95)}75%{transform:translateY(-2px)scaleY(.98)}}@keyframes waveFlow{0%{transform:translate(-100%)skew(-15deg)}to{transform:translate(100%)skew(-15deg)}}@keyframes wavePulse{0%,to{box-shadow:0 0 #4a90e266}50%{box-shadow:0 0 0 10px #4a90e200}}@keyframes pulseECG{0%{stroke-dasharray:0 1000}50%{stroke-dasharray:100 1000}to{stroke-dasharray:0 1000;stroke-dashoffset:-1000px}}@keyframes dnaRotate{0%{transform:rotateY(0)}to{transform:rotateY(360deg)}}@keyframes shimmerWave{0%{background-position:-200%}to{background-position:200%}}@keyframes openai-fade-in{0%{opacity:0}to{opacity:1}}@keyframes openai-slide-up{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes openai-slide-in-left{0%{opacity:0;transform:translate(-10px)}to{opacity:1;transform:translate(0)}}@keyframes openai-scale-in{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}@keyframes blink{0%,49%{opacity:1}50%,to{opacity:0}}@keyframes pulse-gentle{0%,to{opacity:.95}50%{opacity:1}}@keyframes rippleWave{to{opacity:0;transform:scale(4)}}@keyframes typingDot{0%,60%,to{opacity:.5;transform:translateY(0)}30%{opacity:1;transform:translateY(-10px)}}.animate-entrance{animation:.6s cubic-bezier(.23,1,.32,1) forwards slideInUp}.animate-scale{animation:.4s cubic-bezier(.23,1,.32,1) forwards fadeInScale}.animate-glow{animation:2s ease-in-out infinite pulseGlow}.animate-heartbeat{animation:1.5s ease-in-out infinite heartbeat}.wave-motion{animation:3s ease-in-out infinite waveMotion}.wave-flow{position:relative;overflow:hidden}.wave-flow:before{content:"";background:var(--gradient-wave-subtle);width:100%;height:100%;animation:2s linear infinite waveFlow;position:absolute;top:0;left:0}.wave-pulse{animation:2s infinite wavePulse}.pulse-ecg{animation:2s ease-in-out infinite pulseECG}.dna-helix{width:60px;height:60px;position:relative}.dna-helix:before,.dna-helix:after{content:"";border:3px solid #0000;border-top-color:var(--wave-primary);border-radius:50%;width:100%;height:100%;animation:1.5s linear infinite dnaRotate;position:absolute}.dna-helix:after{border-top-color:var(--wave-medium);animation-delay:.25s;transform:scale(.8)}.hover-lift{cursor:pointer;transition:all .3s cubic-bezier(.23,1,.32,1)}.hover-lift:hover{box-shadow:var(--shadow-xl);transform:translateY(-4px)}.hover-grow{transition:transform .2s}.hover-grow:hover{transform:scale(1.05)}.hover-glow:hover{box-shadow:var(--shadow-glow)}.glass{background:var(--glass-bg);-webkit-backdrop-filter:var(--backdrop-blur);border:1px solid var(--glass-border);box-shadow:var(--glass-shadow)}.openai-fade-in{animation:openai-fade-in var(--openai-duration-slow) var(--openai-ease-out)}.openai-slide-up{animation:openai-slide-up var(--openai-duration-slow) var(--openai-ease-out)}.openai-slide-in-left{animation:openai-slide-in-left var(--openai-duration-slow) var(--openai-ease-out)}.openai-scale-in{animation:openai-scale-in var(--openai-duration-normal) var(--openai-ease-out)}.typing-cursor{color:#4a90e2b3;font-weight:300;animation:1s infinite blink}.dark .typing-cursor{color:#87ceebb3}.pulse-gentle{animation:3s ease-in-out infinite pulse-gentle}.openai-button{transition:all var(--openai-duration-normal) var(--openai-ease-in-out);border-radius:var(--radius);position:relative;overflow:hidden}.openai-button:before{content:"";background:var(--gradient-wave-subtle);opacity:0;width:100%;height:100%;transition:all var(--openai-duration-slow) var(--openai-ease-out);position:absolute;top:0;left:-100%}.openai-button:hover{box-shadow:var(--shadow-wave-md);border-color:var(--wave-primary);transform:translateY(-1px)}.openai-button:hover:before{opacity:1;left:0}.openai-button:active{transition-duration:var(--openai-duration-fast);transform:translateY(1px)scale(.98)}.ripple{position:relative;overflow:hidden}.ripple:before{content:"";background:#ffffff80;border-radius:50%;width:0;height:0;transition:width .6s,height .6s;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.ripple:active:before{width:300px;height:300px}.openai-card{transition:all var(--openai-duration-slow) var(--openai-ease-in-out);box-shadow:var(--shadow-wave-sm);border:1px solid var(--border-default);border-radius:var(--radius-lg);background:var(--bg-primary);position:relative;overflow:hidden}.openai-card:before{content:"";background:var(--gradient-wave);opacity:0;width:100%;height:1px;transition:opacity var(--openai-duration-normal) var(--openai-ease-out);position:absolute;top:0;left:0}.openai-card:hover{box-shadow:var(--shadow-wave-lg);border-color:var(--border-hover);transform:translateY(-2px)}.openai-card:hover:before{opacity:1}.stat-card{transition:all var(--openai-duration-slow) var(--openai-ease-in-out);border-radius:var(--radius-lg);background:var(--bg-primary);border:1px solid var(--border-default);padding:var(--spacing-lg);min-height:120px;position:relative;overflow:hidden}.stat-card:before{content:"";background:var(--gradient-wave);opacity:0;width:100%;height:2px;transition:opacity var(--openai-duration-normal);position:absolute;top:0;left:0}.stat-card:hover:before{opacity:1}.stat-bg{opacity:.1;border-radius:0 var(--radius-lg) 0 100%;width:80px;height:80px;position:absolute;top:0;right:0}.stat-content{z-index:2;position:relative}.stat-header{margin-bottom:var(--spacing-sm);justify-content:space-between;align-items:center;display:flex}.stat-title{color:var(--text-secondary);text-transform:uppercase;letter-spacing:.5px;font-size:13px;font-weight:500}.stat-icon{font-size:20px}.stat-value{color:var(--text-primary);margin-bottom:var(--spacing-xs);font-size:32px;font-weight:700;line-height:1}.stat-footer{align-items:center;gap:var(--spacing-xs);display:flex}.stat-change{border-radius:4px;padding:2px 6px;font-size:12px;font-weight:600}.stat-change.positive{color:var(--medical-success);background:#00c8961a}.stat-change.negative{color:var(--medical-danger);background:#ff5a5f1a}.stat-change.neutral{color:var(--text-secondary);background:var(--neutral-100)}.stat-period{color:var(--text-tertiary);font-size:11px}.openai-sidebar{width:var(--sidebar-width);box-shadow:var(--sidebar-shadow);transition:width var(--openai-duration-slower) var(--openai-ease-out);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid var(--sidebar-border);background:var(--sidebar);position:relative}.openai-sidebar:before{content:"";background:var(--gradient-wave);opacity:.3;width:2px;height:100%;position:absolute;top:0;left:0}.openai-sidebar.collapsed{width:var(--sidebar-width-collapsed)}.openai-sidebar-item{transition:all var(--openai-duration-fast) var(--openai-ease-in-out);border-radius:8px;margin:2px 8px;position:relative;overflow:hidden}.openai-sidebar-item:after{content:"";background:var(--gradient-wave);width:3px;height:0;transition:height var(--openai-duration-normal) var(--openai-ease-out);border-radius:0 3px 3px 0;position:absolute;top:50%;left:0;transform:translateY(-50%)}.openai-sidebar-item:hover{background-color:var(--sidebar-accent);color:var(--sidebar-accent-foreground);transform:translate(2px)}.openai-sidebar-item:active{transition-duration:var(--openai-duration-fast);transform:translate(1px)scale(.98)}.openai-sidebar-item.active{background-color:var(--sidebar-accent);color:var(--sidebar-primary);font-weight:500}.openai-sidebar-item.active:after{height:24px}.quick-actions{gap:var(--spacing-sm);padding:var(--spacing-md);margin-bottom:var(--spacing-md);display:flex}.quick-btn{border-radius:var(--radius);border:1px solid var(--border-default);background:var(--bg-primary);justify-content:center;align-items:center;gap:var(--spacing-xs);height:44px;transition:all var(--openai-duration-normal);flex:1;font-size:12px;font-weight:600;display:flex;position:relative;overflow:hidden}.quick-btn:before{content:"";width:100%;height:100%;transition:left var(--openai-duration-slow);background:linear-gradient(90deg,#0000,#fff3,#0000);position:absolute;top:0;left:-100%}.quick-btn:hover:before{left:100%}.quick-btn.emergency{border-color:var(--medical-danger);color:var(--medical-danger)}.quick-btn.emergency:hover{background:var(--medical-danger);color:#fff;box-shadow:0 0 20px #ff5a5f4d}.quick-btn.ai-chat{border-color:var(--medical-info);color:var(--medical-info)}.quick-btn.ai-chat:hover{background:var(--medical-info);color:#fff;box-shadow:0 0 20px #8b7fff4d}.nav-group{margin-bottom:var(--spacing-lg)}.nav-group-title{text-transform:uppercase;letter-spacing:1px;color:var(--text-tertiary);margin:0 var(--spacing-md) var(--spacing-sm);opacity:.8;font-size:11px;font-weight:600}.nav-items{margin:0;padding:0;list-style:none}.nav-item{margin-bottom:2px}.nav-link{align-items:center;gap:var(--spacing-md);padding:var(--spacing-sm) var(--spacing-md);border-radius:var(--radius);color:var(--text-secondary);transition:all var(--openai-duration-fast);text-decoration:none;display:flex;position:relative;overflow:hidden}.nav-link:hover{background:var(--sidebar-accent);color:var(--sidebar-accent-foreground);transform:translate(2px)}.nav-link.active{background:var(--sidebar-accent);color:var(--sidebar-primary);font-weight:500}.nav-icon{flex-shrink:0;justify-content:center;align-items:center;width:20px;height:20px;display:flex}.nav-label{flex:1;font-size:14px;font-weight:500}.nav-badge{background:var(--neutral-200);color:var(--text-secondary);text-align:center;border-radius:10px;min-width:18px;padding:2px 6px;font-size:10px;font-weight:700}.nav-badge.highlight{background:var(--gradient-wave);color:#fff;animation:2s infinite pulse-gentle}.using-keyboard :focus{border-radius:var(--radius);box-shadow:var(--shadow-wave-focus);outline:2px solid var(--wave-primary)!important;outline-offset:2px!important}:not(.using-keyboard) :focus{outline:none!important}.openai-scrollbar{scrollbar-width:thin;scrollbar-color:var(--wave-light) transparent}.openai-scrollbar::-webkit-scrollbar{width:4px}.openai-scrollbar::-webkit-scrollbar-track{background:none}.openai-scrollbar::-webkit-scrollbar-thumb{background:var(--wave-light);border-radius:2px}.openai-scrollbar::-webkit-scrollbar-thumb:hover{background:var(--wave-primary)}.dark .openai-glass{border:1px solid var(--dark-border);background:#1a23324d}.openai-skeleton{background:linear-gradient(90deg,var(--wave-lightest) 25%,#fff 50%,var(--wave-lightest) 75%);border-radius:var(--radius);background-size:200% 100%;animation:1.5s infinite shimmerWave}.dark .openai-skeleton{background:linear-gradient(90deg,var(--dark-bg-secondary) 25%,var(--dark-bg-tertiary) 50%,var(--dark-bg-secondary) 75%);background-size:200% 100%}.skeleton{background:linear-gradient(90deg,var(--neutral-100) 0%,var(--neutral-200) 20%,var(--neutral-100) 40%,var(--neutral-100) 100%);border-radius:var(--radius);background-size:200% 100%;animation:1.5s infinite shimmerWave}.typing-indicator{align-items:center;gap:4px;padding:12px 16px;display:flex}.typing-indicator .dot{background:var(--wave-medium);border-radius:50%;width:8px;height:8px;animation:1.4s infinite typingDot}.typing-indicator .dot:nth-child(2){animation-delay:.2s}.typing-indicator .dot:nth-child(3){animation-delay:.4s}.ripple{pointer-events:none;background:radial-gradient(circle,#4a90e280,#0000 70%);border-radius:50%;animation:.6s ease-out rippleWave;position:absolute;transform:scale(0)}.status-critical{color:#ef4444;animation:2s infinite wavePulse}.status-urgent{color:#f59e0b;animation:3s infinite pulse-gentle}.status-stable{color:var(--wave-primary)}.status-resolved{color:#10b981}.dashboard-header{height:var(--header-height);background:var(--header-bg);-webkit-backdrop-filter:var(--backdrop-blur);border-bottom:1px solid var(--header-border);box-shadow:var(--header-shadow);z-index:40;padding:0 var(--spacing-xl);justify-content:space-between;align-items:center;display:flex;position:sticky;top:0}.page-title{align-items:center;gap:var(--spacing-md);color:var(--text-primary);margin:0;font-size:24px;font-weight:600;display:flex}.title-icon{border-radius:var(--radius);background:var(--gradient-wave);color:#fff;justify-content:center;align-items:center;width:32px;height:32px;display:flex}.breadcrumbs{align-items:center;gap:var(--spacing-xs);color:var(--text-tertiary);margin-top:2px;font-size:13px;display:flex}.breadcrumbs a{color:var(--text-tertiary);transition:color var(--openai-duration-fast);text-decoration:none}.breadcrumbs a:hover{color:var(--wave-primary)}.breadcrumbs .separator{color:var(--text-disabled);margin:0 2px}.breadcrumbs .current{color:var(--text-secondary);font-weight:500}.universal-search{width:400px;max-width:50vw;position:relative}.search-input{width:100%;height:40px;padding:0 var(--spacing-md);border:1px solid var(--border-default);border-radius:var(--radius-lg);background:var(--glass-bg);-webkit-backdrop-filter:var(--backdrop-blur);backdrop-filter:var(--backdrop-blur);transition:all var(--openai-duration-normal);font-size:14px}.search-input:focus{border-color:var(--wave-primary);box-shadow:var(--shadow-wave-focus);outline:none}.search-input::placeholder{color:var(--text-tertiary)}.header-actions{align-items:center;gap:var(--spacing-sm);display:flex}.action-btn{border:1px solid var(--border-default);border-radius:var(--radius);background:var(--glass-bg);width:40px;height:40px;-webkit-backdrop-filter:var(--backdrop-blur);backdrop-filter:var(--backdrop-blur);transition:all var(--openai-duration-normal);cursor:pointer;justify-content:center;align-items:center;display:flex;position:relative}.action-btn:hover{background:var(--bg-accent);border-color:var(--wave-primary);box-shadow:var(--shadow-sm);transform:translateY(-1px)}.notification-dot{background:var(--medical-danger);border:2px solid var(--bg-primary);border-radius:50%;width:8px;height:8px;animation:2s infinite pulse-gentle;position:absolute;top:8px;right:8px}.stats-grid{gap:var(--spacing-lg);margin-bottom:var(--spacing-2xl);grid-template-columns:repeat(auto-fit,minmax(250px,1fr));display:grid}.content-grid{gap:var(--spacing-lg);grid-template-columns:2fr 1fr;display:grid}.card-large{grid-column:span 1}.card-scroll{max-height:600px;overflow-y:auto}.card-header{padding:var(--spacing-lg);border-bottom:1px solid var(--border-default);justify-content:space-between;align-items:center;display:flex}.card-title{align-items:center;gap:var(--spacing-sm);color:var(--text-primary);margin:0;font-size:16px;font-weight:600;display:flex}.card-badge{background:var(--bg-accent);color:var(--wave-primary);border-radius:12px;padding:4px 8px;font-size:11px;font-weight:600}.card-body{padding:var(--spacing-lg)}.filter-select{border:1px solid var(--border-default);border-radius:var(--radius);background:var(--glass-bg);-webkit-backdrop-filter:var(--backdrop-blur);backdrop-filter:var(--backdrop-blur);color:var(--text-primary);cursor:pointer;padding:6px 12px;font-size:13px}.filter-select:focus{border-color:var(--wave-primary);box-shadow:var(--shadow-wave-focus);outline:none}}html{font-size:var(--font-size)}@media (width <= 768px){:root{--sidebar-width: 100vw;--grid-container-padding: var(--spacing-md)}.openai-sidebar{z-index:50;height:100vh;transition:transform var(--openai-duration-slower) var(--openai-ease-out);position:fixed;top:0;left:0;transform:translate(-100%)}.openai-sidebar.mobile-open{box-shadow:var(--shadow-wave-xl);transform:translate(0)}.openai-sidebar-overlay{-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);z-index:40;background:#0006;position:fixed;inset:0}.mobile-menu-button{z-index:999;background:var(--bg-primary);border:1px solid var(--border-default);border-radius:var(--radius);width:40px;height:40px;box-shadow:var(--shadow-wave-md);justify-content:center;align-items:center;display:flex;position:fixed;top:16px;left:16px}.stats-grid{gap:var(--spacing-md);grid-template-columns:repeat(auto-fit,minmax(200px,1fr))}.content-grid{gap:var(--spacing-md);grid-template-columns:1fr}.universal-search{width:200px}.dashboard-header{padding:0 var(--spacing-md)}.header-left{flex:1}.page-title{font-size:20px}}@media (width <= 1024px) and (width >= 769px){.stats-grid{grid-template-columns:repeat(2,1fr)}.content-grid{grid-template-columns:1fr}}@media (prefers-reduced-motion: reduce){:root{--openai-duration-instant: 0s;--openai-duration-fast: 0s;--openai-duration-normal: 0s;--openai-duration-slow: 0s;--openai-duration-slower: 0s;--openai-duration-slowest: 0s}*,:before,:after{scroll-behavior:auto!important;transition-duration:.01ms!important;animation-duration:.01ms!important;animation-iteration-count:1!important}}@media (prefers-contrast: high){:root{--wave-primary: #1e5ba8;--wave-medium: #0f3a6b;--wave-deep: #000;--text-primary: #000;--text-secondary: #333;--border-default: #000}.dark{--wave-primary: #87ceeb;--wave-medium: #e3f5ff;--wave-light: #fff;--text-primary: #fff;--text-secondary: #e0e0e0;--border-default: #fff}}@supports (font-display: swap){@font-face{font-family:Inter-Local;font-style:normal;font-weight:400 700;font-display:swap;src:local(Inter),local(Inter-Regular),local(Inter-Medium),local(Inter-SemiBold),local(Inter-Bold)}}@media (prefers-reduced-data: reduce){*{transition-duration:.01ms!important;animation-duration:.01ms!important}.wave-flow:before,.openai-skeleton,.wave-motion,.wave-pulse{animation:none!important}}@media print{.app-container{height:auto!important;display:block!important}.openai-sidebar,.mobile-menu-button,[data-no-print]{display:none!important}*{color:#000!important;box-shadow:none!important;background:#fff!important}}@property --tw-translate-x{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-translate-y{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-translate-z{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-rotate-x{syntax: "*"; inherits: false; initial-value: rotateX(0);}@property --tw-rotate-y{syntax: "*"; inherits: false; initial-value: rotateY(0);}@property --tw-rotate-z{syntax: "*"; inherits: false; initial-value: rotateZ(0);}@property --tw-skew-x{syntax: "*"; inherits: false; initial-value: skewX(0);}@property --tw-skew-y{syntax: "*"; inherits: false; initial-value: skewY(0);}@property --tw-space-y-reverse{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-space-x-reverse{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-border-style{syntax: "*"; inherits: false; initial-value: solid;}@property --tw-gradient-position{syntax: "*"; inherits: false}@property --tw-gradient-from{syntax: "<color>"; inherits: false; initial-value: #0000;}@property --tw-gradient-via{syntax: "<color>"; inherits: false; initial-value: #0000;}@property --tw-gradient-to{syntax: "<color>"; inherits: false; initial-value: #0000;}@property --tw-gradient-stops{syntax: "*"; inherits: false}@property --tw-gradient-via-stops{syntax: "*"; inherits: false}@property --tw-gradient-from-position{syntax: "<length-percentage>"; inherits: false; initial-value: 0%;}@property --tw-gradient-via-position{syntax: "<length-percentage>"; inherits: false; initial-value: 50%;}@property --tw-gradient-to-position{syntax: "<length-percentage>"; inherits: false; initial-value: 100%;}@property --tw-leading{syntax: "*"; inherits: false}@property --tw-font-weight{syntax: "*"; inherits: false}@property --tw-tracking{syntax: "*"; inherits: false}@property --tw-ordinal{syntax: "*"; inherits: false}@property --tw-slashed-zero{syntax: "*"; inherits: false}@property --tw-numeric-figure{syntax: "*"; inherits: false}@property --tw-numeric-spacing{syntax: "*"; inherits: false}@property --tw-numeric-fraction{syntax: "*"; inherits: false}@property --tw-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-shadow-color{syntax: "*"; inherits: false}@property --tw-shadow-alpha{syntax: "<percentage>"; inherits: false; initial-value: 100%;}@property --tw-inset-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-inset-shadow-color{syntax: "*"; inherits: false}@property --tw-inset-shadow-alpha{syntax: "<percentage>"; inherits: false; initial-value: 100%;}@property --tw-ring-color{syntax: "*"; inherits: false}@property --tw-ring-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-inset-ring-color{syntax: "*"; inherits: false}@property --tw-inset-ring-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-ring-inset{syntax: "*"; inherits: false}@property --tw-ring-offset-width{syntax: "<length>"; inherits: false; initial-value: 0;}@property --tw-ring-offset-color{syntax: "*"; inherits: false; initial-value: #fff;}@property --tw-ring-offset-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-outline-style{syntax: "*"; inherits: false; initial-value: solid;}@property --tw-blur{syntax: "*"; inherits: false}@property --tw-brightness{syntax: "*"; inherits: false}@property --tw-contrast{syntax: "*"; inherits: false}@property --tw-grayscale{syntax: "*"; inherits: false}@property --tw-hue-rotate{syntax: "*"; inherits: false}@property --tw-invert{syntax: "*"; inherits: false}@property --tw-opacity{syntax: "*"; inherits: false}@property --tw-saturate{syntax: "*"; inherits: false}@property --tw-sepia{syntax: "*"; inherits: false}@property --tw-drop-shadow{syntax: "*"; inherits: false}@property --tw-drop-shadow-color{syntax: "*"; inherits: false}@property --tw-drop-shadow-alpha{syntax: "<percentage>"; inherits: false; initial-value: 100%;}@property --tw-drop-shadow-size{syntax: "*"; inherits: false}@property --tw-backdrop-blur{syntax: "*"; inherits: false}@property --tw-backdrop-brightness{syntax: "*"; inherits: false}@property --tw-backdrop-contrast{syntax: "*"; inherits: false}@property --tw-backdrop-grayscale{syntax: "*"; inherits: false}@property --tw-backdrop-hue-rotate{syntax: "*"; inherits: false}@property --tw-backdrop-invert{syntax: "*"; inherits: false}@property --tw-backdrop-opacity{syntax: "*"; inherits: false}@property --tw-backdrop-saturate{syntax: "*"; inherits: false}@property --tw-backdrop-sepia{syntax: "*"; inherits: false}@property --tw-duration{syntax: "*"; inherits: false}@property --tw-ease{syntax: "*"; inherits: false}@property --tw-scale-x{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-scale-y{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-scale-z{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-content{syntax: "*"; inherits: false; initial-value: "";}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}@keyframes enter{0%{opacity:var(--tw-enter-opacity, 1);transform:translate3d(var(--tw-enter-translate-x, 0),var(--tw-enter-translate-y, 0),0) scale3d(var(--tw-enter-scale, 1),var(--tw-enter-scale, 1),var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity, 1);transform:translate3d(var(--tw-exit-translate-x, 0),var(--tw-exit-translate-y, 0),0) scale3d(var(--tw-exit-scale, 1),var(--tw-exit-scale, 1),var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))}}@keyframes accordion-down{0%{height:0}to{height:var(--radix-accordion-content-height, var(--bits-accordion-content-height))}}@keyframes accordion-up{0%{height:var(--radix-accordion-content-height, var(--bits-accordion-content-height))}to{height:0}}@keyframes caret-blink{0%,70%,to{opacity:1}20%,50%{opacity:0}}:root{--app-min-h: 100svh}@supports not (height: 100svh){:root{--app-min-h: 100vh}}@media (prefers-reduced-motion: reduce){*{animation-duration:.01ms!important;animation-iteration-count:1!important;transition-duration:.01ms!important}html:focus-within{scroll-behavior:auto}}html{hanging-punctuation:first last;text-wrap:pretty}.app-container{height:auto;min-height:var(--app-min-h)}.content-gutters{padding-inline:clamp(16px,4vw,32px)}.content-max{max-width:1280px;margin-inline:auto}.safe-top{padding-top:env(safe-area-inset-top)}.safe-bottom{padding-bottom:env(safe-area-inset-bottom)}.safe-left{padding-left:env(safe-area-inset-left)}.safe-right{padding-right:env(safe-area-inset-right)}.min-h-svh-fix{min-height:var(--app-min-h)}
