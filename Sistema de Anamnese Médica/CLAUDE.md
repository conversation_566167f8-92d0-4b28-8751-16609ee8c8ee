# Sistema de Anamnese Médica - Development Guidelines

Auto-generated from all feature plans. Last updated: 2025-09-10

## Active Technologies
- React 18.3 with TypeScript 5.9
- Vite for build tooling
- Tailwind CSS for styling with custom medical design tokens
- Storybook 9.1.5 for component development and documentation
- Chromatic for visual regression testing
- Supabase for backend services (PostgreSQL, Auth, Storage)
- Radix UI for accessible components
- React Hook Form for form management
- Vitest for testing with axe-core for accessibility testing
- jest-axe for automated accessibility testing
- Lucide React for icons
- React Router DOM for navigation

## Project Structure
```
Sistema de Anamnese Médica/
├── .claude/           # Claude Code configuration
│   └── commands/      # Spec-driven commands
├── src/
│   ├── components/    # React components
│   │   ├── premium/   # Premium features
│   │   ├── smart-sidebar/
│   │   └── ui/        # Enhanced UI components
│   ├── design-system/ # Medical design system tokens
│   │   └── tokens/    # CSS custom properties
│   ├── styles/        # CSS and styling
│   └── types/         # TypeScript definitions
├── tests/             # Test suites
│   ├── visual/        # Visual regression tests
│   ├── accessibility/ # Accessibility tests
│   └── performance/   # Performance tests
├── .storybook/        # Storybook configuration
├── scripts/           # Build and utility scripts
├── templates/         # Spec-driven templates
├── memory/            # Project context
└── templates/         # Spec-driven templates (fonte para specs)

Specs directory (repo root):
```
specs/                 # Feature specifications (no raiz do repositório)
```
```

## Commands

### Development
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run preview  # Preview production build
npm run lint     # Run ESLint
npm run typecheck # Run TypeScript compiler
```

### Testing & Documentation
```bash
npm run test            # Run tests
npm run test:visual     # Run visual regression tests
npm run test:a11y       # Run accessibility tests
npm run storybook       # Start Storybook
npm run build-storybook # Build Storybook for deployment
```

### Spec-Driven Development
- `/specify` - Create a new feature specification
- `/plan` - Generate technical implementation plan
- `/tasks` - Break down implementation into tasks

## Code Style
- Use TypeScript for all new code
- Functional components with hooks
- Tailwind CSS for styling (avoid inline styles)
- Component files: PascalCase (e.g., `AnamneseRapida.tsx`)
- Utility files: camelCase (e.g., `apiHelpers.ts`)
- Follow existing patterns in the codebase

## Recent Changes
- ✅ **COMPLETED**: Implemented comprehensive medical design system tokens (colors, typography, spacing, shadows, animations, layout)
- ✅ **COMPLETED**: Set up TDD infrastructure with visual regression testing (Storybook + Chromatic)
- ✅ **COMPLETED**: Configured accessibility testing suite (axe-core + jest-axe)
- ✅ **COMPLETED**: Created 81 comprehensive tasks following TDD methodology
- ✅ **COMPLETED**: All tests properly failing (confirming TDD Red phase)
- 🔄 **IN PROGRESS**: Component enhancement implementation (T020-T028)
- 📋 **PLANNED**: Medical workflow layout implementation
- 📋 **PLANNED**: WCAG 2.2 Level AA compliance validation

## Project Context
This is a medical anamnesis system with:
- Quick anamnesis form (`AnamneseRapida`)
- Premium dashboard features with glass morphism
- Smart sidebar navigation
- Supabase backend integration
- HIPAA compliance requirements
- **Medical Design System**: Comprehensive tokens system with clinical color standards
- **Visual Testing**: TDD approach with Chromatic visual regression testing
- **Accessibility**: WCAG 2.2 Level AA compliance with automated testing
- **Performance**: 60fps animations optimized for medical workstations
- **Premium Components**: Glass morphism UI with medical context variants
- **Medical Workflows**: Specialized layouts for healthcare professionals

<!-- MANUAL ADDITIONS START -->
<!-- Add any project-specific guidelines here -->
<!-- MANUAL ADDITIONS END -->
