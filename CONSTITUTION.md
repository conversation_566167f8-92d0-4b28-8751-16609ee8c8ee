# Sistema de Anamnese Médica Constitution

## Core Principles

### I. Medical-First Design
Every UI component must prioritize medical professional workflows and patient safety; Components must support medical contexts (routine/urgent/critical/emergency); All design decisions validated against healthcare industry standards; Clear visual hierarchy required for medical data presentation

### II. Accessibility Excellence (NON-NEGOTIABLE)
WCAG 2.2 Level AA compliance mandatory for all interactive elements; Touch targets minimum 44px for medical device compatibility; Keyboard navigation required for sterile environments; Screen reader optimization for medical terminology; High contrast mode support for various lighting conditions

### III. Visual Regression TDD (NON-NEGOTIABLE)
Visual tests written first and must fail before component implementation; Chromatic visual regression testing enforced; Storybook stories required for all component variants; Red-Green-Refactor cycle with visual validation at each step; No UI changes without corresponding visual tests

### IV. Performance Standards
60fps animations mandatory on medical workstations (1920x1080, 1440x900); Load times under 2 seconds on medical hardware; Memory efficiency for all-day operation; GPU acceleration for smooth interactions; Bundle size optimization without compromising functionality

### V. Design System Consistency
CSS custom properties with medical-semantic naming (--medical-color-primary); 8px grid system adherence throughout interface; Glass morphism effects professionally applied; Component variants follow medical context patterns; Design tokens centralized and documented

## Medical Interface Standards

### Technology Stack Requirements
React 18.3+ with TypeScript 5.9+ for type safety; Tailwind CSS with custom medical design tokens; Radix UI for accessibility foundation; Framer Motion for professional animations; Vite for fast development builds; Storybook 8.x for component documentation

### Medical Compliance Standards
Color coding follows international medical standards; Emergency actions use red (#D92D20) with maximum contrast; Critical states use amber (#F79009) for urgency; Routine medical workflows use professional blue (#0A6EBD); Success states use medical green (#22C55E); Information states use clear blue (#2E90FA)

### Device Compatibility Matrix
Primary: Medical workstations (1920x1080, 1440x900 displays); Secondary: Tablets for bedside use (iPad Pro, Surface Pro); Tertiary: Mobile for emergency access; High-DPI display support for medical imaging workstations; Cross-browser compatibility (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)

## Development Workflow

### Spec-Driven Development Process
Feature specifications created first with medical context requirements; Research phase validates medical industry standards; Design phase creates visual contracts and accessibility requirements; Task generation follows TDD with visual regression approach; Implementation follows constitutional principles

### Quality Gates
Visual regression tests pass before component merging; Accessibility audits required for all interactive elements; Performance benchmarks validated on medical hardware simulation; Medical professional review required for workflow-critical components; Code review verification of constitutional compliance

### Review Process
All PRs must demonstrate visual test failures before implementation; Accessibility compliance verification with automated tools; Performance impact assessment for medical workstation targets; Medical context validation for appropriate styling and behavior; Documentation updates required for component API changes

## Governance

### Constitutional Authority

This constitution supersedes all other development practices and coding standards; Medical safety and accessibility requirements are non-negotiable; Performance standards must be met for all medical workstation compatibility; Visual regression testing is mandatory before any UI implementation

### Amendment Process

Constitution changes require documentation of medical impact assessment; Amendment proposal must include migration plan for existing components; Medical professional consultation required for workflow-affecting changes; Version increment required for all constitutional amendments

### Compliance Enforcement

All pull requests must verify constitutional compliance before merge; Component complexity must be justified against medical use case requirements; Visual test failures block all component implementations; Performance regressions block deployment to medical environments; Use tasks.md and CLAUDE.md for runtime development guidance

**Version**: 2.0.0 | **Ratified**: 2025-09-10 | **Last Amended**: 2025-09-10