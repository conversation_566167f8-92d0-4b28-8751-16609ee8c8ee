{"name": "1805bcf814bdd432955414e55e57d25c2afdcece744f0cab3a3a591f2eebfe36", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^24.3.1", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.7.0", "comment-json": "^4.2.5", "env-cmd": "^11.0.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "react": "^18.3.1", "react-dom": "^18.3.1", "rollup": "^4.44.1", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-plugin-static-copy": "^3.1.2", "vite-tsconfig-paths": "^5.1.4"}, "dependencies": {"@kombai/react-error-boundary": "^1.1.0", "date-fns": "^4.1.0", "lucide-react": "^0.543.0", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "postcss-preset-env": "^10.3.1", "radix-ui": "^1.4.3", "tailwindcss": "^3.4.17"}, "strictDependencies": {"husky": "^9.1.6"}}