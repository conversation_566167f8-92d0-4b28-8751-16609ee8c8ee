1) Objetivo

Melhorar o layout e a hierarquia visual do projeto existente, aprimorar cores, tipografia, animações, espaçamentos, proporções de tela, e reconfigurar sidebar e estruturas de navegação, garantindo responsividade impecável (mobile, tablet, desktop) e acessibilidade (WCAG 2.2), mantendo a identidade de site para médicos/clinicas (confiança, clareza, serenidade).

Faça assim: apresente um plano resumido em 6–10 passos e implemente imediatamente (não pare para pedir confirmação). Entregue tudo em uma PR interna com diffs claros.

2) Princípios de UX específicos para saúde

Clareza clínica: linguagem visual sóbria, baixo ruído, foco em informações de pacientes, agenda e resultados.

Confiabilidade: evite cores alarmistas como primária (vermelho só em estados de erro).

Leitura prolongada: contraste alto, tipografia legível, linhas com ~60–80 caracteres.

Privacidade: componentes discretos para dados sensíveis (placeholder-friendly, ocultação parcial de IDs).

Acessibilidade first: foco visível, navegação por teclado, suportar prefers-reduced-motion.

3) Design System (crie/atualize tokens)

Implemente tokens como CSS variables (:root) ou configure o tema se o projeto usa Tailwind/Chakra/MUI. Exemplo base (ajuste às tecnologias do projeto):

:root{
  /* cores */
  --color-bg: #F8FAFC;
  --color-surface: #FFFFFF;
  --color-text: #101828;
  --color-muted: #475467;
  --color-border: #E5E7EB;

  --color-primary: #0A6EBD;   /* azul confiável */
  --color-primary-600: #0B5FA3;
  --color-primary-100: #E6F2FB;

  --color-accent: #12A594;    /* teal calmo */
  --color-success: #22C55E;
  --color-warning: #F79009;
  --color-danger:  #D92D20;
  --color-info:    #2E90FA;

  /* tipografia */
  --font-sans: ui-sans-serif, system-ui, "Inter", "SF Pro Text", Arial, sans-serif;
  --fs-300: 0.875rem; /* 14 */
  --fs-400: 1rem;     /* 16 base */
  --fs-500: 1.125rem; /* 18 */
  --fs-600: 1.25rem;  /* 20 */
  --fs-700: 1.5rem;   /* 24 (titular) */

  /* espaçamentos (escala de 4px) */
  --sp-1: 4px; --sp-2: 8px; --sp-3: 12px; --sp-4: 16px;
  --sp-5: 20px; --sp-6: 24px; --sp-8: 32px; --sp-10: 40px;

  /* raios & sombras */
  --radius-sm: 8px; --radius-md: 12px; --radius-lg: 16px;
  --shadow-sm: 0 1px 2px rgba(16,24,40,.06);
  --shadow-md: 0 4px 12px rgba(16,24,40,.08);

  /* animações */
  --ease-standard: cubic-bezier(.2,.8,.2,1);
  --dur-fast: 120ms; --dur-base: 200ms; --dur-slow: 320ms; --dur-overlay: 420ms;

  /* largura de contêiner */
  --max-content: 1200px; /* para leitura confortável */
}


Se Tailwind, atualize tailwind.config com essa paleta (cores, fontSize, spacing, radii) e gere variantes de estado (hover/focus/active/disabled).

4) Grid, proporções e containers

Breakpoints (referência):
xs <480, sm ≥480, md ≥768, lg ≥1024, xl ≥1280, 2xl ≥1536.

Colunas: 4 (xs), 8 (md), 12 (lg+). Gutters de 16–24px.

Container de leitura: largura máxima --max-content com margens fluídas.

Alturas-chave: cabeçalho 64–72px; itens de lista/tabela 48px; botões 44–48px.

Ritmo vertical: use a escala de 4px; seções com padding-top/bottom de 24–40px.

5) Tipografia

Corpo: var(--font-sans), base 16px, line-height 1.5–1.7.

Títulos: use pesos 600–700, fs-700 para H1 de páginas.

Contrast ratio ≥ 4.5:1 para texto normal; use --color-muted para secundarios.

6) Cores (site médico)

Primária: --color-primary (azul confiável).

Estado/feedback: sucesso --color-success, alerta --color-warning, erro --color-danger, info --color-info.

Superfícies: --color-surface e linhas com --color-border.

Forneça tema escuro opcional, mantendo contraste (não obrigatório se o escopo for só claro).

7) Navegação & Sidebar

Desktop (≥1024): sidebar fixa 280px; versão colapsada 72px (ícone + tooltip) com alternância acessível (teclado e screen reader).

Tablet (≥768 & <1024): sidebar 240px; colapsável por padrão.

Mobile (<768): sidebar como drawer sobreposto (abre com botão “Menu”), foco aprisionado no drawer e aria-modal=true.

Itens ativos: cor primária + indicador lateral de 2–3px; ícones consistentes (linha médica/saúde).

Topo: header com busca, atalho “Nova consulta” e estado do médico (online/ocupado).

8) Componentes prioritários

Cards (resumos de paciente, KPIs clínicos).

Tabelas (lista de pacientes, exames): altura 48px, header “sticky”, zebra leve, ícones de status, sort e paginação clara.

Formulários (CRM, CPF, datas, prescrições): labels sempre visíveis; erros com texto curto e ícone; máscaras amigáveis e acessíveis.

Agenda/Calendário (dia/semana): cores de status (comparecem/confirmado/pendente), arrastar para reagendar (se já existir JS), tooltips.

Modais: borda suave, --radius-md, foco inicial no primeiro campo; ESC para fechar.

Toasts: canto superior direito, auto-hide 4–6s, com role="status".

9) Animações & microinterações (conscientes)

Micro (hover/focus): --dur-fast com --ease-standard; deslocamento ≤ 2px, opacidade.

Transições de página/overlay: --dur-overlay (fade+scale leve).

Skeletons para carregamento (linhas em tabelas, cartões de paciente).

Reduzir movimento: respeitar prefers-reduced-motion (desative transformações, mantenha fades curtos).

Estados de foco visível: outline 2px --color-primary + offset.

10) Acessibilidade

Teclado completo (TAB/SHIFT+TAB, ENTER/SPACE).

aria-* coerente em menu, dialog, tabs, toasts.

Labels e aria-describedby conectados a inputs.

Tamanhos de toque ≥ 44×44px em mobile.

11) Responsividade (comportamentos-chave)

Mobile primeiro.

Reordene regiões: em mobile, filtros viram sheet; ações primárias ficam no topo; evite scroll horizontal.

Tabelas responsivas: colunas menos críticas viram “resumo” sob a célula principal.

Sidebar vira bottom nav opcional (5 itens) em mobile, se fizer sentido ao produto.

12) Performance & SEO

Imagens responsivas (srcset, sizes), SVGs para ícones.

Dividir bundles e css “critical” inline (abaixo da dobra).

Metadados de saúde: título de página claro (“Agenda — Dr. Fulano”), descrição.

Lighthouse alvo: Performance ≥ 90, Acessibilidade ≥ 95.

13) Entregáveis

Refactor completo de estilos (CSS/SCSS ou tema Tailwind/Chakra/MUI) usando os tokens acima.

Componentes padronizados: Botão, Input, Select, Checkbox, Radio, Switch, Card, Table, Modal, Tooltip, Toast, Tabs.

Sidebar e Header responsivos refatorados conforme §7.

Página de exemplo com: Dashboard, Lista de Pacientes, Agenda, Prontuário (mock), Exames.

Documento de tokens (README curto) explicando como usar as variáveis/tema.

Testes básicos de acessibilidade (focus-trap em modal, navegação por teclado, contraste).

14) Critérios de aceitação (checagem objetiva)

Não há overflow horizontal em nenhuma página nos breakpoints definidos.

Contraste de texto cumpre WCAG (≥ 4.5:1).

Foco visível em todos os elementos interativos.

Sidebar: 280px (desktop), 240px (tablet), drawer (mobile) com trap de foco.

Tabelas com header sticky, sort e paginação acessíveis.

Lighthouse: Acessibilidade ≥ 95; Performance ≥ 90 (em build de produção).

prefers-reduced-motion suportado.

15) Notas técnicas

Não alterar lógica de backend; alterações são majoritariamente de UI/UX.

Se o projeto já usa um framework de UI, unificar tema em cima dos tokens acima.

Se necessário, migrar estilos dispersos para um sistema de design centralizado com tokens.