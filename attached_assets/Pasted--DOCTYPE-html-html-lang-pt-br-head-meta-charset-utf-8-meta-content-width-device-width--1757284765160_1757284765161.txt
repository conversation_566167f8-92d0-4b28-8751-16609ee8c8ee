<!DOCTYPE html>
<html lang="pt-br"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>WellWave - Anamnese Rápida</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50">
<div class="container mx-auto p-8">
<header class="mb-8">
<h1 class="text-3xl font-bold text-blue-600">Sistema de Anamnese Rápida</h1>
<p class="text-gray-600 mt-1">Preencha os dados abaixo para gerar uma anamnese estruturada. Você poderá copiar cada seção separadamente para o prontuário oficial.</p>
</header>
<div class="bg-white rounded-lg shadow-md p-8">
<div class="mb-8">
<div class="relative">
<div class="h-1 bg-gray-200 rounded-full">
<div class="h-1 bg-blue-600 rounded-full" style="width: 50%;"></div>
</div>
<div class="absolute inset-0 flex items-center justify-between">
<div class="text-center">
<div class="w-4 h-4 rounded-full bg-blue-600 mx-auto"></div>
<span class="text-sm font-medium text-gray-700 mt-2 block">Queixa</span>
</div>
<div class="text-center">
<div class="w-4 h-4 rounded-full bg-blue-600 mx-auto"></div>
<span class="text-sm font-medium text-blue-600 mt-2 block">Formulário</span>
</div>
<div class="text-center">
<div class="w-4 h-4 rounded-full bg-gray-200 mx-auto"></div>
<span class="text-sm font-medium text-gray-500 mt-2 block">Resultado</span>
</div>
</div>
</div>
</div>
<div class="flex items-center mb-6">
<button class="text-blue-600">
<span class="material-icons">arrow_back</span>
</button>
<h2 class="text-xl font-bold text-gray-800 ml-4">Dor Torácica - Formulário Dinâmico</h2>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
<div class="lg:col-span-2">
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-12">
<div>
<h3 class="text-lg font-semibold text-gray-800 mb-4">História da Moléstia Atual</h3>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Duração da dor</label>
<div class="flex flex-wrap gap-2">
<button class="px-4 py-2 text-sm rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100">&lt; 1 hora</button>
<button class="px-4 py-2 text-sm rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100">1-6 horas</button>
<button class="px-4 py-2 text-sm rounded-md border border-blue-600 bg-blue-600 text-white">6-24 horas</button>
<button class="px-4 py-2 text-sm rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100">1-3 dias</button>
<button class="px-4 py-2 text-sm rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100">&gt; 3 dias</button>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Características da dor</label>
<div class="grid grid-cols-2 gap-4">
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="aperto" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="aperto">Em aperto/pressão</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="queimacao" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="queimacao">Em queimação</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="pontada" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="pontada">Em pontada</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="pleuritica" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="pleuritica">Pleurítica</label>
</div>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Fatores associados</label>
<div class="grid grid-cols-2 gap-4">
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="nausea" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="nausea">Náusea/vômito</label>
</div>
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="sudorese" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="sudorese">Sudorese</label>
</div>
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="dispneia" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="dispneia">Dispneia associada</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="sincope" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="sincope">Síncope</label>
</div>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2" for="dor-intensidade">Intensidade da dor (0-10)</label>
<input class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-600" id="dor-intensidade" max="10" min="0" type="range" value="6"/>
<div class="flex justify-between text-xs text-gray-500 mt-1">
<span>0</span><span>1</span><span>2</span><span>3</span><span>4</span><span>5</span><span>6</span><span>7</span><span>8</span><span>9</span><span>10</span>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2" for="info-adicionais">Informações adicionais</label>
<textarea class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" id="info-adicionais" rows="3">Dor que iniciou em repouso, sem irradiação, melhora ao sentar-se inclinado para frente.</textarea>
</div>
<h3 class="text-lg font-semibold text-gray-800 mb-4 pt-4 border-t">Antecedentes Pessoais</h3>
<div class="grid grid-cols-2 gap-4">
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="hipertensao" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="hipertensao">Hipertensão</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="diabetes" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="diabetes">Diabetes</label>
</div>
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="dac" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="dac">DAC prévia</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="avc" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="avc">AVC/AIT</label>
</div>
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="tabagismo" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="tabagismo">Tabagismo</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" id="dislipidemia" type="checkbox"/>
<label class="ml-2 block text-sm text-gray-900" for="dislipidemia">Dislipidemia</label>
</div>
</div>
<div class="mt-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Medicações em uso</label>
<div class="flex flex-wrap gap-2 mb-2">
<span class="inline-flex items-center py-1 pl-3 pr-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                        AAS 100mg/dia
                                        <button class="flex-shrink-0 ml-1 h-4 w-4 rounded-full inline-flex items-center justify-center text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:outline-none focus:bg-blue-500 focus:text-white" type="button">
<span class="sr-only">Remover</span>
<svg class="h-2 w-2" fill="none" stroke="currentColor" viewBox="0 0 8 8">
<path d="M1 1l6 6m0-6L1 7" stroke-linecap="round" stroke-width="1.5"></path>
</svg>
</button>
</span>
<span class="inline-flex items-center py-1 pl-3 pr-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                        Enalapril 10mg 2x/dia
                                        <button class="flex-shrink-0 ml-1 h-4 w-4 rounded-full inline-flex items-center justify-center text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:outline-none focus:bg-blue-500 focus:text-white" type="button">
<span class="sr-only">Remover</span>
<svg class="h-2 w-2" fill="none" stroke="currentColor" viewBox="0 0 8 8">
<path d="M1 1l6 6m0-6L1 7" stroke-linecap="round" stroke-width="1.5"></path>
</svg>
</button>
</span>
</div>
<div class="flex items-center gap-2">
<div class="relative flex-grow">
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Pesquisar medicamentos..." type="text"/>
<span class="absolute inset-y-0 right-0 flex items-center pr-3">
<span class="material-icons text-gray-400">arrow_drop_down</span>
</span>
</div>
<button class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Adicionar</button>
</div>
<p class="text-xs text-gray-500 mt-1">Digite pelo menos 2 caracteres para buscar na base da ANVISA</p>
</div>
</div>
<div>
<h3 class="text-lg font-semibold text-gray-800 mb-4">Exame Físico</h3>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Sinais Vitais</label>
<div class="grid grid-cols-2 gap-4">
<div>
<label class="text-xs text-gray-500">Pressão Arterial (mmHg)</label>
<div class="flex items-center gap-2">
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-center" type="text" value="140"/>
<span>/</span>
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-center" type="text" value="90"/>
</div>
</div>
<div>
<label class="text-xs text-gray-500">Frequência Cardíaca (bpm)</label>
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-center" type="text" value="88"/>
</div>
<div>
<label class="text-xs text-gray-500">Frequência Respiratória (irpm)</label>
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-center" type="text" value="18"/>
</div>
<div>
<label class="text-xs text-gray-500">Temperatura (°C)</label>
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-center" type="text" value="36.8"/>
</div>
<div>
<label class="text-xs text-gray-500">Saturação O2 (%)</label>
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-center" type="text" value="96"/>
</div>
<div>
<label class="text-xs text-gray-500">Glicemia Capilar (mg/dL)</label>
<input class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-center" type="text" value="110"/>
</div>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Achados Cardiovasculares</label>
<div class="space-y-2">
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" id="ritmo-cardiaco" name="achados-cardiovasculares" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="ritmo-cardiaco">Ritmo cardíaco regular, bulhas normofonéticas, sem sopros</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" id="ritmo-irregular" name="achados-cardiovasculares" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="ritmo-irregular">Ritmo cardíaco irregular</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" id="sopro" name="achados-cardiovasculares" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="sopro">Presença de sopro</label>
</div>
<div class="flex items-start">
<input class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500 mt-1" id="cardio-outro" name="achados-cardiovasculares" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="cardio-outro">Outro (especificar):</label>
<input class="ml-2 flex-1 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" type="text"/>
</div>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Achados Respiratórios</label>
<div class="space-y-2">
<div class="flex items-center">
<input checked="" class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" id="murmurio-vesicular" name="achados-respiratorios" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="murmurio-vesicular">Murmúrio vesicular presente bilateralmente, sem ruídos adventícios</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" id="crepitacoes" name="achados-respiratorios" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="crepitacoes">Crepitações em bases pulmonares</label>
</div>
<div class="flex items-center">
<input class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" id="sibilos" name="achados-respiratorios" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="sibilos">Sibilos difusos</label>
</div>
<div class="flex items-start">
<input class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500 mt-1" id="resp-outro" name="achados-respiratorios" type="radio"/>
<label class="ml-2 block text-sm text-gray-900" for="resp-outro">Outro (especificar):</label>
<input class="ml-2 flex-1 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" type="text"/>
</div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="obs-exame-fisico">Observações adicionais do exame físico</label>
<textarea class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" id="obs-exame-fisico" rows="3">Sem edema de membros inferiores. Pulsos periféricos presentes e simétricos.</textarea>
</div>
</div>
</div>
</div>
<div class="lg:col-span-1">
<div class="sticky top-8">
<h3 class="text-lg font-semibold text-gray-800 mb-4">Preview da Anamnese</h3>
<div class="bg-gray-100 rounded-lg p-4 h-[calc(100vh-250px)] flex flex-col">
<div class="bg-white rounded-t-lg p-2 border-b border-gray-200 flex items-center space-x-2">
<button class="p-1 rounded hover:bg-gray-200 text-gray-600"><span class="material-icons text-base">format_bold</span></button>
<button class="p-1 rounded hover:bg-gray-200 text-gray-600"><span class="material-icons text-base">format_italic</span></button>
<button class="p-1 rounded hover:bg-gray-200 text-gray-600"><span class="material-icons text-base">format_underlined</span></button>
<button class="p-1 rounded hover:bg-gray-200 text-gray-600"><span class="material-icons text-base">format_list_bulleted</span></button>
<button class="p-1 rounded hover:bg-gray-200 text-gray-600"><span class="material-icons text-base">format_list_numbered</span></button>
<button class="p-1 rounded hover:bg-gray-200 text-gray-600 ml-auto"><span class="material-icons text-base">content_copy</span></button>
</div>
<textarea class="flex-grow w-full p-4 bg-white rounded-b-lg border-t-0 focus:ring-0 border-0 text-sm text-gray-700 resize-none" placeholder="O texto da anamnese será construído aqui em tempo real...">Paciente procura atendimento com queixa de dor torácica iniciada há cerca de 6-24 horas. Refere dor em aperto/pressão, com intensidade 6/10. Associado ao quadro, apresenta náuseas, vômitos, sudorese e dispneia. Dor que iniciou em repouso, sem irradiação, melhora ao sentar-se inclinado para frente.
Antecedentes pessoais de Hipertensão Arterial Sistêmica, Doença Arterial Coronariana prévia e Tabagismo. Em uso de AAS 100mg/dia e Enalapril 10mg 2x/dia.
Ao exame físico: PA 140x90 mmHg, FC 88 bpm, FR 18 irpm, SatO2 96%, T 36.8°C, Glicemia 110 mg/dL. Ritmo cardíaco regular, bulhas normofonéticas, sem sopros. Murmúrio vesicular presente bilateralmente, sem ruídos adventícios. Sem edema de membros inferiores. Pulsos periféricos presentes e simétricos.</textarea>
</div>
</div>
</div>
</div>
<div class="mt-8 pt-6 border-t flex justify-end">
<button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Gerar Anamnese
                </button>
</div>
</div>
</div>

</body></html>