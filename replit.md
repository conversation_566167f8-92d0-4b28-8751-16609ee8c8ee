# Overview

WellWave is a comprehensive medical anamnesis (medical history) system built as a modern React application with TypeScript. It provides healthcare professionals with tools for structured patient interviews, AI-assisted medical consultations, prescription management, and patient workflow organization. The system features a professional medical form structure with syndrome-based symptom selection, structured duration assessment, and vital signs monitoring. The interface is designed with a premium UI inspired by OpenAI's interface, featuring a medical-focused color palette and sophisticated animations.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Framework
- **React 18+ with TypeScript** - Modern functional components using hooks
- **Vite** as the build tool and development server for fast HMR
- **Motion (Framer Motion)** for sophisticated animations and transitions
- **Radix UI** components as the foundation for accessible UI primitives
- **Tailwind CSS** for utility-first styling with custom medical color variables

## Component Structure
The application follows a modular component architecture:
- **Domain Components** (`src/components/`) - Main application features like AnamneseRapida, ChatMedico, KanbanPlantao
- **UI Primitives** (`src/components/ui/`) - Reusable components built on Radix UI
- **Premium Components** (`src/components/premium/`) - Advanced dashboard and sidebar components
- **Smart Sidebar System** (`src/components/smart-sidebar/`) - Responsive navigation with multiple variants

## State Management
- **React Context** (`MedicalContext`) for global medical data and patient information
- **Local state** with useState/useEffect for component-specific data
- **Custom hooks** (`hooks/`) for reusable logic like `useSmartSidebar` and `useDeduplicated`

## Design System
- **Medical Color Palette** - Blue-based primary colors with specialized medical status colors
- **Responsive Design** - Mobile-first approach with adaptive sidebar behavior
- **Animation System** - Professional motion design with OpenAI-inspired effects
- **Typography** - Consistent font sizing and spacing following medical design principles

## Key Features Architecture
1. **Anamnesis System** - Multi-step patient interview process with:
   - Syndrome-based symptom selection with medical literature integration
   - Structured pain characteristics and duration assessment
   - Associated symptoms tracking with categories
   - Intensity scoring with visual feedback
2. **AI Chat Interface** - Medical consultation assistant with contextual responses  
3. **Kanban Board** - Patient workflow management with drag-and-drop functionality
4. **Prescription Management** - Digital prescription creation and management
5. **Real-time Dashboard** - Patient statistics and monitoring interface
6. **Physical Exam Module** - Structured vital signs input with separate blood pressure fields and cardiovascular findings

## File Organization
- Path aliases configured (`@` → `src`) for clean imports
- Modular CSS with Tailwind utilities and custom medical variables
- Assets managed through Vite with Figma integration for design resources
- Guidelines and documentation in markdown files for development standards

# External Dependencies

## UI Framework Dependencies
- **@radix-ui/* suite** - Complete set of accessible UI primitives (dialogs, dropdowns, forms, navigation)
- **motion** - Advanced animation library for smooth transitions and micro-interactions
- **lucide-react** - Comprehensive icon set with medical-specific icons
- **class-variance-authority & clsx** - Dynamic className composition and variants

## Development Tools
- **Vite** - Modern build tool with fast development server
- **TypeScript** - Static type checking and enhanced developer experience
- **React 18** - Latest React features including concurrent rendering

## Medical Workflow Dependencies
- **react-hook-form** - Form validation and management for medical data entry
- **react-dnd & react-dnd-html5-backend** - Drag and drop functionality for patient workflow boards
- **sonner** - Toast notifications system for user feedback
- **next-themes** - Theme management for dark/light mode support

## Data Visualization
- **recharts** - Charts and graphs for medical statistics and patient data visualization
- **embla-carousel** - Smooth carousels for displaying medical information

## Utility Libraries
- **cmdk** - Command palette interface for quick actions
- **input-otp** - OTP input components for secure authentication
- **react-day-picker** - Date selection for appointments and medical records
- **vaul** - Drawer/modal components for mobile interfaces