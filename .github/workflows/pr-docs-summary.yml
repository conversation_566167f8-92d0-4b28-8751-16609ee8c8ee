name: PR Spec Docs Summary

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  comment-specs:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: read
    steps:
      - name: List changed files in PR
        id: files
        uses: actions/github-script@v7
        with:
          script: |
            const pr = context.payload.pull_request;
            const files = await github.paginate(
              github.rest.pulls.listFiles,
              { owner: context.repo.owner, repo: context.repo.repo, pull_number: pr.number }
            );
            const changed = files
              .map(f => f.filename)
              .filter(f => f.startsWith('specs/') || f === 'Sistema de Anamnese Médica/CLAUDE.md');
            core.setOutput('list', changed.join('\n'));
      - name: Create or update comment
        if: steps.files.outputs.list != ''
        uses: actions/github-script@v7
        with:
          script: |
            const pr = context.payload.pull_request;
            const body = `Updated spec-related files:\n\n${core.getInput('list')}`;
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: pr.number,
              body
            });
