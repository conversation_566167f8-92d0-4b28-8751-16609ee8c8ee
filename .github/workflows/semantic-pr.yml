name: Semantic PR

on:
  pull_request_target:
    types: [opened, edited, synchronize, reopened, ready_for_review]

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
<<<<<<< HEAD
      - uses: amannn/action-semantic-pull-request@v6
=======
      - uses: amannn/action-semantic-pull-request@v5
>>>>>>> cad47a8 (chore(ci): add CI, PR templates, Dependabot, CODEOWNERS, security policy)
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            chore
            docs
            refactor
            test
            build
            ci

