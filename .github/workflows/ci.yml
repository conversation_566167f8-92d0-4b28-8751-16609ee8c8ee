name: ci

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
<<<<<<< HEAD
  lint:
    name: <PERSON>t
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: "Sistema de Anamnese Médica"
    steps:
      - name: Checkout
        uses: actions/checkout@v5
      - name: Setup Node
        uses: actions/setup-node@v5
        with:
          node-version: '22.x'
          cache: npm
          cache-dependency-path: "Sistema de Anamnese Médica/package-lock.json"
      - name: Install
        run: npm ci
      - name: ESLint
        run: npm run lint
      - name: Prettier (non-blocking)
        run: npm run format:check
        continue-on-error: true

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [lint]
=======
  build:
    name: Build
    runs-on: ubuntu-latest
>>>>>>> cad47a8 (chore(ci): add CI, PR templates, Dependabot, CODEOWNERS, security policy)
    defaults:
      run:
        working-directory: "Sistema de Anamnese Médica"
    steps:
      - name: Checkout
<<<<<<< HEAD
        uses: actions/checkout@v5
      - name: Setup Node
        uses: actions/setup-node@v5
=======
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
>>>>>>> cad47a8 (chore(ci): add CI, PR templates, Dependabot, CODEOWNERS, security policy)
        with:
          node-version: '22.x'
          cache: npm
          cache-dependency-path: "Sistema de Anamnese Médica/package-lock.json"
      - name: Install
        run: npm ci
<<<<<<< HEAD
      - name: Type Check
        run: npm run typecheck
        continue-on-error: true
      - name: Build
        run: npm run build
=======
      - name: Build
        run: npm run build

>>>>>>> cad47a8 (chore(ci): add CI, PR templates, Dependabot, CODEOWNERS, security policy)
