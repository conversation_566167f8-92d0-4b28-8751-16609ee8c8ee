## Resumo
- Descreva o objetivo desta PR.

## Mudanças
- <PERSON><PERSON> as mudan<PERSON>s principais (componentes, rotas, configs).

## Testes Locais
1. `cd "Sistema de Anamnese Médica" && npm ci`
2. `npm run build`
3. (Opcional) `npm run dev` para validar UI

## Screenshots/GIFs
Adicione evidências visuais quando alterar UI.

## Checklist
- [ ] Título segue Conventional Commits (ex.: `feat:`, `fix:`)
- [ ] Build passa (`npm run build`)
- [ ] Sem segredos/keys no diff
- [ ] Atualizei docs quando necessário

