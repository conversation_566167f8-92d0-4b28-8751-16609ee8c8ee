#!/usr/bin/env sh
set -e

# Run lint-staged in the app directory
npm --prefix "Sistema de Anamnese Médica" run lint-staged

# Validate spec/plan presence on feature branches only
BRANCH="$(git rev-parse --abbrev-ref HEAD)"
case "$BRANCH" in
  [0-9][0-9][0-9]-*)
    echo "<PERSON><PERSON>: validating spec-kit prerequisites for branch $BRANCH"
    bash "Sistema de Anamnese Médica/scripts/check-task-prerequisites.sh" || exit 1
    ;;
  *)
    echo "<PERSON><PERSON>: skipping spec-kit checks on branch $BRANCH"
    ;;
esac
