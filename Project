# Repository Layout Migration Guide

## 🎯 What Changed

The repository has been reorganized for better structure, navigation, and maintainability.

## 📁 Directory Changes

### Before → After

```
# Old Structure
├── .claude/commands/          → .ai/claude/commands/
├── memory/                    → docs/governance/
├── templates/                 → docs/templates/
├── scripts/                   → automation/scripts/
└── (no root docs)            → README.md, ARCHITECTURE.md, CONTRIBUTING.md

# New Structure
├── docs/                      # All documentation
│   ├── governance/           # Constitution & rules
│   ├── templates/            # SDD templates
│   └── workflows/            # Process guides
├── .ai/                      # AI integration
│   ├── claude/commands/      # Claude commands
│   └── prompts/              # Reusable prompts
├── automation/               # Scripts & workflows
│   └── scripts/
│       ├── feature-lifecycle/ # Core SDD scripts
│       └── utilities/        # Helper scripts
└── (root files)             # Project overview docs
```

## 🔄 Path Updates

### Script Paths
- `scripts/create-new-feature.sh` → `automation/scripts/feature-lifecycle/create-new-feature.sh`
- `scripts/setup-plan.sh` → `automation/scripts/feature-lifecycle/setup-plan.sh`
- `scripts/check-task-prerequisites.sh` → `automation/scripts/feature-lifecycle/check-task-prerequisites.sh`
- `scripts/common.sh` → `automation/scripts/utilities/common.sh`
- `scripts/get-feature-paths.sh` → `automation/scripts/utilities/get-feature-paths.sh`
- `scripts/update-agent-context.sh` → `automation/scripts/utilities/update-agent-context.sh`

### Template Paths
- `templates/spec-template.md` → `docs/templates/spec-template.md`
- `templates/plan-template.md` → `docs/templates/plan-template.md`
- `templates/tasks-template.md` → `docs/templates/tasks-template.md`
- `templates/agent-file-template.md` → `docs/templates/agent-file-template.md`

### Governance Paths
- `memory/constitution.md` → `docs/governance/constitution.md`
- `memory/constitution_update_checklist.md` → `docs/governance/update-checklist.md`

### AI Command Paths
- `.claude/commands/` → `.ai/claude/commands/`

## 🛠️ What Still Works

### Claude Commands
All Claude commands (`/specify`, `/plan`, `/tasks`) continue to work exactly the same. The commands have been updated internally to use the new paths.

### Existing Features
All existing feature directories in `specs/` remain unchanged and functional.

### Scripts
All automation scripts work the same way, just from new locations.

## 📚 New Documentation

### Root Level Guides
- **README.md** - Project overview and quick start
- **ARCHITECTURE.md** - System design and components
- **CONTRIBUTING.md** - Development guidelines and workflows

### Process Documentation
- **docs/workflows/sdd-lifecycle.md** - Complete SDD process guide
- **docs/workflows/feature-development.md** - Development patterns and best practices

### AI Integration
- **.ai/prompts/code-review.md** - Constitutional compliance review template
- **.ai/prompts/feature-analysis.md** - Feature analysis framework

## 🚀 Benefits

### Better Organization
- **Logical Grouping** - Related files are now grouped by purpose
- **Clear Separation** - Documentation, automation, and AI tools are distinct
- **Improved Navigation** - Easier to find what you need

### Enhanced Documentation
- **Root-level Guides** - Quick access to project information
- **Process Documentation** - Detailed workflow guides
- **AI Prompts** - Reusable templates for common tasks

### Maintainability
- **Centralized Templates** - All templates in one location
- **Organized Scripts** - Scripts grouped by function
- **Version Control** - Better tracking of changes

## 🔧 Migration Actions Taken

1. **Created new directory structure**
2. **Moved all files to appropriate locations**
3. **Updated internal path references in scripts and commands**
4. **Created comprehensive documentation**
5. **Added AI prompt templates**
6. **Cleaned up old directory structure**

## ✅ Verification

To verify everything works:

```bash
# Test Claude commands (should work unchanged)
/specify "test feature"
/plan
/tasks

# Test script paths (use new paths)
./automation/scripts/feature-lifecycle/create-new-feature.sh --help
./automation/scripts/utilities/get-feature-paths.sh

# Check documentation
ls docs/
ls .ai/
```

---

*This migration improves repository organization while maintaining full backward compatibility for all SDD workflows.*